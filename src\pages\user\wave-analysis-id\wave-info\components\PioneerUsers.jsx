import PioneerUsersTable from "./PioneerUsersTable copy";

const PioneerUsers = ({ data, setSelectedNode, selectedNode }) => {
  return (
    <div
      className="bg-light-neutral-surface-card rounded-lg p-3 h-[635px] overflow-y-auto scrollbar-thin"
      style={{
        boxShadow: "0px 2px 20px 0px #0000000D",
      }}
    >
      <p className="font-subtitle-large mb-7">
        کاربران پیشرو در شبکه انتشار محتوا
      </p>
      <PioneerUsersTable
        data={data?.top_sources}
        setSelectedNode={setSelectedNode}
        selectedNode={selectedNode}
      />
    </div>
  );
};

export default PioneerUsers;
