import { useEffect, useState } from "react";
import Accordion from "components/ui/Accordion";
import { useBulletinStore } from "store/bulletinStore";
import MediaBadge from "components/ui/MediaBadge";
import StatsCard from "./StatsCard";
import { toPersianNumber } from "utils/helper";
import useSearchStore from "store/searchStore";
import BulletinSearchBox from "../components/bulletinSearchBox/index.jsx";

const Step3 = () => {
  const { chart, content, contentManual, type } = useBulletinStore(
    (state) => state.bulletin,
  );
  const { filters, query } = useSearchStore();
  const setBulletin = useBulletinStore((state) => state.setBulletin);
  const [isOpen, setIsOpen] = useState(null);

  const platforms = [
    {
      text: "فیلتر در ایکس (توییتر)",
      platform: "twitter",
      charts: [
        { text: "نمودار روند انتشار", value: "process" },
        { text: "آمار کلی عددی ", value: "info" },
        { text: "تحلیل احساسات", value: "sentiment" },
        // { text: "دسته‌بندی سن", value: "age" },
        // { text: "دسته‌بندی جنسیت", value: "gender" },
        { text: "دسته‌بندی موضوعی", value: "category" },
        { text: "برترین منابع", value: "top_source" },
        { text: "محتوای توهین‌آمیز", value: "offensive" },
        { text: "هشتگ‌های پرتکرار", value: "hashtag_list" },
        { text: "ابرهشتگ‌های پرتکرار", value: "hashtag_cloud" },
        { text: "لیست کلمات پرتکرار", value: "word_list" },
        { text: "ابر کلمات پرتکرار", value: "word_cloud" },
      ],
    },
    {
      text: "فیلتر در تلگرام",
      platform: "telegram",
      charts: [
        { text: "نمودار روند انتشار", value: "process" },
        { text: "آمار کلی عددی ", value: "info" },
        { text: "تحلیل احساسات", value: "sentiment" },
        { text: "دسته‌بندی موضوعی", value: "category" },
        { text: "برترین منابع", value: "top_source" },
        { text: "محتوای توهین‌آمیز", value: "offensive" },
        { text: "هشتگ‌های پرتکرار", value: "hashtag_list" },
        { text: "ابرهشتگ‌های پرتکرار", value: "hashtag_cloud" },
        { text: "لیست کلمات پرتکرار", value: "word_list" },
        { text: "ابر کلمات پرتکرار", value: "word_cloud" },
      ],
    },
    {
      text: "فیلتر در اینستاگرام",
      platform: "instagram",
      charts: [
        { text: "نمودار روند انتشار", value: "process" },
        { text: "آمار کلی عددی ", value: "info" },
        { text: "تحلیل احساسات", value: "sentiment" },
        // { text: "دسته‌بندی سن", value: "age" },
        // { text: "دسته‌بندی جنسیت", value: "gender" },
        { text: "دسته‌بندی موضوعی", value: "category" },
        { text: "برترین منابع", value: "top_source" },
        { text: "محتوای توهین‌آمیز", value: "offensive" },
        { text: "هشتگ‌های پرتکرار", value: "hashtag_list" },
        { text: "ابرهشتگ‌های پرتکرار", value: "hashtag_cloud" },
        { text: "لیست کلمات پرتکرار", value: "word_list" },
        { text: "ابر کلمات پرتکرار", value: "word_cloud" },
      ],
    },
    {
      text: "فیلتر در سایت‌های خبری",
      platform: "news",
      charts: [
        { text: "نمودار روند انتشار", value: "process" },
        { text: "آمار کلی عددی ", value: "info" },
        { text: "دسته‌بندی موضوعی", value: "category" },
        { text: "برترین منابع", value: "top_source" },
        { text: "لیست کلمات پرتکرار", value: "word_list" },
        { text: "ابر کلمات پرتکرار", value: "word_cloud" },
      ],
    },
    // { text: "فیلتر در ایتا", platform: "eitaa" ,charts:[ {text:"نمودار روند انتشار",value:"process"}, {text:"آمار کلی عددی ",value:"info"}, {text:"تحلیل احساسات",value:"sentiment"}, "age", "gender", "category", "top_source", "word_list", "word_cloud", "hashtag_list", "hashtag_cloud" ]},
  ];

  const updateBulletin = (data) => {
    if (data.platform && Object.keys(data.platform).length === 0)
      delete data.platform;
    if (!data.platform && data.filter) delete data.filter;
    if (data.platform && !data.filter) data.filter = filters;
    setBulletin({ chart: data });
  };

  const selectChart = (Chart, platform) => {
    const help = JSON.parse(JSON.stringify(chart));
    if (!help.platform) help.platform = {};
    if (Object.hasOwn(help.platform, platform)) {
      const index = help.platform?.[platform].chart_type.indexOf(Chart);
      if (index != -1) {
        help.platform?.[platform].chart_type.splice(index, 1);
        if (help.platform?.[platform].chart_type.length === 0) {
          delete help.platform?.[platform];
          updateBulletin(help);
          return;
        }
        updateBulletin(help);
        return;
      }
      help.platform[platform].chart_type = [
        ...help.platform[platform].chart_type,
        Chart,
      ];
      updateBulletin(help);
      return;
    }
    help.platform[platform] = { chart_type: [Chart] };
    updateBulletin(help);
    return true;
  };

  const addAllChart = (platform) => {
    const help = JSON.parse(JSON.stringify(chart));
    if (!help.platform) help.platform = {};
    if (
      help.platform?.[platform]?.chart_type.length ===
      platforms.filter((x) => x.platform === platform)[0].charts.length
    ) {
      delete help.platform[platform];
      updateBulletin(help);
      return;
    }
    help.platform[platform] = {
      chart_type: platforms
        .filter((x) => x.platform === platform)[0]
        ?.charts.map((item) => item.value),
    };
    updateBulletin(help);
    return;
  };

  const { setShowFilterList, setShowSearchBox, setIsFilterListOpen } =
    useSearchStore();

  useEffect(() => {
    setShowFilterList(true);
    setShowSearchBox(false);
    setIsFilterListOpen(false);
    setIsOpen(0);
  }, []);

  useEffect(() => {
    const help = JSON.parse(JSON.stringify(chart));
    for (const key in filters) {
      if (Array.isArray(filters[key]) && filters[key].includes("all")) {
        filters[key] = [];
      }
    }
    help.filter = filters;
    help.q =
      type === "manual"
        ? chart.q !== contentManual.q && !contentManual.q
          ? chart.q
          : contentManual.q
        : content.q;
    updateBulletin(help);
  }, [filters]);

  return (
    <>
      <BulletinSearchBox
        query={
          // type === "manual"
          //   ? contentManual.q ||
          //     contentManual?.twitter?.[0]?.q ||
          //     contentManual?.telegram?.[0]?.q ||
          //     contentManual?.instagram?.[0]?.q ||
          //     contentManual?.news?.[0]?.q
          //   : content.q
          query || chart.q
        }
        // disabled={true}
        setQuery={(e) => {
          const help = JSON.parse(JSON.stringify(chart));
          chart.q = e;
          setBulletin(help);
        }}
        noFilter={true}
      />
      <div className="px-6 bg-white rounded-lg [direction:rtl]">
        <div className=" divide-y">
          {platforms.map(({ platform, text, charts }, index) => (
            <div className="flex">
              <div className="w-full" onClick={() => setIsOpen(index)}>
                <Accordion
                  isOpen={index === isOpen}
                  showBox={false}
                  Header={
                    <div className="flex items-center gap-2 font-body-medium">
                      <div>{<MediaBadge media={platform} />}</div>
                      <div>{text}</div>
                      {!!chart[platform]?.chart_type?.length && (
                        <div>
                          (
                          {toPersianNumber(chart[platform]?.chart_type?.length)}
                          )
                        </div>
                      )}
                    </div>
                  }
                >
                  <div className="flex flex-col gap-6">
                    <div className="font-body-medium flex justify-between">
                      <p>
                        نمودارهایی که قصد دارید در این بولتن قرار بگیرد را
                        انتخاب کنید
                      </p>
                      <span
                        className="text-light-primary-text-rest underline underline-offset-4 cursor-pointer"
                        onClick={() => addAllChart(platform)}
                      >
                        انتخاب همه
                      </span>
                    </div>
                    <div className="grid grid-cols-6 gap-6">
                      {charts.map((item) => (
                        <StatsCard
                          // key={nanoid()}
                          text={item.text}
                          pic={`/bulletin-stats/${item.value}.png`}
                          value={item.value}
                          handleChange={(chart) => {
                            selectChart(chart, platform);
                          }}
                          checked={
                            chart.platform &&
                            chart.platform[platform] &&
                            chart.platform[platform].chart_type.includes(
                              item.value,
                            )
                          }
                        />
                      ))}
                    </div>
                  </div>
                </Accordion>
              </div>
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default Step3;
