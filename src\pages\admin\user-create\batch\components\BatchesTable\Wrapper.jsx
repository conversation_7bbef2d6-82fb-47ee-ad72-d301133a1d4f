import { Info } from "@phosphor-icons/react";
import ToolTip from "components/ui/ToolTip";

const Wrapper = ({ children }) => {
  return (
    <div className="py-4 flex flex-col gap-4">
      <ul className="font-overline-medium text-light-neutral-text-medium grid grid-cols-7 select-none">
        <li className="">نام کاربری</li>
        <li className="">شماره موبایل</li>
        <li className="">نام و نام خانوادگی</li>
        <li>نقش کاربر</li>
        <li className="">پلن کاربری</li>
        <li className=" flex items-center">
          <p>رمز عبور</p>
          <ToolTip comp={<p>asdasdsa</p>}>
            <div className="flex flex-col">
              <Info size={17} />
            </div>
          </ToolTip>
        </li>
        <li className="">
          <p>اقدامات</p>
        </li>
      </ul>
      {children}
    </div>
  );
};

export default Wrapper;
