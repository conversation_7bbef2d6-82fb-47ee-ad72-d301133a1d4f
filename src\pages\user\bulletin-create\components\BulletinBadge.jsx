import { X } from "@phosphor-icons/react";

const BulletinBadge = ({
  title,
  icon: Icon,
  hasDeleteIcon = false,
  icon<PERSON><PERSON><PERSON><PERSON><PERSON>,
  clickHand<PERSON>,
  iconClassName,
  iconColor,
}) => {
  return (
    <div
      onClick={(e) => {
        clickHandler();
      }}
      className="flex items-center flex-row-reverse gap-2 px-2 h-[30px] w-fit rounded-lg bg-light-neutral-background-low border-[1px] border-light-neutral-border-low-rest cursor-pointer"
    >
      {hasDeleteIcon && (
        <X
          className="text-light-neutral-text-medium"
          onClick={(e) => {
            e.stopPropagation();
            iconClickHandler();
          }}
        />
      )}
      <p className="font-body-small">{title}</p>
      {Icon && (
        <Icon
          color={iconColor}
          className={`flex items-center justify-center text-center p-1 rounded-full w-5 h-5 ${iconClassName}`}
        />
      )}
      {/* <img className="rounded-full w-[18px] h-[18px]" src={img} alt={title} /> */}
    </div>
  );
};

export default BulletinBadge;
