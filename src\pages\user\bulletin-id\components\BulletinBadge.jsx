// import { X } from "@phosphor-icons/react";

// import age_img from "/public/bulletin-stats/age.png";
// import category_img from "/public/bulletin-stats/category.png";
// import gender_img from "/public/bulletin-stats/gender.png";
// import hashtag_cloud_img from "/public/bulletin-stats/hashtag_cloud.png";
// import hashtag_list_img from "/public/bulletin-stats/hashtag_list.png";
// import info_img from "/public/bulletin-stats/info.png";
// import process_img from "/public/bulletin-stats/process.png";
// import sentiment_img from "/public/bulletin-stats/sentiment.png";
// import top_source_img from "/public/bulletin-stats/top_source.png";
// import word_cloud_img from "/public/bulletin-stats/word_cloud.png";
// import word_list_img from "/public/bulletin-stats/word_list.png";

// const BulletinBadge = ({
//   title,
//   img,
//   icon,
//   hasDeleteIcon = false,
//   iconClickHandler,
//   clickHandler,
//   iconBgColor,
// }) => {
//   return (
//     <div
//       onClick={(e) => {
//         clickHandler();
//       }}
//       className="flex items-center flex-row-reverse gap-2 px-2 h-[30px] w-fit rounded-lg bg-light-neutral-background-low border-[1px] border-light-neutral-border-low-rest cursor-pointer"
//     >
//       {hasDeleteIcon && (
//         <X
//           className="text-light-neutral-text-medium"
//           onClick={(e) => {
//             e.stopPropagation();
//             iconClickHandler();
//           }}
//         />
//       )}
//       <p className="font-body-small">{title}</p>
//       {img && (
//         <img className="rounded-full w-[18px] h-[18px]" src={img} alt={title} />
//       )}

//       {icon && (
//         <div
//           className={`rounded-full w-[25px] h-[25px] flex items-center justify-center text-white bg-[${iconBgColor}]`}
//         >
//           {icon}
//         </div>
//       )}
//     </div>
//   );
// };

// export default BulletinBadge;

import { X } from "@phosphor-icons/react";

// Importing the images
import age_img from "assets/images/bulletin-stats/age.png";
import category_img from "assets/images/bulletin-stats/category.png";
import gender_img from "assets/images/bulletin-stats/gender.png";
import hashtag_cloud_img from "assets/images/bulletin-stats/hashtag_cloud.png";
import hashtag_list_img from "assets/images/bulletin-stats/hashtag_list.png";
import info_img from "assets/images/bulletin-stats/info.png";
import process_img from "assets/images/bulletin-stats/process.png";
import sentiment_img from "assets/images/bulletin-stats/sentiment.png";
import top_source_img from "assets/images/bulletin-stats/top_source.png";
import word_cloud_img from "assets/images/bulletin-stats/word_cloud.png";
import word_list_img from "assets/images/bulletin-stats/word_list.png";

// Map of image names to default images
const defaultImages = {
  age: age_img,
  category: category_img,
  gender: gender_img,
  hashtag_cloud: hashtag_cloud_img,
  hashtag_list: hashtag_list_img,
  info: info_img,
  process: process_img,
  sentiment: sentiment_img,
  top_source: top_source_img,
  word_cloud: word_cloud_img,
  word_list: word_list_img,
  offensive: sentiment_img,
};

const BulletinBadge = ({
  title,
  img,
  icon,
  hasDeleteIcon = false,
  iconClickHandler,
  clickHandler,
  iconBgColor,
}) => {
  // Use default image if img prop matches one of the keys, else use provided img
  const imageToShow = defaultImages[img] || img;

  return (
    <div
      onClick={(e) => {
        clickHandler();
      }}
      className="flex items-center flex-row-reverse gap-2 px-2 h-[30px] w-fit rounded-lg bg-light-neutral-background-low border-[1px] border-light-neutral-border-low-rest cursor-pointer"
    >
      {hasDeleteIcon && (
        <X
          className="text-light-neutral-text-medium"
          onClick={(e) => {
            e.stopPropagation();
            iconClickHandler();
          }}
        />
      )}
      <p className="font-body-small">{title}</p>
      {imageToShow && (
        <img
          className="rounded-full w-[18px] h-[18px]"
          src={imageToShow}
          alt={title}
        />
      )}

      {icon && (
        <div
          className={`rounded-full w-[25px] h-[25px] flex items-center justify-center text-white bg-[${iconBgColor}]`}
        >
          {icon}
        </div>
      )}
    </div>
  );
};

export default BulletinBadge;
