import { toP<PERSON>ian<PERSON><PERSON>ber } from "utils/helper";
import BulletinBadge from "../components/BulletinBadge";
import { useBulletinStore } from "store/bulletinStore.js";
import PLATFORMS from "constants/platforms.js";
import { InstagramLogo, TelegramLogo, XLogo, Rss } from "@phosphor-icons/react";

const AutoStep2Footer = () => {
  const { content } = useBulletinStore((state) => state.bulletin);

  const platforms = ["twitter", "telegram", "instagram", "news"];
  const sortList = {
    date: "زمان انتشار",
    view: "میزان بازدید",
    retweet: "بازنشر",
    like: "تعداد لایک",
  };

  return (
    <div className="font-body-medium flex items-center flex-wrap gap-3">
      {platforms.map((platform, index) => {
        return content?.platform?.[platform]?.map((chips) => (
          <BulletinBadge
            key={chips.sort + chips.order + platform + index}
            icon={
              platform === PLATFORMS.TWITTER
                ? XLogo
                : platform === PLATFORMS.TELEGRAM
                  ? TelegramLogo
                  : platform === PLATFORMS.INSTAGRAM
                    ? InstagramLogo
                    : platform === PLATFORMS.NEWS
                      ? Rss
                      : null
            }
            iconColor={"#fff"}
            iconClassName={
              platform === PLATFORMS.TELEGRAM
                ? "bg-[#0084C7]"
                : platform === PLATFORMS.INSTAGRAM
                  ? "bg-[#E64787]"
                  : platform === PLATFORMS.TWITTER
                    ? "bg-[#000]"
                    : platform === PLATFORMS.NEWS
                      ? "bg-[#ECA213]"
                      : null
            }
            title={`${sortList[chips.sort]} (${toPersianNumber(chips.content_count)})`}
            clickHandler={() => {}}
          />
        ));
      })}
    </div>
  );
};

export default AutoStep2Footer;
