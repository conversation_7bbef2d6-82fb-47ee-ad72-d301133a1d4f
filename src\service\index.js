import axios from "axios";

const access = localStorage.getItem("accessToken")
  ? JSON.parse(localStorage.getItem("accessToken"))
  : null;

let headers = {
  // 'Content-type': 'application/json',
  // 'Access-Control-Allow-Origin': '*'
};

if (access) {
  headers.Authorization = "Bearer " + access;
}

export default axios.create({
  baseURL: import.meta.env.VITE_API_URL || "/",
  // withCredentials: true,
  headers,
});
