import { CaretLeft } from "@phosphor-icons/react";
import PropTypes from "prop-types";
import { useEffect, useRef, useState } from "react";

const RoleDropdown = ({ roles = [], onChange }) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [selectedRoles, setSelectedRoles] = useState(roles || []);
  const dropdownRef = useRef(null);

  const roleMap = {
    regular: "کاربر عادی",
    manager: "مدیر",
    support: "پشتیبان",
  };

  const availableItems = [
    {
      value: "regular",
      label: "کاربر عادی",
    },
    {
      value: "manager",
      label: "مدیر",
    },
    {
      value: "support",
      label: "پشتیبان",
    },
  ];

  const getSelectedRolesLabel = () => {
    if (
      selectedRoles &&
      selectedRoles?.length > 0 &&
      selectedRoles?.length !== availableItems.length
    ) {
      return selectedRoles?.map((role) => roleMap[role] || role).join(", ");
    }
    return "همه";
  };

  const handleChange = (item) => {
    setSelectedRoles(() => {
      let updatedRoles = [item.value];
      if (onChange) {
        onChange(updatedRoles);
      }
      return updatedRoles;
    });
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowDropdown(false);
      }
    };

    if (showDropdown) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showDropdown]);

  return (
    <div>
      <div className="flex items-center gap-3">
        <span
          className="flex gap-2 items-center cursor-pointer hover:bg-gray-200 transition duration-200 rounded-lg p-2"
          onClick={() => setShowDropdown(!showDropdown)}
        >
          نقش:
          <span className="text-[#7f7f7f]">{getSelectedRolesLabel()}</span>
          <CaretLeft />
        </span>
        {/* <div className="w-[2px] h-5 bg-light-neutral-surface-highlight"></div>
        <span
          className="flex gap-2 items-center cursor-pointer hover:bg-gray-200 transition duration-200 rounded-lg p-2"
          onClick={() => setShowDropdown(!showDropdown)}
        >
          پلن:
          <span className="text-[#7f7f7f]">{getSelectedRolesLabel()}</span>
          <CaretLeft />
        </span>
        <div className="w-[2px] h-5 bg-light-neutral-surface-highlight"></div>

        <span
          className="flex gap-2 items-center cursor-pointer hover:bg-gray-200 transition duration-200 rounded-lg p-2"
          onClick={() => setShowDropdown(!showDropdown)}
        >
          وضعیت:
          <span className="text-[#7f7f7f]">{getSelectedRolesLabel()}</span>
          <CaretLeft />
        </span>
        <div className="w-[2px] h-5 bg-light-neutral-surface-highlight"></div> */}
      </div>

      {showDropdown && (
        <div
          className="bg-white h-auto w-[12rem] shadow-md absolute z-[10] p-4 rounded"
          ref={dropdownRef}
        >
          {availableItems.map((item) => (
            <label key={item.value} className="flex items-center gap-2 py-1">
              <input
                type="radio"
                checked={selectedRoles.includes(item.value)}
                onChange={() => handleChange(item)}
              />
              {item.label}
            </label>
          ))}
        </div>
      )}
    </div>
  );
};

export default RoleDropdown;

RoleDropdown.propTypes = {
  roles: PropTypes.array,
  onChange: PropTypes.func,
};
