import { useCallback, useEffect, useRef, useState } from "react";
import advanceSearch from "service/api/advanceSearch.js";
import Stats from "./components/Stats.jsx";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";
import useSearchStore from "store/searchStore.js";
import MasterpageLayout from "layout/masterpage-layout.jsx";
import { ToastContainer } from "react-toastify";
import OptionMenu from "./components/option-menu.jsx";
import ContentTab from "./components/content-tab.jsx";
import { buildRequestData, transformResponseData } from "utils/requestData.js";

function AdvancedSearch() {
  const breadcrumbList = [{ title: "جست‌وجوی پیشرفته" }];
  useBreadcrumb(breadcrumbList);

  const [activeTab, setActiveTab] = useState("content");
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [sort, setSort] = useState({ fa: "زمان انتشار", en: "date" });
  // const [sort_type, setSort_type] = useState("نزولی");

  const [dataArray, setDataArray] = useState([]);
  const [dataCount, setDataCount] = useState(0);

  const {
    filters,
    setFilters,
    query,
    setIsFilterListOpen,
    loadState,
    doSearch,
    setDoSearch,
  } = useSearchStore();

  useEffect(() => {
    loadState();
  }, [loadState]);

  const countLoadingOffset = useRef();
  const changeActiveTab = (name) => {
    if (query) {
      setActiveTab(name);
      if (name === "stats") {
        setIsFilterListOpen(false);
      } else {
        setIsFilterListOpen(true);
      }
    }
  };

  const handleQuery = useCallback(() => {
    const filterObject = {
      ...filters,
      q: query,
    };
    return buildRequestData(filterObject, "advance", 10);
  }, [filters, query, page, sort /*, sort_type*/]);

  const resetPageNumber = () => page !== 1 && setPage(1);
  const resetSort = () => setSort({ fa: "زمان انتشار", en: "date" });

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const getSearchData = async (abortController = null) => {
    if (loading) return null;
    if (!query || query === "") {
      setDataArray([]);
      setDataCount(0);
      return null;
    }

    setLoading(true);
    try {
      const requestData = buildRequestData(
        {
          ...filters,
          q: query,
        },
        "advance",
        10,
      );

      const responseStinas = await advanceSearch.search(
        requestData,
        // abortController,
      );

      const stinasData = responseStinas?.data?.data;
      const transformedData = transformResponseData(
        stinasData?.[filters?.platform] || [],
      );
      setDataArray(transformedData);
      setDataCount(stinasData?.total);
      if (doSearch) {
        resetPageNumber();
        setDoSearch(false);
      }
    } catch (error) {
      console.log(error);
      setDataArray([]);
      setDataCount(0);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    resetPageNumber();
    resetSort();
  }, [filters.platform]);

  useEffect(() => {
    // Avoid calling getSearchData multiple times on platform change
    let debounceTimer;
    const abortController = new AbortController();

    const handleDataFetch = async () => {
      clearTimeout(debounceTimer); // Clear previous timer

      debounceTimer = setTimeout(async () => {
        await getSearchData(abortController);
        scrollToTop(); // Call getSearchData once after a short delay
      }, 50); // Debounce with 50ms delay
    };

    handleDataFetch();

    return () => {
      clearTimeout(debounceTimer);
      abortController.abort();
    };
  }, [filters, query]);

  useEffect(() => {
    if (!doSearch) return;

    const handleDataFetch = async () => {
      await getSearchData();
      scrollToTop();
    };

    handleDataFetch();
  }, [doSearch]);

  useEffect(() => {
    setFilters({ page: page });
  }, [page]);

  useEffect(() => {
    resetPageNumber();
    setFilters({ sort: sort.en, page: 1 });
  }, [sort]);

  return (
    <MasterpageLayout>
      <div className={"flex-1 flex-col"}>
        {dataArray.length > 0 && (
          <div ref={countLoadingOffset}>
            <OptionMenu
              sort={sort}
              setSort={setSort}
              activeTab={activeTab}
              changeActiveTab={changeActiveTab}
              handleQuery={handleQuery}
              dataCount={dataCount}
            />
          </div>
        )}
        {activeTab === "content" ? (
          <ContentTab
            platform={filters.platform}
            query={query}
            isLoading={loading}
            countLoadingOffset={countLoadingOffset}
            dataArray={dataArray}
            page={page}
            setPage={setPage}
            dataCount={dataCount}
          />
        ) : (
          <Stats />
        )}
        <ToastContainer />
      </div>
    </MasterpageLayout>
  );
}

export default AdvancedSearch;
