/* Swiper style */
.swiper-slide-active {
  background: #084ed0;
}
.highcharts-text-outline {
  stroke-width: 1;
}

.kiosk-swiper .swiper-slide-active {
  background: transparent !important;
}

.swiper-button-prev:after,
.swiper-button-next:after {
  color: black;
  font-size: 14px !important;
  font-weight: bold;
  width: 26px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #5133e44d;
  border-radius: 8px;
}

.leaflet-interactive {
  outline: none !important;
}

.highcharts-menu-item {
  font-family: iranyekan, sans-serif;
}

.rmdp-panel-body li, .rmdp-panel-body li.bg-blue {
  background-color: #432FA7!important;
}
.rmdp-panel-body li .b-deselect {
  background-color: #432FA7!important;
  padding-right: 4px!important;
}
.rmdp-panel-body li .b-date {
  font-size: 11px!important;
  direction: rtl!important;
}



/* opinion-mining swiper */
@media (max-width: 2560px) {
  .opinion-mining-swiper-container {
    max-width: 700px !important;
  }
}
@media (max-width: 1492px) {
  .opinion-mining-swiper-container {
    max-width: 550px !important;
  }
}
@media (max-width: 1704px) {
  .opinion-mining-swiper-container {
    max-width: 550px !important;
  }
}
@media (max-width: 1440px) {
  .opinion-mining-swiper-container {
    max-width: 530px !important;
  }
}
