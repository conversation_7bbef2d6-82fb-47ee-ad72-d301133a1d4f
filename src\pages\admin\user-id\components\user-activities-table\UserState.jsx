import clsx from "clsx";

const UserState = ({ state, title }) => {
  const stepStateClassName = clsx(
    "font-body-small rounded-[4px] px-1 w-20 text-center",
    {
      "text-light-inform-text-rest bg-light-inform-background-highlight":
        state === "فعال",
      "text-light-error-text-rest bg-light-error-background-highlight":
        state === "حذف‌شده" || state === "replied",
      "text-light-success-text-rest bg-light-success-background-highlight":
        state === "فعال",
    }
  );
  return <div className={stepStateClassName}>{title}</div>;
};

export default UserState;
