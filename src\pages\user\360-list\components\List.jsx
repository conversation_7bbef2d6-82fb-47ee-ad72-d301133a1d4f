import { useEffect, useState } from "react";
import ListCard from "./ListCard";
import Loading from "components/ui/Loading";
import Empty from "./Empty";
import report360 from "service/api/report360.js";

const List = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);

  const translateType = (type) => {
    switch (type) {
      case "source":
        return "منبع";
      case "entity":
        return "موجودیت";
      case "location":
        return "مکان";
      default:
        return type;
    }
  };

  const getData = async () => {
    setLoading(true);
    try {
      const response = await report360.get();
      const translatedData = response.data.data.map((item) => ({
        ...item,
        type: translateType(item.report_type),
      }));
      setData(translatedData);
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  const remove360 = async (id) => {
    setLoading(true);
    try {
      await report360.remove(id);
      setData((prevData) => prevData.filter((item) => item.id !== id));
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  useEffect(() => {
    getData();
  }, []);

  return (
    <>
      {loading && <Loading />}
      {data.length > 0 ? (
        <div className="flex flex-col gap-4">
          {data.map(
            ({ id, title, type, subject, subjectPlatform, created_at }) => (
              <ListCard
                key={id}
                id={id}
                title={title}
                type={type}
                subject={subject ? subject : "- - -"}
                subjectPlatform={subjectPlatform}
                created_at={created_at}
                render={getData}
                remove360={remove360}
                is360={true}
              />
            )
          )}
        </div>
      ) : (
        <Empty />
      )}
    </>
  );
};

export default List;
