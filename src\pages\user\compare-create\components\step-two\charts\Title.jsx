import ToolTip from "components/ui/ToolTip.jsx";
import { Info } from "@phosphor-icons/react";

const Title = ({ title, children, pos = null }) => {
  return (
    <div className="flex items-center gap-2 px-6">
      <span className="font-subtitle-large">{title}</span>
      {pos && pos !== "" && (
        <ToolTip comp={children} position={pos}>
          <Info size={20} className="chart-help-icons" />
        </ToolTip>
      )}
    </div>
  );
};

export default Title;
