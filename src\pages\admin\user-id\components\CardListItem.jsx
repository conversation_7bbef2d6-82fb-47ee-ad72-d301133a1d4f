import { CaretLeft } from "@phosphor-icons/react";
import { Link } from "react-router-dom";

const CardListItem = ({
  path = "",
  icon: Icon,
  title,
  description,
  onClick = () => {},
}) => {
  return (
    <Link
      to={path}
      className="flex items-center justify-between cursor-pointer transition-all hover:bg-light-neutral-background-medium px-3 py-1 rounded-md"
      onClick={onClick}
    >
      <div className="flex items-center gap-2">
        <div className="flex items-center justify-center w-10 h-10 bg-light-primary-background-highlight rounded-full">
          <Icon size={23} />
        </div>
        <div className="grid">
          <p className="font-body-large">{title}</p>
          {description && (
            <p className="font-body-small text-light-neutral-text-medium">
              {description}
            </p>
          )}
        </div>
      </div>
      <CaretLeft />
    </Link>
  );
};

export default CardListItem;
