.tippy-box[data-theme~="light"] {
  color: white;
  background-color: #432fa7;
  padding: 4px 8px;
  border-radius: 8px;
  position: relative;
  animation: fade 0.3s;
}
.tippy-box[data-theme~="light"]::before {
  content: "";
  display: inline-block;
  position: absolute;
  width: 15px;
  height: 20px;
  background-color: #432fa7;
  bottom: -7.5px;
  left: calc(50% - 7.5px);
  transform: rotate(45deg);
  z-index: -1;
}

@keyframes fade {
  from {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}
