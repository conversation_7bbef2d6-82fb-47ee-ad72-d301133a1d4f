import IndicatorBadge from "./IndicatorBadge";
import Seprator from "./Seprator";
import PropTypes from "prop-types";

const Container = ({ children, state, step, title }) => {
  return (
    <div className="w-full">
      <IndicatorBadge step={step} state={state}>
        {title}
      </IndicatorBadge>
      <div className="flex gap-6 w-full">
        {step < 5 && <Seprator step={step} state={state} />}
        {state === step && (
          <div className="p-6 grid grid-cols-12 gap-12 w-full">{children}</div>
        )}
      </div>
    </div>
  );
};

Container.propTypes = {
  children: PropTypes.node,
  state: PropTypes.object,
  step: PropTypes.number,
  title: PropTypes.string,
};

export default Container;
