import { useEffect, useState } from "react";
import Chips from "../components/Chips.jsx";

const SelectDay = ({ initialValue, handleChange }) => {
  const [days, setDays] = useState(
    Array.isArray(initialValue) ? [...initialValue] : [initialValue],
  );

  const handleDays = (day) => {
    setDays((l) => {
      const help = [...l];
      const index = help.indexOf(day);
      if (index != -1) {
        help.splice(index, 1);
      } else {
        help.push(day);
      }
      return [...help];
    });
  };

  useEffect(() => {
    handleChange(days);
  }, [days]);

  return (
    <div className="flex gap-4">
      <Chips
        value="saturday"
        handleChange={handleDays}
        isSelect={days.includes("saturday")}
      >
        شنبه
      </Chips>
      <Chips
        value="sunday"
        handleChange={handleDays}
        isSelect={days.includes("sunday")}
      >
        یک‌شنبه
      </Chips>
      <Chips
        value="monday"
        handleChange={handleDays}
        isSelect={days.includes("monday")}
      >
        دوشنبه
      </Chips>
      <Chips
        value="tuesday"
        handleChange={handleDays}
        isSelect={days.includes("tuesday")}
      >
        سه‌شنبه
      </Chips>
      <Chips
        value="wednesday"
        handleChange={handleDays}
        isSelect={days.includes("wednesday")}
      >
        چهارشنبه
      </Chips>
      <Chips
        value="thursday"
        handleChange={handleDays}
        isSelect={days.includes("thursday")}
      >
        پنج‌شنبه
      </Chips>
      <Chips
        value="friday"
        handleChange={handleDays}
        isSelect={days.includes("friday")}
      >
        جمعه
      </Chips>
    </div>
  );
};

export default SelectDay;
