import { useEffect, useState } from "react";
import ListCard from "./ListCard";
import Loading from "components/ui/Loading";
import Empty from "./Empty";
import userService from "service/api/userService.js";
import Paginate from "components/ui/Paginate.jsx";

const List = ({ filter, setFilter, loading = false, setLoading }) => {
  const [data, setData] = useState([]);
  const [total, setTotal] = useState(0);
  const getData = async () => {
    setLoading(true);
    try {
      const response = await userService.getUserList(filter, {
        page: filter.page,
        page_size: filter.page_size,
      });
      setData(response?.data?.data?.users || []);
      setTotal(response?.data?.data?.total_user || 0);
      // setData(sampleData);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getData();
  }, [filter]);

  return (
    <>
      {loading && <Loading />}
      {data.length > 0 ? (
        <div className="flex flex-col gap-4">
          {data.map(
            ({
              id,
              username,
              first_name,
              last_name,
              avatar,
              role,
              email,
              phone_number,
              register_at,
              last_login,
            }) => (
              <ListCard
                key={id}
                id={id}
                avatar={avatar}
                username={username ? `${username}@` : ""}
                fullname={
                  first_name && last_name ? `${first_name} ${last_name}` : ""
                }
                role={role}
                email={email}
                phone={phone_number}
                created_at={register_at}
                last_seen={last_login}
                render={getData}
              />
            ),
          )}
          <Paginate
            per_page={filter.page_size}
            page={filter.page}
            setPage={(page) => setFilter({ ...filter, page: page })}
            dataCount={total}
          />
        </div>
      ) : (
        <Empty />
      )}
    </>
  );
};

export default List;
