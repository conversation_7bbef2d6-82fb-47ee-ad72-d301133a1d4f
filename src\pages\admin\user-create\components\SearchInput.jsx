import { Check, MagnifyingGlass, SpinnerGap } from "@phosphor-icons/react";
import { useMemo, useRef, useState } from "react";
import filter from "service/api/filter";
import useClickAway from "hooks/useClickAway";

const SearchInput = ({ setFilterData }) => {
  const [activeDropDown, setActiveDropdown] = useState(false);
  const [filters, setFilters] = useState([]);
  const [selectedFilter, setSelectedFilter] = useState({});
  const [loading, setLoading] = useState(false);
  const [text, setText] = useState("");
  const ref = useRef(null);
  const getFilters = async () => {
    setLoading(true);
    try {
      const res = await filter.get();
      setFilters(res.data.data.user);
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };
  const showFilters = useMemo(() => {
    return filters.filter((item) => item.title?.includes(text));
  }, [text, filters]);
  useClickAway(ref, () => setActiveDropdown(false));

  return (
    <div className="relative" ref={ref}>
      <div className="flex items-center px-4 py-[10px] border border-light-neutral-border-medium-rest rounded-md gap-2">
        <MagnifyingGlass className="text-light-neutral-text-low" />
        <input
          type="text"
          className="w-full outline-none font-body-large"
          placeholder="نام فیلتر را جست‌وجو کنید"
          onFocus={() => {
            getFilters();
            setActiveDropdown(true);
          }}
          onChange={(e) => {
            setText(e.target.value);
          }}
          value={text}
        />
      </div>

      {activeDropDown && (
        <div className="absolute bg-white w-full">
          {!!loading ? (
            <div className="flex items-center justify-cente rounded-lg border border-light-neutral-border-low-rest shadow-[0px_2px_20px_0px_#0000000D] h-28">
              <SpinnerGap
                className={"animate-spin text-center w-full"}
                size={24}
              ></SpinnerGap>
            </div>
          ) : (
            <>
              {showFilters.length > 0 ? (
                <div className="flex flex-col gap-1 rounded-lg border border-light-neutral-border-low-rest shadow-[0px_2px_20px_0px_#0000000D] p-4 scrollbar-thin overflow-auto h-28">
                  {showFilters.map((item) => (
                    <div
                      className="hover:bg-light-neutral-surface-highlight hover:rounded-lg font-body-small p-2 flex justify-between hover:cursor-pointer"
                      onClick={() => {
                        setSelectedFilter(item);
                        setText(item.title);
                        setFilterData(item);
                        setActiveDropdown(false);
                      }}
                    >
                      <span>{item.title}</span>
                      {selectedFilter.title === item.title && (
                        <Check size={16} />
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center justify-center shadow-[0px_2px_20px_0px_#0000000D]">
                  <span className={"inline-flex flex-1 text-body-small"}>
                    موردی یافت نشد
                  </span>
                </div>
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default SearchInput;
