import Doughnut from "components/Charts/Doughnut";
import { toPersianNumber } from "utils/helper";

const WaveFakeDoughnut = ({ fetchedContentAge }) => {
  const chartData = fetchedContentAge?.total
    ? Object.entries(fetchedContentAge.total).map(([key, value]) => ({
        name: `#${key}`,
        y: value,
      }))
    : [];
  return (
    <div
      className="bg-light-neutral-surface-card rounded-lg p-3"
      style={{
        boxShadow: "0px 2px 20px 0px #0000000D",
      }}
    >
      <p className="font-subtitle-large mb-7 ">
        محتوای منتشر شده بر اساس قدمت حساب‌ها
      </p>
      {chartData.length ? (
        <Doughnut
          colLayout={true}
          name="sentiment"
          size={"75%"}
          height={400}
          data={chartData}
          legendFormatter={function () {
            return `<div style="font-family: iranyekan, sans-serif; display: grid; grid-template-columns: 1fr auto; gap: 16px; width: 100%; padding: 4px; align-items: center;">
                            <span style="font-size: 16px; color: #333;">
                               ${toPersianNumber(this.y)}
                            </span>
        
                            <div style="display: flex; gap: 8px; align-items: center;">
                                <span style="font-size: 14px; color: #555;">
                                ${
                                  this.name == "#post"
                                    ? "توئیت"
                                    : this.name == "#repost"
                                    ? "ریتوئیت"
                                    : this.name == "#quote"
                                    ? "کوت"
                                    : this.name == "#reply"
                                    ? "ریپلای"
                                    : ""
                                }
                                </span>
                                <div style="background-color: ${
                                  this.color
                                }; border-radius: 50%; width: 12px; height: 12px;"></div>
                            </div>
                        </div>`;
          }}
          tooltipFormatter={function () {
            return `<div style="display: flex; flex-direction: column; gap: 8px; text-align: center; font-family: iranyekan;">
                            <p>${
                              this.key == "#post"
                                ? "توئیت"
                                : this.key == "#repost"
                                ? "ریتوئیت"
                                : this.key == "#quote"
                                ? "کوت"
                                : this.key == "#reply"
                                ? "ریپلای"
                                : ""
                            }</p>
                            <p>${toPersianNumber(this.y)}</p>
                          </div>`;
          }}
          colors={["#6D72E5", "#1DCEA3", "#FFB946", "#E0526A"]}
        />
      ) : (
        <div className="w-full h-60 flex justify-center pt-4 items-center text-lg text-light-neutral-text-low font-body-bold-medium min-h-96">
          داده ای برای نمایش وجود ندارد
        </div>
      )}
    </div>
  );
};

export default WaveFakeDoughnut;
