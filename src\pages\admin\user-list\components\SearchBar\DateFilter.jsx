import { Calendar } from "@phosphor-icons/react";
import PropTypes from "prop-types";
import RangeDatePicker from "components/ui/RangeDatePicker.jsx";

const DateFilter = ({ title, handleDateChange, selectedDateRange }) => {
  return (
    <div className="flex items-center gap-2">
      <span className="flex gap-2 items-center cursor-pointer">
        بازه زمانی بررسی:
      </span>

      <div className="flex items-center w-80 rounded-md outline-1 outline-light-neutral-border-low-rest bg-light-neutral-background-low px-1 py-2 text-[#7f7f7f]">
        <RangeDatePicker
          onChange={handleDateChange}
          from={selectedDateRange.date_from}
          to={selectedDateRange.date_to}
        />
        <span className="action-icon text-left">
          <Calendar size={18} color="#00000059" />
        </span>
      </div>
      {/* <div className="w-[2px] h-5 bg-light-neutral-surface-highlight"></div> */}
    </div>
  );
};

export default DateFilter;

DateFilter.propTypes = {
  handleDateChange: PropTypes.func.isRequired,
  selectedDateRange: PropTypes.shape({
    from: PropTypes.instanceOf(Date),
    to: PropTypes.instanceOf(Date),
  }).isRequired,
};
