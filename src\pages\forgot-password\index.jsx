import ForgotPasswordForm from "./components/forgot-password-form";
import loginImg from "../../assets/images/login-img.png";
import HelpMenu from "./components/help-menu/index.jsx";

function ForgotPassword() {
  return (
    <div className={"bg-light-neutral-surface-zero"}>
      <div
        className={"flex flex-row container m-auto h-screen overflow-hidden "}
      >
        <div
          className={
            "flex flex-col w-full sm:w-5/12 justify-center bg-light-neutral-surface-card pt-16 pb-6 px-8"
          }
        >
          <ForgotPasswordForm />
          <div className={"end-0 text-center"}>
            <HelpMenu />
          </div>
        </div>
        <div className={"w-7/12 hidden sm:block p-20"}>
          <div
            className={"size-full bg-center bg-no-repeat bg-contain	"}
            style={{ backgroundImage: `url(${loginImg})` }}
          ></div>
        </div>
      </div>
    </div>
  );
}

export default ForgotPassword;
