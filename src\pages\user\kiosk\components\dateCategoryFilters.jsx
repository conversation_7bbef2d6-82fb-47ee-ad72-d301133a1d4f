import { CaretLeft, Calendar } from "@phosphor-icons/react";
import PropTypes from "prop-types";
import RangeDatePicker from "../../../../components/ui/RangeDatePicker";
import { useEffect, useRef, useState } from "react";

const DateCategoryFilters = ({
  handleDateChange,
  categoryModal,
  setCategoryModal,
  categories,
  selectedCategories,
  selectedCategory = false,
  handleCategoryChange,
  selectedDateRange,
  setSearchParams,
  setIsExactMatch,
  isExactMatch,
}) => {
  const categoryDropdownRef = useRef(null);
  const toggleButtonRef = useRef(null);
  const datePickerRef = useRef(null);
  const [isTodayRange, setIsTodayRange] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);

  const categoryMap = {
    sport: "ورزشی",
    economic: "اقتصادی",
    international: "بین‌الملل",
    other: "سایر",
  };

  const getSelectedCategoriesLabel = () => {
    if (selectedCategory && selectedCategory?.length > 0) {
      return selectedCategory?.map((cat) => categoryMap[cat] || cat).join(", ");
    }
    if (selectedCategories?.length > 0) {
      return selectedCategories
        .map((cat) => categoryMap[cat] || cat)
        .join(", ");
    }
    return "همه";
  };

  const checkIfYesterdayToToday = (fromDate, toDate) => {
    const today = new Date();
    const yesterday = new Date();
    yesterday.setDate(today.getDate() - 1);

    const isFromYesterday =
      fromDate.getDate() === yesterday.getDate() &&
      fromDate.getMonth() === yesterday.getMonth() &&
      fromDate.getFullYear() === yesterday.getFullYear();

    const isToToday =
      toDate.getDate() === today.getDate() &&
      toDate.getMonth() === today.getMonth() &&
      toDate.getFullYear() === today.getFullYear();

    return isFromYesterday && isToToday;
  };

  useEffect(() => {
    if (selectedDateRange?.from && selectedDateRange?.to) {
      if (
        checkIfYesterdayToToday(selectedDateRange.from, selectedDateRange.to)
      ) {
        setIsTodayRange(true);
      } else {
        setIsTodayRange(false);
      }
    }
  }, [selectedDateRange]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        datePickerRef.current &&
        !datePickerRef.current.contains(event.target)
      ) {
        setShowDatePicker(false);
      }
    };

    if (showDatePicker) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showDatePicker]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        categoryDropdownRef.current &&
        !categoryDropdownRef.current.contains(event.target) &&
        toggleButtonRef.current &&
        !toggleButtonRef.current.contains(event.target)
      ) {
        setCategoryModal(false);
      }
    };

    if (categoryModal) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [categoryModal, setCategoryModal]);

  return (
    <div className="flex gap-5 items-center">
      <div className="flex">
        <span className="flex gap-2 items-center cursor-pointer">
          بازه زمانی بررسی:
        </span>
        <div
          className="flex items-center w-80 rounded-md outline-1 outline-light-neutral-border-low-rest bg-light-neutral-background-low px-1 py-2 text-[#7f7f7f]"
          onClick={() => setShowDatePicker(true)}
        >
          <div ref={datePickerRef} className="w-full">
            <RangeDatePicker
              onChange={handleDateChange}
              from={
                selectedDateRange.from ??
                new Date(
                  new Date(
                    new Date().setMonth(new Date().getMonth() - 1),
                  ).setHours(0, 0, 0, 0),
                )
              }
              to={selectedDateRange.to}
            />
          </div>
          <span className="action-icon text-left">
            <Calendar size={18} color="#00000059" />
          </span>
        </div>
      </div>

      <div>
        <span
          className="flex gap-2 items-center cursor-pointer hover:bg-gray-200 transition duration-200 rounded-lg p-2"
          onClick={() => setCategoryModal(!categoryModal)}
          ref={toggleButtonRef}
        >
          دسته‌بندی موضوعی:
          <span className="text-[#7f7f7f]">{getSelectedCategoriesLabel()}</span>
          <CaretLeft />
        </span>
        {categoryModal && (
          <div
            className="bg-white h-auto w-[12rem] shadow-md absolute z-[10] p-4"
            ref={categoryDropdownRef}
          >
            {categories.map((category) => (
              <label key={category} className="flex items-center gap-2 py-1">
                <input
                  type="checkbox"
                  checked={
                    selectedCategory
                      ? selectedCategory?.includes(category)
                      : selectedCategories?.includes(category)
                  }
                  onChange={() => {
                    handleCategoryChange(category);
                  }}
                />
                {categoryMap[category] || category}
              </label>
            ))}
          </div>
        )}
      </div>

      <div className="flex gap-3 items-center">
        <label className="flex items-center">
          <input
            type="checkbox"
            defaultChecked={isExactMatch}
            onChange={(e) => {
              const newIsExactMatch = e.target.checked;

              if (typeof setIsExactMatch === "function") {
                setIsExactMatch(newIsExactMatch);
              } else {
                console.error("setIsExactMatch is not a function");
              }
              setSearchParams((prev) => ({
                ...prev,
                exact_match: newIsExactMatch,
              }));
            }}
          />
        </label>
        <span>این کلمه، دقیقا عنوان روزنامه باشد.</span>
      </div>
    </div>
  );
};

export default DateCategoryFilters;

DateCategoryFilters.propTypes = {
  handleDateChange: PropTypes.func.isRequired,
  categoryModal: PropTypes.bool.isRequired,
  setCategoryModal: PropTypes.func.isRequired,
  categories: PropTypes.arrayOf(PropTypes.string).isRequired,
  selectedCategories: PropTypes.arrayOf(PropTypes.string),
  selectedCategory: PropTypes.arrayOf(PropTypes.string),
  handleCategoryChange: PropTypes.func.isRequired,
  selectedDateRange: PropTypes.shape({
    from: PropTypes.instanceOf(Date),
    to: PropTypes.instanceOf(Date),
  }).isRequired,
  setSearchParams: PropTypes.func.isRequired,
  setIsExactMatch: PropTypes.func,
  isExactMatch: PropTypes.bool.isRequired,
};
