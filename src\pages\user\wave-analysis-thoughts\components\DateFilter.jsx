import { Calendar } from "@phosphor-icons/react";
import RangeDatePicker from "components/ui/RangeDatePicker.jsx";

const DateFilter = ({
  title = false,
  opinionMiningData,
  setOpinionMiningData,
}) => {
  return (
    <div className="flex">
      <span className="flex gap-2 items-center cursor-pointer">
        {title ? title : "تاریخ ایجاد:"}
      </span>
      <div className="flex items-center w-80 rounded-md outline-1 outline-light-neutral-border-low-rest bg-light-neutral-background-low px-1 py-2 text-[#7f7f7f]">
        <RangeDatePicker
          onChange={(e) =>
            setOpinionMiningData((prev) => ({
              ...prev,
              date_range: {
                from_date: e.from || prev.date_range.from_date,
                to_date: e.to || prev.date_range.to_date,
              },
            }))
          }
          from={opinionMiningData?.date_range?.from_date}
          to={opinionMiningData?.date_range?.to_date}
        />
        <span className="action-icon text-left">
          <Calendar size={18} color="#00000059" />
        </span>
      </div>
    </div>
  );
};

export default DateFilter;
