import { Eye, TrashSimple } from "@phosphor-icons/react";
import ToolTip from "components/ui/ToolTip";
import { parseTimeToPersian, toPersianNumber } from "utils/helper";
import { Link } from "react-router-dom";
import GroupsState from "pages/admin/groups-list/components/GroupsState";

const GpMembersTable = ({ data, isFromChangeList = false }) => {
  const ticketStates = ["Inactive", "Deleted", "Pending", "Active"];

  const randomState =
    ticketStates[Math.floor(Math.random() * ticketStates.length)];

  const ticketStateTitles = {
    Inactive: "غیرفعال",
    Deleted: "حذف شده",
    Pending: "در انتظار",
    Active: "فعال",
  };

  return (
    <>
      <div
        className={`grid grid-cols-${
          isFromChangeList ? "6" : "7"
        } hover:bg-light-neutral-surface-highlight py-2 cursor-pointer`}
        //   onClick={() => navigate(`/admin/ticket/list/${data?.id}`)}
      >
        <div className="flex items-center gap-1 font-body-medium">
          {!isFromChangeList && <input type="checkbox" name="" id="" />}
          <p>نام اعضا</p>
        </div>
        <div className="font-body-medium">{data?.username}</div>
        <div className="flex font-body-medium">
          {toPersianNumber(data?.phone_number)}
        </div>
        <div className="font-body-medium">کاربر عادی</div>
        <div className="font-body-medium">
          {parseTimeToPersian(data?.last_login)}
        </div>
        <div className="font-body-medium">
          {/* {parseTimeToPersianSummary(data?.register_at)} */}
          <GroupsState
            title={ticketStateTitles[randomState] || ""}
            state={randomState}
          />
        </div>
        {/*   */}
        {!isFromChangeList && (
          <div className="font-body-medium">
            <div className="flex gap-4">
              <Link
                to={`/admin/groups/detail`}
                className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
              >
                <ToolTip comp="نمایش جزئیات">
                  <Eye size={16} />
                </ToolTip>
              </Link>
              {/* <div className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer">
                <ToolTip comp="ویرایش">
                <PencilSimpleLine size={16} />
                </ToolTip>
                </div> */}
              <div className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer">
                <ToolTip comp="حذف">
                  <TrashSimple size={16} />
                </ToolTip>
              </div>
            </div>
          </div>
        )}
        <div className={`w-40 flex gap-1 font-body-medium`}>
          <span>
            {/* <TicketState
                title={
                  data?.ticket_section === "support"
                    ? "پشتیبانی"
                    : data?.ticket_section === "technical"
                    ? "فنی"
                    : data?.ticket_section === "finances"
                    ? "مالی‌و‌اداری"
                    : ""
                }
              /> */}
          </span>
        </div>
      </div>
    </>
  );
};

export default GpMembersTable;
