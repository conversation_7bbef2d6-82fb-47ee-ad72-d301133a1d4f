import { create } from "zustand";

const initialState = {
  report: {
    report_type: "source",
    start_date: "",
    q: "",
    end_date: "",
    title: "",
    description: "",
    content: {
      source_info: {},
      report_info: {},
      report_platform: "",
    },
  },
};

const use360requestStore = create((set, get) => ({
  ...initialState,

  updateReportField: (path, value) =>
    set((state) => {
      const updatedReport = { ...state?.report };
      const pathParts = path.split(".");
      let current = updatedReport;

      for (let i = 0; i < pathParts.length - 1; i++) {
        const key = pathParts[i];
        if (!current[key] || typeof current[key] !== "object") {
          current[key] = {};
        }
        current = current[key];
      }

      current[pathParts[pathParts.length - 1]] = value;
      return { report: updatedReport };
    }),

  updatePlatformContent: (platform, chartKey, updateData, NER, analysis) => {
    set((state) => {
      const updatedReport = { ...state.report };
      const targetTab = analysis ? "analysis_thoughts" : "resource_overview";

      if (updatedReport.source?.platform[platform]?.[targetTab]) {
        const currentData =
          updatedReport.source.platform[platform][targetTab][chartKey];

        if (Array.isArray(currentData)) {
          updatedReport.source.platform[platform][targetTab][chartKey] =
            Array.isArray(updateData) ? [...updateData] : [updateData];
        } else {
          updatedReport.source.platform[platform][targetTab][chartKey] =
            updateData;
        }
      }

      return { report: updatedReport };
    });
  },

  locationEntityCharts: (data, report_type = false, platform, type360) =>
    set((state) => {
      const updatedReport = { ...state.report };
      if (report_type) {
        updatedReport[type360][platform][report_type] = data;
      } else {
        updatedReport[type360][platform] = {
          ...updatedReport[type360][platform],
          ...data,
        };
      }
      return { report: updatedReport };
    }),

  getChartData: (platform, chartKey) => {
    const state = get();
    return (
      state.report.source.platform[platform]?.resource_overview[chartKey] || {}
    );
  },

  setReportData: (data) => {
    set((state) => {
      const updatedReport = { ...state.report, ...data };
      return { report: updatedReport };
    });
  },

  clearReport: () =>
    set(() => ({
      ...initialState,
    })),
}));

export default use360requestStore;
