import { useEffect, useMemo, useRef, useState } from "react";
import { DateObject } from "react-multi-date-picker";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import SampleLogo from "assets/images/preview/sample-logo.png";
import { useBulletinStore } from "store/bulletinStore.js";
import persian from "react-date-object/calendars/persian";
import persian_en from "react-date-object/locales/persian_en";
import { toPersianNumber } from "utils/helper.js";
import FetchImage from "pages/user/show-profile/components/FetchImage.jsx";
import IMAGES from "constants/images.js";

const PreviewSection = () => {
  const setBulletin = useBulletinStore((state) => state.setBulletin);
  const { metadata } = useBulletinStore((state) => state.bulletin);

  const [logoImage, setLogoImage] = useState(metadata?.logo_image);
  const [coverImage, setCoverImage] = useState();

  const [previewSlideShow, setPreviewSlideShow] = useState(IMAGES);

  const containerRef = useRef(null);

  const calculateFontSize = (baseSize) => {
    if (!containerRef.current) return baseSize;
    const containerWidth = containerRef.current.clientWidth;
    const referenceWidth = 550; // Approx. A4 width in pixels at 96 DPI
    const scaleFactor = containerWidth / referenceWidth;
    return `${baseSize * scaleFactor}px`;
  };

  const titleStyle = useMemo(() => {
    const style = metadata?.style?.cover_title || {};
    return {
      textAlign: style?.alignment || "center",
      fontWeight: style?.bold === true ? "bold" : 400,
      fontStyle: style?.italic === true ? "italic" : "normal",
      fontSize: calculateFontSize(parseFloat(style?.font_size) || 26),
      // fontSize: style?.font_size || "24px",
      color: style?.text_color || "#00000",
      backgroundColor: style?.text_highlight || "auto",
      textDecoration: style?.underlined === true ? "underline" : "none",
    };
  }, [metadata?.style?.cover_title]);

  const subTitleStyle = useMemo(() => {
    const style = metadata?.style?.cover_subtitle || {};
    return {
      textAlign: style?.alignment || "center",
      fontWeight: style?.bold === true ? "bold" : 400,
      fontStyle: style?.italic === true ? "italic" : "normal",
      fontSize: calculateFontSize(parseFloat(style?.font_size) || 20),
      // fontSize: style?.font_size || "20px",
      color: style?.text_color || "#00000",
      backgroundColor: style?.text_highlight || "auto",
      textDecoration: style?.underlined === true ? "underline" : "none",
    };
  }, [metadata?.style?.cover_subtitle]);

  const creatorStyle = useMemo(() => {
    const style = metadata?.style?.creator || {};
    return {
      textAlign: style?.alignment || "center",
      fontWeight: style?.bold === true ? "bold" : 400,
      fontStyle: style?.italic === true ? "italic" : "normal",
      fontSize: calculateFontSize(parseFloat(style?.font_size) || 14),
      // fontSize: style?.font_size || "14px",
      color: style?.text_color || "#00000",
      backgroundColor: style.text_highlight || "auto",
      textDecoration: style?.underlined === true ? "underline" : "none",
    };
  }, [metadata?.style?.creator]);

  useEffect(() => {
    if (!metadata?.cover_image) return;
    const index = previewSlideShow
      .map((prev) => prev.key)
      .indexOf(metadata?.cover_image);
    if (index >= 0) {
      handleSlideFocus(index);
    } else {
      setPreviewSlideShow([
        {
          file: metadata.cover_image?.toString().startsWith("/")
            ? `${metadata.cover_image}`
            : IMAGES[
                IMAGES.map((item) => item.key).indexOf(metadata.cover_image)
              ]?.file || metadata.cover_image,
          key: metadata.cover_image,
        },
        ...IMAGES,
      ]);
      setCoverImage(metadata.cover_image);
    }
  }, []);

  useEffect(() => {
    if (!metadata?.cover_image) {
      setPreviewSlideShow([...IMAGES]);
      handleSlideFocus(0);
      return;
    }
    const index = previewSlideShow
      .map((prev) => prev.key)
      .indexOf(metadata?.cover_image);
    if (index < 0) {
      handleSlideFocus(0);
      setTimeout(() => {
        setPreviewSlideShow([
          {
            file: metadata.cover_image?.toString().startsWith("/")
              ? `${metadata.cover_image}`
              : IMAGES[
                  IMAGES.map((item) => item.key).indexOf(metadata.cover_image)
                ]?.file || metadata.cover_image,
            key: metadata.cover_image,
          },
          ...previewSlideShow,
        ]);
        setCoverImage(metadata.cover_image);
      }, 100);
    }
  }, [metadata.cover_image]);

  // Preview Slide show logic
  const swiperRef = useRef(null);
  const [activeSlide, setActiveSlide] = useState(previewSlideShow[0].file);
  const handleSlideClick = (index) => {
    setActiveSlide(previewSlideShow[index]?.file || previewSlideShow[0].file);

    const help = JSON.parse(JSON.stringify(metadata));
    help["cover_image"] = previewSlideShow[index].key || "";
    setBulletin({ metadata: { ...help } });
  };

  const handleSlideFocus = (index) => {
    const swiper = swiperRef.current;

    if (swiper) {
      // Calculate the centered index and slide to it
      swiper.slideToLoop(index, 500, () => {});
    }
  };

  return (
    <div className="col-span-4 pt-6">
      <span className="font-body-medium">پیش‌نمایش</span>
      <FetchImage imageUrl={coverImage} onFetchSuccess={setActiveSlide} />
      <div
        ref={containerRef}
        className={
          "w-full border-[1px] border-light-neutral-border-medium-rest relative mt-2"
        }
        style={{
          aspectRatio: "1 / 1.414",
          backgroundImage: `url(${activeSlide})`,
          backgroundPosition: "center center",
          backgroundSize: "contain",
          backgroundRepeat: "no-repeat",
        }}
      >
        <FetchImage
          imageUrl={metadata?.logo_image}
          onFetchSuccess={setLogoImage}
        />
        <div
          className={
            "w-full text-center !h-[13%] !max-h-[13%] absolute top-[13%] left-0"
          }
          style={{
            backgroundImage: logoImage
              ? `url(${logoImage})`
              : `url(${SampleLogo})`,
            backgroundPosition: "center center",
            backgroundSize: "contain",
            backgroundRepeat: "no-repeat",
          }}
        ></div>
        <div
          className={
            "w-full text-center !max-w-[68%] font-headline-large absolute top-[28%] left-[16%]"
          }
          style={{
            textAlign: titleStyle.textAlign,
            lineHeight: calculateFontSize(42),
            clear: "both",
          }}
        >
          <span style={titleStyle}>
            {metadata.cover_title ||
              "عنوان اصلی بولتن: مانند بررسی توئیت‌های در مورد باشگاه آث میلان"}
          </span>
          <p style={{ textAlign: subTitleStyle.textAlign }}>
            <span
              className={"clear-both text-[20px] mt-4"}
              style={subTitleStyle}
            >
              {metadata.cover_subtitle ||
                "عنوان فرعی بولتن: مانند رصد فضای مجازی"}
            </span>
          </p>
        </div>
        <p
          className={
            "w-full text-center !max-w-[67%] absolute top-[76%] left-[16%] font-body-medium"
          }
          style={{
            textAlign: creatorStyle.textAlign,
            lineHeight: calculateFontSize(42),
          }}
        >
          <span style={creatorStyle}>
            {metadata.creator || "ایجاد کننده: مانند سازمان ورزش"}
          </span>
          <span
            style={{
              display: "block",
              clear: "both",
              textAlign: "center",
              marginTop: "8px",
              fontSize: calculateFontSize(14),
            }}
          >
            {metadata.cover_has_date_time ||
              toPersianNumber(
                new DateObject().convert(persian, persian_en).format(),
              )}
          </span>
        </p>
      </div>
      <Swiper
        navigation={true}
        slidesPerView={5}
        modules={[Navigation]}
        className={"mt-2"}
        centeredSlides={true}
        loop={true}
        onSlideChange={(e) => {
          handleSlideClick(e.realIndex);
        }}
        onSwiper={(swiper) => (swiperRef.current = swiper)}
      >
        {previewSlideShow.map((item, index) => (
          <SwiperSlide key={index}>
            <div className={"p-1"}>
              <div
                className={"border-[1px] cursor-pointer"}
                style={{
                  aspectRatio: "1 / 1.414",
                  backgroundImage: `url(${item.file})`,
                  backgroundPosition: "center center",
                  backgroundSize: "contain",
                  backgroundRepeat: "no-repeat",
                }}
                onClick={() => handleSlideFocus(index)} // Scroll to and center the clicked slide
              ></div>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};

export default PreviewSection;
