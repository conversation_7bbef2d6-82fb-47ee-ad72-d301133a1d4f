import ContentCard from "./ContentCard.jsx";
import { useCompareStore } from "store/compareStore.js";
import { countAvg } from "../../../utils.js";

const ContentNews = () => {
  const { content } = useCompareStore((state) => state.compare);

  const chartData = (key, displayName) => {
    const filteredContent = content.filter((item) => {
      return Object.values(item).some((nestedItem) => nestedItem?.key === key);
    });

    if (filteredContent.length === 0) return [];

    const data = countAvg(filteredContent, key).map((item) => ({
      ...item,
      name: displayName,
    }));

    return data;
  };

  const repostData = () => {
    const filteredContent = content.filter((item) =>
      Object.values(item).some((nestedItem) =>
        ["post", "original"].includes(nestedItem.key),
      ),
    );

    if (filteredContent.length === 0) return [];

    const totalRepost = filteredContent.reduce((acc, item) => {
      const postCount = item[1]?.count || 0;
      const originalCount = item[2]?.count || 0;
      return acc + (postCount - originalCount);
    }, 0);

    return filteredContent.map((item) => {
      const postCount = item[1]?.count || 0;
      const originalCount = item[2]?.count || 0;
      const repostCount = postCount - originalCount;

      return {
        name: "محتوای بازنشری",
        x: repostCount,
        y: totalRepost ? (repostCount / totalRepost) * 100 : 0,
      };
    });
  };

  return (
    <>
      <ContentCard
        titleOffset={-12.5 * content.length}
        title="مجموع محتوا"
        data={chartData("post", "مجموع محتوا")}
      />
      {/*<ContentCard*/}
      {/*  titleOffset={-12.5 * content.length}*/}
      {/*  title="محتوای تولیدی"*/}
      {/*  data={chartData("original", "محتوای تولیدی")}*/}
      {/*/>*/}
      {/*<ContentCard*/}
      {/*  titleOffset={-12.5 * content.length}*/}
      {/*  title="محتوای بازنشری"*/}
      {/*  data={repostData()}*/}
      {/*/>*/}
    </>
  );
};

export default ContentNews;
