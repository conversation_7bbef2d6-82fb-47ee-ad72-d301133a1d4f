import { useEffect, useState } from "react";
import MultipleBar from "components/Charts/MultipleBar.jsx";
import { Card } from "components/ui/Card.jsx";
import { useCompareStore } from "store/compareStore.js";
import advanceSearch from "service/api/advanceSearch.js";
import { generateStatsQuery } from "utils/stats.js";
// import { GenderFemale, GenderNeuter, GenderMale } from "@phosphor-icons/react";
import { SpinnerGap } from "@phosphor-icons/react";
import Title from "./Title.jsx";

const GenderChart = () => {
  const { fields, platform, date } = useCompareStore((state) => state.compare);

  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const colors = ["#1DCEA2", "#6D72E5", "#DB6DE5", "#F7A912"];

  const category = ["زن", "مرد", "نامشخص"];

  const getData = async (fieldsCleared) => {
    if (!fieldsCleared.length) return;

    setLoading(true);
    let reqsQueries = [];
    let result = [];

    try {
      const { from, to } = date;
      reqsQueries = fieldsCleared?.map((field) => {
        const query = generateStatsQuery(
          platform,
          "gender",
          {
            from: parseInt(new Date(from).getTime() / 1000),
            to: parseInt(new Date(to).getTime() / 1000),
            fields: ["male", "female", "org"],
            q: field.q,
            count: 10,
          },
          field.filters
        );

        return advanceSearch.search({ q: query });
      });

      const response = await Promise.allSettled(reqsQueries);
      result = response
        .filter((res) => res.status === "fulfilled")
        .map((res) => {
          const data = JSON.parse(res.value.data.data);
          return data.result.data || {};
        });

      const allCategories = new Set();
      result.forEach((cat) =>
        Object.keys(cat).forEach((category) => allCategories.add(category))
      );

      const allCategoriesArray = Array.from(allCategories).sort();

      const categoriesWithZeroValue = result.map((cat) => {
        const updatedCat = { ...cat };
        allCategoriesArray.forEach((element) => {
          if (!updatedCat[element]) {
            updatedCat[element] = 0;
          }
        });
        return updatedCat;
      });

      const sortedOutputCategory = categoriesWithZeroValue.map((item) =>
        Object.entries(item).sort((x, y) => x[0].localeCompare(y[0]))
      );

      const finalCategoryData = sortedOutputCategory.map((item) =>
        item.map((x) => Math.ceil(x[1] * 100 * 100) / 100)
      );

      const resultData = fieldsCleared.map((item, index) => ({
        name: `${item?.username ? `@${item?.username}` : item.q}`,
        data: finalCategoryData[index],
        color: colors[index],
      }));

      setData(resultData);
      setLoading(false);
    } catch (error) {
      setLoading(false);
      console.error("Error fetching data:", error);
    }
  };

  useEffect(() => {
    const fieldsCleared = fields.filter((value) => value.q);
    getData(fieldsCleared);
  }, [fields, date]);

  if (!fields.filter((user) => user.id || user.q).length) {
    return null;
  }

  return (
    <>
      <div className="flex">
        <Card className="!p-6">
          <div className="flex flex-col gap-6 w-full">
            <Title title="دسته‌بندی جنسیت"></Title>
            {loading ? (
              <div className="w-full h-[400px] flex justify-center items-center">
                <SpinnerGap size={40} className="animate-spin" />
              </div>
            ) : (
              <>
                {data.map((item) => item?.data?.length).filter(Boolean)
                  ?.length ? (
                  <MultipleBar categories={category} data={data} />
                ) : (
                  <div className="h-[430px] flex items-center justify-center font-subtitle-medium">
                    داده‌ای برای نمایش وجود ندارد
                  </div>
                )}
              </>
            )}
          </div>
        </Card>
      </div>
    </>
  );
};

export default GenderChart;
