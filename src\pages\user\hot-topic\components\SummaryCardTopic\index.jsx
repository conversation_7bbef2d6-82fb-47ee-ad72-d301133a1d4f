import React, { useState } from "react";
import SummaryCard from "components/SummaryCard";
import PropTypes from "prop-types";
import useSearchStore from "store/searchStore";
import TextSlicer from "components/TextSlicer";
import HighlightedText from "./HighlightedText";
import { findPlatform } from "../../utils";
import clsx from "clsx";

const SummaryCardTopic = ({
  data,
  media,
  selected = false,
  platform,
  word,
}) => {
  const [select, setSelect] = useState(selected);
  const { query } = useSearchStore();

  return (
    <div
      className={clsx(
        "bg-light-neutral-surface-card rounded-lg shadow-[0px_2px_20px_0px_#0000000D] w-full hover:outline hover:outline-1 hover:outline-light-primary-border-rest",
        select && "outline outline-1 outline-light-primary-border-rest",
      )}
    >
      <SummaryCard
        media={data?.platform || findPlatform(data)}
        data={data}
        showMediaName
        handleSelect={setSelect}
      >
        <TextSlicer media={media}>
          <HighlightedText
            description={data?.description || data?.content || ""}
            query={word || query}
          />
        </TextSlicer>
      </SummaryCard>
    </div>
  );
};

export default React.memo(SummaryCardTopic);

SummaryCardTopic.propTypes = {
  data: PropTypes.object.isRequired,
  media: PropTypes.oneOf(["telegram", "twitter", "instagram", "eitaa", "news"]),
  selected: PropTypes.bool,
  platform: PropTypes.string.isRequired,
};
