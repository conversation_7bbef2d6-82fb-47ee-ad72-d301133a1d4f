import useFetch from "../index";

class opinion {
  deleteOpinionReport(id) {
    return useFetch.delete(`/api/v1/opinion/${id}/`);
  }
  getOpinionReports() {
    return useFetch.get(`/api/v1/opinion/`);
  }
  getOpinionReport(id) {
    return useFetch.get(`/api/v1/opinion/${id}/`);
  }
  createWaveAnalysisOpinion(data) {
    return useFetch.post(`/api/v1/opinion/`, data);
  }
  updateWaveAnalysisOpinion(data, id) {
    return useFetch.put(`/api/v1/opinion/${id}/`, data);
  }
}
export default new opinion();
