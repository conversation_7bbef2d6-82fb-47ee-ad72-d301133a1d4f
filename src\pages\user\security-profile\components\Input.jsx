import { Eye, EyeClosed } from "@phosphor-icons/react";
import { useState } from "react";

const Input = ({
  type,
  label,
  value,
  placeholder,
  onChange = () => {},
  className = "",
}) => {
  const [eyeAction, setEyeAction] = useState(false);
  const [eyeIsOpen, setEyeIsOpen] = useState(true);
  const eyeClicked = async () => {
    setEyeIsOpen(!eyeIsOpen);
  };

  return (
    <div className="">
      <label
        className="font-overline-large text-light-neutral-text-high mr-1 mb-1 inline-block"
        htmlFor={label}
      >
        {label}
      </label>
      <input
        id={label}
        type={type === "password" && eyeIsOpen ? "password" : "text"}
        className={`w-full h-12 rounded-md outline-none border border-light-neutral-border-medium-rest px-4 py-3 font-body-large placeholder:font-body-medium ${className}`}
        placeholder={value || placeholder}
        value={value}
        onChange={onChange}
      />
      {eyeAction && eyeIsOpen && (
        <button
          type={"button"}
          onClick={eyeClicked}
          className={"action-icon text-right"}
        >
          <Eye />
        </button>
      )}
      {eyeAction && !eyeIsOpen && (
        <button
          type={"button"}
          onClick={eyeClicked}
          className={"action-icon text-right"}
        >
          <EyeClosed />
        </button>
      )}
    </div>
  );
};

export default Input;
