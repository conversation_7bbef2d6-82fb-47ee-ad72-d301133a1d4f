import { useEffect, useState } from "react";
import SummaryCardTopic from "../hot-topic/components/SummaryCardTopic";
import TextSlicer from "components/TextSlicer";
import { queryHandler, toPersianNumber } from "utils/helper";
import advanceSearch from "service/api/advanceSearch";
import Loading from "components/ui/Loading";
import Paginate from "components/ui/Paginate";
import DropDown from "components/ui/DropDown";
import CardsLayout from "layout/CardsLayout";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";
import MasterpageLayout from "layout/masterpage-layout.jsx";
import useSearchStore from "store/searchStore.js";
import { PATHS } from "constants/index.js";
import PLATFORMS from "constants/platforms.js";
import { DownloadExcelButton } from "components/DownloadExcelButton/index.jsx";
import { ToastContainer } from "react-toastify";

const HotWord = () => {
  const { filters, query, cluster, loadState } = useSearchStore();

  useEffect(() => {
    loadState();
  }, []);

  const breadcrumbList = [
    { title: `خانه`, link: PATHS.basePath + PATHS.dashboard },
    {
      title: `${
        cluster.name === "all" ? "همه دسته ها" : `دسته‌بندی ${cluster.name}`
      }`,
      link: PATHS.basePath + PATHS.dashboard,
    },
    { title: `${query}` },
  ];
  const setBreadCrumb = useBreadcrumb(breadcrumbList);

  const [topics, setTopics] = useState({});
  const [sort, setSort] = useState("زمان انتشار");
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    getTopics(page, "desc", convertPerfilterToEn[sort]);
    setBreadCrumb([
      { title: `خانه`, link: PATHS.basePath + PATHS.dashboard },
      {
        title: `${
          cluster.name === "all" ? "همه دسته ها" : `دسته‌بندی ${cluster.name}`
        }`,
        link: PATHS.basePath + PATHS.hotTopic,
      },
      { title: `${query}` },
    ]);
  }, [cluster, query]);

  const med = {
    telegram: ["میزان بازدید", "زمان انتشار"],
    twitter: ["بازنشر", "تعداد لایک", "زمان انتشار"],
    instagram: ["تعداد لایک", "تعداد نظرات", "زمان انتشار"],
    news: ["تعداد نظرات", "زمان انتشار"],
    eitaa: ["میزان بازدید", "زمان انتشار"],
  };

  const convertPerfilterToEn = {
    "زمان انتشار": "time",
    "میزان بازدید": "view_count",
    "تعداد لایک": "like_count",
    بازنشر: "copy_count",
    "تعداد نظرات": "",
  };

  const getTopics = async (page, sort_type, sort) => {
    setLoading(true);
    try {
      const q_ =
        filters.platform === PLATFORMS.TWITTER
          ? `&extracted_hashtags[0]=${query}&count=18`
          : `&q=${query}&count=18`;

      const responseStinas = await advanceSearch.search({
        q: queryHandler(cluster.baseUrl, q_, page, "desc", sort),
      });
      setTopics(JSON.parse(responseStinas.data.data).result);
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  useEffect(() => {
    getTopics(page, "desc", convertPerfilterToEn[sort]);
    setPage(1);
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, [sort]);

  useEffect(() => {
    getTopics(page, "desc", convertPerfilterToEn[sort]);
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, [page]);

  return (
    <MasterpageLayout>
      <div className={"flex-1"}>
        {loading ? (
          <Loading />
        ) : (
          <>
            <div className="bg-transparent flex justify-between items-center mb-4 [direction:rtl]">
              <div className="flex items-center gap-4">
                <DropDown
                  title="نمایش بر اساس"
                  subsets={med[filters.platform]}
                  selected={sort}
                  setSelected={setSort}
                />
                <div className="font-overline-medium text-light-neutral-text-medium flex gap-2">
                  <div className="flex gap-2">
                    نمایش
                    <div>
                      <span>{toPersianNumber(page * 16)}</span>-
                      <span>{toPersianNumber((page - 1) * 16 + 1)}</span>
                    </div>
                    از
                    <span>{toPersianNumber(topics?.meta?.total || 0)}</span>
                    محتوا
                  </div>
                  <div>-</div>
                  <div className="flex gap-2">
                    صفحه
                    <span>{toPersianNumber(String(page))}</span>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-4">
                <DownloadExcelButton
                  platform={filters.platform}
                  q={queryHandler(
                    cluster.baseUrl,
                    filters.platform === PLATFORMS.TWITTER
                      ? `&extracted_hashtags[0]=${query}`
                      : `&q=${query}`,
                    page,
                    "desc",
                    convertPerfilterToEn[sort],
                  )}
                >
                  دانلود خروجی اکسل
                </DownloadExcelButton>
              </div>
            </div>
            <CardsLayout>
              {topics?.data?.map((item, index) => (
                <SummaryCardTopic
                  key={index}
                  media={filters.platform}
                  data={item}
                >
                  <TextSlicer>{item.description}</TextSlicer>
                </SummaryCardTopic>
              ))}
            </CardsLayout>
            <Paginate
              page={page}
              setPage={setPage}
              dataCount={topics?.meta?.total?.toString()}
              per_page={16}
            />
          </>
        )}
      </div>
      <ToastContainer />
    </MasterpageLayout>
  );
};

export default HotWord;
