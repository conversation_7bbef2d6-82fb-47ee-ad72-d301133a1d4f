import PropTypes from "prop-types";
import IndicatorBadge from "./IndicatorBadge";
import Separator from "./Separator.jsx";

const Container = ({ children, status, step, title }) => {
  return (
    <div className="w-full">
      <IndicatorBadge step={step} status={status}>
        {title}
      </IndicatorBadge>

      <div className="flex gap-6 w-full">
        {step < 4 && <Separator step={step} status={status} />}
        {status === step && (
          <div className="p-6 grid grid-cols-12 gap-12 w-full">{children}</div>
        )}
      </div>
    </div>
  );
};

Container.propTypes = {
  children: PropTypes.node,
  status: PropTypes.number,
  step: PropTypes.number,
  title: PropTypes.string,
};

export default Container;
