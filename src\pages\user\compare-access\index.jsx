import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  CaretDown,
  CaretRight,
  User,
  WarningDiamond,
} from "@phosphor-icons/react";
import { ArrowLeft } from "@phosphor-icons/react";
import { Users } from "@phosphor-icons/react";
import Loading from "components/ui/Loading";
import Title from "components/alertAndFilterDetails/Title";
import { useParams } from "react-router-dom";
import { UserCircleGear } from "@phosphor-icons/react";
import compare from "service/api/compare";
import access from "service/api/access";
import Popup from "components/ui/PopUp";
import { CheckCircle } from "@phosphor-icons/react";
import { notification } from "utils/helper";
import { ToastContainer } from "react-toastify";
import "./style.css";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";
import { useLayoutContext } from "context/layout-context";
import PermissionsTable from "./components/permissionsTable";
import { CButton } from "components/ui/CButton.jsx";

const CompareAccess = () => {
  const [isGuideOpen, setIsGuideOpen] = useState(false);

  const [loading, setLoading] = useState(false);
  const [groupData, setGroupData] = useState([]);
  const [isSharePopupOpen, setIsSharePopupOpen] = useState(false);

  const MODES = { SHARED: "shared", PRIVATE: "private" };
  const [currentMode, setCurrentMode] = useState(MODES.PRIVATE);

  const navigate = useNavigate();
  const { id } = useParams();

  const { setBreadcrumb } = useLayoutContext();
  const breadcrumbList = [
    { title: "مقایسه", link: "/app/compare/list" },
    { title: groupData?.title },
    { title: "سطح دسترسی" },
  ];
  useBreadcrumb(breadcrumbList);
  useEffect(() => {
    setBreadcrumb(breadcrumbList);
  }, [groupData]);

  const getGroup = async (id) => {
    if (loading) return;
    setLoading(true);
    try {
      const {
        data: { data },
      } = await compare.getById(id);
      const help = JSON.parse(JSON.stringify(data));
      help.platform = help.params.platform;
      help.q = help.params.q;
      delete help.params;
      setGroupData(help);

      if (help?.group) {
        setCurrentMode(MODES.SHARED);
      } else {
        setCurrentMode(MODES.PRIVATE);
      }
    } catch (error) {
      console.error(error.response.data.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSharePopupSubmit = async () => {
    if (currentMode === MODES.SHARED) {
      try {
        const res = await access.delete(groupData?.group?.id);
        if (res?.data?.code === 200) {
          await getGroup(id);
          setCurrentMode(MODES.PRIVATE);
          notification.success(
            `نوع دسترسی با موفقیت به «شخصی» تغییر یافت`,
            <CheckCircle className="text-light-success-text-rest" />
          );
        }
      } catch (e) {
        console.log(e);
        notification.error(
          `با عرض پوزش، امکان تغییر نوع دسترسی وجود ندارد. لطفا پس از چند لحظه دوباره تلاش کنید و در صورت عدم حل مشکل با پشتیبانی تماس بگیرید.`,
          <WarningDiamond className="text-light-error-text-rest" />
        );
      } finally {
        setIsSharePopupOpen(false);
      }
    } else {
      try {
        const data = {
          group_type: "comparison",
          target_id: id,
          description: "",
          contact: [],
        };
        const res = await access.new(data);
        if (res?.data?.code === 200) {
          await getGroup(id);
          setCurrentMode(MODES.SHARED);
          notification.success(
            `نوع دسترسی با موفقیت به «اشتراک‌گذاری شده» تغییر یافت`,
            <CheckCircle className="text-light-success-text-rest" />
          );
        }
      } catch (e) {
        console.log(e);
        notification.error(
          `خطا در تغییر دسترسی!`,
          <WarningDiamond className="text-light-error-text-rest" />
        );
      } finally {
        setIsSharePopupOpen(false);
      }
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      await getGroup(id);
    };
    fetchData();
  }, [id]);

  return (
    <>
      <div className="flex justify-center">
        <div className="bg-light-neutral-surface-card shadow-md w-[875px] rounded-lg p-6 font-body-medium">
          <div className="flex flex-col">
            <div>
              <Title>
                <p className="font-body-medium w-36 font-bold">
                  دسترسی کاربران
                </p>
              </Title>
            </div>
            <div className="flex justify-between items-center">
              <div className="flex flex-col gap-2 justify-between my-6">
                <span className="font-body-medium text-[#7f7f7f]">
                  وضعیت دسترسی
                </span>
                <span className="flex items-center gap-1">
                  {currentMode === MODES.PRIVATE ? <User /> : <Users />}
                  {currentMode === MODES.PRIVATE ? "شخصی" : "اشتراک‌گذاری شده"}
                </span>
              </div>
              <CButton
                mode={"fill"}
                className="!w-[17rem] !text-black !bg-light-primary-background-highlight"
                onClick={() => setIsSharePopupOpen(!isSharePopupOpen)}
              >
                <span className="flex items-center gap-1">
                  <UserCircleGear />
                  {currentMode === MODES.PRIVATE
                    ? "تغییر به دسترسی «اشتراک‌گذاری شده»"
                    : "تغییر به دسترسی «شخصی»"}
                </span>
              </CButton>
            </div>
          </div>
          <hr />
          <div className="w-full mt-4 bg-[#f7f9fb] p-4 rounded-lg">
            <div
              className="flex justify-between items-center cursor-pointer"
              onClick={() => setIsGuideOpen(!isGuideOpen)}
            >
              <div className="text-right py-2">راهنمای دسترسی</div>
              <CaretDown
                className={`transition-transform duration-300 ${
                  isGuideOpen ? "rotate-180" : ""
                }`}
              />
            </div>
            <div
              className={`overflow-hidden transition-all duration-300 ${
                isGuideOpen ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
              }`}
            >
              <ul className="flex flex-col pl-6 pt-3 font-body-medium gap-6 list-none">
                <li className="text-black relative pr-6 before:content-['•'] before:absolute before:right-0 before:text-gray-500">
                  در صورتی که وضعیت دسترسی را به «شخصی» تغییر دهید، دیگر هیچ‌کس
                  به این مقایسه دسترسی نخواهد داشت.
                </li>
                <li className="text-black relative pr-6 before:content-['•'] before:absolute before:right-0 before:text-gray-500">
                  در صورتی که دسترسی را به «اشتراک‌گذاری شده» تغییر دهید،
                  می‌توانید مقایسه را با سطوح مختلف دسترسی با افراد خاص به
                  اشتراک بگذارید.
                </li>
                <li className="text-black relative pr-6 before:content-['•'] before:absolute before:right-0 before:text-gray-500">
                  به صورت پیش‌فرض هر فرد که دسترسی به این مقایسه را داشته باشد
                  فقط مجوز «بازدیدکننده» را خواهد داشت و می‌تواند از مقایسه
                  استفاده کند و هیچ‌گونه دخل و تصرفی در این مقایسه نخواهد داشت.
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <PermissionsTable
        showTable={currentMode === MODES.SHARED}
        groupData={groupData}
        setGroupData={setGroupData}
      />
      <Popup
        isOpen={isSharePopupOpen}
        onClose={() => setIsSharePopupOpen(!isSharePopupOpen)}
        hasCloseIcon={false}
        submitHandler={handleSharePopupSubmit}
        width="545px"
      >
        <div className="flex flex-col gap-3 justify-center">
          <div className="flex justify-center gap-2 items-center">
            {currentMode === MODES.PRIVATE ? (
              <>
                <User size={40} />
                <ArrowLeft size={33} />
                <Users size={55} />
              </>
            ) : (
              <>
                <Users size={40} />
                <ArrowLeft size={33} />
                <User size={55} />
              </>
            )}
          </div>
          <h4 className="font-headline-medium p-3 text-center">
            {currentMode === MODES.PRIVATE
              ? "آیا می‌خواهید سطح دسترسی را به وضعیت <<اشتراک گذاری شده>> تغییر دهید؟"
              : "آیا می‌خواهید سطح دسترسی را به وضعیت <<شخصی>> تغییر دهید؟"}
          </h4>
          <p className="font-body-medium text-center pb-5">
            {currentMode === MODES.PRIVATE
              ? "در صورت تغییر نوع دسترسی، امکان اشتراک این مقایسه با سایر کابران فراهم خواهد شد."
              : "در صورت تغییر نوع دسترسی، تنظیمات اعمال شده حذف خواهد شد."}
          </p>
        </div>
      </Popup>
      {loading && <Loading />}
      <ToastContainer />
    </>
  );
};

export default CompareAccess;
