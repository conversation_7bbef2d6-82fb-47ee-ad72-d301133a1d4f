import { useState } from "react";
import { CInput } from "components/ui/CInput.jsx";
import { MagnifyingGlass } from "@phosphor-icons/react";
import { CButton } from "components/ui/CButton.jsx";
import DateFilter from "./DateFilter.jsx";
import PropTypes from "prop-types";
import RoleDropdown from "pages/admin/user-list/components/SearchBar/RoleDropdown.jsx";
import formatDate from "pages/admin/user-list/utils.js";

const SearchBar = ({ filter, setFilter, loading = false }) => {
  const [searchValue, setSearchValue] = useState(filter.q || "");

  const handleDateChange = (dates) => {
    const { from, to } = dates;
    setFilter({
      ...filter,
      register_at: { date_from: formatDate(from), date_to: formatDate(to) },
    });
  };

  const handleRoleChange = (role) => {
    setFilter({ ...filter, role });
  };

  const handleSearch = async (event) => {
    if (event && event.key === "Enter") {
      await handleSubmit();
    }
  };

  const handleSubmit = async () => {
    setFilter({ ...filter, q: searchValue });
  };

  return (
    <div className="font-body-medium h-full bg-white p-5 overflow-hidden rounded-lg shadow-[0_2px_20px_0_rgba(0,0,0,0.05)]">
      <div className="flex flex-row gap-[16px]">
        <CInput
          id={"q"}
          name={"q"}
          inset={true}
          headingIcon={<MagnifyingGlass />}
          size={"md"}
          validation={"none"}
          inputProps={{ onKeyDown: handleSearch }}
          direction={"rtl"}
          placeholder={"نام کاربری / ایمیل /  شماره موبایل را جست‌و‌جو کنید"}
          onChange={(e) => setSearchValue(e.target.value)}
          className={"flex-1 !mb-0"}
        />
        <CButton
          type={"submit"}
          onClick={handleSubmit}
          size={"md"}
          className={"[direction:rtl] [width:150px!important]"}
          disabled={loading}
        >
          جست‌وجو
        </CButton>
      </div>
      <div className="flex pt-1 gap-4">
        <RoleDropdown role={""} onChange={handleRoleChange} />
        <DateFilter
          title={"تاریخ ایجاد:"}
          handleDateChange={handleDateChange}
          selectedDateRange={filter.register_at}
        />
        {/* <DateFilter2
          handleDateChange={handleDateChange2}
          selectedDateRange={filter.updated_at}
        /> */}
      </div>
    </div>
  );
};

SearchBar.propTypes = {
  filter: PropTypes.object.isRequired,
  setFilter: PropTypes.func.isRequired,
  loading: PropTypes.bool.isRequired,
};

export default SearchBar;
