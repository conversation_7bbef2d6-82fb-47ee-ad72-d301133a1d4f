import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import PropTypes from "prop-types";
import { useCompareStore } from "store/compareStore.js";
import { useEffect, useState } from "react";
import { formatShortNumber, shortener, toPersianNumber } from "utils/helper.js";

const BasicBar = ({ categories = [], data = [], title }) => {
  const maxDataValue = Math.max(...data.flatMap((series) => series.data));
  const [num, setNum] = useState(0);
  const { fields } = useCompareStore((state) => state.compare);

  const totalDataSum = data.reduce(
    (sum, series) => sum + series.data.reduce((a, b) => a + b, 0),
    0,
  );

  const chartOptions = {
    chart: {
      type: "column",
      height: 230,
      backgroundColor: "#fff",
    },
    title: {
      text: title ? title : null,
      align: "right",
      style: {
        fontSize: "15px",
      },
    },
    legend: {
      enabled: true,
      layout: "vertical",
      align: "right",
      verticalAlign: "middle",
      rtl: true,
      useHTML: true,
      labelFormatter: function () {
        const percentage = (
          (this.yData.reduce((a, b) => a + b, 0) / totalDataSum) *
          100
        ).toFixed(2);

        return `
          <div style="display: flex; align-items: center; direction: rtl; gap: 50px;">
            <span style="font-family: 'iranyekan', serif; font-size: 14px; color: #555;">
              ${this.name}
            </span>
            <span style="font-family: 'iranyekan', serif; font-size: 14px; color: #555; margin-right: auto;">
              ${toPersianNumber(percentage)}%
            </span>
          </div>`;
      },
      itemStyle: {
        fontSize: "12px",
        fontFamily: "'iranyekan', serif",
      },
    },
    credits: {
      enabled: false,
    },
    tooltip: {
      enabled: true,
      shared: true,
      useHTML: true,
      formatter: function () {
        let tooltipHTML = `<div style="font-family:'iranyekan',serif;font-size: 11px; direction: rtl">`;
        this.points.forEach((point) => {
          tooltipHTML += `
            <div style="display: flex; align-items: center; margin-bottom: 5px;">
              <div style="width: 10px; height: 10px; border-radius: 50%; background-color: ${
                point.color
              }; margin-left: 3px;"></div>
              <span>${shortener(point.series.name, 12)}:</span> 
              <span style="color: #333; margin-right: 5px;">${toPersianNumber(
                formatShortNumber(point.y),
              )}</span>
            </div>`;
        });
        tooltipHTML += "</div>";
        return tooltipHTML;
      },
    },
    xAxis: {
      categories: categories ? categories : null,
      crosshair: true,
      accessibility: {
        description: "categories",
      },
      visible: false,
    },
    yAxis: {
      min: 0,
      title: {
        text: null,
      },
      labels: {
        formatter: function () {
          return toPersianNumber(formatShortNumber(this.value));
        },
      },
      gridLineDashStyle: "dash",
      max: maxDataValue, // Use the previously calculated max value
    },
    plotOptions: {
      column: {
        borderWidth: 0,
      },
      series: {
        enableMouseTracking: true,
        borderRadius: {
          radius: 11,
        },
      },
    },
    series: data,
  };
  useEffect(() => {
    setNum((l) => l + 1);
  }, [fields]);

  return (
    <div className="w-full">
      <HighchartsReact
        highcharts={Highcharts}
        options={chartOptions}
        key={num}
      />
    </div>
  );
};

BasicBar.propTypes = {
  categories: PropTypes.array,
  data: PropTypes.array.isRequired,
  title: PropTypes.string.isRequired,
};

export default BasicBar;
