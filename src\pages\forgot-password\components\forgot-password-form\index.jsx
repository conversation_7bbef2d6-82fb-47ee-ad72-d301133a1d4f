import { useState } from "react";
import ForgotPasswordStep1 from "../step1/ForgotPasswordStep1.jsx";
import ForgotPasswordStep2 from "../step2/ForgotPasswordStep2.jsx";
import ForgotPasswordStep3 from "../step3/ForgotPasswordStep3.jsx";
import ForgotPasswordStep4 from "../step4/ForgotPasswordStep4.jsx";

function ForgotPasswordForm() {
  const [step, setStep] = useState(1);
  const [mobile, setMobile] = useState("");
  const [otp, setOtp] = useState("");

  return (
    <>
      {step === 1 && (
        <ForgotPasswordStep1 setStep={setStep} setMobile={setMobile} />
      )}
      {step === 2 && (
        <ForgotPasswordStep2
          setStep={setStep}
          setOtp={setOtp}
          mobile={mobile}
        />
      )}
      {step === 3 && (
        <ForgotPasswordStep3 otp={otp} mobile={mobile} setStep={setStep} />
      )}
      {step === 4 && <ForgotPasswordStep4 />}
    </>
  );
}
export default ForgotPasswordForm;
