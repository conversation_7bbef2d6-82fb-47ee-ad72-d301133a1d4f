import useFetch from "../index";

class userService {
  getProfile() {
    return useFetch.get("/api/v1/account/profile/");
  }

  getUserList(data, query = {}) {
    return useFetch.post(
      `/api/admin/v1/account/search/?${new URLSearchParams(query).toString()}`,
      data,
    );
  }

  getImage(imageUrl) {
    return useFetch.get(imageUrl, { responseType: "blob" });
  }

  updateProfile(data) {
    return useFetch.put("/api/v1/account/profile/", data);
  }

  updateProfilePicture(data) {
    return useFetch.patch("/api/v1/account/profile/avatar/", data);
  }
}
export default new userService();
