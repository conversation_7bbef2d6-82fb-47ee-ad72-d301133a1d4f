import { parseTimeToPersian } from "utils/helper";
import Divider from "components/ui/Divider";
import StepState from "../../bulletin-list/components/StepState";
import Status from "components/ui/Status";
import {
  CheckCircle,
  PencilSimpleLine,
  ToggleLeft,
  TrashSimple,
} from "@phosphor-icons/react";
import { useNavigate } from "react-router-dom";
import bulletin from "service/api/bulletin";
import { useState } from "react";
import DeletePopUp from "components/ui/DeletePopUp";
import { notification } from "utils/helper";

const BulletinDetailsInfo = ({ bulletinData, setRerender }) => {
  const [loading, setLoading] = useState(false);
  const [isDeletePopUpOpen, setIsDeletePopUpOpen] = useState(false);
  const [selectedId, setSelectedId] = useState(null);
  const navigate = useNavigate();
  const changeStatus = async (id) => {
    setLoading(true);
    try {
      await bulletin.status(id);
      setRerender((prev) => !prev);
    } catch (error) {
      notification.success(
        error?.response?.data?.message,
        <CheckCircle className="text-light-success-text-rest" />
      );
    }
    setLoading(false);
  };
  const deleteBulletin = async (id) => {
    setLoading(true);
    try {
      await bulletin.remove(id);
      navigate("/app/bulletin/list");
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
    setIsDeletePopUpOpen(false);
  };
  const handleDeleteClick = (id) => {
    setSelectedId(id);
    setIsDeletePopUpOpen(true);
  };
  return (
    <>
      <div className="w-full flex flex-col gap-4 rounded-lg bg-white p-6 col-span-3 shadow-[0px_2px_20px_0px_#0000000D] *:font-body-medium *:text-light-neutral-text-medium">
        <div className="flex justify-between">
          <span>تاریخ ایجاد</span>
          <span>{parseTimeToPersian(bulletinData?.created_at)}</span>
        </div>

        <div className="flex justify-between">
          <span>تاریخ به‌روزرسانی</span>
          <span>{parseTimeToPersian(bulletinData?.updated_at)}</span>
        </div>

        <div className="flex justify-between">
          <span>آخرین فعالیت</span>
          {bulletinData?.last_execute ? (
            <span>{parseTimeToPersian(bulletinData.last_execute)}</span>
          ) : (
            <span>_</span>
          )}
        </div>
        <div className="flex justify-between">
          <span>مرحله</span>
          <StepState
            title={
              bulletinData?.bulletin_state === "draft"
                ? "پیش‌نویس"
                : bulletinData?.bulletin_state === "in_progress"
                ? "در‌حال‌ایجاد"
                : bulletinData?.bulletin_state === "created"
                ? "ایجاد‌شده"
                : ""
            }
            state={bulletinData?.bulletin_state}
          />
        </div>

        {bulletinData?.bulletin_type === "periodic" && (
          <div className="flex justify-between">
            <span>وضعیت</span>
            <Status
              status={bulletinData?.is_active}
              onClick={() => changeStatus(bulletinData?.id)}
            />
          </div>
        )}
        <div className="py-2">
          <Divider />
        </div>

        <div className="flex gap-2">
          <button
            className="font-button-medium bg-light-neutral-background-medium rounded-lg w-full h-8 flex justify-center items-center gap-2"
            onClick={() =>
              navigate(`/app/bulletin/edit/${bulletinData?.id}`, {
                state: {
                  ...bulletinData,
                },
              })
            }
          >
            <PencilSimpleLine />
            <span className="font-button-medium">ویرایش</span>
          </button>
          {bulletinData?.bulletin_type === "periodic" && (
            <button
              className="bg-light-neutral-background-medium rounded-lg w-full h-8 flex justify-center items-center gap-2"
              onClick={() => changeStatus(bulletinData?.id)}
            >
              <ToggleLeft />
              <span className="font-button-medium">
                {bulletinData?.is_active ? "غیرفعال‌سازی" : "فعال‌سازی"}
              </span>
            </button>
          )}

          <button
            className="bg-light-neutral-background-medium rounded-lg w-full h-8 flex justify-center items-center gap-2"
            onClick={() => handleDeleteClick(bulletinData?.id)}
          >
            <TrashSimple />
            <span className="font-button-medium">حذف</span>
          </button>
        </div>
      </div>
      <DeletePopUp
        onClose={() => setIsDeletePopUpOpen(false)}
        isOpen={isDeletePopUpOpen}
        submitHandler={() => deleteBulletin(selectedId)}
        title="آیا می‌خواهید این بولتن را حذف کنید؟"
      />
    </>
  );
};
export default BulletinDetailsInfo;
