import ColumnChart from "./charts/ColumnChart";
import HorizontalBar from "components/Charts/HorizontalBar.jsx";
import { Card } from "components/ui/Card.jsx";
import MultipleBar from "components/Charts/MultipleBar.jsx";
import ReactWordcloud from "react-wordcloud";

const ReportWordCloud = () => {
  const words = [
    { text: "خبرجدید", value: 90 }, // "New News"
    { text: "ترندایران", value: 85 }, // "Iran Trend"
    { text: "فیلمروز", value: 80 }, // "Daily Movie"
    { text: "موسیقی‌نو", value: 75 }, // "New Music"
    { text: "مدتهران", value: 70 }, // "Tehran Fashion"
    { text: "غذای‌خوش", value: 68 }, // "Tasty Food"
    { text: "سفرنامه", value: 65 }, // "Travel Story"
    { text: "تکنولوژیست", value: 62 }, // "Technologist"
    { text: "عکس‌بین", value: 60 }, // "Photo Viewer"
    { text: "ورزش‌جدید", value: 58 }, // "New Sport"
    { text: "هنرمدرن", value: 55 }, // "Modern Art"
    { text: "کتاب‌خوان", value: 53 }, // "Book Reader"
    { text: "جشنواره‌ها", value: 50 }, // "Festivals"
    { text: "سیاست‌روز", value: 48 }, // "Daily Politics"
    { text: "طبیعت‌گرد", value: 46 }, // "Nature Explorer"
    { text: "خنده‌دار", value: 45 }, // "Funny"
    { text: "عشق‌نو", value: 44 }, // "New Love"
    { text: "بازی‌تازه", value: 43 }, // "Fresh Game"
    { text: "آشپزی‌خانگی", value: 42 }, // "Home Cooking"
    { text: "دیجیتال‌نما", value: 41 }, // "Digital Display"
    { text: "شعرجوان", value: 40 }, // "Youth Poem"
    { text: "مدل‌مو", value: 39 }, // "Hair Model"
    { text: "خودروبرقی", value: 38 }, // "Electric Car"
    { text: "سینماگرام", value: 37 }, // "Cinemagram"
    { text: "پادکست‌فارسی", value: 36 }, // "Persian Podcast"
    { text: "استارتاپ‌ها", value: 35 }, // "Startups"
    { text: "گشت‌شب", value: 34 }, // "Night Tour"
    { text: "رقص‌محلی", value: 33 }, // "Local Dance"
    { text: "تصاویرزیبا", value: 32 }, // "Beautiful Images"
    { text: "اخبارجهان", value: 31 }, // "World News"
    { text: "فشن‌بلاگ", value: 30 }, // "Fashion Blog"
    { text: "هوای‌تهران", value: 29 }, // "Tehran Weather"
    { text: "جوک‌تازه", value: 28 }, // "Fresh Joke"
    { text: "آموزش‌آنلاین", value: 27 }, // "Online Education"
    { text: "موبایل‌نو", value: 26 }, // "New Mobile"
  ];
  return (
    <Card className="flex flex-col gap-2 h-full">
      <div className="flex items-center gap-1">
        <p className="font-subtitle-large">
          کاربرانی که بیشترین جست‌‌وجو‌ را انجام داده‌اند
        </p>
      </div>

      <ReactWordcloud
        options={{
          rotations: 1,
          rotationAngles: [0],
          enableTooltip: true,
          deterministic: false,
          fontFamily: "iranyekan",
          fontSizes: [14, 54],
          padding: 10,
          colors: [
            "#1CB0A5",
            "#EFA9B4",
            "#85AEFA",
            "#FEBE8C",
            "#AD88C6",
            "#99BC85",
          ],
          tooltipOptions: { theme: "light", arrow: true },
        }}
        words={words}
      />

      {/*<ColumnChart*/}
      {/*  xAxisCategory={[*/}
      {/*    "فرهنگ",*/}
      {/*    "اجتماعی",*/}
      {/*    "ورزشی",*/}
      {/*    "سیاسی",*/}
      {/*    "اقتصادی",*/}
      {/*    "علمی",*/}
      {/*    "هنر و رسانه",*/}
      {/*    "روزمره",*/}
      {/*    "بین الملل",*/}
      {/*  ]}*/}
      {/*  seriesColor={"#1DCEA3"}*/}
      {/*/>*/}
    </Card>
  );
};

export default ReportWordCloud;
