import { useEffect, useState } from "react";
import SearchBar from "../SearchBar";
import TableContent from "../TableContent";
import formatDate from "pages/admin/user-list/utils";
import PropTypes from "prop-types";
import userService from "service/api/userService";
import { Card } from "components/ui/Card";
import AddedGroupUsers from "../AddedGroupUsers";
import { ToastContainer } from "react-toastify";

const Step2 = ({ step, setStep }) => {
  const [table, setTable] = useState([]);
  const [table2, setTable2] = useState([]);
  const [tableItemsLimit, setTableItemsLimit] = useState(20);
  const [page, setPage] = useState(1);
  const [activeTab, setActiveTab] = useState("همه");
  const [loading, setLoading] = useState(false);
  const oneYearInMillis = 12 * 30 * 24 * 60 * 60 * 1000;
  const [filter, setFilter] = useState({
    q: "",
    register_at: {
      date_from: formatDate(new Date(new Date().getTime() - oneYearInMillis)),
      date_to: formatDate(new Date(new Date().getTime() + 24 * 60 * 60 * 1000)),
    },
    sort_rules: [
      { field: "login_at", direction: "desc" },
      { field: "register_at", direction: "desc" },
    ],
  });

  const getUsers = async () => {
    setLoading(true);
    try {
      let newFiler = {};
      if (filter?.q.trim() !== "") newFiler.q = filter.q;
      newFiler.register_at = filter?.register_at;
      newFiler.sort_rules = filter?.sort_rules;

      let newQuery = {};
      newQuery.page = page;
      newQuery.page_size = tableItemsLimit;
      newQuery.role = filter?.role || "all";
      const {
        data: { data },
      } = await userService.getUserList(newFiler, newQuery);
      setTable(data?.users);
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  const handleAddToSecondTable = (data) => {
    setTable((prev) => prev.filter((item) => item.id !== data.id));
    setTable2((prev) => [...prev, data]);
  };

  const handleRemoveFromSecondTable = (data) => {
    setTable2((prev) => prev.filter((item) => item.id !== data.id));
    setTable((prev) => {
      if (prev.some((item) => item.id === data.id)) {
        return prev; // User already exists, no change
      }
      return [...prev, data];
    });
  };

  useEffect(() => {
    getUsers();
  }, [page, tableItemsLimit, activeTab, filter]);

  return (
    <>
      <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-2 px-4 min-h-[600px]">
        <div className="h-full">
          <div className="px-3 h-full">
            <Card className="px-6 flex flex-col h-full">
              <SearchBar
                filter={filter}
                placeholder={
                  "نام کاربری / نام شخص / شماره موبایل را جست‌وجو کنید"
                }
                isUserList={true}
                setFilter={setFilter}
              />
              {loading ? (
                <div>Loading...</div>
              ) : (
                <TableContent
                  table={table}
                  tableItemsLimit={tableItemsLimit}
                  setTableItemsLimit={setTableItemsLimit}
                  page={page}
                  handleAddToSecondTable={handleAddToSecondTable}
                  setPage={setPage}
                  filter={filter}
                  setFilter={setFilter}
                  dataCount={100}
                  per_page={10}
                />
              )}
            </Card>
          </div>
        </div>
        <div className="h-full">
          <div className="px-3 h-full">
            <Card className="px-6 flex flex-col h-full">
              <SearchBar
                filter={filter}
                placeholder={
                  "نام کاربری / نام شخص / شماره موبایل را جست‌وجو کنید"
                }
                isUserList={true}
                setFilter={setFilter}
              />
              {loading ? (
                <div>Loading...</div>
              ) : (
                <AddedGroupUsers
                  table={table2}
                  tableItemsLimit={tableItemsLimit}
                  setTableItemsLimit={setTableItemsLimit}
                  page={page}
                  setPage={setPage}
                  filter={filter}
                  setFilter={setFilter}
                  dataCount={100}
                  handleRemoveFromSecondTable={handleRemoveFromSecondTable}
                  per_page={10}
                  handleAddToSecondTable={handleAddToSecondTable}
                />
              )}
            </Card>
          </div>
        </div>
      </div>
      <ToastContainer />
    </>
  );
};

Step2.propTypes = {
  step: PropTypes.number.isRequired,
  setStep: PropTypes.func.isRequired,
};

export default Step2;
