import { useBreadcrumb } from "hooks/useBreadcrumb";
import { useLocation } from "react-router-dom";
import UserPlanLoginHistoryTable from "./plan-login-history-table";

const UserPlanLoginHistory = () => {
  const { pathname } = useLocation();
  const userId = pathname.split("/").slice(-2)[0];
  const breadcrumbList = [
    { title: "کاربران", link: "/admin/user/list" },
    { title: "پرهام", link: `/admin/user/list/${userId}` },
    { title: "تاریخچه لاگین" },
  ];
  useBreadcrumb(breadcrumbList);

  return (
    <div className="container px-5 mx-auto">
      <UserPlanLoginHistoryTable />
    </div>
  );
};

export default UserPlanLoginHistory;
