@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.card-animation {
  opacity: 0;
  animation: fadeIn 0.8s ease-in-out forwards; 
}

.card-delay {
  animation-delay: 0.2s;
}

@media (max-width: 1750px) {
  .tab-content .tab-title {
    display: none; 
  }
}

@media (min-width: 1751px) {
  .tab-content .tab-title {
    display: inline; 
  }
}

@media (max-width: 1750px) {
  .tabs-360 {
    display: none;
  }

  .dropdown-360 {
    display: block;
  }
}

@media (min-width: 1751px) {
  .tabs-360 {
    display: flex;
  }

  .dropdown-360 {
    display: none;
  }
}

@media (max-width: 1239px) {
  .info-box {
    width: 92% !important; 
  }
}

@media (min-width: 1240px) and (max-width: 1650px) {
  .info-box {
    width: 96% !important; 
  }
}

@media (min-width: 1651px) and (max-width: 1800px) {
  .info-box {
    width: 99% !important; 
  }
}

@media (min-width: 1801px) {
  .info-box {
    width: 100% !important; 
  }
}


/* .mask-image-avatar {
  mask-image: linear-gradient(to bottom, black 50%, transparent 100%);
  filter: blur(5px);
  mask-composite: exclude;
} */

/* .overlay {
  position: absolute;
  bottom: 0;
  left: 42%;
  width: 18%;
  height: 45%;
   background-image: inherit;
  background-size: cover; 
  background: linear-gradient(to top, rgba(255, 255, 255, 0) 40%, rgba(255, 255, 255, 1) 60%);
  filter: blur(25px); 
  pointer-events: none; 
  border-bottom-left-radius: 50%; 
  border-bottom-right-radius: 50%;
} */
