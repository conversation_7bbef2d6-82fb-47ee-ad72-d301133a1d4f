import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";

const ColumnAndLineChart = () => {
  const pieOptions = {
    title: {
      text: null,
    },
    credits: {
      enabled: false,
    },
    xAxis: {
      categories: [
        "Jet fuel",
        "Duty-free diesel",
        "Petrol",
        "Diesel",
        "Gas oil",
      ],
      lineWidth: 1,
      minorGridLineWidth: 0,
      lineColor: "#949EB7",
      labels: {
        enabled: false,
      },
      minorTickLength: 0,
      tickLength: 0,
      gridLineWidth: 0,
      startOnTick: false,
      endOnTick: false,
      title: {
        text: "تاریخ ایجاد حساب کاربری",
      },
    },
    yAxis: {
      lineWidth: 1,
      minorGridLineWidth: 0,
      lineColor: "#949EB7",
      labels: {
        enabled: false,
      },
      minorTickLength: 0,
      tickLength: 0,
      gridLineWidth: 0,
      startOnTick: false,
      endOnTick: false,
      title: {
        text: "تعداد محتوا",
      },
    },
    tooltip: {
      valueSuffix: "",
    },
    plotOptions: {
      series: {
        borderRadius: "25%",
      },
    },
    series: [
      {
        type: "column",
        name: "محتوای بازنشری",
        data: [59, 83, 65, 228, 184],
        color: "#6D72E5",
      },
      {
        type: "column",
        name: "محتوای تولیدی",
        data: [58, 88, 75, 250, 176],
        color: "#1DCEA3",
      },
      {
        type: "line",
        step: "center",
        name: "میانگین",
        data: [47, 83.33, 70.66, 239.33, 175.66],
        color: "#E0526A",
        marker: {
          lineWidth: 2,
          lineColor: Highcharts.getOptions().colors[3],
          fillColor: "white",
        },
      },
      {
        type: "pie",
        name: "Total",
        data: [
          {
            name: "2020",
            y: 619,
            color: "#1DCEA3",
            dataLabels: {
              enabled: false,
              distance: -50,
              format: "{point.total} M",
              style: {
                fontSize: "15px",
              },
            },
          },
          {
            name: "2021",
            y: 586,
            color: "#6D72E5",
          },
        ],
        center: [75, 65],
        size: 100,
        innerSize: "70%",
        showInLegend: false,
        dataLabels: {
          enabled: false,
        },
      },
    ],
  };

  return (
    <div>
      {/* Column Chart */}
      <div>
        <HighchartsReact highcharts={Highcharts} options={pieOptions} />
      </div>
    </div>
  );
};

export default ColumnAndLineChart;
