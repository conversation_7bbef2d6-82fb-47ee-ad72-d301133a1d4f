import { memo, useEffect, useRef, useState } from "react";
import { CaretDown } from "@phosphor-icons/react";
import useClickAway from "hooks/useClickAway.jsx";
import NumberInput from "./NumberInput.jsx";
import { toPersianNumber } from "utils/helper.js";

const TimeDropDown = ({ handleChange, initialHour, initialMinite }) => {
  const [open, setOpen] = useState(false);
  const [hour, sethour] = useState(initialHour || "00");
  const [min, setMin] = useState(initialMinite || "00");
  const ref = useRef(null);
  useClickAway(ref, () => setOpen(false));
  useEffect(() => handleChange(`${hour}:${min}`), [hour, min]);
  return (
    <div ref={ref} className="relative w-full">
      <div
        className="flex gap-4 items-center justify-between p-3 rounded-lg bg-white border border-light-neutral-border-medium-rest w-full"
        style={{
          border: open && "1px solid #9198AD",
        }}
        onClick={() => setOpen((l) => !l)}
      >
        <span className="font-body-medium">
          {toPersianNumber(`${hour}:${min}`)}
        </span>
        <CaretDown
          style={{
            transform: open ? "rotate(180deg)" : "rotate(0deg)",
            transition: "all 0.5s",
          }}
        />
      </div>

      {open && (
        <div className="absolute top-12 left-0 bg-white rounded-lg shadow-[0px_4px_20px_0px_#0000001A] p-4 w-full flex justify-between items-center gap-2">
          <NumberInput
            max={60}
            handleChange={(x) => setMin(x)}
            initilaValue={min}
            className="focus:text-light-primary-text-hover"
          />
          <span className="font-headline-large">:</span>
          <NumberInput
            max={24}
            handleChange={(x) => sethour(x)}
            initilaValue={hour}
            className="focus:text-light-primary-text-hover"
          />
        </div>
      )}
    </div>
  );
};

export default memo(TimeDropDown);
