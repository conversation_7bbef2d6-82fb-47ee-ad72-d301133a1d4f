import { useEffect, useState } from "react";
import BulletinListHeader from "./components/BulletinListHeader";
import Wrapper from "./components/Wrapper";
import Empty from "./components/Empty";
import List from "./components/List";
import Loading from "components/ui/Loading";
import { ToastContainer } from "react-toastify";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";
import bulletin from "service/api/bulletin";
import { useBulletinStore } from "store/bulletinStore.js";

const BulletinList = () => {
  const breadcrumbList = [{ title: "بولتن‌" }];
  useBreadcrumb(breadcrumbList);
  const [bulletins, setBulletins] = useState([]);
  const [loading, setLoading] = useState(false);
  const [rerender, setRerender] = useState(false);
  const { resetBulletin } = useBulletinStore();

  const getBulletins = async () => {
    setLoading(true);
    try {
      const {
        data: { data },
      } = await bulletin.get();
      setBulletins(data);
      // console.log(data);
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  useEffect(() => {
    getBulletins();
  }, [rerender]);

  useEffect(() => {
    resetBulletin();
  }, []);

  return (
    <>
      {loading ? (
        <Loading />
      ) : (
        <>
          <div className="flex flex-col h-full w-full px-6">
            <div>
              <BulletinListHeader />
            </div>
            <Wrapper>
              {bulletins.length ? (
                <List
                  data={bulletins}
                  setRerender={setRerender}
                  setLoading={setLoading}
                />
              ) : (
                <Empty />
              )}
            </Wrapper>
          </div>
          <ToastContainer />
        </>
      )}
    </>
  );
};

export default BulletinList;
