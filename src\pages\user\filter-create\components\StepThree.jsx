import { useState } from "react";
import AllFilter from "components/FiltersOfPlatform/AllFilter";
import TwitterFilter from "components/FiltersOfPlatform/TwitterFilter";
import TelegramFilter from "components/FiltersOfPlatform/TelegramFilter";
import NewsFilter from "components/FiltersOfPlatform/NewsFilter";
import InstagramFilter from "components/FiltersOfPlatform/InstagramFilter";
import EitaaFilters from "components/FiltersOfPlatform/EitaaFilters";
import {
  CaretLeft,
  InstagramLogo,
  IntersectThree,
  Rss,
  Sliders,
  TelegramLogo,
  TwitterLogo,
} from "@phosphor-icons/react";
import EitaaLogo from "components/ui/EitaaLogo";
import Accordion from "components/ui/Accordion";
import MediaBadge from "components/ui/MediaBadge";
import { CInput } from "components/ui/CInput";
import CustomSearchDrawer from "components/CustomSearchDrawer";
import { CButton } from "components/ui/CButton.jsx";
import useSearchStore from "store/searchStore.js";
import PropTypes from "prop-types";

const StepThree = ({ filterData, setStatus, setFilterData }) => {
  const { setFilters } = useSearchStore();
  const [touchedQ, setTouchedQ] = useState(false);
  const [editQ, setEditQ] = useState(false);
  const [isOpen, setIsOpen] = useState(null);

  const [showAdvancedSearchDrawer, setShowAdvancedSearchDrawer] =
    useState(false);

  const selectHeader = {
    all: {
      name: "همه",
      icon: <IntersectThree className="size-4" color="#000000" />,
    },
    twitter: {
      name: "توئیتر",
      icon: <TwitterLogo className="size-4" color="#000000" />,
    },
    telegram: {
      name: "تلگرام",
      icon: <TelegramLogo className="size-4" color="#000000" />,
    },
    news: {
      name: "سایت‌های خبری",
      icon: <Rss className="size-4" color="#000000" />,
    },
    instagram: {
      name: "اینستاگرام",
      icon: <InstagramLogo className="size-4" color="#000000" />,
    },
    eitaa: { name: "ایتا", icon: <EitaaLogo color="#000000" /> },
  };

  const selectFilters = {
    all: (
      <AllFilter
        setData={setFilterData}
        initialValue={filterData.platform.all}
      />
    ),

    twitter: (
      <TwitterFilter
        setData={setFilterData}
        initialValue={filterData.platform.twitter}
      />
    ),
    telegram: (
      <TelegramFilter
        setData={setFilterData}
        initialValue={filterData.platform.telegram}
      />
    ),
    news: (
      <NewsFilter
        setData={setFilterData}
        initialValue={filterData.platform.news}
      />
    ),

    instagram: (
      <InstagramFilter
        setData={setFilterData}
        initialValue={filterData.platform.instagram}
      />
    ),
    eitaa: (
      <EitaaFilters
        setData={setFilterData}
        initialValue={filterData.platform.eitaa}
      />
    ),
  };

  const handleCustomSearchSubmit = async (value) => {
    setFilterData((prev) => ({
      ...prev,
      q: value,
    }));
    setShowAdvancedSearchDrawer(false);
  };
  return (
    <div className="p-6 flex flex-col gap-6">
      {editQ ? (
        <div className="shadow[0px_2px_20px_0px_#0000000D] rounded-lg bg-white p-6 flex flex-col gap-4">
          <div className="flex flex-col gap-2">
            <span className="font-overline-large">کلمات کلیدی</span>
            <CInput
              id={"q2"}
              name={"q2"}
              inset={true}
              readOnly={true}
              size={"md"}
              validation={"none"}
              direction={"rtl"}
              className={"flex-1 mt-2"}
              onFocus={() => setTouchedQ(false)}
              onBlur={() => setTouchedQ(true)}
              value={filterData.q}
              field={{}}
              onChange={(e) => {
                setFilterData((l) => {
                  const copy = JSON.parse(JSON.stringify(l));
                  copy.q = e.target.value;
                  return copy;
                });
              }}
              form={{ errors: [], touched: [] }}
              customAction={() => setShowAdvancedSearchDrawer(true)}
              customActionText={<Sliders size={20} />}
            ></CInput>
            <span className="font-body-small text-light-neutral-text-medium">
              از عملوندهای منطقی AND و OR و NOT استفاده کنید.
            </span>
            {touchedQ && filterData.q.length < 2 && (
              <span className="font-body-small text-light-error-text-rest">
                طول کلمات کلیدی حداقل باید ۲ کاراکتر باشد
              </span>
            )}
          </div>
          <div className="flex flex-row-reverse">
            <button
              type="submit"
              className={`w-[200px] h-10 ${
                filterData.q.length >= 2
                  ? "bg-light-primary-background-rest text-light-neutral-text-white"
                  : "bg-light-neutral-background-disable text-light-neutral-text-disable"
              } rounded-lg font-button-medium`}
              onClick={() => {
                if (filterData.q.length >= 2) {
                  setEditQ(false);
                }
              }}
            >
              ثبت کلمات کلیدی
            </button>
          </div>
        </div>
      ) : (
        <div className="shadow[0px_2px_20px_0px_#0000000D] rounded-lg bg-white p-6 flex flex-col gap-4">
          <div className="flex justify-between items-center">
            <span className="font-body-medium">کلمات کلیدی</span>
            <button
              className="flex gap-2 text-light-primary-text-rest items-center font-button-medium"
              onClick={() => setEditQ(true)}
            >
              <span>ویرایش</span>
              <CaretLeft />
            </button>
          </div>
          <div className="rounded-lg bg-light-neutral-background-low py-[6px] px-2 self-start border border-light-neutral-border-low-rest font-body-medium">
            {filterData.q}
          </div>
        </div>
      )}

      <div className={!showAdvancedSearchDrawer ? "hidden" : "block"}>
        <CustomSearchDrawer
          setShowMore={setShowAdvancedSearchDrawer}
          inputQuery={filterData.q}
          onSubmit={handleCustomSearchSubmit}
        />
      </div>

      <div className="flex flex-col gap-6">
        {Object.keys(filterData.platform).map((item, index) => (
          <div
            onClick={() => {
              setFilters({ platform: item });
              setIsOpen(index);
            }}
          >
            <Accordion
              isOpen={index === isOpen}
              Header={
                <div className="flex gap-2">
                  <div>{<MediaBadge media={item} />}</div>
                  <div className="font-body-medium">
                    <span>فیلتر در</span> <span>{selectHeader[item].name}</span>
                  </div>
                </div>
              }
            >
              {selectFilters[item]}
            </Accordion>
          </div>
        ))}
      </div>

      <div className="flex flex-row-reverse bg-white p-6 rounded-lg">
        <CButton
          type={"submit"}
          className={`!w-[200px]`}
          onClick={() => {
            setStatus(4);
          }}
        >
          ادامه
        </CButton>
        <button
          className="w-[100px] h-10 flex justify-center items-center text-light-primary-text-rest font-button-medium"
          onClick={() => {
            setStatus(2);
            // setSelectFiter("");
          }}
        >
          مرحله قبل
        </button>
      </div>
    </div>
  );
};

StepThree.propTypes = {
  setStatus: PropTypes.func,
  filterData: PropTypes.object,
  setFilterData: PropTypes.func,
};

export default StepThree;
