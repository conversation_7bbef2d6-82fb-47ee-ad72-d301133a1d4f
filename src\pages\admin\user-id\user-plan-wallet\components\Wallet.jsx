import {
  Co<PERSON>,
  <PERSON><PERSON>oi<PERSON>,
  Minus,
  PencilSimpleLine,
  Plus,
} from "@phosphor-icons/react";
import SingleButton from "./SingleButton";
import Areaspline from "components/Charts/Areaspline";
import { useState } from "react";

const Wallet = () => {
  const [time_gap, setTime_gap] = useState("hour");
  const categories = [];
  const seriesData = [{}];

  return (
    <>
      {/* wallet info */}
      <div className="bg-light-neutral-surface-card rounded-[8px] p-6">
        <p className="font-subtitle-large text-light-neutral-text-medium">
          کیف پول کاربر
        </p>

        <div className="flex items-center justify-between my-6">
          <p className="font-body-medium ">موجودی قابل استفاده:</p>
          <div className="flex items-center gap-2">
            <p className="font-headline-medium">۱۲٬۷۵۰٬۰۰۰</p>
            <span className="font-body-small text-light-neutral-text-medium">
              تومان
            </span>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <SingleButton title="افزایش اعتبار" Icon={Plus} />
          <SingleButton title="کاهش اعتبار" Icon={Minus} />
          <SingleButton title="استرداد وجه" Icon={HandCoins} />
        </div>
      </div>
      {/* wallet activities */}
      <div className="my-4 bg-light-neutral-surface-card rounded-[8px] p-6">
        <p className="font-subtitle-large text-light-neutral-text-medium">
          فعالیت‌های کیف پول
        </p>
        <Areaspline cat={categories} time_gap={time_gap} data={seriesData} />
      </div>
      {/* user bank info */}
      <div className="bg-light-neutral-surface-card rounded-[8px] p-6">
        <div className="flex items-center justify-between mb-6">
          <p className="font-subtitle-large text-light-neutral-text-medium">
            اطلاعات بانکی کاربر
          </p>
          <div className="w-8 h-8 rounded-md transition hover:bg-light-neutral-background-medium cursor-pointer flex items-center justify-center">
            <PencilSimpleLine size={21} />
          </div>
        </div>

        <div className="grid gap-3">
          <div className="flex items-center justify-between">
            <p className="font-body-medium ">شماره شباه</p>
            <div className="flex items-center gap-1">
              <p className="font-body-large">
                IR17 2745 9300 0000 3458 8123 01
              </p>
              <Copy className="cursor-pointer" />
            </div>
          </div>
          <div className="flex items-center justify-between">
            <p className="font-body-medium ">شماره کارت</p>
            <div className="flex items-center gap-1">
              <p className="font-body-large">۶۰۳۷ ۷۶۱۳ ۸۲۶۲ ۹۶۱۲</p>
              <Copy className="cursor-pointer" />
            </div>
          </div>
          <div className="flex items-center justify-between">
            <p className="font-body-medium">بانک</p>
            <p className="font-body-large">بانک قرض‌الحسنه مهر ایران</p>
          </div>
        </div>
      </div>
    </>
  );
};

export default Wallet;
