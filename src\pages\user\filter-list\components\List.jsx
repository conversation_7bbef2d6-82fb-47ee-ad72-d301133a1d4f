import FilterCard from "./FilterCard";
const List = ({ data, setRerender, setLoading }) => {
  return (
    <div className="flex flex-col gap-4">
      {data?.user?.map((item) => (
        <FilterCard
          setLoading={setLoading}
          media={
            Object.hasOwn(item?.params?.platform, "all")
              ? ["telegram", "twitter", "instagram", "news", "eitaa"]
              : Object.keys(item?.params?.platform)
          }
          key={item.id}
          data={item}
          filter_id={item.id}
          setRerender={setRerender}
        />
      ))}
      {data?.group?.map((item) => (
        <FilterCard
          setLoading={setLoading}
          media={
            Object.hasOwn(item?.params?.platform, "all")
              ? ["telegram", "twitter", "instagram", "news", "eitaa"]
              : Object.keys(item?.params?.platform)
          }
          key={item.id}
          data={item}
          filter_id={item.id}
          setRerender={setRerender}
          isGroup={true}
        />
      ))}
    </div>
  );
};

export default List;
