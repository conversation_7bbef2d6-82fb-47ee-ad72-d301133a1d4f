import * as Yup from "yup";

export const loginSchema = Yup.object().shape({
  username: Yup.string().required("نام کاربری اجباری است"),
  password: Yup.string().required("رمز عبور اجباری است"),
});

export const forgetPasswordStep1Schema = Yup.object().shape({
  mobile: Yup.string().required("شماره موبایل اجباری است"),
});

export const phoneVerifyOTPSchema = Yup.object().shape({
  otp: Yup.string().required("کد وارد شده اشتباه است"),
});
export const emailVerifyOTPSchema = Yup.object().shape({
  otp: Yup.string().required("کد وارد شده اشتباه است"),
});
export const forgetPasswordStep2Schema = Yup.object().shape({
  mobile: Yup.string().required("شماره موبایل اجباری است"),
  otp: Yup.string()
    .required("کد تایید اجباری است")
    .length(6, "کد تایید وارد شده نادرست است"),
});

export const forgetPasswordStep3Schema = Yup.object().shape({
  password: Yup.string()
    .min(8, "رمز عبور باید حداقل ۸ کاراکتر باشد")
    .matches(/[a-zA-Z]/, "رمز عبور باید شامل حداقل یک حرف انگلیسی باشد")
    .matches(/\d/, "رمز عبور باید شامل حداقل یک عدد باشد")
    .matches(/[@#$%]/, "رمز عبور باید شامل حداقل یک نماد مانند @#$% باشد")
    .required("رمز عبور الزامی است"),
  repeatPassword: Yup.string()
    .oneOf(
      [Yup.ref("password"), null],
      "تکرار رمز عبور باید با رمز عبور مطابقت داشته باشد",
    )
    .required("تکرار رمز عبور الزامی است"),
});

export const basicSearchSchema = Yup.object().shape({
  q: Yup.string().required("کلمه برای جست‌وجو اجباری است"),
});

export const customSearchSchema = Yup.object().shape({
  andWord: Yup.string(),
  orWord: Yup.string(),
  notWord: Yup.string(),
  q: Yup.string(),
});

export const alertStepOneSchema = Yup.object().shape({
  title: Yup.string().required("عنوان اجباری است"),
});

export const alertStepTwoSchema = Yup.object().shape({
  messenger: Yup.string()
    .oneOf(["telegram", "bale", "e"])
    .required("انتخاب پیامرسان اجباری است"),
  channel_id: Yup.string().required("آیدی اجباری است"),
});

export const filterStepOneSchema = Yup.object().shape({
  title: Yup.string().required("عنوان اجباری است"),
});

export const updateProfileSchema = Yup.object().shape({
  phone_number: Yup.string()
    .length(11, "شماره موبایل وارد شده صحیح نیست")
    .matches(/^09?(9\d{9})$/, "شماره موبایل وارد شده صحیح نیست")
    .required("شماره موبایل اجباری است"),
  email: Yup.string()
    .min(5, "ایمیل وارد شده صحیح نیست")
    .max(50, "ایمیل وارد شده صحیح نیست")
    .matches(/^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/, "ایمیل وارد شده صحیح نیست")
    .required("ایمیل اجباری است"),
  first_name: Yup.string()
    .min(2, "نام باید بیشتر از ۲ حرف باشد")
    .max(70, "نام باید کمتر از ۷۰ حرف باشد"),
  last_name: Yup.string()
    .min(2, "نام خانوادگی باید بیشتر از ۲ حرف باشد")
    .max(70, "نام خانوادگی باید کمتر از ۷۰ حرف باشد"),
  description: Yup.string()
    .min(2, "توضیحات باید بیشتر از ۲ حرف باشد")
    .max(255, "توضیحات باید کمتر از ۷۰ حرف باشد"),
});

export const bulletinStepOneSchema = Yup.object().shape({
  title: Yup.string().trim().required("عنوان اجباری است"),
});

export const addUserStep1Schema = Yup.object().shape({
  username: Yup.string()
    .min(2, "توضیحات باید بیشتر از ۲ حرف باشد")
    .max(70, "توضیحات باید کمتر از ۷۰ حرف باشد")
    .matches(
      /^[a-zA-Z0-9_]+$/,
      "نام کاربری باید فقط شامل حروف انگلیسی، اعداد و خط زیر باشد",
    )
    .required("نام کاربری اجباری است"),
  phone_number: Yup.string()
    .length(11, "شماره موبایل وارد شده صحیح نیست")
    .matches(/^(09|۰۹)([0-9۰-۹]{9})$/, "شماره موبایل وارد شده صحیح نیست")
    .required("شماره موبایل اجباری است"),
  email: Yup.string()
    .min(5, "ایمیل وارد شده صحیح نیست")
    .max(50, "ایمیل وارد شده صحیح نیست")
    .matches(/^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/, "ایمیل وارد شده صحیح نیست"),
  first_name: Yup.string()
    .min(2, "نام باید بیشتر از ۲ حرف باشد")
    .max(70, "نام باید کمتر از ۷۰ حرف باشد"),
  last_name: Yup.string()
    .min(2, "نام خانوادگی باید بیشتر از ۲ حرف باشد")
    .max(70, "نام خانوادگی باید کمتر از ۷۰ حرف باشد"),
  description: Yup.string()
    .min(2, "توضیحات باید بیشتر از ۲ حرف باشد")
    .max(255, "توضیحات باید کمتر از ۲۵۵ حرف باشد"),
});

export const addUserStep2Schema = Yup.object().shape({
  alert_access: Yup.number().required("*").min(0, "-"),
  bulletin_access: Yup.number().required("*").min(0, "-"),
  folder_access: Yup.number().required("*").min(0, "-"),
  report_360_access: Yup.number().required("*").min(0, "*"),
  comparison_access: Yup.number().required("*").min(0, "-"),
  filter_access: Yup.number().required("*").min(0, "-"),
  graph_access: Yup.number().required("*").min(0, "-"),
  opinion_access: Yup.number().required("*").min(0, "-"),
  // limited_content_access: Yup.number().when("unlimited_content_access", {
  //   is: (value) => value === "yes",
  //   then: Yup.number().required("*").min(0, "-"),
  //   otherwise: Yup.number().min(0, "-"),
  // }),
});

export const addUserStep4Schema = Yup.object().shape({
  password: Yup.string().required("کلمه عبور جدید اجباری است"),
  repeatPassword: Yup.string().required("تکرار کلمه عبور اجباری است"),
});
