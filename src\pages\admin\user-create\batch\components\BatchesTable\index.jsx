import { useState } from "react";
import Table from "./Table";
import { CButton } from "components/ui/CButton";
import Wrapper from "./Wrapper";

const BatchesTable = () => {
  const [data, setData] = useState([{}, {}, {}]);
  return (
    <>
      <div className="col-span-8 bg-light-neutral-surface-card rounded-[8px] p-6 mb-6">
        <Wrapper>
          {data?.map((item) => (
            <Table data={data} />
          ))}
        </Wrapper>
        <div className="flex items-center justify-end gap-2">
          <div className="w-48">
            <CButton role="neutral">انصراف</CButton>
          </div>
          <div className="w-48">
            <CButton>افزودن کاربران</CButton>
          </div>
        </div>
      </div>
    </>
  );
};

export default BatchesTable;
