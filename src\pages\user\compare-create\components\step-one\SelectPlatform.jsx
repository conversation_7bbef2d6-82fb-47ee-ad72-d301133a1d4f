import { useEffect, useState } from "react";
import MediaBage from "components/ui/MediaBadge";
import clsx from "clsx";
import { PLATFORMS_ARRAY } from "constants/platforms.js";
import PropTypes from "prop-types";

const SelectPlatform = ({ handleChange = () => {} }) => {
  const [platform, setPlatform] = useState("");

  useEffect(() => {
    handleChange(platform);
  }, [platform]);

  return (
    <div className="flex flex-col gap-4">
      <span className="font-subtitle-large">بستر مقایسه را انتخاب کنید</span>
      <div className="flex gap-[34px] items-center">
        {PLATFORMS_ARRAY.map((item) => (
          <div
            className={clsx(
              "flex items-center justify-center rounded-lg w-[120px] h-[100px] border cursor-pointer",
              platform === item.value
                ? "border-light-primary-border-rest bg-light-primary-background-highlight"
                : "border-light-neutral-border-low-rest",
            )}
            key={item.value}
            onClick={() => setPlatform(item.value)}
          >
            <div className="flex items-center flex-col gap-4">
              <MediaBage media={item.value} size="big" />
              <span className="font-body-medium">{item.label}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

SelectPlatform.propTypes = {
  handleChange: PropTypes.func,
};

export default SelectPlatform;
