import PropTypes from "prop-types";
import formatDate from "pages/admin/user-list/utils.js";
import DateFilter from "./DateFilter.jsx";
import TopMenuTabs from "./TopMenuTabs.jsx";

const TopMenu = ({ filter, setFilter, activeTab, setActiveTab }) => {
  const handleDateChange = (dates) => {
    const { from, to } = dates;
    setFilter({
      ...filter,
      register_at: { date_from: formatDate(from), date_to: formatDate(to) },
    });
  };

  return (
    <div className="flex items-center justify-between">
      <TopMenuTabs setActiveTab={setActiveTab} activeTab={activeTab} />
      <div className="flex items-center gap-2">
        <DateFilter
          handleDateChange={handleDateChange}
          selectedDateRange={filter.register_at}
        />
      </div>
    </div>
  );
};

TopMenu.propTypes = {
  filter: PropTypes.object.isRequired,
  setFilter: PropTypes.func.isRequired,
  activeTab: PropTypes.string.isRequired,
  setActiveTab: PropTypes.func.isRequired,
};

export default TopMenu;
