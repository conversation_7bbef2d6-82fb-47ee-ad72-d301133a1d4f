import Doughnut from "components/Charts/Doughnut";
import { shortener, toPersianNumber } from "utils/helper";

const generateUniqueColors = (count) => {
  const colors = [];
  for (let i = 0; i < count; i++) {
    const hue = (i * 360) / count; // Evenly distribute hues
    const saturation = 70; // Fixed saturation for vibrancy
    const lightness = 50; // Fixed lightness for consistency
    colors.push(`hsl(${hue}, ${saturation}%, ${lightness}%)`);
  }
  return colors;
};

const WaveHashtagDistribute = ({ elements }) => {
  // Calculate total hashtag count
  const totalHashtagsCount =
    elements
      ?.filter((item) => item?.element_type === "Hashtag")
      ?.reduce((acc, item) => acc + (item?.stats?.posts || 0), 0) || 0;

  // Map hashtag data
  const hashtagData =
    elements
      ?.filter((element) => element?.element_type === "Hashtag")
      ?.map((item) => {
        const postCount = item?.stats?.posts || 0;
        return {
          ...item,
          postCount,
          total: totalHashtagsCount,
          percentage:
            totalHashtagsCount > 0 ? (postCount / totalHashtagsCount) * 100 : 0,
        };
      }) || [];

  // Limit to top 10 items for display
  const displayedData = hashtagData.slice(0, 10);

  // Generate unique colors for the displayed data
  const colors = displayedData.length
    ? generateUniqueColors(displayedData.length)
    : ["#cccccc"]; // Fallback color for empty data

  return (
    <div
      className="bg-light-neutral-surface-card rounded-lg p-3 h-[400px]"
      style={{
        boxShadow: "0px 2px 20px 0px #0000000D",
      }}
    >
      <p className="font-subtitle-large mb-7">توزیع هشتگ‌ها</p>
      {displayedData.length ? (
        <Doughnut
          name="Hashtags"
          height={240}
          data={displayedData.map((item) => ({
            name: item?.content,
            y: item?.percentage,
          }))}
          maxWidth={"200rem"}
          legendFormatter={function () {
            return `
              <div style="font-family: iranyekan; display: flex; justify-content: space-between; align-items: center; width: 200px; gap: 16px;">
                <div style="font-size: 12px; color: #333;">${toPersianNumber(
                  this.y?.toFixed(2)
                )} %</div>
                <div style="display: flex; align-items: center; gap: 8px;">
                  <span style="font-size: 14px; color: #555;">${shortener(
                    this.name.split("#")[1],
                    13,
                    "rtl"
                  )}</span>
                  <div style="background-color: ${
                    this.color
                  }; border-radius: 50%; width: 12px; height: 12px;"></div>
                </div>
              </div>
            `;
          }}
          tooltipFormatter={function () {
            return `<div style="display: flex; flex-direction: column; gap: 8px; text-align: center; font-family: iranyekan;">
                  <div>${this.key}</div>
                  <div>${toPersianNumber(this.y?.toFixed(2))} %</div>
                </div>`;
          }}
          colors={colors}
        />
      ) : (
        <div className="w-full h-60 flex justify-center pt-4 items-center text-lg text-light-neutral-text-low font-body-bold-medium">
          داده ای برای نمایش وجود ندارد
        </div>
      )}
    </div>
  );
};

export default WaveHashtagDistribute;
