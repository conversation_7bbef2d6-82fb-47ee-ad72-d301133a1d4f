const Existence = () => {
  return (
    <div>
      <p className="font-subtitle-medium mb-4">موجودیت‌های درون متن</p>
      <div className="flex flex-col gap-4">
        <div className="flex gap-4">
          <p className="font-body-small text-light-neutral-text-medium w-10">شخص</p>
          <p className="font-body-medium">جواد نکونام, مهرداد محمدی, ...</p>
        </div>
        <div className="flex gap-4">
          <p className="font-body-small text-light-neutral-text-medium w-10">مکان</p>
          <p className="font-body-medium">ورزشگاه تختی</p>
        </div>
        <div className="flex gap-4">
          <p className="font-body-small text-light-neutral-text-medium w-10">مک<PERSON></p>
          <p className="font-body-medium">باشگاه استقلال </p>
        </div>
        <div className="flex gap-4">
          <p className="font-body-small text-light-neutral-text-medium w-10">رویداد</p>
          <p className="font-body-medium">دربی تهران</p>
        </div>
      </div>
    </div>
  );
};

export default Existence;
