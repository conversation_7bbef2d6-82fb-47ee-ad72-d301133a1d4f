import { Doughnut } from "react-chartjs-2";
import { toPersianNumber } from "utils/helper";

const ContentDistributionByAccountAgeBarchart = ({
  fetchedParticipantCredits,
}) => {
  return (
    <div>
      <div
        className="bg-light-neutral-surface-card rounded-lg p-3 h-full"
        style={{
          boxShadow: "0px 2px 20px 0px #0000000D",
        }}
      >
        <div className="flex items-center justify-between mb-5">
          <div className="flex items-center gap-1">
            <p className="font-subtitle-large">
              محتوای منتشر شده بر اساس قدمت حساب‌ها
            </p>
          </div>
        </div>
        {/* <Doughnut
          showDataLabels={showDataLabels}
          name="sentiment"
          height={240}
          data={[
            {
              name: "محتوای تولیدی",
              y: fetchedParticipantCredits.total?.post || 0,
              color: "#1DCEA3",
            },
            {
              name: "بازنشر",
              y: fetchedParticipantCredits.total?.repost || 0,
              color: "#6D72E5",
            },
            {
              name: "ریپلای",
              y: fetchedParticipantCredits.total?.reply || 0,
              color: "#E0526A",
            },
            {
              name: "کوت",
              y: fetchedParticipantCredits.total?.quote || 0,
              color: "#FFB946",
            },
          ]}
          legendFormatter={function () {
            return `<div dir="rtl" style="font-family: iranyekan; display: flex; gap: 30%; width: 100%; padding: 2px; align-items: center; margin-right: 2rem;">
                          <span style="font-size: 16px; justify-self: start;">
                            ${toPersianNumber(this.y)}%
                          </span>
                          <div style="display: flex; gap: 8px; align-items: center; padding-right: 1rem;">
                            <span style="color: ${
                              this.color
                            }; font-size: 14px;">
                              ${this.name}
                            </span> 
                            <img src=${
                              sentiment[this.name]
                            } style="width: 20px; height: 20px; margin-right: 15px;" />
                          </div>
                        </div>`;
          }}
          tooltipFormatter={function () {
            return `<div style="display:flex;flex-direction:column;gap:8px;text-align:center;font-family:iranyekan"><div>${this.key}</div><b>${this.y}%</b></div>`;
          }}
          colors={["#1CB0A5", "#********", "#E0526A"]}
        /> */}
      </div>
    </div>
  );
};

export default ContentDistributionByAccountAgeBarchart;
