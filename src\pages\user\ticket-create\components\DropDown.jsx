import { useState } from "react";
import { CaretDown, Check } from "@phosphor-icons/react";

const DropDown = ({ subsets = [], handleChange, initialValue, title }) => {
  const [open, setOpen] = useState(false);
  const [selected, setSelected] = useState(initialValue || subsets[0]);

  return (
    <>
      {title && <p className="font-overline-large pb-1">{title}</p>}
      <button
        onBlur={() => setOpen(false)}
        className="relative w-full"
        type="button"
      >
        <div
          className="flex gap-4 items-center justify-between p-2 rounded-lg bg-white border border-light-neutral-border-medium-rest w-full"
          style={{
            border: open && "1px solid #9198AD",
          }}
          onClick={() => setOpen((l) => !l)}
        >
          <span className="font-body-medium">{selected?.text}</span>
          <CaretDown
            style={{
              transform: open ? "rotate(180deg)" : "rotate(0deg)",
              transition: "all 0.5s",
            }}
          />
        </div>

        {open && (
          <div className="absolute top-12 left-0 bg-white rounded-lg shadow-[0px_4px_20px_0px_#0000001A] p-4 w-full">
            <div className="font-body-small flex flex-col gap-1">
              {subsets?.map((item) => (
                <div
                  key={item.text}
                  className="flex items-center justify-between gap-4 p-2 rounded-lg hover:bg-light-neutral-surface-highlight"
                  onClick={() => {
                    setOpen(false);
                    handleChange(item);
                    setSelected(item);
                  }}
                >
                  <span>{item.text}</span>
                  {selected.value === item.value && <Check />}
                </div>
              ))}
            </div>
          </div>
        )}
      </button>
    </>
  );
};

export default DropDown;
