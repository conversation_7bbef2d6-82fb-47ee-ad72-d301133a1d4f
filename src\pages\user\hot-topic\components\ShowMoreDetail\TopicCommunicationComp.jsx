import { fixPercentToShow } from "utils/helper";

const TopicCommunicationComp = ({ title, percent }) => {
  const selectCategory = {
    FarhangHonarVaResane: "فرهنگ و هنر و رسانه",
    <PERSON>asi: "سیاسی",
    <PERSON><PERSON><PERSON><PERSON>: "روزمره",
    <PERSON>jtemae<PERSON>: "اجتماعی",
    Varzeshi: "ورزشی",
    ElmiVaD<PERSON>shghai: "علمی و دانشگاهی",
    Beinolmelal: "بین الملل",
    Eghtesadi: "اقتصادی",
  };

  return (
    <div className="flex gap-12">
      <span className="font-body-medium text-light-neutral-text-medium w-32 text-start">
        {selectCategory[title]}
      </span>
      <span className="font-body-large">{fixPercentToShow(percent, 0)}</span>
    </div>
  );
};

export default TopicCommunicationComp;
