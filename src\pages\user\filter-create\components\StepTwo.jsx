import { useState } from "react";
import Divider from "components/ui/Divider";
import SelectPlatform from "components/FilterSelector/SelectPlatform";
import { platformFilters } from "utils/platformFilters";
import { CInput } from "components/ui/CInput";
import { Sliders } from "@phosphor-icons/react";
import CustomSearchDrawer from "components/CustomSearchDrawer";
import { CButton } from "components/ui/CButton.jsx";

const StepTwo = ({ setStatus, filterData, setFilterData }) => {
  const [touchedQ, setTouchedQ] = useState(false);

  const [showAdvancedSearchDrawer, setShowAdvancedSearchDrawer] =
    useState(false);

  const setPlatform = (media) => {
    setFilterData((l) => {
      const copy = JSON.parse(JSON.stringify(l));
      copy.platform = {};
      media.forEach((item) => {
        copy.platform[item] = Object.hasOwn(l.platform, item)
          ? l.platform[item]
          : platformFilters[item];
      });
      return copy;
    });
  };
  const handleCustomSearchSubmit = async (value) => {
    setFilterData((prev) => ({
      ...prev,
      q: value,
    }));
    setShowAdvancedSearchDrawer(false);
  };
  return (
    <div className="p-6 w-full">
      <div className="flex flex-col gap-4">
        <span className="font-body-medium">بستر مورد بررسی:</span>
        <p className="font-body-small">
          می‌توانید هم‌زمان چند بستر گوناگون را انتخاب کنید و در صورتی که قصد
          دارید همه بسترها را انتخاب کنید٬ کافی است همه گزینه ها را انتخاب کنید{" "}
        </p>
        <SelectPlatform
          handleChange={setPlatform}
          initialValue={Object.keys(filterData.platform)}
        />
      </div>

      <div className="my-6">
        <Divider />
      </div>

      <div className="flex flex-col gap-2">
        <span className="font-overline-large">کلمات کلیدی</span>
        <CInput
          id={"q2"}
          name={"q2"}
          inset={true}
          readOnly={true}
          size={"md"}
          validation={"none"}
          direction={"rtl"}
          className={"flex-1 mt-2"}
          onFocus={() => setTouchedQ(false)}
          onBlur={() => setTouchedQ(true)}
          value={filterData.q}
          field={{}}
          onChange={(e) => {
            setFilterData((l) => {
              const copy = JSON.parse(JSON.stringify(l));
              copy.q = e.target.value;
              return copy;
            });
          }}
          form={{ errors: [], touched: [] }}
          customAction={() => setShowAdvancedSearchDrawer(true)}
          customActionText={<Sliders size={20} />}
        ></CInput>
        <span className="font-body-small text-light-neutral-text-medium">
          از عملوندهای منطقی AND و OR و NOT استفاده کنید.
        </span>
        {touchedQ && filterData.q.length < 2 && (
          <span className="font-body-small text-light-error-text-rest">
            طول کلمات کلیدی حداقل باید ۲ کاراکتر باشد
          </span>
        )}
      </div>

      <div className="flex flex-row-reverse mt-12">
        <CButton
          type={"submit"}
          className={`!w-[200px]`}
          readOnly={
            !(
              filterData.q.length >= 2 &&
              Object.values(filterData.platform).length
            )
          }
          onClick={() => {
            if (
              filterData.q.length >= 2 &&
              Object.values(filterData.platform).length
            ) {
              setStatus(3);
            }
          }}
        >
          ادامه
        </CButton>
        <button
          className="w-[100px] h-10 flex justify-center items-center text-light-primary-text-rest font-button-medium"
          onClick={() => {
            setStatus(1);
          }}
        >
          مرحله قبل
        </button>
      </div>
      <div className={!showAdvancedSearchDrawer ? "hidden" : "block"}>
        <CustomSearchDrawer
          setShowMore={setShowAdvancedSearchDrawer}
          inputQuery={filterData.q}
          onSubmit={handleCustomSearchSubmit}
        />
      </div>
    </div>
  );
};

export default StepTwo;
