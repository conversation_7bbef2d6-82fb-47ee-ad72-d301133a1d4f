import { useBreadcrumb } from "hooks/useBreadcrumb";
import SingleButton from "./components/SingleButton";
import { HandCoi<PERSON>, Minus, Plus } from "@phosphor-icons/react";
import Wallet from "./components/Wallet";
import WalletTable from "./wallet-table";
import { useLocation } from "react-router-dom";

const UserWallet = () => {
  const { pathname } = useLocation();
  const userId = pathname.split("/").slice(-2)[0];
  const breadcrumbList = [
    { title: "کاربران", link: "/admin/user/list" },
    { title: "پرهام", link: `/admin/user/list/${userId}` },
    { title: "کیف پول" },
  ];
  useBreadcrumb(breadcrumbList);
  return (
    <>
      <div className="grid grid-cols-12 gap-4 container lg:px-4 mx-auto">
        <div className="col-span-4">
          <Wallet />
        </div>

        <div className="col-span-8 row-span-1 bg-light-neutral-surface-card rounded-[8px] p-6">
          <WalletTable />
        </div>
      </div>
    </>
  );
};

export default UserWallet;
