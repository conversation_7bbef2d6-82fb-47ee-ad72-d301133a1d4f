import { useEffect, useState } from "react";
import { useLayoutContext } from "context/layout-context";
import Menu from "./components/Menu";
import Notifications from "./components/Notifications";
import Announcement from "./components/Announcement";
import Paginate from "components/ui/Paginate";
import notification from "service/api/notification";
import queryString from "query-string";
import { Outlet, useNavigate } from "react-router-dom";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";

const Notif = () => {
  const navigate = useNavigate();
  const handleChange = (x) => {
    navigate(`/app/notif/${x}`);
  };

  const { setIsSidebarOpened } = useLayoutContext();
  useEffect(() => {
    setIsSidebarOpened(false);
  }, []);

  return (
    <div className="flex flex-row gap-6 p-6 relative h-full ">
      <div className="flex w-1/2 w-[20%]">
        <Menu handleChange={handleChange} />
      </div>
      <div className="w-1/2 w-[80%]">
        <Outlet />
      </div>
    </div>
  );
};

export default Notif;
