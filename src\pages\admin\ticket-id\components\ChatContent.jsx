import {
  ArrowDown,
  File,
  FileZip,
  FilePdf,
  MicrosoftExcelLogo,
  FileTxt,
  FileDoc,
} from "@phosphor-icons/react";
import clsx from "clsx";
import { parseTimeToPersian, shortener } from "utils/helper";
const ChatContent = ({ data }) => {
  const chatBubbleClassName = clsx("p-4 my-1", {
    "bg-light-primary-background-highlight": data?.is_responder,
    "bg-light-success-background-highlight": !data?.is_responder,
  });

  return (
    <>
      {data?.status != "closed" && (
        <div
          className="pb-4 w-[600px] ml-3"
          style={{
            direction: !data?.is_responder ? "ltr" : "rtl",
          }}
        >
          <span className="font-body-small text-light-neutral-text-medium">
            {!data?.is_responder ? "پیام کاربر" : "پیام شما"}
          </span>
          <div
            style={{
              borderRadius: data?.is_responder
                ? "32px 4px 32px 32px"
                : "4px 32px 32px 32px",
            }}
            className={chatBubbleClassName}
          >
            <p className="font-paragraph-medium break-all" dir="rtl">
              {data?.description}
            </p>

            <div className="flex flex-wrap gap-4" dir="rtl">
              {data?.attachment?.map((item, index) => (
                <div
                  key={index}
                  className="relative w-16 h-16 rounded my-3 group cursor-pointer"
                >
                  <div
                    className={clsx(
                      "w-full h-full rounded-md flex items-center justify-center",
                      {
                        "text-yellow-500 bg-yellow-100":
                          item?.mime_type?.endsWith("zip"),
                        "text-red-500 bg-red-100":
                          item?.mime_type?.endsWith("pdf"),
                        "text-green-500 bg-green-100":
                          item?.mime_type?.endsWith("csv"),
                        "text-blue-500 bg-blue-100":
                          item?.mime_type?.endsWith("document"),
                        "bg-gray-300": !["zip", "pdf", "csv", "document"].some(
                          (type) => item?.mime_type?.endsWith(type),
                        ),
                      },
                    )}
                  >
                    {item?.mime_type?.startsWith("image/") ? (
                      <img
                        src={item?.file}
                        alt={item?.filename}
                        className="w-full h-full object-cover rounded-md"
                      />
                    ) : item?.mime_type?.endsWith("pdf") ? (
                      <FilePdf
                        size={30}
                        className="flex items-center justify-center"
                      />
                    ) : item?.mime_type?.endsWith("zip") ? (
                      <FileZip
                        size={30}
                        className="flex items-center justify-center"
                      />
                    ) : item?.mime_type?.endsWith("rar") ? (
                      <FileZip
                        size={30}
                        className="flex items-center justify-center"
                      />
                    ) : item?.mime_type?.endsWith("csv") ? (
                      <MicrosoftExcelLogo
                        size={30}
                        className="flex items-center justify-center"
                      />
                    ) : item?.mime_type?.endsWith("xls") ? (
                      <MicrosoftExcelLogo
                        size={30}
                        className="flex items-center justify-center"
                      />
                    ) : item?.mime_type?.endsWith("txt") ? (
                      <FileTxt
                        size={30}
                        className="flex items-center justify-center"
                      />
                    ) : item?.mime_type?.endsWith("docx") ? (
                      <FileDoc
                        size={30}
                        className="flex items-center justify-center"
                      />
                    ) : item?.mime_type?.endsWith("doc") ? (
                      <FileDoc
                        size={30}
                        className="flex items-center justify-center"
                      />
                    ) : item?.mime_type?.endsWith("document") ? (
                      <FileDoc
                        size={30}
                        className="flex items-center justify-center"
                      />
                    ) : (
                      <File
                        size={30}
                        className="flex items-center justify-center"
                      />
                    )}
                  </div>
                  <p className="font-body-bold-small mt-1">
                    {shortener(item?.filename, 6)}
                  </p>

                  <a
                    href={`${item?.file}`}
                    target="_blank"
                    rel="noreferrer"
                    download={item?.filename}
                    className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 bg-[#00000066] rounded-md"
                  >
                    <ArrowDown size={28} className="text-white p-1" />
                  </a>
                </div>
              ))}
            </div>
          </div>
          <span className="font-body-small text-light-neutral-text-medium float-end">
            {parseTimeToPersian(data?.created_at)}
          </span>
        </div>
      )}
    </>
  );
};

export default ChatContent;
