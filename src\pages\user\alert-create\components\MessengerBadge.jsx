const MessengerBadge = ({ children, name, selected, height, dir, onClick,disabled }) => {
  return (
    <div
      className={`w-full rounded-lg border border-light-neutral-border-low-rest flex items-center gap-4 justify-center cursor-pointer ${
        disabled ? "opacity-50" : ""
      }`}
      style={{
        ...(selected
          ? { backgroundColor: "#B4ABE34D", borderColor: "#B4ABE34D" }
          : {}),
        height: height,
        flexDirection: dir,
      }}
      onClick={onClick}
    >
      <div>{children}</div>
      <span className={height > 70 ? "font-body-medium" : "font-body-small"}>
        {name}
      </span>
    </div>
  );
};

export default MessengerBadge;
