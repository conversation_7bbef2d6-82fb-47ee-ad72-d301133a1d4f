import CardListItem from "../CardListItem";
import { Detective, TrashSimple } from "@phosphor-icons/react";
import ToggleInput from "components/ui/ToggleInput";
import Divider from "components/ui/Divider";
import { CButton } from "components/ui/CButton";
import { useState } from "react";
import Popup from "components/ui/PopUp";
import { CInput } from "components/ui/CInput";

const UserActions = () => {
  const [isDeletePopupOpen, setIsDeletePopupOpen] = useState(false);
  const [disableActionToggle, setDisableActionToggle] = useState(false);
  const [inputVal, setInputVal] = useState("");
  return (
    <>
      <div className="flex justify-center items-center">
        <div className="bg-light-neutral-surface-card w-[605px] p-6 pt-0 rounded-b-[8px]">
          <p className="font-body-medium text-light-neutral-text-medium mb-8">
            در اینجا شما می‌توانید اکانت کاربر را حذف یا فعال و ... کنید
          </p>
          <div className="flex items-center justify-between cursor-pointer transition-all hover:bg-light-neutral-background-medium px-3 py-1 rounded-md">
            <div className="flex items-center gap-2">
              <div className="flex items-center justify-center w-10 h-10 bg-light-primary-background-highlight rounded-full">
                <Detective size={23} />
              </div>
              <div className="grid">
                <p className="font-body-large">{"غیرفعال کردن"}</p>
                <p className="font-body-small text-light-neutral-text-medium">
                  {
                    "در صورت غیرفعال کردن کاربر٬ دسترسی وی به سامانه امکان‌پذیر نخواهد بود"
                  }
                </p>
              </div>
            </div>
            <ToggleInput
              isOn={disableActionToggle}
              onChange={() => setDisableActionToggle(!disableActionToggle)}
              className=""
            />
          </div>
          <Divider className={"my-5"} />
          <CardListItem
            icon={TrashSimple}
            title="تنظیمات رمز عبور"
            description="در صورت حذف کاربر. امکان بازگرانی وی به سامانه ممکن نیست"
            onClick={() => setIsDeletePopupOpen(true)}
          />
          <div className="flex items-center gap-3 mt-7">
            <div className="w-full">
              <CButton role="neutral">انصراف</CButton>
            </div>
            <div className="w-full">
              <CButton>ذخیره</CButton>
            </div>
          </div>
        </div>
      </div>
      <Popup
        isOpen={isDeletePopupOpen}
        onClose={() => {
          setIsDeletePopupOpen(false);
          setInputVal("");
        }}
        hasCloseIcon
        hasButton={false}
        title="تایید حذف کاربر"
      >
        <p className="font-overline-large my-6">
          برای حذف کاربر٬ لطفا رمزعبور خود را وارد کنید
        </p>
        <CInput
          id={"password"}
          type="password"
          size={"md"}
          state={"filled"}
          placeholder="رمز عبور خود را وارد کنید"
          title={"رمز عبور"}
          value={inputVal}
          onChange={(e) => setInputVal(e?.target?.value)}
        />
        <div className="mt-9">
          <CButton>حذف کاربر</CButton>
        </div>
      </Popup>
    </>
  );
};

export default UserActions;
