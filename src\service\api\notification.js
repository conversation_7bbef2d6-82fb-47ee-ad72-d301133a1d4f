import useFetch from "../index";

class Notification {
  get(query) {
    return useFetch.get(
      `/api/v1/notification/?${new URLSearchParams(query).toString()}`,
    );
  }
  getById(id) {
    return useFetch.get(`/api/v1/notification/${id}/`);
  }
  markAsReadNotification(id) {
    return useFetch.patch(`/api/v1/notification/mark/read/${id}/`);
  }
  statistical() {
    return useFetch.get("/api/v1/notification/statistical/");
  }
}
export default new Notification();
