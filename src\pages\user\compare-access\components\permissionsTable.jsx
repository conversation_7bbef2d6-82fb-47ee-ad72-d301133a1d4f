import { CButton } from "components/ui/CButton";
import {
  CheckCircle,
  MagnifyingGlass,
  PencilSimple,
  Plus,
  Trash,
} from "@phosphor-icons/react";
import PropTypes from "prop-types";
import { useContext, useEffect, useState } from "react";
import Popup from "components/ui/PopUp";
import ToolTip from "components/ui/ToolTip";
import { notification, parseTimeToPersianSummary } from "utils/helper";
import AuthContext from "context/auth-context.jsx";
import { DropDownInput } from "components/ui/DropDownInput.jsx";
import access from "service/api/access.js";

const UserPermissionsTable = ({ showTable, groupData, setGroupData }) => {
  const { profile } = useContext(AuthContext);

  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [dropDownUsers, setDropDownUsers] = useState([]);
  const [selectedUser, setSelectedUser] = useState([]);
  const [selectedPermissions, setSelectedPermissions] = useState([]);
  const [editMode, setEditMode] = useState(false);

  useEffect(() => {
    const groupUsers = groupData?.group?.contact || [];
    const filtered = profile?.limitations?.user_access.filter((user) => {
      let found = false;
      groupUsers.forEach((item) => {
        if (item.user === user) found = true;
      });
      return !found;
    });
    setDropDownUsers(filtered);
  }, [groupData, selectedUser]);

  const handlePermissionChange = (user, permissionType) => {
    setSelectedPermissions((prevPermissions) => {
      // Check if permissionType already exists in the array
      if (prevPermissions.includes(permissionType)) {
        // If it exists, remove it by filtering it out
        return prevPermissions.filter((perm) => perm !== permissionType);
      } else {
        // If it doesn't exist, add it to the array
        return [...prevPermissions, permissionType];
      }
    });
  };

  const updateServer = async (data) => {
    const groupId = groupData?.group?.id || undefined;
    if (!groupId) return false;

    try {
      const res = await access.update(groupId, data);

      if (res?.data?.status !== "OK") {
        return false;
      }

      groupData.group = res.data.data;
      setGroupData(groupData);
      return true;
    } catch (error) {
      console.error("Error saving or updating data:", error);
      return false;
    }
  };

  const handleSubmit = async () => {
    if (typeof selectedUser[0] !== "string") return;

    const existingContacts = groupData.group.contact;
    const data = {
      description: "",
      contact: existingContacts.some(
        (contact) => contact.user === selectedUser[0]
      )
        ? existingContacts.map((contact) =>
            contact.user === selectedUser[0]
              ? { user: selectedUser[0], permission: selectedPermissions }
              : contact
          )
        : [
            ...existingContacts,
            { user: selectedUser[0], permission: selectedPermissions },
          ],
    };

    const done = await updateServer(data);
    if (done) {
      notification.success(
        `دسترسی با موفقیت ذخیره شد`,
        <CheckCircle className="text-light-success-text-rest" />
      );
      handleClosePopUp();
    } else {
      notification.error(
        `با عرض پوزش، امکان ذخیره دسترسی وجود ندارد. لطفا پس از چند لحظه دوباره تلاش کنید و در صورت عدم حل مشکل با پشتیبانی تماس بگیرید.`
      );
    }
  };

  const handleRemoveUser = async (userToRemove) => {
    const existingContacts = groupData.group.contact;
    const updatedContact = existingContacts.filter(
      (item) => item.user !== userToRemove
    );

    const data = {
      description: "",
      contact: updatedContact,
    };

    const done = await updateServer(data);
    if (done) {
      notification.success(
        `دسترسی با موفقیت حذف شد`,
        <CheckCircle className="text-light-success-text-rest" />
      );

      setSelectedPermissions([]);
      setSelectedUser([]);
      setEditMode(false);
      setIsPopupOpen(false);
    } else {
      notification.error(
        `با عرض پوزش، امکان حذف دسترسی وجود ندارد. لطفا پس از چند لحظه دوباره تلاش کنید و در صورت عدم حل مشکل با پشتیبانی تماس بگیرید.`
      );
    }
  };

  const handleEditUser = (user) => {
    setSelectedUser([user.user]);
    setSelectedPermissions(user.permission);
    setEditMode(true);
    setIsPopupOpen(true);
  };

  const handleClosePopUp = () => {
    setSelectedPermissions([]);
    setSelectedUser([]);
    setEditMode(false);
    setIsPopupOpen(false);
  };

  console.log(groupData);

  return (
    <div className="flex justify-center">
      {showTable && (
        <div className="bg-light-neutral-surface-card box-shadow[0px_2px_20px_0px_#0000000D] shadow-md w-[875px] mt-5 rounded-lg p-6 !mx-6 font-body-medium">
          <div className="flex flex-col">
            <div className="flex justify-between items-center pb-7">
              <p className="font-body-medium font-bold">مشترکین</p>
              <CButton
                mode={"outline"}
                className="!w-[8rem] text-[#634fc7] gap-2"
                size="sm"
                onClick={() => {
                  setIsPopupOpen(true);
                  setEditMode(false);
                }}
              >
                <Plus />
                افزودن کاربر
              </CButton>
            </div>
          </div>
          <div className="flex justify-between pb-5">
            <span className="text-light-neutral-text-medium">نام کاربر</span>
            <span className="text-light-neutral-text-medium w-[7rem]">
              تاریخ دسترسی
            </span>
            <span className="text-light-neutral-text-medium ml-4">مجوزها</span>
            <span className="text-light-neutral-text-medium">اقدامات</span>
          </div>

          <div className="grid grid-cols-4 gap-x-40 pb-4">
            <span className="flex-1">
              {groupData?.group?.creator || profile?.username}
            </span>
            <span className="flex-none w-[370px] text-right">
              {groupData?.created_at
                ? parseTimeToPersianSummary(groupData?.created_at)
                : ""}
            </span>
            <div className="flex-none w-[465px] flex gap-4 items-center">
              <span>ایجاد کننده (مدیر اصلی)</span>
            </div>
            <div></div>
          </div>
          {groupData?.group?.contact?.map((user) => (
            <>
              <div
                key={user.user}
                className="grid grid-cols-4 py-2 items-center gap-x-40"
              >
                <span className="flex-1">{user.user}</span>

                <span className="flex-none w-[275px] text-right">
                  {user?.created_at
                    ? parseTimeToPersianSummary(user?.created_at)
                    : ""}
                </span>

                <div className="flex-none w-[400px] flex items-center gap-1">
                  {user.permission.length === 0 && (
                    <span className="font-body-small border p-[4px] pr-[7px] pb-[4px] pl-[7px] rounded-lg">
                      بازدید کننده
                    </span>
                  )}
                  {user.permission?.includes("edit") && (
                    <span className="font-body-small border p-[4px] pr-[7px] pb-[4px] pl-[7px] rounded-lg">
                      ویرایش فیلتر
                    </span>
                  )}
                  {user.permission?.includes("remove") && (
                    <span className="font-body-small border p-[4px] pr-[7px] pb-[4px] pl-[7px] rounded-lg">
                      حذف مشترک
                    </span>
                  )}
                  {user.permission?.includes("append") && (
                    <span className="font-body-small border p-[4px] pr-[7px] pb-[4px] pl-[7px] rounded-lg">
                      افزودن مشترک
                    </span>
                  )}
                </div>
                <div className="flex justify-end">
                  <div className="flex items-center gap-1">
                    <ToolTip comp={<p>ویرایش</p>}>
                      <button
                        className="flex-none flex justify-end  p-1 bg-[#f3f3f7] rounded-md"
                        onClick={() => {
                          setEditMode(true);
                          handleEditUser(user);
                        }}
                      >
                        <PencilSimple size={16} />
                      </button>
                    </ToolTip>
                    <ToolTip comp={<p>حذف</p>}>
                      <button
                        className="flex-none flex justify-end  p-1 bg-[#f3f3f7] rounded-md"
                        onClick={() => {
                          handleRemoveUser(user?.user);
                        }}
                      >
                        <Trash size={16} />
                      </button>
                    </ToolTip>
                  </div>
                </div>
              </div>
            </>
          ))}
        </div>
      )}
      <Popup
        isOpen={isPopupOpen}
        onClose={handleClosePopUp}
        submitHandler={handleSubmit}
        title={editMode ? "ویرایش مشترک" : "افزودن مشترک"}
        agreeButton={editMode ? "ویرایش مشترک" : "افزودن مشترک"}
        cancleButton="انصراف"
      >
        <div className="relative">
          {!editMode && (
            <>
              <p className="font-overline-large pt-5 pb-2">
                برای اضافه کردن مشترک، نام کاربر آن را جست‌وجو کنید
              </p>
              <div className="flex gap-4">
                <DropDownInput
                  id={"savedFilter"}
                  name={"savedFilter"}
                  inset={true}
                  headingIcon={<MagnifyingGlass />}
                  size={"lg"}
                  validation={"none"}
                  direction={"rtl"}
                  placeholder={"نام کاربر را جست‌وجو کنید"}
                  className={"flex-1"}
                  field={{}}
                  form={{ errors: [], touched: [] }}
                  onChange={() => {}}
                  selectedItems={selectedUser}
                  setSelectedItems={(item) => {
                    setSelectedUser(item);
                  }}
                  dropdownArray={dropDownUsers.map((user) => ({
                    id: user,
                    title: user,
                  }))}
                  singleSelect={true}
                />
              </div>
            </>
          )}

          {typeof selectedUser[0] === "string" && (
            <div className="flex flex-col gap-2 bg-[#ededf3] p-3 justify-self-start w-full items-start rounded">
              <span className="flex-1 font-headline-small px-2">
                {selectedUser[0]}
              </span>

              <label className="flex font-body-medium py-1 px-1 items-center gap-2">
                <input className="mx-1" type="checkbox" checked={true} />
                بازدید کننده
              </label>

              <div className="flex-none flex flex-col gap-4 font-body-medium items-center justify-self-start">
                <div className="grid grid-cols-1 gap-4 px-1 h-full">
                  <label className="flex items-center gap-2">
                    <input
                      className="mx-1"
                      type="checkbox"
                      name={`permissions-${selectedUser[0]}`}
                      checked={selectedPermissions?.includes("edit") || false}
                      onChange={() =>
                        handlePermissionChange(selectedUser[0], "edit")
                      }
                    />
                    ویرایش فیلتر
                  </label>

                  <label className="flex items-center gap-2">
                    <input
                      className="mx-1"
                      type="checkbox"
                      name={`permissions-${selectedUser[0]}`}
                      checked={selectedPermissions.includes("remove") || false}
                      onChange={() =>
                        handlePermissionChange(selectedUser[0], "remove")
                      }
                    />
                    حذف مشترک
                  </label>

                  <label className="flex items-center gap-2">
                    <input
                      className="mx-1"
                      type="checkbox"
                      name={`permissions-${selectedUser[0]}`}
                      checked={selectedPermissions?.includes("append") || false}
                      onChange={() =>
                        handlePermissionChange(selectedUser[0], "append")
                      }
                    />
                    افزودن مشترک
                  </label>
                </div>
              </div>
            </div>
          )}
        </div>
      </Popup>
    </div>
  );
};

UserPermissionsTable.propTypes = {
  showTable: PropTypes.bool.isRequired,
  groupData: PropTypes.object.isRequired,
  setGroupData: PropTypes.func.isRequired,
};

export default UserPermissionsTable;
