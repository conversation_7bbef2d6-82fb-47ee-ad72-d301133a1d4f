// import Steps from "components/ui/Steps";
// import { useBreadcrumb } from "hooks/useBreadcrumb";
// import StepOne from "./components/step-1";
// import { useState } from "react";
// import StepTwo from "./components/step-2";
// import StepThree from "./components/step-3";
// import poster from "/opinion-mining/poster.png";

// const OpinionMiningCreate = () => {
//   const [step, setStep] = useState(1);
//   const breadcrumbList = [
//     { title: "گزارش افکارسنجی", link: "/app/opinion-mining/list" },
//     { title: "گزارش جدید" },
//   ];
//   const [opinionMiningData, setOpinionMiningData] = useState({
//     type: "",
//     title: "",
//     description: "",
//     opinion_platform: "twitter",
//     opinion_type: "customized",
//     query: {
//       platform: {
//         twitter: {
//           sentiment: ["positive", "negative", "neutral"],
//           adv: ["adv", "non-adv"],
//           hashtags: [],
//         },
//       },
//     },
//   });
//   useBreadcrumb(breadcrumbList);
//   const components = {
//     1: (
//       <StepOne
//         opinionMiningData={opinionMiningData}
//         setOpinionMiningData={setOpinionMiningData}
//         setStep={setStep}
//       />
//     ),
//     2: (
//       <StepTwo
//         opinionMiningData={opinionMiningData}
//         setOpinionMiningData={setOpinionMiningData}
//         setStep={setStep}
//       />
//     ),
//     3: (
//       <StepThree
//         opinionMiningData={opinionMiningData}
//         setOpinionMiningData={setOpinionMiningData}
//         setStep={setStep}
//       />
//     ),
//   };
//   // console.log(opinionMiningData)
//   return (
//     <div className="container mx-auto flex items-start justify-between px-5">
//       <div className="w-[800px]">
//         <div>
//           <div className="rounded-md bg-light-neutral-surface-card p-4">
//             <p className="font-body-large pb-3">فرآیند ایجاد گزارش</p>
//             <Steps
//               step={step}
//               texts={["ثبت عنوان و توضیحات", "انتخاب فیلتر", "مشاهده نتایج"]}
//               stepCounts={3}
//             />
//           </div>
//         </div>
//         <div className="mt-4">{components[step]}</div>
//       </div>
//       <img src={poster} className="w-[450px]" />
//     </div>
//   );
// };

// export default OpinionMiningCreate;

import Steps from "components/ui/Steps";
import { useBreadcrumb } from "hooks/useBreadcrumb";
import StepOne from "./components/step-1";
import { useState } from "react";
import StepTwo from "./components/step-2";
import StepThree from "./components/step-3";
import poster from "/opinion-mining/poster.png";
import useOpinionMiningStore from "store/opinionMining";

const OpinionMiningCreate = () => {
  const [step, setStep] = useState(1);
  const { opinionMiningData, setOpinionMiningData, updateOpinionMiningData } =
    useOpinionMiningStore();

  const breadcrumbList = [
    { title: "گزارش افکارسنجی", link: "/app/opinion-mining/list" },
    { title: "گزارش جدید" },
  ];

  useBreadcrumb(breadcrumbList);

  const components = {
    1: (
      <StepOne
        opinionMiningData={opinionMiningData}
        setOpinionMiningData={setOpinionMiningData}
        updateOpinionMiningData={updateOpinionMiningData}
        setStep={setStep}
      />
    ),
    2: (
      <StepTwo
        opinionMiningData={opinionMiningData}
        setOpinionMiningData={setOpinionMiningData}
        updateOpinionMiningData={updateOpinionMiningData}
        setStep={setStep}
      />
    ),
    3: (
      <StepThree
        opinionMiningData={opinionMiningData}
        setOpinionMiningData={setOpinionMiningData}
        updateOpinionMiningData={updateOpinionMiningData}
        setStep={setStep}
      />
    ),
  };

  return (
    <div className="container mx-auto flex items-start justify-between px-5">
      <div className="w-[800px]">
        <div>
          <div className="rounded-md bg-light-neutral-surface-card p-4">
            <p className="font-body-large pb-3">فرآیند ایجاد گزارش</p>
            <Steps
              step={step}
              texts={["ثبت عنوان و توضیحات", "انتخاب فیلتر", "مشاهده نتایج"]}
              stepCounts={3}
            />
          </div>
        </div>
        <div className="mt-4">{components[step]}</div>
      </div>
      <img src={poster} className="w-[450px]" />
    </div>
  );
};

export default OpinionMiningCreate;
