import { CaretLeft } from "@phosphor-icons/react";
import { Card } from "components/ui/Card";
import ChangeListState from "./ChangeListState";
import { memo, useEffect, useState } from "react";
import PopUp from "components/ui/PopUp"; // Assuming PopUp component is available
import GpMembersWrapper from "./GpMembersWrapper";
import Empty from "pages/admin/groups-list/components/Empty";
import GpMembersTable from "./GpMembersTable";
import formatDate from "pages/admin/user-list/utils";
import Paginate from "components/ui/Paginate";
import userService from "service/api/userService";
import { CButton } from "components/ui/CButton";

const TICKET_STATES = ["Unsuccessful", "successful"];
const TICKET_STATE_TITLES = {
  Unsuccessful: "ناموفق",
  successful: "موفق",
};

const changeItems = [
  { id: 1, title: "افزودن کاربر", date: "۱۴۰۲/۱۱/۰۱ - ۱۲:۳۴" },
  { id: 2, title: "ویرایش گروه", date: "۱۴۰۲/۱۱/۰۱ - ۱۲:۳۴" },
  { id: 3, title: "حذف کاربر", date: "۱۴۰۲/۱۱/۰۱ - ۱۲:۳۴" },
  { id: 4, title: "ویرایش وضعیت", date: "۱۴۰۲/۱۱/۰۱ - ۱۲:۳۴" },
  { id: 5, title: "ایجاد گروه", date: "۱۴۰۲/۱۱/۰۱ - ۱۲:۳۴" },
];

const ChangeListItem = ({
  title,
  date,
  onAddUser,
  onEditGroup,
  onDeleteUser,
}) => {
  const randomState =
    TICKET_STATES[Math.floor(Math.random() * TICKET_STATES.length)];
  const stateTitle = TICKET_STATE_TITLES[randomState];

  const isInteractive = ["افزودن کاربر", "ویرایش گروه", "حذف کاربر"].includes(
    title
  );

  const handleClick = () => {
    if (isInteractive) {
      switch (title) {
        case "افزودن کاربر":
          onAddUser();
          break;
        case "ویرایش گروه":
          onEditGroup();
          break;
        case "حذف کاربر":
          onDeleteUser();
          break;
        default:
          break;
      }
    }
  };

  return (
    <>
      <div
        className={`grid grid-cols-3 p-2 items-center ${
          isInteractive ? "hover:bg-[#E9E6F7] cursor-pointer" : ""
        }`}
        onClick={handleClick}
      >
        <div className="flex flex-col gap-2 font-medium-body">
          <div>{title}</div>
          <span className="text-[#00000080]">{date}</span>
        </div>
        <div className="font-body-medium flex justify-center">
          <ChangeListState title={stateTitle} state={randomState} />
        </div>
        {isInteractive && (
          <button
            dir="ltr"
            className="rounded-md p-2 bg-[#E1E8EF80] flex justify-end justify-self-end"
          >
            <CaretLeft size={16} />
          </button>
        )}
      </div>
      <hr className="border-t border-gray-200" />
    </>
  );
};

const ChangeList = () => {
  const [isAddUserOpen, setIsAddUserOpen] = useState(false);
  const [table, setTable] = useState([]);
  const [isEditGroupOpen, setIsEditGroupOpen] = useState(false);
  const [page, setPage] = useState(1);
  const [isDeleteUserOpen, setIsDeleteUserOpen] = useState(false);
  const oneYearInMillis = 12 * 30 * 24 * 60 * 60 * 1000;
  const ticketStates = ["successful"];
  const [filter, setFilter] = useState({
    q: "",
    register_at: {
      date_from: formatDate(new Date(new Date().getTime() - oneYearInMillis)),
      date_to: formatDate(new Date(new Date().getTime() + 24 * 60 * 60 * 1000)),
    },
    sort_rules: [
      {
        field: "login_at",
        direction: "desc",
      },
      {
        field: "register_at",
        direction: "desc",
      },
    ],
  });

  const randomState =
    ticketStates[Math.floor(Math.random() * ticketStates.length)];

  const ticketStateTitles = {
    successful: "فعال",
    Unsuccessful: "غیرفعال",
  };

  const getUsers = async () => {
    try {
      let newFiler = {};
      if (filter?.q.trim() !== "") newFiler.q = filter.q;
      newFiler.register_at = filter?.register_at;
      newFiler.sort_rules = filter?.sort_rules;

      let newQuery = {};
      newQuery.page = page;
      newQuery.page_size = 6;
      // newQuery.status =
      //   activeTab == "همه"
      //     ? "all"
      //     : activeTab == "کاربران فعال"
      //     ? "active_users"
      //     : activeTab == "کاربران غیرفعال"
      //     ? "inactive_users"
      //     : "managers";
      newQuery.role = filter?.role || "all";
      const {
        data: { data },
      } = await userService.getUserList(newFiler, newQuery);
      setTable(data?.users);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getUsers();
  }, [page, filter]);

  const handleAddUserSubmit = () => {
    // Implement add user logic here
    console.log("Adding user...");
    setIsAddUserOpen(false);
  };

  const handleEditGroupSubmit = () => {
    // Implement edit group logic here
    console.log("Editing group...");
    setIsEditGroupOpen(false);
  };

  const handleDeleteUserSubmit = () => {
    // Implement delete user logic here
    console.log("Deleting user...");
    setIsDeleteUserOpen(false);
  };

  return (
    <Card className="flex flex-col gap-7">
      <h3 className="font-subtitle-large text-right">لیست تغییرات</h3>
      <div className="flex flex-col">
        {changeItems.map((item) => (
          <ChangeListItem
            key={item.id}
            title={item.title}
            date={item.date}
            onAddUser={() => setIsAddUserOpen(true)}
            onEditGroup={() => setIsEditGroupOpen(true)}
            onDeleteUser={() => setIsDeleteUserOpen(true)}
          />
        ))}
      </div>

      {/* Add User Popup */}
      <PopUp
        isOpen={isAddUserOpen}
        onClose={() => setIsAddUserOpen(false)}
        titleRow={"جزئیات تغییرات گروه"}
        hasCloseIcon={false}
        submitHandler={handleAddUserSubmit}
        width="1080px"
        hasButton={false}
      >
        <div className="flex flex-col gap-4 p-4 w-full">
          <div className="bg-[#eff3f6] flex flex-col gap-3 py-4 px-3 rounded-md w-full">
            <div className="w-full grid grid-cols-5 font-overline-medium text-light-neutral-text-medium justify-items-between">
              <span>نوع اقدام</span>
              <span>مجری اقدام</span>
              <span>نقش مجری</span>
              <span>تاریخ اقدام</span>
              <span className="flex justify-center">وضعیت</span>
            </div>
            <div className="w-full grid grid-cols-5 font-body-medium justify-items-between">
              <span>افزودن کاربر</span>
              <span>نام و نام خانوادگی</span>
              <span>مدیر سازمان</span>
              <span>۱۲:۳۴ - ۱۴۰۲/۱۱/۰۱</span>
              <div className="font-body-medium flex justify-center">
                <ChangeListState
                  title={ticketStateTitles[randomState] || ""}
                  state={randomState}
                />
              </div>
            </div>
          </div>
          <h1 className="font-subtitle-large">لیست افراد اضافه شده به گروه</h1>
          <GpMembersWrapper
            isFromChangeList={true}
            filter={filter}
            setFilter={setFilter}
          >
            {table.length > 0 ? (
              table?.map((item) => (
                <>
                  <GpMembersTable
                    isFromChangeList={true}
                    key={item.id}
                    data={item}
                  />
                </>
              ))
            ) : (
              <Empty />
            )}
          </GpMembersWrapper>
          <div className="w-full flex justify-between items-end">
            <Paginate
              page={page}
              setPage={setPage}
              dataCount={100}
              per_page={10}
            />
            <CButton width={200} onClick={() => setIsAddUserOpen(false)}>
              متوجه شدم
            </CButton>
          </div>
        </div>
      </PopUp>

      {/* Edit Group Popup */}
      <PopUp
        isOpen={isEditGroupOpen}
        onClose={() => setIsEditGroupOpen(false)}
        titleRow={"جزئیات تغییرات گروه"}
        hasCloseIcon={false}
        width="980px"
        hasButton={false}
      >
        <div className="flex flex-col gap-4 p-4 w-full">
          <div className="bg-[#eff3f6] flex flex-col gap-3 py-4 px-3 rounded-md w-full">
            <div className="w-full grid grid-cols-5 font-overline-medium text-light-neutral-text-medium justify-items-between">
              <span>نوع اقدام</span>
              <span>مجری اقدام</span>
              <span>نقش مجری</span>
              <span>تاریخ اقدام</span>
              <span className="flex justify-center">وضعیت</span>
            </div>
            <div className="w-full grid grid-cols-5 font-body-medium justify-items-between">
              <span>افزودن کاربر</span>
              <span>نام و نام خانوادگی</span>
              <span>مدیر سازمان</span>
              <span>۱۲:۳۴ - ۱۴۰۲/۱۱/۰۱</span>
              <div className="font-body-medium flex justify-center">
                <ChangeListState
                  title={ticketStateTitles[randomState] || ""}
                  state={randomState}
                />
              </div>
            </div>
          </div>
          <h1 className="font-subtitle-large">لیست افراد حذف شده از گروه</h1>
          <div className="flex flex-col gap-5 justify-center pt-2 w-full">
            <div className="flex gap-8 justify-center w-full">
              <div className="flex flex-col gap-2">
                <span className="font-overline-large">عنوان گروه</span>
                <span className="font-body-large w-52">
                  عنوان پیش از ویرایش
                </span>
              </div>
              <div className="flex items-center">
                <div className="w-32 h-1 bg-black"></div>
                <div
                  className="w-0 h-5 border-t-8 border-b-8 border-r-8 border-r-black border-transparent"
                  style={{
                    borderTopWidth: 11,
                    borderBottomWidth: 11,
                    borderRightWidth: 11,
                  }}
                ></div>
              </div>
              <div className="flex flex-col gap-2">
                <span className="font-overline-large">عنوان گروه</span>
                <span className="font-body-large w-52">عنوان پس از ویرایش</span>
              </div>
            </div>
            <div className="flex gap-8 justify-center">
              <div className="flex flex-col gap-2">
                <span className="font-overline-large">توضیحات گروه</span>
                <span className="font-body-large w-52">
                  یه متن بلند که توضیحات گروه توش نوشته شده و در مورد اقدامات
                  گروه یک شرحی تعیین شده
                </span>
              </div>
              <div className="flex items-center">
                <div className="w-32 h-1 bg-black"></div>
                <div
                  className="w-0 h-5 border-t-8 border-b-8 border-r-8 border-r-black border-transparent"
                  style={{
                    borderTopWidth: 11,
                    borderBottomWidth: 11,
                    borderRightWidth: 11,
                  }}
                ></div>
              </div>
              <div className="flex flex-col gap-2">
                <span className="font-overline-large">توضیحات گروه</span>
                <span className="font-body-large w-52">
                  یه متن بلند که توضیحات گروه توش نوشته شده و در مورد اقدامات
                  گروه یک شرحی تعیین شده
                </span>
              </div>
            </div>
          </div>
          <div className="flex w-full justify-end pt-3">
            <CButton width={200} onClick={() => setIsAddUserOpen(false)}>
              متوجه شدم
            </CButton>
          </div>
        </div>
      </PopUp>

      {/* Delete User Popup */}
      <PopUp
        isOpen={isDeleteUserOpen}
        onClose={() => setIsDeleteUserOpen(false)}
        titleRow={"جزئیات تغییرات گروه"}
        hasCloseIcon={false}
        submitHandler={handleAddUserSubmit}
        width="1080px"
        hasButton={false}
      >
        <div className="flex flex-col gap-4 p-4 w-full">
          <div className="bg-[#eff3f6] flex flex-col gap-3 py-4 px-3 rounded-md w-full">
            <div className="w-full grid grid-cols-5 font-overline-medium text-light-neutral-text-medium justify-items-between">
              <span>نوع اقدام</span>
              <span>مجری اقدام</span>
              <span>نقش مجری</span>
              <span>تاریخ اقدام</span>
              <span className="flex justify-center">وضعیت</span>
            </div>
            <div className="w-full grid grid-cols-5 font-body-medium justify-items-between">
              <span>افزودن کاربر</span>
              <span>نام و نام خانوادگی</span>
              <span>مدیر سازمان</span>
              <span>۱۲:۳۴ - ۱۴۰۲/۱۱/۰۱</span>
              <div className="font-body-medium flex justify-center">
                <ChangeListState
                  title={ticketStateTitles[randomState] || ""}
                  state={randomState}
                />
              </div>
            </div>
          </div>
          <h1 className="font-subtitle-large">لیست افراد حذف شده از گروه</h1>
          <GpMembersWrapper
            isFromChangeList={true}
            filter={filter}
            setFilter={setFilter}
          >
            {table.length > 0 ? (
              table?.map((item) => (
                <>
                  <GpMembersTable
                    isFromChangeList={true}
                    key={item.id}
                    data={item}
                  />
                </>
              ))
            ) : (
              <Empty />
            )}
          </GpMembersWrapper>
          <div className="w-full flex justify-between items-end">
            <Paginate
              page={page}
              setPage={setPage}
              dataCount={100}
              per_page={10}
            />
            <CButton width={200} onClick={() => setIsAddUserOpen(false)}>
              متوجه شدم
            </CButton>
          </div>
        </div>
      </PopUp>
    </Card>
  );
};

export default memo(ChangeList);
