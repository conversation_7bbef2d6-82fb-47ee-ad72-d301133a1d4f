import { Info } from "@phosphor-icons/react";
import ToolTip from "components/ui/ToolTip";

const UserInviteList = ({ children, setFilter, filter, col5 = false }) => {
  return (
    <div className="py-4 flex flex-col gap-4">
      <ul
        className={`font-overline-medium text-light-neutral-text-medium grid ${
          col5 ? "grid-cols-5" : "grid-cols-4"
        } select-none`}
      >
        <li className="flex items-center gap-2">
          <p>نام کاربری</p>
        </li>
        <li className="">نام و نام خانوادگی</li>
        <li className="">شماره موبایل</li>

        <li className="flex items-center gap-1">
          <p>وضعیت</p>
          <ToolTip
            comp={
              <div className="">
                <h3 className="font-body-bold-small">راهنمای وضعیت:</h3>
                <ul className="list-disc px-4 pt-5">
                  <li>
                    <p className="font-body-small">
                      <span className="font-body-bold-small pl-1">فعال:</span>
                      کاربرانی که می‌توانند از سیناپس یا ادمین پنل استفاده کنند.
                    </p>
                  </li>
                  <li>
                    <p className="font-body-small">
                      <span className="font-body-bold-small pl-1">
                        غیرفعال:
                      </span>
                      کاربرانی که پلن کاربری ندارند یا توسط مدیران غیرفعال
                      شده‌اند. این کاربران قابلیت فعال شدن دوباره را دارند.
                    </p>
                  </li>
                  <li>
                    <p className="font-body-small">
                      <span className="font-body-bold-small pl-1">
                        حذف شده:
                      </span>
                      این کاربران توسط مدیران از سیستم حذف شدند و امکان
                      بازگردانی آن‌ها مقدور نیست.
                    </p>
                  </li>
                  <li>
                    <p className="font-body-small">
                      <span className="font-body-bold-small pl-1">
                        در انتظار:
                      </span>
                      کاربرانی که توسط مدیر به محصول اضافه شده‌اند اما هنوز
                      کاربر وارد سامانه نشده است.
                    </p>
                  </li>
                </ul>
              </div>
            }
          >
            <div className="flex flex-col">
              <Info size={17} />
            </div>
          </ToolTip>
        </li>
      </ul>
      {children}
    </div>
  );
};

export default UserInviteList;
