import { create } from "zustand";
import PLATFORMS from "../constants/platforms.js";
import { loadFromLocalStorage, saveToLocalStorage } from "../utils/storage.js";

const oneDayInMillis = 24 * 60 * 60 * 1000;

const initialState = {
  filters: {
    platform: PLATFORMS.TWITTER,
    date: {
      from: new Date(new Date().getTime() - oneDayInMillis),
      to: new Date(),
      index: 1,
    },
    sentiment: [],
    gender: ["all"],
    subjectCategory: [],
    language: [],
    adv: [], // "adv" "non-adv"
    offensive: [],
    sources: [],
    keywords: [],
    hashtags: [],
    page: 1,
    sort: "date",
    sort_type: "نزولی",
  },
  query: "",
  cluster: {
    name: "",
    keywords: [],
    counts: [],
    total: 0,
    baseUrl: "",
  },
  showSearchBox: true,
  showFilterList: true,
  isFilterListOpen: false,
  doSearch: false,
  searchBoxConfig: {
    disableScroll: false,
    disablePlatform: false,
    disableButton: false,
    readOnly: false,
    updateQueryOnType: false,
  },
};

const isEqual = (obj1, obj2) => {
  return JSON.stringify(obj1) === JSON.stringify(obj2);
};

// Create Zustand store
const useSearchStore = create((set, get) => ({
  ...initialState,
  setFilters: (newFilters) =>
    set((state) => ({ filters: { ...state.filters, ...newFilters } })),
  setQuery: (newQuery) => set({ query: newQuery }),
  setDoSearch: (value = false) => set({ doSearch: value }),
  setSearchBoxConfig: (newConfig) =>
    set((state) => ({
      searchBoxConfig: { ...state.searchBoxConfig, ...newConfig },
    })),
  setCluster: (newCluster) =>
    set((state) => ({ cluster: { ...state.cluster, ...newCluster } })),
  setIsFilterListOpen: (isOpen) => set({ isFilterListOpen: isOpen }),
  setShowSearchBox: (show) => set({ showSearchBox: show }),
  setShowFilterList: (show) => set({ showFilterList: show }),
  toggleOpenClose: () =>
    set((state) => ({ isFilterListOpen: !state.isFilterListOpen })),
  saveState: () => {
    saveToLocalStorage(get(), "search-store");
  },
  updateDateFilters: () =>
    set((state) => ({
      filters: {
        ...state.filters,
        date: {
          from: new Date(new Date().getTime() - oneDayInMillis),
          to: new Date(),
        },
      },
    })),
  loadState: () => {
    const loadedState = loadFromLocalStorage("search-store");
    if (loadedState) {
      set(loadedState);
      get().clearSearchBoxConfig();
    }
  },
  clearQuery: () => {
    set({ query: "" });
  },
  clearCluster: () => {
    set({
      cluster: {
        name: "",
        keywords: [],
        baseUrl: "",
      },
    });
  },
  clearFilter: () => {
    set({ filters: initialState.filters });
  },
  clearSearchBoxConfig: () => {
    set({ searchBoxConfig: initialState.searchBoxConfig });
  },
  getActiveFilters: () => {
    const currentFilters = get().filters;
    const activeFilters = [];

    Object.keys(currentFilters).forEach((key) => {
      if (!isEqual(currentFilters[key], initialState.filters[key])) {
        activeFilters.push(key);
      }
    });

    return activeFilters;
  },
  checkIsFilterActive: (key) => {
    const currentFilters = get().filters;
    return !isEqual(currentFilters[key], initialState.filters[key]);
  },
}));

export default useSearchStore;
