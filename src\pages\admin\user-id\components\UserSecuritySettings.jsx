import { <PERSON><PERSON><PERSON>, SignIn, Tidal<PERSON><PERSON> } from "@phosphor-icons/react";
import CardListItem from "./CardListItem";
import Divider from "components/ui/Divider";
import { useLocation } from "react-router-dom";

const UserSecuritySettings = () => {
  const { pathname } = useLocation();
  return (
    <div className="p-6">
      <p className="font-subtitle-large mb-6">تنظیمات امنیتی کاربر</p>
      <div className="my-1">
        <CardListItem
          path={`/admin/user/list/${
            pathname?.split("/").slice(-1)[0]
          }/planLoginHistory`}
          icon={SignIn}
          title="تاریخچه لاگین"
        />
      </div>
      <Divider />
      <div className="my-1">
        <CardListItem icon={LockKey} title="تنظیمات رمز عبور" />
      </div>
      <Divider />
      <div className="my-1">
        <CardListItem icon={TidalLogo} title="تنظیمات دسترسی" />
      </div>
    </div>
  );
};

export default UserSecuritySettings;
