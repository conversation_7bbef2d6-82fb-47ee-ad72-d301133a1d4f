import { Plus } from "@phosphor-icons/react";
import { CButton } from "components/ui/CButton";
import { Link } from "react-router-dom";

const Wrapper = ({ children }) => {
  return (
    <div className="p-4 flex flex-col gap-4 rounded-lg bg-light-neutral-surface-card shadow-[0px_2px_20px_0px_#0000000D]">
      <div className="flex items-center justify-between">
        <div className="font-subtitle-medium text-light-neutral-text-high mb-4">
          لیست تیکت‌ها
        </div>
        <Link to={"/app/ticket/create"} className="">
          <CButton
            rightIcon={<Plus />}
            width={200}
            className={"[direction:ltr]"}
            mode="outline"
          >
            تیکت جدید
          </CButton>
        </Link>
      </div>
      <div>
        <ul className="font-overline-medium text-light-neutral-text-medium grid grid-cols-5">
          <li>عنوان تیکت</li>
          <li>تاریخ ایجاد</li>
          <li>تاریخ به‌روزرسانی</li>
          <li>وضعیت تیکت</li>
          <li>واحد مربوطه</li>
        </ul>
      </div>
      {children}
    </div>
  );
};

export default Wrapper;
