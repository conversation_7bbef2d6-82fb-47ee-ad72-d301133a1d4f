import { Link } from "react-router-dom";

const Empty = () => {
  return (
    <div className="w-full h-[640px] flex flex-col justify-center items-center">
      <div className="size-12 bg-[url(/empty_folder.png)] bg-no-repeat bg-contain bg-center mb-4"></div>
      <p className="font-body-large mb-2">کاربری یافت نشد.</p>
      {/*<p className="font-body-medium flex gap-1">*/}
      {/*  <span>لطفا بر روی دکمه</span>*/}
      {/*  <Link*/}
      {/*    to="/admin/user/create"*/}
      {/*    className="underline underline-offset-4 text-light-primary-text-rest cursor-pointer"*/}
      {/*  >*/}
      {/*    کاربر جدید*/}
      {/*  </Link>*/}
      {/*  <span>کلیک کنید</span>*/}
      {/*</p>*/}
    </div>
  );
};

export default Empty;
