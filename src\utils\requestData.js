export function buildRequestData(filterObject, report_type, rowsNum = 10) {
  try {
    const fromDate = new Date(filterObject.date?.from);
    const toDate = new Date(filterObject.date?.to);

    const result = {};
    filterObject.platform &&
      (result["platform"] = filterObject.platform || "news");
    filterObject.q && (result["q"] = filterObject.q);
    filterObject.sort_type &&
      (result["order"] = filterObject.sort_type === "نزولی" ? "desc" : "asc");
    filterObject.date?.from && (result["start_date"] = fromDate.toISOString());
    filterObject.date?.to && (result["end_date"] = toDate.toISOString());
    // filterObject.sentiment &&
    //   (result["sentiment"] = filterObject.sentiment.includes("all")
    //     ? []
    //     : filterObject.sentiment);
    // filterObject.language &&
    //   (result["languages"] = filterObject.language.includes("all")
    //     ? []
    //     : filterObject.language);
    filterObject.gender &&
      (result["gender"] = filterObject.gender.includes("all")
        ? []
        : filterObject.gender);
    filterObject.keywords &&
      (result["keywords"] = filterObject.keywords.includes("all")
        ? []
        : filterObject.keywords);
    filterObject.hashtags &&
      (result["hashtags"] = filterObject.hashtags.includes("all")
        ? []
        : filterObject.hashtags);
    // filterObject.subjectCategory &&
    //   (result["categories"] = filterObject.subjectCategory.includes("all")
    //     ? []
    //     : filterObject.subjectCategory);

    filterObject.adv &&
      (result["adv"] = filterObject.adv.includes("all")
        ? []
        : filterObject.adv);
    filterObject.offensive &&
      (result["offensive"] = filterObject.offensive.includes("all")
        ? []
        : filterObject.offensive);

    result["sentiment"] = filterObject.sentiment;
    result["languages"] = filterObject.language;
    result["categories"] = filterObject.subjectCategory;
    result["sources"] = filterObject.sources;
    result["rows"] = rowsNum;
    result["page"] = filterObject.page || 1;
    result["sort"] = filterObject.sort;

    if (report_type) {
      result["report_type"] = report_type;
    }

    return result;
  } catch (e) {
    console.log(e);
    throw e;
  }
}

export function transformResponseData(newsArray) {
  const keyMapping = {
    description: "description",
    alexa_rank: "rank",
    extracted_email: "extracted_email",
    summary: "description",
    avatar: "avatar",
    title: "title",
    user_title: "title",
    text: "content",
    post_url: "url",
    retweet_count: "retweet_count",
    like_count: "like_count",
    id: "id",
    date: "time",
    username: "username",
  };

  function transformProperties(obj) {
    if (typeof obj === "object" && obj !== null) {
      const newObj = Array.isArray(obj) ? [] : {};

      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          const value = transformProperties(obj[key]);
          const newKey = keyMapping[key] || key;
          newObj[newKey] = value;
        }
      }
      return newObj;
    }
    return obj;
  }

  const transformedNews = newsArray.map((item) => {
    const transformedItem = transformProperties(item);

    return {
      id: transformedItem.id,
      avatar: transformedItem.avatar,
      title: transformedItem.title || "",
      username: transformedItem.username || transformedItem.user_name || "",
      description: transformedItem.description || "",
      content: transformedItem.content || "",
      time: transformedItem.time || "",
      agency: transformedItem.agency?.name || "",
      category: transformedItem.category || {},
      tags: transformedItem.tags || [],
      top_category: transformedItem.top_category || "Unknown",
      sentiment: transformedItem.sentiment || "neutral",
      view_count: transformedItem.view_count || 0,
      like_count: transformedItem.like_count || 0,
      retweet_count: transformedItem.retweet_count || 0,
      channel_title: transformedItem.channel_title || "",
      post_url: transformedItem.post_url || "",
      channel_username: transformedItem.channel_username || "",
      url: transformedItem.url || "",
    };
  });

  return transformedNews;
}
