import { useState } from "react";
import BaleLogoBlack from "components/ui/BaleLogoBlack";
import MessengerBadge from "./MessengerBadge";
import { SpinnerGap, TelegramLogo } from "@phosphor-icons/react";
import { Field, Form, Formik } from "formik";
import { alertStepTwoSchema } from "utils/validationSchemas";
import { ListMagnifyingGlass, At } from "@phosphor-icons/react";
import NotFoundChannel from "./NotFoundChannel";
import FoundChannel from "./FoundChannel";
import alert from "service/api/alert";
import { CButton } from "components/ui/CButton.jsx";

const StepTwo = ({ setStatus, setAlertData, alertData }) => {
  const [isValidChannelId, setIsValidChannelId] = useState("");
  const [loading, setLoading] = useState(false);
  const handleSubmit = async (data) => {
    setAlertData((l) => {
      return { ...l, ...data };
    });
    setLoading(true);
    try {
      const response = await alert.verify(data);
      setIsValidChannelId(response.data.status);
    } catch (error) {
      setIsValidChannelId(error.response.data.status);
      console.log(error);
    }
    setLoading(false);
  };

  return (
    <div className="p-6 w-full">
      <Formik
        initialValues={{
          messenger: alertData.messenger,
          channel_id: alertData.channel_id,
        }}
        validationSchema={alertStepTwoSchema}
        onSubmit={handleSubmit}
      >
        {({ values, handleChange, errors, handleBlur }) => (
          <Form>
            <div
              role="group"
              aria-labelledby="my-radio-group"
              className="grid grid-cols-2 gap-4"
            >
              <label>
                <Field
                  type="radio"
                  name="messenger"
                  value="telegram"
                  style={{ display: "none" }}
                  onChange={handleChange("messenger")}
                />
                <MessengerBadge
                  height={100}
                  dir="col"
                  name="تلگرام"
                  selected={values.messenger == "telegram"}
                >
                  <TelegramLogo className="size-8" color="#00000080" />
                </MessengerBadge>
              </label>
              <label>
                <Field
                  type="radio"
                  name="messenger"
                  value="bale"
                  style={{ display: "none" }}
                  onChange={handleChange("messenger")}
                />
                <MessengerBadge
                  name="بله"
                  selected={values.messenger == "bale"}
                  height={100}
                  dir="col"
                >
                  <BaleLogoBlack />
                </MessengerBadge>
              </label>
              <span className="text-light-error-text-rest font-overline-medium">
                {errors.messenger}
              </span>
            </div>

            <div>
              <h3 className="mb-2 font-overline-large">شناسه کانال یا شخص</h3>
              <div className="flex w-full gap-6 mb-12">
                <div className="w-full">
                  <div className="w-full flex items-center text-light-neutral-text-low gap-2 border border-light-neutral-border-medium-rest rounded-md px-4 py-[10px] [direction:ltr]">
                    {/*<At />*/}
                    <Field
                      id={"channel_id"}
                      name={"channel_id"}
                      type="text"
                      size={"md"}
                      placeholder="به عنوان مثال: 87654321"
                      onChange={handleChange("channel_id")}
                      onBlur={handleBlur("channel_id")}
                      className="w-full font-overline-large [direction:ltr] outline-none text-black"
                    />
                  </div>
                  <span className="text-light-error-text-rest font-overline-medium">
                    {errors.channel_id}
                  </span>
                </div>

                <button
                  type="submit"
                  className="flex items-center justify-center font-button-medium text-light-primary-text-rest w-[150px] h-10 border border-light-primary-border-rest rounded-lg"
                >
                  <div className="flex items-end gap-2">
                    {loading ? (
                      <SpinnerGap size={26} className="animate-spin" />
                    ) : (
                      <>
                        <ListMagnifyingGlass />
                        <span>بررسی</span>
                      </>
                    )}
                  </div>
                </button>
              </div>
              {isValidChannelId == "OK" ? (
                <FoundChannel />
              ) : isValidChannelId == "NOK" ? (
                <NotFoundChannel />
              ) : (
                ""
              )}
            </div>
          </Form>
        )}
      </Formik>

      <div className="flex flex-row-reverse mt-12">
        <CButton
          type={"submit"}
          className={`!w-[200px]`}
          readOnly={isValidChannelId !== "OK"}
          onClick={() => isValidChannelId === "OK" && setStatus(3)}
        >
          ادامه
        </CButton>

        <button
          className="w-[100px] h-10 flex justify-center items-center text-light-primary-text-rest font-button-medium"
          onClick={() => setStatus(1)}
        >
          مرحله قبل
        </button>
      </div>
    </div>
  );
};

export default StepTwo;
