import Divider from "components/ui/Divider";
import { useState } from "react";
import UserInfoSettings from "./UserInfoSettings";
import AccessSettings from "./AccessSettings";
import UserActions from "./UserActions";
import { ClockCounterClockwise } from "@phosphor-icons/react";

const UserProfileSettings = () => {
  const [tabs, setTabs] = useState([
    "اطلاعات کاربری",
    "تنظیمات دسترسی",
    "اقدامات",
  ]);
  const [activeTab, setActiveTab] = useState("اطلاعات کاربری");

  return (
    <>
      {/* Tabs */}
      <div className="flex justify-center items-center">
        <div className="bg-light-neutral-surface-card w-[605px] p-6 rounded-t-[8px]">
          <div className="flex items-center justify-between">
            <div className="flex items-center font-body-large gap-4">
              {tabs?.map((title) => (
                <p
                  key={title}
                  className={`cursor-pointer ${
                    title === activeTab
                      ? "border-b-2 border-light-primary-text-rest text-black"
                      : "text-light-neutral-text-medium"
                  }`}
                  onClick={() => setActiveTab(title)}
                >
                  {title}
                </p>
              ))}
            </div>
            <ClockCounterClockwise
              size={22}
              className="text-light-primary-text-rest cursor-pointer"
            />
          </div>
          <Divider className={"mt-4"} />
        </div>
      </div>

      {activeTab === "اطلاعات کاربری" ? (
        <UserInfoSettings />
      ) : activeTab === "تنظیمات دسترسی" ? (
        <AccessSettings />
      ) : (
        <UserActions />
      )}
    </>
  );
};

export default UserProfileSettings;
