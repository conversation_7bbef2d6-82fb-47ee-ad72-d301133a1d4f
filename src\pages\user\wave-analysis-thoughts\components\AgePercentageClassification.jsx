import { memo } from "react";
import Doughnut from "components/Charts/Doughnut";
import { toPersianNumber } from "utils/helper";

const transformGenderData = (gendersData) => {
  const { gender_aggs } = gendersData;
  const total = gender_aggs?.reduce((sum, { count }) => sum + count, 0);
  if (!total || total === 0) {
    return [];
  }
  const genderMap = [
    { key: "male", name: "مرد", color: "#1C60B0" },
    { key: "female", name: "زن", color: "#E052B8" },
    { key: "unknown", name: "نامشخص", color: "#e7edf2" },
  ];
  const data = genderMap.map((gender) => ({
    name: gender.name,
    y: 0,
  }));
  gender_aggs.forEach(({ key, count }) => {
    const genderIndex = genderMap.findIndex((g) => g.key === key);
    if (genderIndex !== -1) {
      data[genderIndex].y = (count / total) * 100;
    }
  });
  return data;
};

const AgePercentageClassification = ({ gendersData }) => {
  const data = transformGenderData(gendersData);
  if (!data.length) {
    return (
      <div className="bg-light-neutral-surface-card rounded-[8px] p-6 h-[490px]">
        <p className="font-subtitle-large pb-4">دسته‌بندی جنسیت</p>
        <div className="h-full flex items-center justify-between mx-auto">
          <p className="py-1.5 text-center font-body-bold-large text-light-neutral-text-medium flex items-center justify-between mx-auto">
            داده ای یافت نشد
          </p>
        </div>
      </div>
    );
  }
  return (
    <div className="bg-light-neutral-surface-card rounded-[8px] p-6">
      <p className="font-subtitle-large pb-4">دسته‌بندی جنسیت</p>
      <Doughnut
        size={"80%"}
        height={400}
        colLayout={true}
        name="sentiment"
        data={data}
        legendFormatter={function () {
          return `<div style="font-family: iranyekan, sans-serif; display: block; width: 100%; padding: 4px; align-items: center;">
                    <div style="display: flex; gap: 8px; align-items: center;">
                    <span style="font-size: 16px; color: #333;">
                    ${toPersianNumber(this?.y.toFixed(2))}%
                    </span>
                        <span style="font-size: 14px; color: #555;">
                          ${this.name}
                        </span>
                        <div style="background-color: ${
                          this.color
                        }; border-radius: 50%; width: 12px; height: 12px;"></div>
                    </div>
                </div>`;
        }}
        tooltipFormatter={function () {
          return `<div style="display: flex; flex-direction: column; gap: 8px; text-align: center; font-family: iranyekan;">
                    <p>${this.key}</p>
                    <p>${toPersianNumber(this?.y.toFixed(2))}%</p>
                  </div>`;
        }}
        colors={["#1C60B0", "#E052B8", "#e7edf2"]}
      />
    </div>
  );
};

export default memo(AgePercentageClassification);
