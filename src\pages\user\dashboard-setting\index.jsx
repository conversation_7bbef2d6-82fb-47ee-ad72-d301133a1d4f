import { useEffect, useState, useCallback, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { useLayoutContext } from "context/layout-context";
import Loading from "components/ui/Loading";
import {
  CaretRight,
  Compass,
  HeadCircuit,
  ListMagnifyingGlass,
  Newspaper,
  Scales,
  TrendUp,
  UserSwitch,
} from "@phosphor-icons/react";
import { Card } from "components/ui/Card.jsx";
import { CButton } from "components/ui/CButton.jsx";
import { ToastContainer } from "react-toastify";
import { notification } from "utils/helper.js";
import Compare from "service/api/compare.js";
import Report360 from "service/api/report360.js";
import waveAnalytics from "service/api/waveAnalytics.js";
import opinion from "service/api/opinion.js";
import filter from "service/api/filter.js";
import DropDown from "./components/DropDown";
import { DateInput } from "../../../components/ui/DateInput";

const DashboardSetting = () => {
  const { setBreadcrumb } = useLayoutContext();
  const navigate = useNavigate();

  const [formState, setFormState] = useState({
    selectedType: "",
    selectedItem: null,
    isSubmitting: false,
  });

  const [uiState, setUiState] = useState({
    loading: true,
    dropdownLoading: false,
    dropdownData: [],
  });

  const dropdownConfig = useMemo(
    () => ({
      home: { requiresSecondDropdown: false },
      search: {
        requiresSecondDropdown: true,
        apiCall: () => filter.get(),
        title: "انتخاب فیلتر",
        placeholder: "فیلتر را انتخاب کنید",
      },
      kiosk: { requiresSecondDropdown: false },
      compare: {
        requiresSecondDropdown: true,
        apiCall: () => Compare.get(),
        title: "مقایسه خاص",
        placeholder: "مقایسه خاصی که مد نظر دارید را انتخاب کنید",
      },
      report360: {
        requiresSecondDropdown: true,
        apiCall: () => Report360.get(),
        title: "گزارش ۳۶۰ خاص",
        placeholder: "گزارش خاصی که مد نظر دارید را انتخاب کنید",
      },
      waveAnalytics: {
        requiresSecondDropdown: true,
        apiCall: () => waveAnalytics.get(),
        title: "موج خاص",
        placeholder: "موج خاصی که مد نظر دارید را انتخاب کنید",
      },
      opinion: {
        requiresSecondDropdown: true,
        apiCall: () => opinion.getOpinionReports(),
        title: "افکارسنجی خاص",
        placeholder: "افکارسنجی خاصی که مد نظر دارید را انتخاب کنید",
      },
    }),
    []
  );

  const options = [
    { value: "home", text: "در یک نگاه (پیشفرض)", icon: <Compass size={20} /> },
    {
      value: "search",
      text: "جستجوی پیشرفته",
      icon: <ListMagnifyingGlass size={20} />,
    },
    { value: "report360", text: "گزارش ۳۶۰", icon: <UserSwitch size={20} /> },
    { value: "compare", text: "مقایسه", icon: <Scales size={20} /> },
    { value: "waveAnalytics", text: "موج شناسی", icon: <TrendUp size={20} /> },
    { value: "opinion", text: "افکارسنجی", icon: <HeadCircuit size={20} /> },
    {
      value: "kiosk",
      text: "پیشخوان",
      icon: <Newspaper size={20} />,
    },
  ];

  // Unified data fetching function using the configuration
  const fetchSecondDropdownData = useCallback(
    async (type) => {
      const config = dropdownConfig[type];
      if (!config?.requiresSecondDropdown) return;

      setUiState((prev) => ({
        ...prev,
        dropdownLoading: true,
        dropdownData: [],
      }));

      try {
        const response = await config.apiCall();
        const transformedData = [
          ...response.data.data.user,
          ...response.data.data.group,
        ].map((item) => ({
          value: item.id,
          text: item.title,
        }));
        setUiState((prev) => ({ ...prev, dropdownData: transformedData }));
      } catch (error) {
        console.error(`Error fetching ${type} data:`, error);
        setUiState((prev) => ({ ...prev, dropdownData: [] }));
      } finally {
        setUiState((prev) => ({ ...prev, dropdownLoading: false }));
      }
    },
    [dropdownConfig]
  );

  // Handle first dropdown change
  const handleTypeChange = useCallback((selectedOption) => {
    setFormState((prev) => ({
      ...prev,
      selectedType: selectedOption.value,
      selectedItem: null, // Reset second dropdown selection
    }));
  }, []);

  // Handle second dropdown change
  const handleItemChange = useCallback((selectedItem) => {
    setFormState((prev) => ({
      ...prev,
      selectedItem: selectedItem,
    }));
  }, []);

  useEffect(() => {
    const fetchDashboardSettings = async () => {
      setUiState((prev) => ({ ...prev, loading: true }));
      try {
        // const response = await dashboard.getData();
        // setFormState(prev => ({ ...prev, selectedType: response.data.data?.type || "home" }));
      } catch (error) {
        console.error("Error fetching dashboard settings:", error);
      } finally {
        setUiState((prev) => ({ ...prev, loading: false }));
      }
    };

    fetchDashboardSettings();
  }, []);

  useEffect(() => {
    setBreadcrumb([{ title: "تنظیمات صفحه نخست" }]);
  }, [setBreadcrumb]);

  // Effect to handle cascading dropdown logic
  useEffect(() => {
    if (formState.selectedType) {
      fetchSecondDropdownData(formState.selectedType);
    }
  }, [formState.selectedType, fetchSecondDropdownData]);

  const handleSubmit = async () => {
    setFormState((prev) => ({ ...prev, isSubmitting: true }));
    try {
      // Assuming there's a dashboard API service to update settings
      // await dashboard.updateData({
      //   type: formState.selectedType,
      //   item: formState.selectedItem
      // });

      // For now, just simulate a successful update
      setTimeout(() => {
        notification.success("تنظیمات با موفقیت ذخیره شد");
        setFormState((prev) => ({ ...prev, isSubmitting: false }));
      }, 500);
    } catch (error) {
      console.error("Error updating dashboard settings:", error);
      notification.error("خطا در ذخیره تنظیمات");
      setFormState((prev) => ({ ...prev, isSubmitting: false }));
    }
  };

  // Helper function to determine if submit button should be disabled
  const isSubmitDisabled = () => {
    // if (formState.isSubmitting) return true;
    if (!formState.selectedType) return true;

    const config = dropdownConfig[formState.selectedType];
    if (config?.requiresSecondDropdown && !formState.selectedItem) {
      return true;
    }

    return false;
  };

  // Handle back button click
  const handleBackClick = () => {
    navigate(-1); // Go back to the previous page in history
  };

  if (uiState.loading) return <Loading />;

  return (
    <div className="flex-1">
      <div
        className="flex justify-start gap-2 items-center mx-auto !w-1/2 cursor-pointer hover:text-light-primary-text-high transition-colors"
        onClick={handleBackClick}
      >
        <CaretRight size={18} />
        <h2 className="font-subtitle-large">بازگشت به صفحه قبل</h2>
      </div>
      <div className="h-full w-full pt-6 [direction:rtl]">
        <Card className="!mx-auto !w-1/2 !h-full flex-col !p-6">
          <div
            className={
              "flex w-full flex-col justify-start font-body-medium pb-6 border-b"
            }
          >
            <h3 className={"text-light-neutral-text-high font-subtitle-large"}>
              تنظیمات صفحه نخست
            </h3>
            <p className="text-light-neutral-text-medium font-body-small">
              صفحه نخستی که پس از ورود به سیناپس باید نمایش داده شود را انتخاب
              کنید.
            </p>
          </div>
          <div className="flex flex-col gap-6 mt-6">
            <div className="flex flex-col gap-2">
              <DropDown
                subsets={options}
                // initialValue={}
                handleChange={handleTypeChange}
                title={"صفحه مورد نظر"}
                size={"md"}
              />
            </div>

            {/* Second dropdown for types that require it */}
            {dropdownConfig[formState.selectedType]?.requiresSecondDropdown && (
              <div className="flex flex-col gap-2">
                <DropDown
                  subsets={uiState.dropdownData}
                  initialValue={formState.selectedItem || {}}
                  handleChange={handleItemChange}
                  title={dropdownConfig[formState.selectedType]?.title}
                  size={"md"}
                  loading={uiState.dropdownLoading}
                  placeholder={
                    dropdownConfig[formState.selectedType]?.placeholder
                  }
                  setEmptyAtFirst={true}
                />
                {formState.selectedType === "search" && (
                  <p
                    className={"font-body-small text-light-neutral-text-medium"}
                  >
                    براساس فیلترهای موجود صفحه جست‌وجوی پیشرفته را شخصی سازی
                    کنید
                  </p>
                )}
              </div>
            )}
            {formState.selectedType === "search" && formState.selectedItem && (
              <div className={"w-full flex [direction:ltr]"}>
                <DateInput
                  id={"date"}
                  name={"date"}
                  inset={true}
                  size={"lg"}
                  validation={"none"}
                  direction={"rtl"}
                  className={"flex-1"}
                  field={{}}
                  form={{ errors: [], touched: [] }}
                  onChange={() => {}}
                  // value={filters.date}
                />
              </div>
            )}

            <div className="flex justify-end">
              <div className="w-[220px]">
                <CButton
                  onClick={handleSubmit}
                  size="md"
                  disabled={formState.isSubmitting}
                  readOnly={isSubmitDisabled()}
                >
                  ذخیره
                </CButton>
              </div>
            </div>
          </div>
        </Card>
        <ToastContainer />
      </div>
    </div>
  );
};

export default DashboardSetting;
