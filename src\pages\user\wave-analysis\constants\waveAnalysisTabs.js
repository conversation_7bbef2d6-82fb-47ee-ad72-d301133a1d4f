import TrendThunder from "assets/images/waveAnalytics/TrendThunder.png";
import TrendStatic from "assets/images/waveAnalytics/TrendStatic.png";
import { SquaresFour, TrendDown, TrendUp } from "@phosphor-icons/react";

const tabs = [
  { title: "همه موج‌ها", icon: SquaresFour, color: "#000" },
  {
    title: "موج‌های بالقوه",
    icon: null,
    background: "#E9A70A",
    color: "#fff",
    img: TrendStatic,
  },
  {
    title: "موج‌های افزایشی",
    icon: TrendUp,
    background: "#F60000",
    color: "#fff",
  },
  {
    title: "موج‌های فراگیر",
    icon: null,
    background: "#04A286",
    color: "#fff",
    img: TrendThunder,
  },
  {
    title: "موج‌های کاهشی",
    icon: TrendDown,
    background: "#1371F4",
    color: "#fff",
  },
];
export default tabs;
