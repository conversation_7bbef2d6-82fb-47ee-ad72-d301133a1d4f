import { SpinnerGap } from "@phosphor-icons/react";
import Divider from "components/ui/Divider";
import { toPersianNumber } from "utils/helper";
import PropTypes from "prop-types";

const FrequentWord = ({ words, loading, type }) => {
  return (
    <div className="flex flex-col gap-4">
      <p className="font-subtitle-large text-right">
        {type === "hashtags" ? "لیست هشتگ‌های پرتکرار" : "لیست کلمات پرتکرار"}
      </p>
      <Divider />
      {loading ? (
        <div className="w-full h-[270px] flex justify-center items-center">
          <SpinnerGap size={40} className="animate-spin" />
        </div>
      ) : words?.length === 0 ? (
        <div className="h-[270px] flex items-center justify-center font-subtitle-medium">
          داده ای برای نمایش وجود ندارد
        </div>
      ) : (
        <div className="flex flex-col gap-4 overflow-auto scrollbar-thin h-[270px] p-4">
          {words?.map((item, index) => (
            <div className="grid grid-cols-12 *:font-body-medium">
              <span className="col-span-2 text-center">
                {toPersianNumber(item?.value)}
              </span>
              <p className="col-span-9 text-right">
                {item?.text?.replace("#", "")}
              </p>
              <span className="col-span-1 text-right">
                {toPersianNumber(index + 1)}
              </span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

FrequentWord.propTypes = {
  words: PropTypes.array.isRequired,
  loading: PropTypes.bool.isRequired,
  type: PropTypes.string.isRequired,
};

export default FrequentWord;
