import useFetch from "../index";

class Compare {
  create(data) {
    return useFetch.post("/api/v1/queries/comparison-search/", data);
  }
  edit(id, data) {
    return useFetch.put(`/api/v1/queries/comparison-search/${id}/`, data);
  }

  get() {
    return useFetch.get("/api/v1/queries/comparison-search/");
  }

  getById(id) {
    return useFetch.get(`/api/v1/queries/comparison-search/${id}/`);
  }

  remove(id) {
    return useFetch.delete(`/api/v1/queries/comparison-search/${id}/`);
  }
}
export default new Compare();
