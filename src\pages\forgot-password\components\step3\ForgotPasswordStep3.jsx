import { useState } from "react";
import { Field, Form, Formik } from "formik";
import { forgetPasswordStep3Schema } from "utils/validationSchemas.js";

import logoImg from "assets/images/logo_main.png";
import PropTypes from "prop-types";
import { FormErrorWrapper } from "components/ui/FormErrorArea.jsx";
import { CButton } from "components/ui/CButton.jsx";
import { CaretLeft } from "@phosphor-icons/react";
import { CInput } from "components/ui/CInput.jsx";
import AuthService from "service/api/authService.js";

const ForgotPasswordStep3 = ({ setStep, otp, mobile }) => {
  const [formErrorTitle, setFormErrorTitle] = useState(null);
  const [formErrorText, setFormErrorText] = useState(null);

  const handleSubmit = async (values, { setSubmitting, setFieldError }) => {
    setFormErrorTitle(null);
    setSubmitting(true);
    try {
      const response = await AuthService.confirm({
        parameter: values.mobile,
        token: values.otp,
        password: values.password,
      });
      if (!(response?.data?.code === 200)) {
        setFormErrorTitle("خطا در فراموشی رمز عبور");
        setSubmitting(false);
        return false;
      }

      setStep(4);
    } catch (error) {
      if (error.response) {
        setFieldError("general", error.response.data.message);
        setFieldError("otp", " ");
        setFormErrorTitle(error.response.data.message);
      } else if (error.request) {
        // The request was made but no response was received
        console.error("No response received:", error.request);
        setFormErrorTitle("پاسخی از سرور دریافت نشد");
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error("Error", error.message);
        setFormErrorTitle("خطا در فرآیند!");
      }
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <>
      <Formik
        initialValues={{
          mobile: mobile,
          otp: otp,
          password: "",
          repeatPassword: "",
        }}
        validationSchema={forgetPasswordStep3Schema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting, handleChange, handleBlur, isValid, dirty }) => (
          <Form className="font-body-medium flex-1 bg-white mx-auto w-full h-full max-w-lg overflow-hidden">
            {/* Title */}
            <img
              className={"top-0 w-auto m-auto h-[96px]"}
              src={logoImg}
              alt="logo"
            />

            <h2 className="font-headline-medium text-light-neutral-text-high mt-8 mb-5 text-right">
              ثبت رمز عبور جدید
            </h2>
            <Field
              id={"password"}
              name={"password"}
              type={"password"}
              component={CInput}
              size={"lg"}
              validation={"none"}
              direction={"ltr"}
              title={"رمز عبور جدید"}
              placeholder={"رمز عبور جدید را تعیین کنید"}
              onChange={handleChange("password")}
              onBlur={handleBlur("password")}
            ></Field>

            <ul
              className={
                "mb-6 text-light-neutral-text-medium list-disc list-inside font-body-medium opacity-50"
              }
            >
              <li>حداقل ۸ کاراکتر باشد</li>
              <li>شامل حرف انگلیسی و عدد باشد</li>
              <li>شامل حداقل یک نماد مانند @#$% باشد</li>
            </ul>

            <Field
              id={"repeatPassword"}
              name={"repeatPassword"}
              type={"password"}
              component={CInput}
              size={"lg"}
              validation={"none"}
              direction={"ltr"}
              title={"تکرار رمز عبور"}
              placeholder={"رمز عبور را دوباره بنویسید"}
              onChange={handleChange("repeatPassword")}
              onBlur={handleBlur("repeatPassword")}
            ></Field>

            <FormErrorWrapper
              show={!!formErrorTitle}
              title={formErrorTitle}
              text={formErrorText}
            />

            {/* Submit button */}
            <div className="flex justify-end">
              <CButton
                rightIcon={<CaretLeft />}
                type={"submit"}
                size={"lg"}
                disabled={isSubmitting}
              >
                ثبت رمز عبور
              </CButton>
            </div>
          </Form>
        )}
      </Formik>
    </>
  );
};

ForgotPasswordStep3.propTypes = {
  setStep: PropTypes.func.isRequired,
  otp: PropTypes.string.isRequired,
  mobile: PropTypes.string.isRequired,
};

export default ForgotPasswordStep3;
