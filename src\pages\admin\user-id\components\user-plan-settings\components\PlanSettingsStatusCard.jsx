import { Diamond } from "@phosphor-icons/react";
import { CButton } from "components/ui/CButton";
import { toPersianNumber } from "utils/helper";

const PlanSettingsStatusCard = ({
  planChangeToggle,
  planRevivalToggle,
  setPlanChangeToggle,
  setPlanRevivalToggle,
  setIsPopupOpen,
}) => {
  return (
    <div className="col-span-4 bg-light-neutral-surface-card rounded-[8px] p-6">
      <p className="font-subtitle-large">وضعیت پلن کاربر</p>

      <div className="flex items-center gap-2 my-7">
        <Diamond size={40} className="text-light-primary-background-rest" />
        <div className="">
          <p className="font-headline-medium">سازمانی</p>
          <div className="font-body-medium flex items-center justify-evenly gap-3">
            <p className="text-light-neutral-text-medium">از</p>
            <span>۱۲:۳۴:۵۴ - ۱۴۰۲/۱۱/۰۱</span>
            <p className="text-light-neutral-text-medium">تا</p>
            <span>۱۲:۳۴:۵۴ - ۱۴۰۲/۱۱/۰۱</span>
          </div>
        </div>
      </div>

      <div
        className={`font-body-medium leading-9 ${
          planChangeToggle || planRevivalToggle ? "hidden" : "block"
        }`}
      >
        <div className="flex items-center justify-between">
          <p className="text-light-neutral-text-medium">تعداد کاربران</p>
          <div className="flex items-center gap-2">
            <p>{toPersianNumber("5")}</p>
            <p className="text-light-neutral-text-medium">کاربر</p>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <p className="text-light-neutral-text-medium">
            فیلتر زمانی دسترسی به داده‌ها
          </p>
          <div className="flex items-center gap-2">
            <p>{toPersianNumber("5")}</p>
            <p className="text-light-neutral-text-medium">سال گذشته</p>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <p className="text-light-neutral-text-medium">
            تعداد رکوردهای قابل دانلود
          </p>
          <div className="flex items-center gap-2">
            <p>{toPersianNumber("5")}</p>
            <p className="text-light-neutral-text-medium">میلیون</p>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <p className="text-light-neutral-text-medium">تعداد بولتن </p>
          <div className="flex items-center gap-2">
            <p>{toPersianNumber("30")}</p>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <p className="text-light-neutral-text-medium">تعداد هشدار</p>
          <div className="flex items-center gap-2">
            <p>{toPersianNumber("30")}</p>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <p className="text-light-neutral-text-medium">
            تعداد گزارش مقایسه‌ای
          </p>
          <div className="flex items-center gap-2">
            <p>{toPersianNumber("30")}</p>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <p className="text-light-neutral-text-medium">تعداد گزارش ۳۶۰</p>
          <p>{toPersianNumber("30")} </p>
        </div>
        <div className="flex items-center justify-between">
          <p className="text-light-neutral-text-medium">
            دسترسی به روزنامه‌های پیشخوان
          </p>
          <p>کامل </p>
        </div>
        <div className="flex items-center justify-between">
          <p className="text-light-neutral-text-medium">
            تعداد محتوای نشان شده
          </p>
          <p>{toPersianNumber("30")} </p>
        </div>
        <div className="flex items-center justify-between">
          <p className="text-light-neutral-text-medium">
            تعداد محتوای یاداداشت گذاری شده
          </p>
          <p>{toPersianNumber("30")} </p>
        </div>
      </div>

      <div className="flex items-center gap-2 mt-5">
        {!planChangeToggle && (
          <div
            className="w-full"
            onClick={() => {
              setPlanChangeToggle(true);
              setPlanRevivalToggle(false);
            }}
          >
            <CButton role="neutral">تغییر پلن</CButton>
          </div>
        )}
        {!planRevivalToggle && (
          <div
            className="w-full"
            onClick={() => {
              setPlanRevivalToggle(true);
              setPlanChangeToggle(false);
            }}
          >
            <CButton role="neutral">تمدید</CButton>
          </div>
        )}
        <div className="w-full" onClick={() => setIsPopupOpen(true)}>
          <CButton role="error" mode="outline">
            لغو پلن
          </CButton>
        </div>
      </div>
    </div>
  );
};

export default PlanSettingsStatusCard;
