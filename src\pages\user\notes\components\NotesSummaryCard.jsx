import {
  <PERSON>,
  ChatTeardropDots,
  <PERSON>,
  <PERSON>eat,
  <PERSON>rashSimple,
  CaretLeft,
} from "@phosphor-icons/react";
import { parseNumber, parseTimeToPersian } from "utils/helper.js";
import TextSlicer from "components/TextSlicer/index.jsx";
import Divider from "components/ui/Divider.jsx";
import SummaryCardHeaderMenu from "../../hot-topic/components/SummaryCardTopic/SummaryCardHeaderMenu.jsx";
import { CButton } from "components/ui/CButton.jsx";
import MediaBadge from "components/ui/MediaBadge.jsx";
const NotesSummaryCard = ({
  media,
  data,
  height,
  handleClickOnAvatar,
  setIsDeletePopUpOpen,
  setNoteToModify,
  handleEditClick,
  isBookmarked,
  myNotesLists,
  setUpdater,
}) => {
  const selectMedia = {
    telegram: {
      avatar: data?.content?.avatar
        ? "https://f002.backblazeb2.com/file/all-gather-media/" +
          data?.content?.avatar
        : "/logo_small.png",
      sub: [
        {
          icon: <Eye color="#256EF6" weight="fill" />,
          value: parseNumber(String(data?.view_count || "0")),
        },
      ],
    },
    twitter: {
      avatar: data?.content?.avatar
        ? "https://f002.backblazeb2.com/file/all-gather-media/" +
          data?.content?.avatar
        : "/logo_small.png",
      sub: [
        {
          icon: <Repeat color="#54ADEE" />,
          value: parseNumber(String(data?.content?.copy_count || "0")),
        },
        {
          icon: <Heart color="#E0526A" weight="fill" />,
          value: parseNumber(String(data?.content?.like_count || "0")),
        },
      ],
    },
    instagram: {
      avatar: data?.content?.avatar
        ? "https://f002.backblazeb2.com/file/all-gather-media/" + data?.avatar
        : "/logo_small.png",
      sub: [
        {
          icon: <ChatTeardropDots color="#1CB0A5" weight="fill" />,
          value: parseNumber(String(data?.content?.comment_count || "0")),
        },
        {
          icon: <Heart color="#E0526A" weight="fill" />,
          value: parseNumber(String(data?.content?.like_count || "0")),
        },
      ],
    },
    news: {
      avatar: data["relation.avatar"],
      sub: [
        {
          icon: <ChatTeardropDots color="#1CB0A5" weight="fill" />,
          value: parseNumber(String(data?.content?.comment_count || "0")),
        },
        {
          icon: <Eye color="#256EF6" weight="fill" />,
          value: parseNumber(String(data?.content?.view_count || "0")),
        },
      ],
    },
    eitaa: {
      avatar: "/logo_small.png",
      sub: [
        {
          icon: <Eye color="#256EF6" weight="fill" />,
          value: parseNumber(String(data?.view_count || "0")),
        },
      ],
    },
  };
  console.log(data);
  return (
    <div
      className="p-4 flex flex-col justify-between gap-4 min-h-40 w-full [direction:rtl]"
      style={{ ...(height ? { height } : {}) }}
    >
      <div className="flex justify-between">
        <div className="flex gap-2 relative">
          <div
            className={
              "size-10 rounded-full bg-contain ml-2 bg-no-repeat bg-center"
            }
            style={{
              backgroundImage: `url(${data?.content?.avatar})`,
              cursor: handleClickOnAvatar ? "zoom-in" : "default",
            }}
            onClick={handleClickOnAvatar}
          ></div>
          <span
            className={
              "absolute top-6 right-0 rounded-full overflow-hidden !w-[20px] !h-[20px]"
            }
          >
            <MediaBadge media={media} className={"!h-[20px] !w-[20px]"} />
          </span>
          <div className="flex flex-col items-start">
            <span className="font-subtitle-medium text-light-neutral-text-high">
              {data?.content?.title || "کاربر"}
            </span>
            <span className="font-overline-medium text-light-neutral-text-medium">
              {data?.content?.username}@
            </span>
          </div>
        </div>
        <SummaryCardHeaderMenu
          media={media}
          data={data?.content}
          is_Pin={data?.note?.is_pin}
          myLists={myNotesLists}
          hasNotes={!!data?.note?.text?.length}
          isBookmarked={isBookmarked}
          setUpdater={setUpdater}
        />
      </div>
      <TextSlicer media={"twitter"} length={100}>
        {data?.content?.description ||
          data?.content?.content ||
          data?.content?.text}
      </TextSlicer>
      <div className="flex justify-between">
        <div className="flex gap-1 font-overline-medium text-light-neutral-text-medium">
          {data.content?.time ? parseTimeToPersian(data.content?.time) : ""}
        </div>
        <div className="flex gap-4">
          {selectMedia[media]?.sub.map(({ icon, value }, index) => (
            <div className="flex items-center gap-1" key={index}>
              <span className="font-overline-medium text-light-neutral-text-medium">
                {value}
              </span>
              <div>{icon}</div>
            </div>
          ))}
        </div>
      </div>
      <Divider />
      <div className="flex items-center justify-between">
        <span className="font-subtitle-medium text-light-neutral-text-high">
          متن یادداشت
        </span>
        <div className="flex gap-1 font-overline-medium text-light-neutral-text-medium">
          {data.content?.time ? parseTimeToPersian(data.content?.time) : ""}
        </div>
      </div>

      <div className="bg-light-neutral-surface-highlight rounded-lg p-2 justify-between font-body-large flex flex-col">
        <pre className="font-body-medium break-all px-2 overflow-hidden">
          <TextSlicer length={80}>{data?.note?.text}</TextSlicer>
        </pre>

        <div className="flex items-center  justify-between mt-3">
          <div
            className="rounded-[6px] p-1 cursor-pointer w-6 h-6"
            onClick={() => {
              setNoteToModify({
                platform: data?.platform,
                content_id: data?.content_id,
                content: data?.content,
              });
              setIsDeletePopUpOpen(true);
            }}
          >
            <TrashSimple size={18} />
          </div>
          <div
            onClick={() =>
              handleEditClick({
                platform: data?.platform,
                content_id: data?.content_id,
                content: data?.content,
              })
            }
          >
            <CButton mode="text" size="sm" rightIcon={<CaretLeft />}>
              بیشتر
            </CButton>
          </div>
        </div>
      </div>
    </div>
  );
};
export default NotesSummaryCard;
