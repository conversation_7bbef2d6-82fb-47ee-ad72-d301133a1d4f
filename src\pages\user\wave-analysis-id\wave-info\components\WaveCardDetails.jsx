import { toPersianNumber } from "utils/helper";

const WaveCardDetails = ({
  title,
  count,
  variant,
  color,
  icon: Icon,
  subtitle,
  ratio,
}) => {
  return (
    <div
      className="bg-light-neutral-surface-card rounded-lg px-2 py-3 !w-full min-h-28"
      style={{
        boxShadow: "0px 2px 20px 0px #0000000D",
      }}
    >
      <div className="flex items-start justify-between">
        <div>
          <p className="font-body-small text-light-neutral-text-medium">
            {title}
          </p>
          <p className="font-title-medium font-semibold" style={{ color }}>
            {toPersianNumber(count)}
          </p>
        </div>

        <div
          className={`rounded-2xl flex items-center justify-center p-3`}
          style={{
            background: variant,
          }}
        >
          <Icon size={32} color={color} />
        </div>
      </div>
      <div className="flex items-center justify-between font-overline-medium text-light-neutral-text-low">
        {ratio && (
          <p className="flex items-center font-bold">
            {toPersianNumber(ratio)}
          </p>
        )}
        {subtitle && <p>{subtitle ? subtitle : "نسبت به آخرین به‌روزرسانی"}</p>}
      </div>
    </div>
  );
};

export default WaveCardDetails;
