import FilterInfoSection from "./FilterInfoSection";

const FilterInfo = () => {
  return (
    <div className="flex flex-col gap-6">
      <FilterInfoSection title="فیلتر چیست؟">
        فیلتر مجموعه‌ای از محدودیت‌هاست که برای جست‌وجوی دقیق بر روی داده‌ها
        اعمال می‌شود. در یک فیلتر، شما می‌توانید ترکیبی از کلمات کلیدی را به
        همراه سایر فیلدها بازای چندین بستر ذخیره کرده و در صفحات مختلف از جمله
        جست‌وجوی پیشرفته، هشدار، پرونده و ... استفاده نمایید.
      </FilterInfoSection>
      <FilterInfoSection title="دلیل  وجود فیلتر چیست در حالی که می‌توان در حین ساخت یک هشدار، انجام جست‌وجوی پیشرفته و غیره نیز در لحظه فیلترها را تنظیم کرد؟">
        مهم‌ترین هدف از ایجاد این بخش، سهولت دسترسی کاربر و امکان استفاده از
        فیلترهای مورد نظر در صفحات مختلف است. تنظیم فیلترهای پیشرفته تکراری بعضا
        ممکن است زمان‌گیر بوده و خارج از حوصله کاربر باشد بنابراین کاربر
        می‌تواند فقط یک بار آن را ساخته و به دفعات استفاده نماید.
      </FilterInfoSection>
      <FilterInfoSection title="چگونه از فیلتر ایجاد شده در سایر صفحات استفاده کنم؟">
        در قسمت فیلترهای مربوط به هر صفحه یک گزینه با عنوان "فیلترهای از پیش
        ساخته" وجود دارد که از طریق آن می‌توان فیلتر مورد نظر را جست‌وجو و
        انتخاب نمود.
      </FilterInfoSection>
    </div>
  );
};

export default FilterInfo;
