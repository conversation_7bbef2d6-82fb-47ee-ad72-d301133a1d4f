import { useState } from "react";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";
import Steps from "components/ui/Steps";
import StepOne from "./components/StepOne";
import StepTwo from "./components/StepTwo";
import StepThree from "./StepThree";
import StepFour from "./stepFour";
import StepFive from "./components/stepFive";
// import BatchUserCreatePage from "./batch";

const AdminUserCreate = () => {
  // const [attachments, setAttachments] = useState([]);
  const [step, setStep] = useState(1);
  const breadcrumbList = [
    { title: "کاربران", link: "/admin/user/list" },
    { title: "ایجاد کاربر جدید" },
  ];
  useBreadcrumb(breadcrumbList);
  const currentStep = {
    1: <StepOne setStep={setStep} />,
    2: <StepTwo setStep={setStep} />,
    3: <StepThree setStep={setStep} />,
    4: <StepFour setStep={setStep} />,
    5: <StepFive setStep={setStep} />,
  };
  return (
    <>
      <div className="container mx-auto w-[674px]">
        <div className="bg-light-neutral-surface-card rounded-[8px] p-6 mb-6">
          <p className="font-body-large pb-4">
            {step == 1
              ? "فرآیند افزودن کاربر"
              : step == 2
              ? "نقش کاربر"
              : step == 3
              ? "انتخاب پلن"
              : step == 4
              ? "رمز عبور"
              : step == 5
              ? "بررسی نهایی"
              : ""}
          </p>
          <Steps
            texts={[
              "اطلاعات شخصی",
              "نقش کاربر",
              "انتخاب پلن",
              "رمز عبور",
              "بررسی نهایی",
            ]}
            stepCounts={5}
            step={step}
          />
        </div>

        {currentStep[step]}
      </div>
    </>
  );
};

export default AdminUserCreate;
