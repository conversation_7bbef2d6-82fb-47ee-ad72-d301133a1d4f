import { useMemo, useState } from "react";
import MediaBadge from "components/ui/MediaBadge";
import Status from "components/ui/Status";
import {
  Eye,
  PencilSimpleLine,
  TrashSimple,
  WarningCircle,
} from "@phosphor-icons/react";
import alert from "service/api/alert";
import { useNavigate } from "react-router-dom";
import ToolTip from "components/ui/ToolTip";
import { notification, toPersianNumber } from "utils/helper.js";
import DeletePopUp from "components/ui/DeletePopUp.jsx";

const AlertCard = ({ media, data, alert_id, setRerender, setLoading }) => {
  const [showRemove, setShowRemove] = useState(false);
  const navigate = useNavigate();
  const changeStatus = async () => {
    setLoading(true);
    try {
      const response = await alert.status(alert_id);
      setRerender((l) => !l);
      // console.log(error.response.data.message);
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  const dataToEdit = useMemo(() => {
    const help = JSON.parse(JSON.stringify(data));
    return help;
  }, []);

  const formatTimeWithoutSecondsToPersian = (dateString) => {
    const date = new Date(dateString);
    const hours = date.getHours().toString().padStart(2, "0");
    const minutes = date.getMinutes().toString().padStart(2, "0");
    const formattedDate = `${hours}:${minutes} - ${date.toLocaleDateString(
      "fa-IR",
    )}`;
    return toPersianNumber(formattedDate);
  };

  const deleteAlert = async () => {
    setLoading(true);
    try {
      const { data } = await alert.remove(alert_id);
      notification.error(
        data.message,
        <WarningCircle className="text-light-inform-text-rest" />,
      );
      setShowRemove(false);
      if (setRerender) {
        setRerender((l) => !l);
      }
      navigate("/app/alert/list");
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  return (
    <>
      <div className="grid grid-cols-7 hover:bg-light-neutral-surface-highlight rounded p-1 items-center">
        <div className="font-body-medium">{data.title}</div>
        <div className="flex">
          <MediaBadge media={media} showMediaName />
        </div>
        <div className="font-body-medium">
          <p>{data?.receiver || "---"}</p>
        </div>
        <div className="w-40 flex gap-1 font-body-medium text-light-neutral-text-medium">
          <span>
            {data.created_at
              ? formatTimeWithoutSecondsToPersian(data.created_at)
              : ""}
          </span>
        </div>
        <div className="w-40 flex gap-1 font-body-medium text-light-neutral-text-medium">
          <span>
            {data.last_execute
              ? formatTimeWithoutSecondsToPersian(data.last_execute)
              : ""}
          </span>
        </div>

        <div className="">
          {/* <span
              className="font-body-small text-light-neutral-text-medium cursor-pointer w-20"
              onClick={() => changeStatus()}
            >
              {data.is_active ? "غیرفعال سازی" : "فعال سازی"}
            </span> */}
          <Status status={data.is_active} onClick={() => changeStatus()} />
        </div>

        <div className="w-56">
          <div className="flex gap-4">
            <div
              className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
              onClick={() => navigate(`/app/alert/list/${alert_id}`)}
            >
              <ToolTip comp="نمایش جزئیات">
                <Eye size={16} />
              </ToolTip>
            </div>
            <div
              className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
              onClick={() =>
                navigate("/app/alert/edit", {
                  state: {
                    ...dataToEdit,
                  },
                })
              }
            >
              <ToolTip comp="ویرایش">
                <PencilSimpleLine size={16} />
              </ToolTip>
            </div>
            <div
              className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
              onClick={() => setShowRemove(true)}
            >
              <ToolTip comp="حذف">
                <TrashSimple size={16} />
              </ToolTip>
            </div>
          </div>
        </div>
      </div>
      <DeletePopUp
        onClose={() => setShowRemove(false)}
        isOpen={showRemove}
        submitHandler={deleteAlert}
        title="آیا مطمئن هستید؟"
        description="در صورت حذف این هشدار، امکان بازیابی آن وجود ندارد"
      />
    </>
  );
};

export default AlertCard;
