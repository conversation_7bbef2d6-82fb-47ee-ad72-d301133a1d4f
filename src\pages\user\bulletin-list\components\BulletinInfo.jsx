import { toPersianNumber } from "utils/helper";

const BulletinInfo = () => {
  return (
    <div>
      <h3 className="font-subtitle-medium py-4">بولتن</h3>
      <p className="font-body-medium text-justify">
        بولتن نوعی پرونده موضوعی است که امکان دریافت نتایج گزارش‌ها را در قالب
        خروجی‌های Word و PDF فراهم می‌کند که دارای فیلدهای مختلفی جهت شخصی‌سازی
        خروجی نهایی است. فیلدهای اطلاعات بولتن شامل موارد زیر است:
      </p>

      <ul className="list-disc leading-9 font-body-medium px-4 my-4">
        <li>تعیین نوع گزارش‌های آماری برای نمایش در فایل خروجی</li>
        <li>امکان آپلود تصویر لوگوی بولتن</li>
        <li>امکان آپلود تصویر صفحه اصلی بولتن</li>
        <li>ثبت عنوان اصلی و عناوین فرعی برای فایل خروجی</li>
        <li>ثبت سربرگ، پاورقی و جایگاه شماره صفحه برای فایل خروجی</li>
        <li>قرار دادن صفحه مقدمه و فهرست در فایل خروجی</li>
        <li>قابلیت اشتراک‌گذاری خروجی بولتن با سایر کاربران سامانه</li>
        <li>امکان ویرایش بولتن و دریافت خروجی به‌روزرسانی شده</li>
      </ul>

      <h3 className="font-subtitle-medium py-4">بولتن موردی</h3>
      <p className="font-body-medium text-justify">
        بولتن موردی در یک بازه زمانی معین که توسط کاربر مشخص خواهد شد ایجاد شده
        و هر زمان که نیاز باشد از طریق گزینه ویرایش، امکان بروزرسانی آن وجود
        دارد.
      </p>
      <h3 className="font-subtitle-medium py-4">بولتن دوره‌ای</h3>
      <p className="font-body-medium text-justify">
        {toPersianNumber(
          "بولتن‌های دوره‌ای مطابق با فواصل زمانی مشخص (روزانه، هفتگی، دور روز یکبار و ...) ایجاد شده و قابلیت به‌روز شدن را ندارند. برای مثال کاربر می‌تواند بولتنی را ایجاد کند که هر روز ساعت 12:30 آماده شده و گزارش 24 ساعت اخیر را در قالب یک فایل خروجی تولید نماید."
        )}
      </p>
    </div>
  );
};

export default BulletinInfo;
