server {
    #listen 443 ssl http2;
    listen 80;
    server_name _;

    access_log  /var/log/nginx/prod.userpanel.access.log;
    error_log /var/log/nginx/prod.userpanel.error.log;


    #access_log off;

    #ssl_certificate /etc/letsencrypt/live/demogames.adrogame.com/fullchain.pem;
    #ssl_certificate_key /etc/letsencrypt/live/demogames.adrogame.com/privkey.pem;

    location / {
        #add_header Access-Control-Allow-Origin "$http_origin" always;
        autoindex off;
        root /app/build;
        index index.html;

        try_files $uri $uri/ /index.html;
    }

}
