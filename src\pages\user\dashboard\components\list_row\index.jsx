import { useEffect, useState } from "react";
import ListTile from "../list_title";
import SummaryCard from "components/SummaryCard";
import TextSlicer from "components/TextSlicer";
import SummaryCardFooter from "../../../hot-topic/components/SummaryCardTopic/SummaryCardFooter";

import clsx from "clsx";

const ListRow = ({ media, data, base_url, countInRow = 4 }) => {
  const getGridColsClass = (cols) => {
    switch (cols) {
      case 1:
        return "lg:grid-cols-1";
      case 2:
        return "lg:grid-cols-2";
      case 3:
        return "lg:grid-cols-3";
      case 4:
        return "lg:grid-cols-4";
      // Add more cases as needed
      default:
        return "lg:grid-cols-4";
    }
  };

  return (
    <div className="[direction:rtl] flex flex-col gap-8 py-4">
      <ListTile data={data} base_url={base_url} media={media} />
      <div
        className={clsx(
          "grid sm:grid-cols-1 gap-4",
          getGridColsClass(countInRow),
        )}
      >
        {data?.data.slice(0, countInRow).map((item) => (
          <div
            className="flex flex-col justify-between rounded-lg shadow-[0px_2px_20px_0px_#0000000D] bg-white"
            key={item.id}
          >
            <SummaryCard media={media} data={item}>
              <TextSlicer media={media}>
                {media === "news" ? item.content : item.description}
              </TextSlicer>
            </SummaryCard>
            <SummaryCardFooter media={media} data={item} lowWidth />
          </div>
        ))}
      </div>
    </div>
  );
};

export default ListRow;
