import { useEffect, useState } from "react";
import Divider from "components/ui/Divider";
import RadialBar from "components/Charts/RadialBar";
import advanceSearch from "service/api/advanceSearch";
import DropDown from "components/ui/DropDown";
import { parseNumber } from "utils/helper";
import { SpinnerGap, UserSwitch } from "@phosphor-icons/react";
import { useNavigate } from "react-router-dom";
import { useReport360Store } from "store/report360Store.js";
import useSearchStore from "store/searchStore.js";
import { buildRequestData } from "utils/requestData";
import { RESOURCE_SORT_TYPE } from "constants/sort-type";
import PLATFORMS from "constants/platforms.js";
import Popup from "components/ui/PopUp";
import ExportMenu from "components/ExportMenu/index.jsx";

const BestSource = () => {
  const setReport = useReport360Store((state) => state.setReport);
  const navigate = useNavigate();
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [sort, setSort] = useState({ fa: "محتوا", en: "date" });
  const { filters, query } = useSearchStore();

  const getData = async () => {
    setLoading(true);
    try {
      const requestData = buildRequestData(
        {
          ...filters,
          q: query,
          sort: sort.en,
        },
        "top_sources"
      );
      const res = await advanceSearch.search(requestData);

      const platformData = res?.data?.data?.[filters.platform] || [];
      const dataArray = platformData.slice(0, 5);
      setData(dataArray);
    } catch (error) {
      console.error(error);
    }
    setLoading(false);
  };

  // Prepare series and time for ExportMenu
  const series = [
    {
      name: sort.fa || "منبع",
      data: data.map((item) => item?.count || 0),
      time: data.map((item) => item?.title || item?.key || ""),
    },
  ];

  const time = data.map((item) => item?.title || item?.key || "");

  useEffect(() => {
    getData();
  }, [filters, query, sort]);

  useEffect(() => {
    setSort(RESOURCE_SORT_TYPE[filters.platform][0]);
  }, [filters.platform]);

  const openConfirmPopup = (item) => {
    setSelectedItem(item);
    setIsPopupOpen(true);
  };

  const closeConfirmPopup = () => {
    setIsPopupOpen(false);
  };

  const submitHandler = () => {
    if (selectedItem) {
      handleProfileClick(selectedItem);
      setIsPopupOpen(false);
    }
  };

  const handleProfileClick = (profile) => {
    try {
      const selectedProfile = {
        id: profile?.id,
        user_title: profile?.title,
        user_name: profile?.key,
        platform: filters?.platform,
        avatar: profile?.avatar,
      };

      setReport({
        isFromTopSource: true,
        step: 3,
        type: "profile",
        profile: selectedProfile,
      });

      return navigate("/app/report-360/create");
    } catch (e) {
      console.log(e);
    }
  };

  return (
    <div className="best-source-container">
      <div className="flex items-center justify-between">
        <div className="flex flex-row items-center gap-4 [direction:rtl] relative z-30">
          <DropDown
            title="نمایش بر اساس"
            subsets={RESOURCE_SORT_TYPE[filters.platform].map(
              (item) => item.fa
            )}
            selected={sort.fa}
            setSelected={(value) => {
              setSort(
                RESOURCE_SORT_TYPE[filters.platform].filter(
                  (item) => item.fa === value
                )[0]
              );
            }}
          />
          <ExportMenu
            chartSelector=".best-source-container"
            fileName="best-source"
            series={series}
            time={time}
            excelHeaders={["Source", sort.fa || "منبع"]}
            onError={(error) => console.error("Export error:", error)}
            menuItems={["PNG", "JPEG", "Excel"]}
          />
        </div>
        <div className="font-subtitle-large">برترین منابع</div>
      </div>
      <div className="py-4">
        <Divider />
      </div>

      <div className="relative">
        {loading && (
          <div className="w-full h-[365px] flex justify-center items-center absolute top-0 left-0 z-50">
            <SpinnerGap size={40} className="animate-spin" />
          </div>
        )}
        <div
          className={`w-full grid grid-cols-12 gap-6 h-96 ${
            loading && "blur-sm"
          }`}
        >
          {data.length === 0 ? (
            <>
              {!loading && (
                <div className="h-[270px] flex items-center justify-center font-subtitle-medium col-span-12">
                  داده ای برای نمایش وجود ندارد
                </div>
              )}
            </>
          ) : (
            <>
              <div className="col-span-6">
                {!!data.filter((item) => item?.count).length && (
                  <RadialBar
                    data={data
                      .filter((item) => item?.count)
                      .map((item) => ({
                        y: item.count,
                        title: item.key,
                      }))}
                  />
                )}
              </div>
              <div className="col-span-6 w-full flex flex-col gap-4 [direction:rtl]">
                <div className="grid grid-cols-2 *:font-body-medium *:text-light-neutral-text-medium">
                  <div>منبع</div>
                  <div className={"text-center"}>{sort.fa}</div>
                </div>

                {data.map((item) => (
                  <div
                    className="grid grid-cols-2 items-center p-1 rounded-lg hover:cursor-pointer hover:bg-light-primary-background-highlight"
                    key={item.id}
                    onClick={() => openConfirmPopup(item)}
                  >
                    <div className="flex gap-2 items-center">
                      <div
                        className="size-10 rounded-full"
                        style={{
                          backgroundImage: item?.avatar
                            ? `url(${item.avatar})`
                            : "url(/logo_small.png)",
                          backgroundRepeat: "no-repeat",
                          backgroundPosition: "center center",
                          backgroundSize: "contain",
                        }}
                      ></div>
                      <div>
                        <div
                          className="font-subtitle-medium overflow-hidden whitespace-nowrap text-ellipsis max-w-[10.5rem]"
                          title={item?.title || item?.key}
                        >
                          {item?.title || item?.key}
                        </div>
                        <div className="font-overline-medium">
                          {item.key &&
                            (filters.platform === PLATFORMS.NEWS
                              ? `${item.key}`
                              : `${item.key}@`)}
                        </div>
                      </div>
                    </div>
                    <div className="font-body-bold-medium text-center">
                      {parseNumber(item?.count || 0)}
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      </div>
      <Popup
        isOpen={isPopupOpen}
        onClose={closeConfirmPopup}
        submitHandler={submitHandler}
        title="آیا می‌خواهید گزارش های 360 این منبع نمایش داده شود؟"
        agreeButton="بله"
        cancleButton="خیر"
        icon={<UserSwitch size={45} />}
      >
        <p className="py-5 font-body-medium">
          توجه کنید که با کلیک برروی گزینه (( بله )) به صفحه گزارشات 360 این
          منبع منتقل خواهید شد.
        </p>
      </Popup>
    </div>
  );
};

export default BestSource;
