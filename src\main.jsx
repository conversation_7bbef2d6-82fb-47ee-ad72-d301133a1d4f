import React from "react";
import React<PERSON><PERSON> from "react-dom/client";
import { <PERSON><PERSON><PERSON><PERSON>out<PERSON> } from "react-router-dom";
import App from "./App.jsx";
import "./index.css";
import { LayoutProvider } from "./context/layout-context";
import { AuthContextProvider } from "./context/auth-context";
import CronJobProvider from "context/cronJob.jsx";

ReactDOM.createRoot(document.getElementById("root")).render(
  <React.StrictMode>
    <LayoutProvider>
      <AuthContextProvider>
        <BrowserRouter>
          <CronJobProvider>
            <App />
          </CronJobProvider>
        </BrowserRouter>
      </AuthContextProvider>
    </LayoutProvider>
  </React.StrictMode>
);
