import { Eye, FileArrowDown, Info, TrashSimple } from "@phosphor-icons/react";
import ToolTip from "components/ui/ToolTip";
import { Link } from "react-router-dom";
import Wrapper from "./Wrapper";

const Table = ({ data }) => {
  return (
    <>
      <div
        className="grid grid-cols-7 hover:bg-light-neutral-surface-highlight py-2 cursor-pointer font-body-medium"
        //   onClick={() => navigate(`/admin/ticket/list/${data?.id}`)}
      >
        <div className="">username</div>
        <div className="">۰۹۳۶۶۰۹۰۹۹</div>
        <div className="">نام و نام خانوادگی</div>
        <div className="">مدیر سازمان</div>
        <div className="">پایه</div>
        <div className="">*********</div>
        <div className="">
          <div className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer w-fit">
            <ToolTip comp="حذف">
              <TrashSimple size={16} />
            </ToolTip>
          </div>
        </div>
      </div>
    </>
  );
};

export default Table;
