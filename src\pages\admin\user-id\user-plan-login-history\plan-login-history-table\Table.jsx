import { Eye, FileArrowDown, Info } from "@phosphor-icons/react";
import ToolTip from "components/ui/ToolTip";
import UserState from "./UserState";
import { Link } from "react-router-dom";

const Table = ({ data }) => {
  return (
    <>
      <div
        className="grid grid-cols-5 hover:bg-light-neutral-surface-highlight py-2 cursor-pointer font-body-medium"
        //   onClick={() => navigate(`/admin/ticket/list/${data?.id}`)}
      >
        <div className="">
          Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36
          (KHTML, like Gecko) Chrome/********* Safari/537.36
        </div>
        <div  className="mx-auto">Iran</div>
        <div  className="mx-auto">***************</div>
        <div  className="mx-auto">
          <p>۱۴۰۲/۱۱/۰۱ - ۱۲:۳۴</p>
        </div>
        <div className="mx-auto">
          <UserState title={"موفق"} state={"موفق"} />
        </div>
      </div>
    </>
  );
};

export default Table;
