import { useState } from "react";
import { Card } from "components/ui/Card";
import Steps from "components/ui/Steps";
import Step1 from "./steps/step-1";
import Step2 from "./steps/step-2";
import Step3 from "./steps/step-3";
import Step4 from "./steps/step-4";

const AnnouncementCreate = () => {
  const [step, setStep] = useState(1);
  const [selectedCard, setSelectedCard] = useState(null);

  const handleCardClick = (cardIndex) => {
    setSelectedCard(cardIndex);
  };

  return (
    <div className="min-h-screen pt-4 flex items-center justify-center">
      <div className="w-[667px]">
        <Card>
          <div className="flex flex-col gap-6 w-full">
            {step !== 4 && (
              <Steps
                step={step}
                stepCounts={3}
                texts={["محتوای اطلاعیه", "مخاطبان", "زمان انتشار"]}
              />
            )}
            {step === 1 && <Step1 setStep={setStep} step={step} />}
            {step === 2 && (
              <Step2
                setStep={setStep}
                step={step}
                selectedCard={selectedCard}
                handleCardClick={handleCardClick}
              />
            )}
            {step === 3 && (
              <Step3
                setStep={setStep}
                step={step}
                selectedCard={selectedCard}
                handleCardClick={handleCardClick}
              />
            )}
            {step === 4 && (
              <Step4
                setStep={setStep}
                step={step}
                selectedCard={selectedCard}
              />
            )}
          </div>
        </Card>
      </div>
    </div>
  );
};

export default AnnouncementCreate;
