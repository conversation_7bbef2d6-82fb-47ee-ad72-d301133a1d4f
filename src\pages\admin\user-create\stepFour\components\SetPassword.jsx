import { useState } from "react";
import { Circle, CheckCircle, Eye, EyeClosed } from "@phosphor-icons/react";
import { ToastContainer } from "react-toastify";
import ErrorCard from "pages/user/security-profile/components/ErrorCard";
import SuccessCard from "pages/user/security-profile/components/SuccessCard";
import Input from "pages/user/security-profile/components/Input";
import { CButton } from "components/ui/CButton";
const SetPassword = ({ setStep }) => {
  const [newPass, setNewPass] = useState({ value: "", showValue: false });
  const [confirmPass, setConfirmPass] = useState({
    value: "",
    showValue: false,
    isTouched: false,
  });
  const [error, setError] = useState("");
  const [response, setResponse] = useState("");
  const [newPasswordVisible, setNewPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);

  return (
    <>
      <form
        className="flex flex-col gap-6"
        onSubmit={(e) => e.preventDefault()}
      >
        <div className="relative">
          <Input
            type={newPass?.showValue ? "text" : "password"}
            label="رمز عبور جدید"
            placeholder="رمز عبور جدید تعیین کنید"
            controller={setNewPass}
            value={newPass?.value}
            onBlur={() => setResponse("")}
            onFocus={() => setError("")}
            onChange={(e) => setNewPass({ ...newPass, value: e.target?.value })}
          />
          {newPasswordVisible ? (
            <Eye
              className="absolute left-3 top-[3.4rem] transform -translate-y-1/2 cursor-pointer text-light-neutral-text-low"
              onClick={() => {
                setNewPass((prev) => ({
                  ...prev,
                  showValue: !prev.showValue,
                }));
                setNewPasswordVisible(!newPasswordVisible);
              }}
              size={20}
            />
          ) : (
            <EyeClosed
              onClick={() => {
                setNewPass((prev) => ({
                  ...prev,
                  showValue: !prev.showValue,
                }));
                setNewPasswordVisible(!newPasswordVisible);
              }}
              className="absolute left-3 top-[3.4rem] transform -translate-y-1/2 cursor-pointer text-light-neutral-text-low"
              size={20}
            />
          )}
        </div>
        {error && <ErrorCard title={error} />}

        <div className="font-body-medium text-light-neutral-text-medium flex flex-col gap-2">
          <div className="flex items-center gap-2">
            <div>
              {newPass.value.length > 8 ? (
                <CheckCircle
                  className="text-light-success-text-rest"
                  size={18}
                />
              ) : (
                <Circle size={18} />
              )}
            </div>
            <span
              className={
                newPass.value.length > 8
                  ? "text-light-success-text-rest"
                  : "text-light-neutral-text-medium"
              }
            >
              حداقل ۸ کاراکتر باشد
            </span>
          </div>

          <div className="flex items-center gap-2">
            <div>
              {/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d#@$!%*#?&]{8,}$/.test(
                newPass.value
              ) ? (
                <CheckCircle
                  className="text-light-success-text-rest"
                  size={18}
                />
              ) : (
                <Circle size={18} />
              )}
            </div>
            <span
              className={
                /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d#@$!%*#?&]{2,}$/.test(
                  newPass.value
                )
                  ? "text-light-success-text-rest"
                  : "text-light-neutral-text-medium"
              }
            >
              شامل حرف انگلیسی و عدد باشد
            </span>
          </div>

          <div className="flex items-center gap-2">
            <div>
              {/(?=.*[a-z])(?=.*[A-Z])/.test(newPass.value) ? (
                <CheckCircle
                  className="text-light-success-text-rest"
                  size={18}
                />
              ) : (
                <Circle size={18} />
              )}
            </div>
            <span
              className={
                /(?=.*[a-z])(?=.*[A-Z])/.test(newPass.value)
                  ? "text-light-success-text-rest"
                  : "text-light-neutral-text-medium"
              }
            >
              شامل حداقل یک حرف بزرگ و یک حرف کوچک باشد
            </span>
          </div>

          <div className="flex items-center gap-2">
            <div>
              {/[#@$!%*#?&]{1,}/.test(newPass.value) ? (
                <CheckCircle
                  className="text-light-success-text-rest"
                  size={18}
                />
              ) : (
                <Circle size={18} />
              )}
            </div>
            <span
              className={
                /[#@$!%*#?&]{1,}/.test(newPass.value)
                  ? "text-light-success-text-rest"
                  : "text-light-neutral-text-medium"
              }
            >
              شامل حداقل یک نماد مانند #@$!%*#?& باشد
            </span>
          </div>
        </div>

        <div className="relative">
          <Input
            type={confirmPass?.showValue ? "text" : "password"}
            label="تکرار رمز عبور"
            placeholder="رمز عبور را دوباره بنویسید"
            controller={setConfirmPass}
            value={confirmPass?.value}
            className="pr-10"
            onFocus={() => {
              setConfirmPass((l) => ({ ...l, isTouched: false }));
              setError("");
            }}
            onBlur={() => {
              setConfirmPass((l) => ({ ...l, isTouched: true }));
              setResponse("");
            }}
            onChange={(e) =>
              setConfirmPass({ ...confirmPass, value: e.target?.value })
            }
          />

          {confirmPasswordVisible ? (
            <Eye
              className="absolute left-3 top-[3.4rem] transform -translate-y-1/2 cursor-pointer text-light-neutral-text-low"
              onClick={() => {
                setConfirmPass((prev) => ({
                  ...prev,
                  showValue: !prev.showValue,
                }));
                setConfirmPasswordVisible(!confirmPasswordVisible);
              }}
              size={20}
            />
          ) : (
            <EyeClosed
              onClick={() => {
                setConfirmPass((prev) => ({
                  ...prev,
                  showValue: !prev.showValue,
                }));
                setConfirmPasswordVisible(!confirmPasswordVisible);
              }}
              className="absolute left-3 top-[3.4rem] transform -translate-y-1/2 cursor-pointer text-light-neutral-text-low"
              size={20}
            />
          )}
        </div>

        {response ? (
          <SuccessCard text={response} />
        ) : (
          confirmPass.isTouched &&
          newPass.value !== confirmPass.value && (
            <ErrorCard
              title="رمز عبور و تکرار آن با هم یکسان نیستند"
              text="لطفا رمز عبور و تکرار آن را با دقت وارد نمایید"
            />
          )
        )}

        <div className="flex items-center gap-2">
          <div className="w-full" onClick={() => setStep(3)}>
            <CButton role="neutral">مرحله قبل</CButton>
          </div>
          <div className="w-full" onClick={() => setStep(5)}>
            <CButton>مرحله بعد</CButton>
          </div>
        </div>
      </form>
      <ToastContainer />
    </>
  );
};

export default SetPassword;
