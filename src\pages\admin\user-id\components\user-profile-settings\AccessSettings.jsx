import { Faders, TrashSimple, UserPlus, Users } from "@phosphor-icons/react";
import { CButton } from "components/ui/CButton";
import { useState } from "react";
import AccessSettingsTable from "./access-setting-table";
import Alert from "components/ui/Alert";
import PopUp from "components/ui/PopUp";
import UserRolePopup from "./access-setting-table/UserRolePopup";
const AccessSettings = () => {
  const [isUserRolePopupOpen, setIsUserRolePopupOpen] = useState(false);
  const [options, setOptions] = useState([
    "کاربر عادی",
    "مدیر سازمان",
    "مدیر سینپاس",
    "پشتیبان",
  ]);
  const [selectedOption, setSelectedOption] = useState("کاربر عادی");
  const [tempSelectedOption, setTempSelectedOption] = useState(selectedOption);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const handleSelectOption = (option) => {
    setTempSelectedOption(option);
    setIsDropdownOpen(false);
  };
  return (
    <>
      <div className="flex justify-center items-center">
        <div className="bg-light-neutral-surface-card w-[605px] px-6 pb-6 rounded-b-[8px]">
          <div className="mb-6">
            <Alert showCloseButton={false} type="warning">
              شما مجوز ویرایش تنظیمات دسترسی را ندارید.
            </Alert>
          </div>
          <div className="border border-light-neutral-border-low-rest rounded-lg flex items-center justify-between w-full p-4">
            <div className="flex flex-col">
              <p className="font-subtitle-large">نقش کاربر</p>
              <p className="font-body-large">{selectedOption}</p>
            </div>
            <div className="w-fit">
              <CButton
                mode="outline"
                size="sm"
                className="gap-2"
                leftIcon={<Faders size={18} />}
                onClick={() => {
                  setIsUserRolePopupOpen(true);
                  setTempSelectedOption(selectedOption);
                }}
              >
                تغییر نقش کاربر
              </CButton>
            </div>
          </div>
          <div className="border border-light-neutral-border-low-rest rounded-lg my-6">
            <div className="flex items-center justify-between w-full p-4">
              <div className="flex flex-col ">
                <p className="font-subtitle-large">گروه‌های کاربر</p>
              </div>
              <div className="w-fit">
                <CButton
                  mode="outline"
                  size="sm"
                  className="gap-2"
                  leftIcon={<Users size={18} />}
                >
                  افزودن به گروه جدید
                </CButton>
              </div>
            </div>
            <div className="p-4">
              <p className="font-body-small text-light-neutral-text-low">
                لیست گروه‌هایی که کاربر در آن‌ها عضو است. کاربران یک گروه
                فعالیت‌های یکدیگر را می‌توانند مشاهده کنند
              </p>

              <div className="py-4 flex flex-col gap-4">
                <ul className="font-overline-medium text-light-neutral-text-medium grid grid-cols-3 select-none">
                  <li>نام گروه</li>
                  <li>تاریخ عضویت</li>
                  <li className="text-center">اقدامات</li>
                </ul>
                <div className="grid grid-cols-3 hover:bg-light-neutral-surface-highlight py-2 font-body-medium">
                  <div>پشتیبان به کاربر عادی</div>
                  <div>۱۴۰۲/۱۱/۰۱ - ۱۲:۳۴ </div>
                  <div className="text-center bg-light-error-background-highlight w-fit p-1 rounded-md cursor-pointer m-auto">
                    <TrashSimple className="text-light-error-background-rest" />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="flex justify-center items-center mt-5  border border-light-neutral-border-low-rest rounded-lg">
            <div className="bg-light-neutral-surface-card w-[605px] p-4 rounded-[8px]">
              <div className="flex items-center justify-between">
                <p className="font-subtitle-large">کاربران زیرمجموعه</p>
                <div className="w-fit">
                  <CButton
                    mode="outline"
                    size="sm"
                    className="gap-2"
                    leftIcon={<UserPlus size={18} />}
                  >
                    کاربر زیرمجموعه جدید
                  </CButton>
                </div>
              </div>
              <p className="font-body-small text-light-neutral-text-low mt-4">
                لیست افرادی که این کاربر می‌تواند فعالیت‌های آن‌ها را ببیند.
                کاربران زیرمجموعه نمی‌تواند فعالیت‌های کاربر بالاتر را ببیند
              </p>
              <AccessSettingsTable className="!p-0" />
            </div>
          </div>
          <div className="flex items-center gap-3 mt-6">
            <div className="w-full">
              <CButton role="neutral">انصراف</CButton>
            </div>
            <div className="w-full">
              <CButton>ذخیره</CButton>
            </div>
          </div>
        </div>
      </div>
      <PopUp
        isOpen={isUserRolePopupOpen}
        onClose={() => {
          setIsUserRolePopupOpen(false);
          setTempSelectedOption(selectedOption);
        }}
        submitHandler={() => {
          setSelectedOption(tempSelectedOption);
          setIsUserRolePopupOpen(false);
        }}
        agreeButton="ذخیره"
      >
        <p className="font-body-bold-large">تغییر نقش کاربر</p>
        <UserRolePopup
          isDropdownOpen={isDropdownOpen}
          handleSelectOption={handleSelectOption}
          selectedOption={tempSelectedOption}
          options={options}
          setIsDropdownOpen={setIsDropdownOpen}
        />
      </PopUp>
    </>
  );
};
export default AccessSettings;
