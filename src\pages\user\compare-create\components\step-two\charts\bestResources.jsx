import { memo, useEffect, useState } from "react";
import Divider from "components/ui/Divider.jsx";
import advanceSearch from "service/api/advanceSearch.js";
import DropDown from "components/ui/DropDown.jsx";
import { parseNumber } from "utils/helper.js";
import { SpinnerGap, UserSwitch } from "@phosphor-icons/react";
import { useCompareStore } from "store/compareStore.js";
import { buildRequestData } from "utils/requestData";
import { RESOURCE_SORT_TYPE } from "constants/sort-type.js";
import { useNavigate } from "react-router-dom";
import Popup from "components/ui/PopUp";
import ExportMenu from "components/ExportMenu/index.jsx";

const BestResource = () => {
  const { platform, date, fields } = useCompareStore((state) => state.compare);
  const [data, setData] = useState([]);
  const [result, setResult] = useState([]);
  const [selectedItem, setSelectedItem] = useState(null);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [sort, setSort] = useState({ fa: "محتوا", en: "date" });
  const [fieldsCleared, setFieldsCleared] = useState([]);
  const navigate = useNavigate();

  // Update fieldsCleared only when relevant field property (q) changes
  useEffect(() => {
    const newFieldsCleared = fields.filter((value) => value?.q);
    const hasChanged =
      newFieldsCleared.length !== fieldsCleared.length ||
      newFieldsCleared.some((field, index) => {
        const prevField = fieldsCleared[index] || {};
        return field.q !== prevField.q;
      });

    if (hasChanged) {
      console.log("fieldsCleared updated:", newFieldsCleared);
      setFieldsCleared(newFieldsCleared);
    }
  }, [fields]);

  const chartHeader = ["منبع", `تعداد ${sort.fa}`];

  function transposeArray(array) {
    return array[0].map((_, colIndex) => array.map((row) => row[colIndex]));
  }

  const getData = async (fieldsCleared) => {
    setLoading(true);
    try {
      const reqsQueries = fieldsCleared.map((field) => {
        let requestData;
        const filters = {
          date,
          platform,
          q: field?.q || "",
          sort: sort.en,
        };
        requestData = buildRequestData(filters, "top_sources", 5);
        return advanceSearch.search(requestData);
      });
      const response = await Promise.allSettled(reqsQueries);
      const result = response
        .filter((res) => res.status === "fulfilled")
        .map((res) => {
          return res?.value?.data?.data?.[platform]?.slice(0, 5) || [];
        });

      const list = [];
      console.log("result");
      console.log(result);
      let maxLength = Math.max(...result.map((item) => item.length));
      console.log(maxLength);
      for (let i = 0; i < maxLength; i++) {
        for (let j = 0; j < result.length; j++) {
          list.push(result[j][i] || {});
        }
      }
      console.log(list);
      setData(list);
      setResult(result);
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  const handleProfileClick = (profile) => {
    try {
      profile.platform = platform;
      return navigate(
        `/app/report-360/report/source/${platform}/${profile.key}`
      );
    } catch (e) {
      console.log(e);
    }
  };

  const openConfirmPopup = (item) => {
    setSelectedItem(item);
    setIsPopupOpen(true);
  };

  const closeConfirmPopup = () => {
    setIsPopupOpen(false);
  };

  const submitHandler = () => {
    if (selectedItem) {
      handleProfileClick(selectedItem);
      setIsPopupOpen(false);
    }
  };

  // Fetch data when fieldsCleared, platform, sort, or date changes
  useEffect(() => {
    console.log("Fetch effect triggered with:", {
      fieldsCleared,
      platform,
      sort,
      date,
    });
    if (fieldsCleared.length) {
      getData(fieldsCleared);
    }
  }, [fieldsCleared, platform, sort, date]);

  // Prepare series and time for ExportMenu
  const series = fieldsCleared.map((field, index) => ({
    name: field.q || `Source ${index + 1}`,
    data: data
      .filter((_, i) => i % fieldsCleared.length === index)
      .map((item) => item?.count || 0),
    time: data
      .filter((_, i) => i % fieldsCleared.length === index)
      .map((item) => item?.title || item?.key || ""),
  }));

  const time = data.map((item) => item?.title || item?.key || "");

  if (!fields.filter((user) => user.id || user.q).length) {
    return null;
  }

  return (
    <>
      <div className="flex best-resource-container">
        <div className="bg-white w-full">
          <div className="flex items-center justify-between px-10 pt-4">
            <div className="font-subtitle-large">برترین منابع</div>
            <div className="flex flex-row items-center gap-4 [direction:rtl] relative z-20">
              {data.length !== 0 && (
                <DropDown
                  title="نمایش بر اساس"
                  subsets={RESOURCE_SORT_TYPE[platform].map((item) => item.fa)}
                  selected={sort.fa}
                  setSelected={(value) => {
                    setSort(
                      RESOURCE_SORT_TYPE[platform].filter(
                        (item) => item.fa === value
                      )[0]
                    );
                  }}
                />
              )}
              <ExportMenu
                chartSelector=".best-resource-container"
                fileName="best-resources"
                series={series}
                time={time}
                excelHeaders={["Source", ...series.map((s) => s.name)]}
                onError={(error) => console.error("Export error:", error)}
                menuItems={["PNG", "JPEG", "Excel"]}
              />
            </div>
          </div>
          <div className="py-4">
            <Divider />
          </div>

          <div className="relative pb-4">
            <div className="w-full flex gap-0 h-86">
              {loading ? (
                <div className="w-full h-[270px] flex justify-center items-center ">
                  <SpinnerGap size={40} className="animate-spin" />
                </div>
              ) : (
                <>
                  {data.length === 0 ? (
                    <div className="h-[270px] flex items-center justify-center font-subtitle-medium">
                      {!loading && "داده ای برای نمایش وجود ندارد"}
                    </div>
                  ) : (
                    <div className="flex w-full gap-0">
                      {result.map((list, index) => (
                        <div
                          key={index}
                          className={`flex flex-col gap-1 w-1/${
                            result.length
                          } ${index !== 0 ? "border-r-2" : ""}`}
                        >
                          <div className="grid grid-cols-2 items-center gap-6 px-8 font-body-medium text-light-neutral-text-medium mb-3">
                            {chartHeader.map((headerItem, headerIndex) => (
                              <div
                                key={headerIndex}
                                className={`flex-1 min-w-[5rem] ${
                                  headerIndex === 1 ? "text-left" : ""
                                }`}
                              >
                                {headerItem}
                              </div>
                            ))}
                          </div>
                          {list.map((item, index) => (
                            <div
                              className={`grid grid-cols-2 items-center px-8 p-1 hover:cursor-pointer hover:bg-light-primary-background-highlight`}
                              key={item?.[0]?.id || item?.id || index}
                              onClick={() => openConfirmPopup(item)}
                            >
                              <div className="flex gap-2 items-center">
                                <div
                                  className="size-10 rounded-full"
                                  style={{
                                    backgroundImage: `url(${item.avatar}), url(/logo_small.png)`,
                                    backgroundRepeat: "no-repeat",
                                    backgroundPosition: "center center",
                                    backgroundSize: "contain",
                                  }}
                                ></div>
                                <div>
                                  <div className="font-subtitle-medium">
                                    {item?.title?.slice(0, 20) || item?.title}
                                  </div>
                                  <div className="font-overline-medium">
                                    {item.key &&
                                      `${item.key}${
                                        platform !== "news" ? "@" : ""
                                      }`}
                                  </div>
                                </div>
                              </div>
                              <div className="flex-none text-left font-body-bold-medium">
                                {parseNumber(item?.count || 0)}
                              </div>
                            </div>
                          ))}
                        </div>
                      ))}
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
      <Popup
        isOpen={isPopupOpen}
        onClose={closeConfirmPopup}
        submitHandler={submitHandler}
        title="آیا می‌خواهید گزارش های ۳۶۰ این منبع نمایش داده شود؟"
        agreeButton="بله"
        cancleButton="خیر"
        icon={<UserSwitch size={45} />}
      >
        <p className="py-5 font-body-medium">
          توجه کنید که با کلیک برروی گزینه (( بله )) به صفحه گزارشات ۳۶۰ این
          منبع منتقل خواهید شد.
        </p>
      </Popup>
    </>
  );
};

export default memo(BestResource);
