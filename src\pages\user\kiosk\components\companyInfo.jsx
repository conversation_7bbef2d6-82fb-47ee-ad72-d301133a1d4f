import PropTypes from "prop-types";
import Divider from "components/ui/Divider";
import { useNavigate } from "react-router-dom";
import { Card } from "components/ui/Card";
import { ArrowSquareOut, Copy } from "@phosphor-icons/react";
import { useState } from "react";
import { toPersianNumber } from "utils/helper.js";

const CompanyInfo = ({ data, newsPaperId }) => {
  const navigate = useNavigate();
  const information = data?.information || [];
  const [copiedText, setCopiedText] = useState("");

  const categoryTranslations = {
    political: "سیاسی",
    economic: "اقتصادی",
    social: "اجتماعی",
    culture: "فرهنگی",
    sport: "ورزشی",
    environment: "محیط زیست",
    international: "بین‌الملل",
    other: "سایر",
  };

  const handleCopy = (text) => {
    const textarea = document.createElement("textarea");
    textarea.value = text;
    document.body.appendChild(textarea);
    textarea.select();
    document.execCommand("copy");
    document.body.removeChild(textarea);
    setCopiedText(text);
    setTimeout(() => {
      setCopiedText("");
    }, 2000);
  };

  return (
    <Card className="!bg-white !w-[35%] flex flex-col duration-300 shadow-md p-4">
      <div>
        <div className="flex gap-5 items-center">
          <div
            className="h-[4.5rem] w-[4.5rem] flex justify-center items-center rounded-[130px] shadow-xl transition-shadow duration-300 hover:shadow-xl cursor-pointer"
            onClick={() =>
              navigate(
                `/app/newspaper/special-newspaper/${data?.agency_id}/${information?.agency}`,
                { state: { id: newsPaperId } },
              )
            }
          >
            <img
              src={`${information?.logo_image}`}
              className="h-[4.5rem] w-[4.5rem] bg-[#FFFFFF] flex justify-center items-center rounded-[130px] shadow-md transition-shadow duration-300 hover:shadow-xl cursor-pointer"
              alt={`Avatar for ${data?.agency_en}`}
            />
          </div>
          <div className="flex gap-2 flex-col">
            <span className="font-headline-medium">{information?.agency}</span>
            <div>
              <span className="font-body-medium text-[#7f7f7f] pl-1">
                دسته‌بندی:
              </span>
              {information?.categories && (
                <span className="font-body-large">
                  {information.categories.length === 1
                    ? categoryTranslations[information.categories]
                    : information.categories
                        .map((category) => categoryTranslations[category])
                        .join(", ")}
                </span>
              )}
            </div>
          </div>
        </div>
        <p className="pt-6 font-body-medium text-[#7f7f7f]">
          {information?.description}
        </p>
        <h6 className="font-bold pt-6">شناسنامه</h6>
        <div className="flex justify-between font-body-medium">
          <div className="flex flex-col gap-4 py-4 text-[#7f7f7f]">
            <span> امتیاز</span>
            <span>مدیر مسئول</span>
            <span>سردبیر</span>
          </div>
          <div className="flex flex-col gap-4 py-4 text-left">
            <span>{information?.concessioner ?? "-"}</span>
            <span>{information?.manager ?? "-"}</span>
            <span>{information?.lead_editor ?? "-"}</span>
          </div>
        </div>
      </div>
      <Divider />
      <div>
        <div className="flex justify-between font-body-medium">
          <div className="flex flex-col gap-12 py-4 text-[#7f7f7f]">
            <span>تلفن</span>
          </div>
          <div className="flex flex-col gap-5 py-4 text-left">
            <div className="flex flex-col gap-3">
              <div
                style={{ display: "flex", flexDirection: "column", gap: "8px" }}
              >
                {information?.phone?.length > 0 ? (
                  information.phone.map((phone, index) => (
                    <span key={index} className="flex justify-between gap-2">
                      {toPersianNumber(phone)}
                      <div className="relative">
                        <Copy
                          className={`cursor-pointer hover:text-[#6F5CD1] ${
                            copiedText === phone ? "text-[#6F5CD1]" : ""
                          }`}
                          size={15}
                          onClick={() => handleCopy(phone)}
                        />
                        {copiedText === phone && (
                          <span className="absolute -top-8 left-[5%] text-xs bg-gray-800 transition-all duration-300 text-white px-2 py-1 rounded whitespace-nowrap">
                            کپی شد
                          </span>
                        )}
                      </div>
                    </span>
                  ))
                ) : (
                  <span>-</span>
                )}
              </div>
            </div>
          </div>
        </div>
        <Divider />
      </div>
      <div>
        <div className="flex justify-between font-body-medium">
          <div className="flex flex-col gap-4 py-4 text-[#7f7f7f]">
            <span>وب‌سایت</span>
            <span>ایمیل</span>
          </div>
          <div className="flex flex-col gap-4 py-4 !text-left">
            <div className="flex justify-end gap-2">
              <a
                href={information?.website}
                target="_blank"
                className="hover:underline hover:text-blue-500 transition-all duration-300"
                rel="noopener noreferrer"
              >
                {information?.website ?? "-"}
              </a>
              <ArrowSquareOut
                className="cursor-pointer hover:text-[#6F5CD1]"
                size={15}
              />
            </div>
            <div className="flex justify-between gap-2">
              <span className="flex-1">{information?.email ?? "-"}</span>
              {information?.email && (
                <div className="relative">
                  <Copy
                    size={15}
                    className={`cursor-pointer hover:text-[#6F5CD1] ${
                      copiedText === information?.email ? "text-[#6F5CD1]" : ""
                    }`}
                    onClick={() => handleCopy(information?.email)}
                  />
                  {copiedText === information?.email && (
                    <span className="absolute -top-8 right-0 text-xs bg-gray-800 transition-all duration-300 text-white px-2 py-1 rounded whitespace-nowrap">
                      کپی شد
                    </span>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
        <Divider />
      </div>
      <div className="font-body-medium">
        <span className="block py-3 text-[#7f7f7f]">نشانی</span>
        <p className="flex gap-2 justify-between">
          {information?.address ?? "-"}
          {information?.address && (
            <div className="relative">
              <Copy
                size={15}
                className={`cursor-pointer hover:text-[#6F5CD1] ${
                  copiedText === information?.address ? "text-[#6F5CD1]" : ""
                }`}
                onClick={() => handleCopy(information?.address)}
              />
              {copiedText === information?.address && (
                <span className="absolute -top-8 right-0 text-xs bg-gray-800 text-white px-2 py-1 rounded whitespace-nowrap">
                  کپی شد
                </span>
              )}
            </div>
          )}
        </p>
      </div>
    </Card>
  );
};

export default CompanyInfo;

CompanyInfo.propTypes = {
  data: PropTypes.shape({
    agency_en: PropTypes.string,
    cover_image: PropTypes.string,
    agency_id: PropTypes.number,
    categories: PropTypes.arrayOf(PropTypes.string),
    information: PropTypes.arrayOf(
      PropTypes.shape({
        description: PropTypes.string,
        concessioner: PropTypes.string,
        manager: PropTypes.string,
        lead_editor: PropTypes.string,
        website: PropTypes.string,
        email: PropTypes.string,
        address: PropTypes.string,
      }),
    ),
  }),
};
