// src/components/RepeatHashtags.jsx
import { useState, useEffect, memo } from "react";
import HorizontalBar from "components/Charts/HorizontalBar.jsx";
import { useCompareStore } from "store/compareStore.js";
import { Cloud, FunnelSimple, SpinnerGap } from "@phosphor-icons/react";
import { Card } from "components/ui/Card.jsx";
import ReactWordcloud from "react-wordcloud";
import CLUSTER_COLORS from "constants/colors.js";
import PropTypes from "prop-types";
import { CTabs } from "components/ui/CTabs.jsx";
import advanceSearch from "service/api/advanceSearch.js";
import Title from "./Title.jsx";
import { buildRequestData } from "utils/requestData.js";
import { toPersianNumber } from "utils/helper.js";
import ExportMenu from "components/ExportMenu/index.jsx";

const RepeatHashtags = ({ compareType }) => {
  const { fields, platform, date } = useCompareStore((state) => state.compare);
  const [categories, setCategories] = useState([]);
  const [wordCloudActiveTab, setWordCloudActiveTab] = useState("wordCloud");
  const [wordCloud, setWordCloud] = useState([]);
  const [info, setInfo] = useState([]);
  const [loading, setLoading] = useState(false);
  const [fieldsCleared, setFieldsCleared] = useState([]);

  // Update fieldsCleared only when relevant field properties change
  useEffect(() => {
    const newFieldsCleared = fields.filter((value) =>
      compareType === "profile" ? value?.id || value?.user_id : value?.q
    );
    const hasChanged =
      newFieldsCleared.length !== fieldsCleared.length ||
      newFieldsCleared.some((field, index) => {
        const prevField = fieldsCleared[index] || {};
        return compareType === "profile"
          ? field.id !== prevField.id || field.user_id !== prevField.user_id
          : field.q !== prevField.q;
      });

    if (hasChanged) {
      console.log("fieldsCleared updated:", newFieldsCleared);
      setFieldsCleared(newFieldsCleared);
    }
  }, [fields, compareType]);

  const fetchProfileData = async (id, index, abortController) => {
    const filters = {
      date,
      platform,
      sources: fieldsCleared[index]?.id
        ? [fieldsCleared[index]?.id.toString()]
        : fieldsCleared[index]?.user_id
        ? [fieldsCleared[index]?.user_id.toString()]
        : [],
    };

    let infoQuery = buildRequestData(filters, "cloud", 30);
    const response = await advanceSearch.search(infoQuery, abortController, {
      cloud_type: "hashtags",
    });
    return response.data.data?.[platform];
  };

  const fetchTopicData = async (index, abortController) => {
    const filters = {
      date,
      platform,
      q: fieldsCleared[index]?.q,
    };
    let infoQuery = buildRequestData(filters, "cloud", 30);
    const response = await advanceSearch.search(infoQuery, abortController, {
      cloud_type: "hashtags",
    });
    return response.data.data?.[platform];
  };

  const updateWordCloudState = (result, index) => {
    const arabicRange = /[\u0600-\u06FF]/;
    setWordCloud((prev) => {
      const updatedWordClouds = [...prev];
      updatedWordClouds[index] =
        result?.map(({ word, count, key }) => {
          if (!arabicRange.test(word)) {
            if (word?.startsWith("#")) {
              word = word?.slice(1) + "#";
            }
          }
          return { text: word || key, value: count };
        }) || [];
      return updatedWordClouds;
    });

    setInfo((prev) => {
      const updatedInfo = [...prev];
      updatedInfo[index] = result;
      return updatedInfo;
    });

    setCategories((prev) => {
      const updatedCategories = [...prev];
      updatedCategories[index] = result
        ?.slice(0, 10)
        ?.map((value) => value.word || value?.key);
      return updatedCategories;
    });
  };

  const fetchWordCloudData = async (id, index, abortController) => {
    setLoading(true);
    try {
      const result =
        compareType === "profile"
          ? await fetchProfileData(id, index, abortController)
          : await fetchTopicData(index, abortController);

      if (!abortController.signal.aborted) {
        updateWordCloudState(result, index);
      }
    } catch (error) {
      if (!abortController.signal.aborted)
        console.error("Error fetching word cloud data", error);
    } finally {
      if (!abortController.signal.aborted) setLoading(false);
    }
  };

  // Fetch data when fieldsCleared or date changes
  useEffect(() => {
    const abortController = new AbortController();
    console.log("Fetch effect triggered with:", {
      fieldsCleared,
      date,
      platform,
    });
    if (fieldsCleared.length > 0) {
      setWordCloud(new Array(fieldsCleared.length).fill([]));
      fieldsCleared.forEach((field, index) =>
        fetchWordCloudData(field.id, index, abortController)
      );
    }
    return () => abortController.abort();
  }, [fieldsCleared, date, platform]);

  const onClusterTabChange = (name) => {
    setWordCloudActiveTab(name);
  };

  const dataColors = {
    yellow: ["#F7A912"],
    blue: ["#6D72E5"],
    green: ["#1DCEA3"],
    purple: ["#DB6DE5"],
  };

  const colorAssignments = [];
  fieldsCleared.forEach((_, index) => {
    if (index % 4 === 0) {
      colorAssignments.push(dataColors.green[index % dataColors.green.length]);
    } else if (index % 4 === 1) {
      colorAssignments.push(dataColors.blue[index % dataColors.blue.length]);
    } else if (index % 4 === 2) {
      colorAssignments.push(
        dataColors.purple[index % dataColors.purple.length]
      );
    } else {
      colorAssignments.push(
        dataColors.yellow[index % dataColors.yellow.length]
      );
    }
  });

  // Prepare series and time for ExportMenu
  const series = fieldsCleared.map((field, index) => ({
    name: field.q || field.name || field.user_name || `Hashtag ${index + 1}`,
    data: wordCloud[index]?.map((word) => word.value) || [],
    time: wordCloud[index]?.map((word) => word.text) || [],
  }));

  const time = wordCloud.flatMap((words) => words.map((word) => word.text));

  if (loading) {
    return (
      <div className="flex chart-container">
        <Card
          className={`flex flex-col gap-4 ${
            fieldsCleared.length < 4
              ? "w-[83%]"
              : fieldsCleared.length === 4
              ? "w-full"
              : null
          }`}
        >
          <div className="!flex w-full h-80 justify-center items-center">
            <SpinnerGap size={40} className="animate-spin" />
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex compare-hashtags-container">
      {fieldsCleared.length > 0 && (
        <Card className="px-0">
          <div className="w-full flex flex-col gap-6">
            <div className="px-6 flex items-center justify-between">
              <Title title="هشتگ‌های پر تکرار" />
              <div className="flex items-center gap-4">
                <div className="flex" id={"repeatHashtagTabs"}>
                  <CTabs
                    tabArray={[
                      {
                        id: "wordCloud",
                        title: "ابر کلمات",
                        icon: Cloud,
                      },
                      {
                        id: "cluster",
                        title: "نمودار",
                        icon: FunnelSimple,
                      },
                    ]}
                    activeTab={wordCloudActiveTab}
                    onChange={onClusterTabChange}
                  />
                </div>
                <ExportMenu
                  chartSelector=".compare-hashtags-container"
                  fileName={`hashtags-${wordCloudActiveTab}`}
                  series={series}
                  time={time}
                  excelHeaders={["Hashtag", ...series.map((s) => s.name)]}
                  onError={(error) => console.error("Export error:", error)}
                  menuItems={["PNG", "JPEG", "Excel"]}
                />
              </div>
            </div>
            <div
              className="grid divide-x-[1px] divide-x-reverse"
              style={{
                gridTemplateColumns: `repeat(${fieldsCleared.length}, 1fr)`,
              }}
            >
              {wordCloudActiveTab === "wordCloud" &&
                fieldsCleared?.map((item, index) => (
                  <div className="flex flex-1 responsive-svg" key={index}>
                    {info[index]?.length ? (
                      <ReactWordcloud
                        options={{
                          rotations: 1,
                          rotationAngles: [0],
                          enableTooltip: true,
                          deterministic: false,
                          fontFamily: "iranyekan",
                          fontSizes: [14, 54],
                          padding: 10,
                          colors: CLUSTER_COLORS,
                          tooltipOptions: { theme: "light", arrow: true },
                        }}
                        words={wordCloud[index] || []}
                        callbacks={{
                          getWordTooltip: (word) => {
                            if (word.text.endsWith("#")) {
                              return `${word.text?.slice(
                                0,
                                word.text.length - 1
                              )} (${toPersianNumber(word.value)})`;
                            }
                            return `${word.text} (${toPersianNumber(
                              word.value
                            )})`;
                          },
                        }}
                      />
                    ) : (
                      <div className="w-full h-40 flex justify-center pt-4 items-center text-lg text-gray-600">
                        داده ای برای نمایش وجود ندارد
                      </div>
                    )}
                  </div>
                ))}

              {wordCloudActiveTab === "cluster" &&
                fieldsCleared?.map((item, index) => (
                  <HorizontalBar
                    colors={[colorAssignments[index]]}
                    data={item}
                    info={info[index]}
                    key={item?.id}
                    loading={loading}
                    categories={categories[index]}
                    minWidth={"15rem"}
                  />
                ))}
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

RepeatHashtags.propTypes = {
  compareType: PropTypes.string.isRequired,
};

export default memo(RepeatHashtags);
