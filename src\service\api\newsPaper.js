import useFetch from "../index";

class Newspaper {
  get() {
    return useFetch.get("/api/v1/newspaper/");
  }
  post(data) {
    return useFetch.post("/api/v1/newspaper/", data);
  }
  getById(id) {
    return useFetch.get(`/api/v1/newspaper/get/${id}/`);
  }
  info(id) {
    return useFetch.get(`/api/v1/newspaper/info/${id}/`);
  }
  getTop() {
    return useFetch.get("/api/v1/newspaper/top/");
  }

  searchSuggest(q) {
    return useFetch.post(`/api/v1/newspaper/source_suggest/`, q);
  }
}
export default new Newspaper();
