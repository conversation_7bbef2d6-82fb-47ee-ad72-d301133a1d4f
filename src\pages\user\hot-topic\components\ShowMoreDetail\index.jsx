import Divider from "components/ui/Divider";
import TextWrapper from "components/TextWrapper";
import SentimentAnalysis from "./SentimentAnalysis";
import SummaryCard from "components/SummaryCard";
import TopicCommunication from "./TopicCommunication";
import PropTypes from "prop-types";
import PostType from "./PostType";

const ShowMoreDetail = ({
  media,
  data,
  handleClickOnAvatar,
  platform = false,
  isShowMore = false,
}) => {
  return (
    <div className="flex flex-col gap-4 min-h-screen overflow-hidden">
      <SummaryCard
        data={data}
        media={platform || media}
        showMediaName
        isDrawer={true}
        isShowMore={isShowMore}
        showHeaderMenu={false}
        handleClickOnAvatar={handleClickOnAvatar}
      >
        <TextWrapper media={media}>{`${data?.description || ""} \n ${
          data?.content || data?.text
        }`}</TextWrapper>
      </SummaryCard>
      <Divider />

      {data?.sentiment?.label && (
        <>
          <SentimentAnalysis data={data} />
          <Divider />
        </>
      )}

      {data?.category ? <TopicCommunication data={data} /> : ""}
      {media === "twitter" && (
        <>
          <Divider />
          <PostType />
        </>
      )}

      {/*<Existence data={data}/> */}
    </div>
  );
};
ShowMoreDetail.propTypes = {
  media: PropTypes.object,
  data: PropTypes.object.isRequired,
  handleClickOnAvatar: PropTypes.func.isRequired,
  platform: PropTypes.bool,
  isShowMore: PropTypes.bool,
};

export default ShowMoreDetail;
