import Areaspline from "components/Charts/Areaspline";

const ContentShareFlow = ({ data }) => {
  return (
    <div
      className="bg-light-neutral-surface-card rounded-lg p-3"
      style={{
        boxShadow: "0px 2px 20px 0px #0000000D",
      }}
    >
      <div className="flex items-center justify-between mb-8">
        <p className="font-subtitle-large">روند انتشار محتوا</p>
        {/* <ul className="flex gap-2 *:font-overline-medium *:p-2 *:rounded-md *:cursor-pointer">
          <li
            className={`${
              time_gap === "week"
                ? "bg-light-neutral-background-high"
                : "bg-light-neutral-background-low"
            }`}
            onClick={() => {
              setProcess_range("weekly");
              setTime_gap("week");
            }}
          >
            هفتگی
          </li>
          <li
            className={`${
              time_gap === "day"
                ? "bg-light-neutral-background-high"
                : "bg-light-neutral-background-low"
            }`}
            onClick={() => {
              setProcess_range("daily");
              setTime_gap("day");
            }}
          >
            روزانه
          </li>
          <li
            className={`${
              time_gap === "hour"
                ? "bg-light-neutral-background-high"
                : "bg-light-neutral-background-low"
            }`}
            onClick={() => {
              setProcess_range("hourly");
              setTime_gap("hour");
            }}
          >
            ساعتی
          </li>
        </ul> */}
      </div>
      <Areaspline
        cat={data?.twitter?.map((item) => item?.datetime)}
        time_gap={"hour"}
        data={data?.twitter?.map((item) => item?.count)}
      />
    </div>
  );
};
export default ContentShareFlow;
