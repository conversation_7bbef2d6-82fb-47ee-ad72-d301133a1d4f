import { useEffect, useState } from "react";
import Bar from "components/Charts/Bar";
import Divider from "components/ui/Divider";
import { generateStatsQuery } from "utils/stats";
import advanceSearch from "service/api/advanceSearch";
import { fixPercentToShow } from "utils/helper";
import { SpinnerGap } from "@phosphor-icons/react";
import PLATFORMS from "constants/platforms.js";
import PropTypes from "prop-types";
import useSearchStore from "store/searchStore.js";

const Age = () => {
  const { filters, query } = useSearchStore();

  const [data, setData] = useState({
    teen: 0,
    young: 0,
    adult: 0,
    old: 0,
  });
  const [loading, setLoading] = useState(false);
  const from = parseInt(new Date(filters?.date?.from).getTime() / 1000);
  const to = parseInt(new Date(filters?.date?.to).getTime() / 1000);
  const percent = (value, all) => {
    const sum = Object.values(all).reduce((prev, cur) => prev + cur, 0);
    return (value / sum) * 100;
  };
  const getData = async () => {
    setLoading(true);
    try {
      const res = await advanceSearch.search({
        q: generateStatsQuery(
          filters.platform,
          "age",
          {
            from,
            to,
            q: query,
            fields: ["adult", "old", "young", "teen"],
            count: 10,
          },
          filters
        ),
      });
      const result = JSON.parse(res.data.data).result.data;
      const prasedData = {
        teen: percent(result.teen, result),
        young: percent(result.young, result),
        adult: percent(result.adult, result),
        old: percent(result.old, result),
      };
      setData(prasedData);
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  useEffect(() => {
    if (
      filters.platform === PLATFORMS.TELEGRAM ||
      filters.platform === PLATFORMS.NEWS
    )
      return;
    getData();
  }, [query, filters]);

  if (
    filters.platform === PLATFORMS.TELEGRAM ||
    filters.platform === PLATFORMS.NEWS
  )
    return (
      <div className="flex flex-col gap-4 justify-between">
        <p className="font-subtitle-large text-right">دسته‌بندی سن</p>
        <Divider />
        {loading ? (
          <div className="w-full h-[365px] flex justify-center items-center">
            <SpinnerGap size={40} className="animate-spin" />
          </div>
        ) : Object.values(data).filter(Boolean).length === 0 ? (
          <div className="h-full flex items-center justify-center font-subtitle-medium">
            داده ای برای نمایش وجود ندارد
          </div>
        ) : (
          <>
            <Bar data={data} />
            <div className="flex flex-col gap-2 *:flex *:justify-between">
              <div>
                <div className="font-body-large">
                  {fixPercentToShow(String(data.teen.toFixed(2) / 100))}
                </div>
                <div className="font-body-medium text-[#43B3E4] flex gap-1">
                  <p>نوجوان</p>
                  <img src="/icons/teenager-small.svg" alt="icon" />
                </div>
              </div>
              <div>
                <div className="font-body-large">
                  {fixPercentToShow(String(data.young.toFixed(2) / 100))}
                </div>
                <div className="font-body-medium text-[#2A7DE0] flex gap-1">
                  <p>جوان</p>
                  <img src="/icons/young-small.svg" alt="icon" />
                </div>
              </div>
              <div>
                <div className="font-body-large">
                  {fixPercentToShow(String(data.adult.toFixed(2) / 100))}
                </div>
                <div className="font-body-medium text-[#3144EF] flex gap-1">
                  <p>میانسال</p>
                  <img src="/icons/middleage-small.svg" alt="icon" />
                </div>
              </div>
              <div>
                <div className="font-body-large">
                  {fixPercentToShow(String(data.old.toFixed(2) / 100))}
                </div>
                <div className="font-body-medium text-[#5911CF] flex gap-1">
                  <p>سالخورده</p>
                  <img src="/icons/old-small.svg" alt="icon" />
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    );
};

Age.propTypes = {
  platform: PropTypes.string,
  req_data: PropTypes.object,
  query: PropTypes.string,
};

export default Age;
