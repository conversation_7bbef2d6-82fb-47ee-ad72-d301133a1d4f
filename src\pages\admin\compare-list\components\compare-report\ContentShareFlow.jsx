import { Info } from "@phosphor-icons/react";
import Areaspline from "components/Charts/Areaspline";
import ToolTip from "components/ui/ToolTip";
import { useState } from "react";
import { Card } from "components/ui/Card.jsx";

const ContentShareFlow = () => {
  const [data, setData] = useState([150, 200, 180, 300, 250, 400]);
  const [loading, setLoading] = useState(false);
  const [time_gap, setTime_gap] = useState("hour");
  const [process_range, setProcess_range] = useState("hourly");
  return (
    <>
      <Card className="flex flex-col gap-2 h-full">
        <div className="flex items-center justify-between mb-2">
          <p className="font-subtitle-large">روند ایجاد مقایسه</p>
          <ul className="flex gap-2 *:font-overline-medium *:p-2 *:rounded-md *:cursor-pointer">
            <li
              className={`${
                time_gap === "week"
                  ? "bg-light-neutral-background-high"
                  : "bg-light-neutral-background-low"
              }`}
              onClick={() => {
                setProcess_range("weekly");
                setTime_gap("week");
              }}
            >
              هفتگی
            </li>
            <li
              className={`${
                time_gap === "day"
                  ? "bg-light-neutral-background-high"
                  : "bg-light-neutral-background-low"
              }`}
              onClick={() => {
                setProcess_range("daily");
                setTime_gap("day");
              }}
            >
              روزانه
            </li>
            <li
              className={`${
                time_gap === "hour"
                  ? "bg-light-neutral-background-high"
                  : "bg-light-neutral-background-low"
              }`}
              onClick={() => {
                setProcess_range("hourly");
                setTime_gap("hour");
              }}
            >
              ساعتی
            </li>
          </ul>
        </div>

        <Areaspline cat={[]} time_gap={time_gap} data={data} />
      </Card>
    </>
  );
};

export default ContentShareFlow;
