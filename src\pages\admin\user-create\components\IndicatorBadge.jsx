import PropTypes from "prop-types";
import { toPersianNumber } from "utils/helper.js";

const IndicatorBadge = ({ children, step, state }) => {
  return (
    <div className="flex items-center gap-4">
      <div
        className="flex justify-center items-center font-body-medium size-6 border border-light-neutral-border-medium-rest text-light-neutral-text-medium rounded-lg"
        style={{
          ...(state === step
            ? {
                color: "#000000",
                borderColor: "#8f81d3",
                backgroundColor: "#ded9f5",
              }
            : state > step
              ? {
                  color: "#000000",
                  borderColor: "#19A399",
                  backgroundColor: "#73EBE24D",
                }
              : {}),
        }}
      >
        {toPersianNumber(step)}
      </div>
      {/*{state >= step && <p className="font-body-medium">{children}</p>}*/}
      <p className="font-body-medium">{children}</p>
    </div>
  );
};

IndicatorBadge.propTypes = {
  children: PropTypes.node,
  step: PropTypes.number,
  state: PropTypes.object,
};

export default IndicatorBadge;
