import Divider from "components/ui/Divider";
import ProgressBar from "components/ui/ProgressBar";
import ToolTip from "components/ui/ToolTip";
import ColumnChart from "pages/user/wave-analysis-id/wave-info/components/charts/ColumnChart";
import { toPersianNumber } from "utils/helper";
import DominantEmotions from "./DominantEmotions";
import GaugeRateChart from "./GaugeRateChart";
import Age from "../charts/Age";
import { memo, useState } from "react";
import PioneerUsersTable2 from "pages/user/wave-analysis-id/wave-info/components/PioneerUsersTable2";
import emotionMap from "./constants/emotionMap";
import emotionColorMap from "./constants/emotionColorMap";
import sentimentColorMap from "./constants/sentimentColorMap";
import sentimentAnalysisMap from "./constants/sentimentAnalysisMap";
import genderMap from "./constants/genderMap";
import factionMap from "./constants/factionMap";
import ageMap from "./constants/ageMap";
import ageColorMap from "./constants/ageColorMap";
import genderColorMap from "./constants/genderColorMap";
import { StarFour } from "@phosphor-icons/react";
import GaugeChart from "react-gauge-chart";

const PoliticalParties = ({
  topSourcesData,
  sentimentData,
  gendersData,
  emotionsData,
  agesData,
  offensiveData,
  categoriesData,
  graphData,
}) => {
  const [activeGroup, setActiveGroup] = useState("eslahtalab");
  const transformedOffensiveData = offensiveData?.twitter?.map((item) => ({
    id: item?.key,
    title: item?.key === "offensive" ? "" : "",
    tooltip:
      item?.key === "offensive" ? "میزان محتوای ناسالم" : "میزان محتوای سالم",
    percentage: ((item.count / offensiveData?.total) * 100).toFixed(1),
    color: item?.key === "offensive" ? "#E0526A" : "#1CB0A5",
  }));

  const getPersianCategory = (key) => {
    const categoryMap = {
      culture_art: "فرهنگ و هنر",
      politic: "سیاسی",
      security_defense: "امنیت و دفاع",
      dailylife: "روزمره",
      social: "اجتماعی",
      economic: "اقتصادی",
      religion: "مذهب",
      science_tech_health: "علم و فناوری",
      sport: "ورزشی",
    };
    return categoryMap[key] || key;
  };

  const tabArray = [
    {
      id: "eslahtalab",
      title: "حامیان انقلاب اسلامی",
      color: "#000000",
    },
    {
      id: "osoolgera",
      title: "فعالین مدنی غرب‌گرا",
      color: "#000000",
    },
    {
      id: "saltanat",
      title: "سلطنت طلب",
      color: "#000000",
    },
    {
      id: "barandaz",
      title: "برانداز",
      color: "#000000",
    },
    {
      id: "edalatkhah",
      title: "عدالت‌خواه",
      color: "#000000",
    },
    {
      id: "monafegh",
      title: "منافقین",
      color: "#000000",
    },
    {
      id: "ahmadinezhad",
      title: "احمدی‌نژادی",
      color: "#000000",
    },
    {
      id: "restart",
      title: "ری‌استارتی",
      color: "#000000",
    },
  ];
  const tabs = tabArray?.map((legend) => {
    const value = graphData?.[legend?.id] || 0;
    const percent = ((value / graphData?.total) * 100).toFixed(1);
    return {
      ...legend,
      percent,
    };
  });

  const emotionsTransformedData = {
    data: emotionsData?.emotion?.reduce((acc, item) => {
      const key = Object.keys(item)[0]; // Get the key (e.g., "edalatkhah")
      acc[key] = item[key]; // Assign the array to the key
      return acc;
    }, {}),
  };
  const categoryTransformedData = {
    data: categoriesData?.category?.reduce((acc, item) => {
      const key = Object.keys(item)[0]; // Get the key (e.g., "edalatkhah")
      acc[key] = item[key]; // Assign the array to the key
      return acc;
    }, {}),
  };
  console.log(categoryTransformedData);
  const ageTransformedData = {
    data: agesData?.age_groups?.reduce((acc, item) => {
      const key = Object.keys(item)[0]; // Get the key (e.g., "edalatkhah")
      acc[key] = item[key]; // Assign the array to the key
      return acc;
    }, {}),
  };
  const sentimentTransformedData = {
    data: sentimentData?.sentiment?.reduce((acc, item) => {
      const key = Object.keys(item)[0]; // Get the key (e.g., "edalatkhah")
      acc[key] = item[key]; // Assign the array to the key
      return acc;
    }, {}),
  };
  const genderTransformedData = {
    data: gendersData?.gender_groups?.reduce((acc, item) => {
      const key = Object.keys(item)[0]; // Get the key (e.g., "edalatkhah")
      acc[key] = item[key]; // Assign the array to the key
      return acc;
    }, {}),
  };
  return (
    <div className="grid gap-3">
      <div className="col-span-12 bg-light-neutral-surface-card p-4 rounded-lg">
        <div className="col-span-12 bg-light-neutral-surface-card rounded-lg">
          <div className="flex items-center gap-12 mx-auto my-4 px-2">
            {tabs
              ?.sort((a, b) => Number(b.percent) - Number(a.percent))
              ?.map((item) => (
                <div key={item?.id} className="font-body-medium">
                  <span
                    className={`cursor-pointer select-none ${
                      item?.id === activeGroup
                        ? "text-light-primary-background-rest border-b-2 pb-1 border-light-primary-background-rest"
                        : ""
                    }`}
                    onClick={() => setActiveGroup(item?.id)}
                  >
                    {item?.title} {` (${toPersianNumber(item.percent)}٪)`}
                  </span>
                </div>
              ))}
          </div>
        </div>
        <Divider />
        <div className="flex items-center gap-5 mt-4">
          <h2 className="font-body-bold-large">{factionMap[activeGroup]}</h2>
          {/* <ToolTip comp={toPersianNumber("تعداد: 565")}>
            <span className="text-light-neutral-text-low font-body-small">
              (۳۷.۲۵٪)
            </span>
          </ToolTip> */}
        </div>

        {/* <div className="border rounded-lg p-4 mt-4">
          <div className="flex items-center flex-row-reverse justify-end gap-2">
            <p className="font-subtitle-medium text-[14px]">
              (خلاصه تحلیل از نگاه هوش‌مصنوعی)
            </p>
            <StarFour size={20} />
          </div>

          <p className="text-light-neutral-text-low font-body-medium">
            خلاصه مطلبی که به کاربر نمایش می‌دهیم و در صورتی که کاربر بر روی آن
            کلیک کند به صورت یک پاپ‌اپ دراور اطلاعات کامل ا خلاصه مطلبی که به
            کاربر نمایش می‌دهیم و در صورتی که کاربر بر روی آن کلیک کند به صورت
            یک پاپ‌اپ دراور اطلاعات کامل ا به صورت یک پاپ‌اپ دراور به صورت یک
            پاپ‌اپ دراور اطلاعات کامل این است :
          </p>
        </div> */}

        <div className="grid grid-cols-12 gap-4 mt-14">
          <div className="col-span-3">
            <div className="border rounded-md px-2">
              <p className="font-subtitle-large mb-2 p-2">
                برترین کاربران در انتشار محتوا
                <Divider />
              </p>
              <PioneerUsersTable2
                data={topSourcesData?.top_sources?.[activeGroup]}
              />
            </div>
            <div className="border rounded-md p-3 mt-4">
              <p className="font-subtitle-large mb-4">
                توزیع محتوای توهین‌آمیز
              </p>
              <ProgressBar data={transformedOffensiveData} />
            </div>
          </div>

          <div className="col-span-5">
            <div className="p-3 border rounded-md">
              <div className="flex items-center gap-1">
                <p className="font-subtitle-large">تحلیل هیجانات (دسته غالب)</p>
              </div>
              {emotionsTransformedData?.data &&
              Object.values(emotionsTransformedData.data)?.length > 0 ? (
                <div className="flex flex-row-reverse items-center justify-around">
                  <DominantEmotions
                    activeGroup={activeGroup}
                    emotionMap={emotionMap}
                    colorMap={emotionColorMap}
                    emotionsData={emotionsData}
                    emotionsTransformedData={emotionsTransformedData}
                  />
                  <div>
                    <p className={`font-headline-medium leading-10`}>
                      {
                        emotionMap[
                          emotionsTransformedData?.data?.[activeGroup]?.sort(
                            (a, b) => b?.count - a?.count
                          )[0]?.key
                        ]
                      }
                    </p>
                    <p className="font-headline-large text-light-neutral-text-low">
                      {toPersianNumber(
                        (
                          (emotionsTransformedData?.data?.[activeGroup]?.sort(
                            (a, b) => b?.count - a?.count
                          )?.[0]?.count /
                            emotionsTransformedData?.data?.[
                              activeGroup
                            ]?.reduce(
                              (acc, curr) => acc + (curr?.count || 0),
                              0
                            )) *
                          100
                        )?.toFixed(0)
                      )}
                      ٪
                    </p>
                  </div>
                </div>
              ) : (
                <div className="h-[250px] flex items-center justify-between mx-auto">
                  <p className="py-1.5 text-center font-body-bold-large text-light-neutral-text-medium flex items-center justify-between mx-auto">
                    موردی یافت نشد
                  </p>
                </div>
              )}
            </div>

            {/* <div className="p-3 border rounded-md mt-4">
              <div className="flex items-center gap-1">
                <p className="font-subtitle-large">دسته‌بندی سن</p>
              </div>
              {ageTransformedData?.data &&
              Object.values(ageTransformedData.data)?.length > 0 ? (
                <div className="flex flex-row-reverse items-center justify-around">
                  <Age legend={false} agesData={agesData?.age_aggs} />
                  <div>
                    <p
                      className={`font-headline-medium leading-10`}
                      style={{
                        color:
                          ageColorMap?.[
                            ageTransformedData?.data?.[activeGroup]?.sort(
                              (a, b) => b?.count - a?.count
                            )[0]?.key
                          ],
                      }}
                    >
                      {
                        ageMap[
                          ageTransformedData?.data?.[activeGroup]?.sort(
                            (a, b) => b?.count - a?.count
                          )[0]?.key
                        ]
                      }
                    </p>
                    <p className="font-headline-large text-light-neutral-text-low">
                      {toPersianNumber(
                        (
                          (ageTransformedData?.data?.[activeGroup]?.sort(
                            (a, b) => b?.count - a?.count
                          )?.[0]?.count /
                            ageTransformedData?.data?.[activeGroup]?.reduce(
                              (acc, curr) => acc + (curr?.count || 0),
                              0
                            )) *
                          100
                        )?.toFixed(0)
                      )}
                      ٪
                    </p>
                  </div>
                </div>
              ) : (
                <div className="h-[240px] flex items-center justify-between mx-auto">
                  <p className="py-1.5 text-center font-body-bold-large text-light-neutral-text-medium flex items-center justify-between mx-auto">
                    موردی یافت نشد
                  </p>
                </div>
              )}
            </div> */}

            <div className="border rounded-md p-3 mt-4">
              <p className="font-subtitle-large mb-4">
                توزیع محتوای توهین‌آمیز
              </p>
              <ProgressBar data={transformedOffensiveData} mode="pie" />
            </div>
          </div>

          <div className="col-span-4">
            <div className="p-3 border rounded-md">
              <div className="flex items-center gap-1">
                <p className="font-subtitle-large">تحلیل احساسات (دسته غالب)</p>
              </div>
              {sentimentTransformedData?.data &&
              Object.values(sentimentTransformedData?.data)?.length > 0 ? (
                <div className="flex flex-row-reverse items-center justify-around">
                  <div className="w-72 h-[250px] pt-12">
                    <GaugeChart
                      nrOfLevels={3}
                      arcWidth={0.3}
                      cornerRadius={3}
                      percent={
                        sentimentTransformedData?.data[activeGroup][0]?.count /
                        sentimentTransformedData?.data[activeGroup]?.reduce(
                          (sum, item) => sum + item.count,
                          0
                        )
                      }
                      colors={["#1CB0A5", "#9D9D9D", "#E0526A"]}
                      hideText
                    />
                  </div>
                  {/* <GaugeRateChart data={sentimentData?.twitter} /> */}
                  <div>
                    <div className="flex items-center gap-1">
                      <img
                        src={`/icons/${
                          sentimentTransformedData?.data?.[activeGroup]?.sort(
                            (a, b) => b?.count - a?.count
                          )[0]?.key
                        }.svg`}
                        width={30}
                        alt=""
                      />

                      <p
                        style={{
                          color:
                            sentimentColorMap?.[
                              sentimentTransformedData?.data?.[
                                activeGroup
                              ]?.sort((a, b) => b?.count - a?.count)[0]?.key
                            ],
                        }}
                        className="font-headline-medium text-light-success-text-rest leading-10"
                      >
                        {
                          sentimentAnalysisMap[
                            sentimentTransformedData?.data?.[activeGroup]?.sort(
                              (a, b) => b?.count - a?.count
                            )[0]?.key
                          ]
                        }
                      </p>
                    </div>
                    <p className="font-headline-large text-light-neutral-text-low">
                      {genderTransformedData?.data?.[activeGroup]
                        ? toPersianNumber(
                            (
                              (sentimentTransformedData?.data?.[
                                activeGroup
                              ]?.sort((a, b) => b?.count - a?.count)?.[0]
                                ?.count /
                                sentimentTransformedData?.data?.[
                                  activeGroup
                                ]?.reduce(
                                  (acc, curr) => acc + (curr?.count || 0),
                                  0
                                )) *
                              100
                            )?.toFixed(0)
                          ) + "٪"
                        : "یافت نشد!"}
                    </p>
                  </div>
                </div>
              ) : (
                <div className="h-[250px] flex items-center justify-between mx-auto">
                  <p className="py-1.5 text-center font-body-bold-large text-light-neutral-text-medium flex items-center justify-between mx-auto">
                    موردی یافت نشد
                  </p>
                </div>
              )}
            </div>

            <div className="p-3 border rounded-md mt-4 max-h-[295px]">
              <div className="flex items-center gap-1">
                <p className="font-subtitle-large">دسته‌بندی جنسیت</p>
              </div>
              {genderTransformedData?.data &&
              Object.values(genderTransformedData?.data)?.length > 0 ? (
                <div className="flex flex-row-reverse items-center justify-around">
                  <div className="w-72 h-[250px] pt-12">
                    <GaugeChart
                      nrOfLevels={3}
                      arcWidth={0.3}
                      cornerRadius={3}
                      percent={
                        genderTransformedData?.data[activeGroup][0]?.count /
                        genderTransformedData?.data[activeGroup]?.reduce(
                          (sum, item) => sum + item.count,
                          0
                        )
                      }
                      colors={["#E052B8", "#9D9D9D", "#1C60B0"]}
                      hideText
                    />
                  </div>
                  <div>
                    <div className="flex items-center gap-1">
                      <img
                        src={`/icons/${
                          genderTransformedData?.data?.[activeGroup]?.sort(
                            (a, b) => b?.count - a?.count
                          )[0]?.key
                        }.svg`}
                        width={30}
                        alt=""
                      />
                      <p
                        style={{
                          color:
                            genderColorMap?.[
                              genderTransformedData?.data?.[activeGroup]?.sort(
                                (a, b) => b?.count - a?.count
                              )[0]?.key
                            ],
                        }}
                        className="font-headline-medium text-light-success-text-rest leading-10"
                      >
                        {
                          genderMap[
                            genderTransformedData?.data?.[activeGroup]?.sort(
                              (a, b) => b?.count - a?.count
                            )[0]?.key
                          ]
                        }
                      </p>
                    </div>
                    <p className="font-headline-large text-light-neutral-text-low">
                      {genderTransformedData?.data?.[activeGroup]
                        ? toPersianNumber(
                            (
                              (genderTransformedData?.data?.[activeGroup]?.sort(
                                (a, b) => b?.count - a?.count
                              )?.[0]?.count /
                                genderTransformedData?.data?.[
                                  activeGroup
                                ]?.reduce(
                                  (acc, curr) => acc + (curr?.count || 0),
                                  0
                                )) *
                              100
                            )?.toFixed(0)
                          ) + "٪"
                        : "یافت نشد!"}
                    </p>
                  </div>
                </div>
              ) : (
                <div className="h-[240px] flex items-center justify-between mx-auto">
                  <p className="py-1.5 text-center font-body-bold-large text-light-neutral-text-medium flex items-center justify-between mx-auto">
                    داده ای یافت نشد
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="col-span-9 mt-4">
          <div className="p-3 border rounded-md">
            <div className="flex items-center gap-1">
              <p className="font-subtitle-large">دسته بندی موضوعی</p>
            </div>
            <Divider className={"mb-10"} />

            <ColumnChart
              data={categoryTransformedData?.data?.[activeGroup]?.map(
                (item) => item.count
              )}
              xAxisCategory={categoryTransformedData?.data?.[activeGroup]?.map(
                (item) => getPersianCategory(item.key)
              )}
              seriesColor={"#7E6CD5"}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default memo(PoliticalParties);
