import { CaretLeft } from "@phosphor-icons/react";
import profile from "/logo_small.png";
import { toPersianNumber } from "utils/helper";

const PioneerUsersTable = ({ data }) => {
  return (
    <>
      <div className="overflow-x-auto">
        <table className="min-w-full">
          <thead>
            <tr>
              <th className="px-3 py-2 text-right font-body-medium text-light-neutral-text-medium">
                اخرین فیلترها توسط چه کاربرانی ایجاد شده
              </th>
              <th className="px-3 py-2 text-end font-body-medium text-light-neutral-text-medium">
                نام فیلتر
              </th>
            </tr>
          </thead>
          <tbody className="font-subtitle-medium">
            {data.map((row, index) => (
              <tr key={row.id} className="hover:bg-gray-50 cursor-pointer">
                <td className="flex items-center gap-1 pl-4 py-2 font-body-medium text-light-neutral-text-medium">
                  <img
                    src={profile}
                    alt="profile"
                    width={35}
                    className="border rounded-full"
                  />
                  <div className="grid">
                    <p className="font-subtitle-medium text-black leading-4">
                      {toPersianNumber(row.title)}
                    </p>
                    <p className="font-overline-medium text-light-neutral-text-medium">
                      {row.accountId}
                    </p>
                  </div>
                </td>
                <td className="px-3 py-2 font-body-medium text-light-neutral-text-medium text-center">
                  {toPersianNumber(row.connections)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </>
  );
};

export default PioneerUsersTable;
