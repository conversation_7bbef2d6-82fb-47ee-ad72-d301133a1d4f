import { MagnifyingGlass, Plus } from "@phosphor-icons/react";
import { CButton } from "components/ui/CButton";
import { CInput } from "components/ui/CInput";
import DateFilter from "pages/admin/ticket-list/components/SearchBar/DateFilter";
import SectionDropdown from "pages/admin/ticket-list/components/SearchBar/SectionDropdown";
import StatusDropdown from "pages/admin/ticket-list/components/SearchBar/StatusDropdown";
import formatDate from "pages/admin/user-list/utils";
import { useState } from "react";
import GroupsStatus from "./ChangeListStatus";
import UserRole from "./UserRole";
import { useNavigate } from "react-router-dom";

const GpViewSearchbar = ({
  ticketStatus,
  filter,
  setFilter,
  loading = false,
  onSearch = () => {},
  isUserList = false,
  placeholder = "نام کاربر و یا شماره موبایل را جست‌وجو کنید",
}) => {
  const [searchValue, setSearchValue] = useState(filter.q || "");
  const navigate = useNavigate();

  const handleDateChange = (dates) => {
    const { from, to } = dates;
    setFilter((prevFilter) => ({
      ...prevFilter,
      created_at: { date_from: formatDate(from), date_to: formatDate(to) },
    }));
  };
  const handleSectionChange = (section) => {
    setFilter((prevFilter) => ({
      ...prevFilter,
      section,
    }));
  };
  const handleStatusChange = (status) => {
    setFilter((prevFilter) => ({
      ...prevFilter,
      status,
    }));
  };

  const handleSearch = async (event) => {
    if (event && event.key === "Enter") {
      setFilter((prevFilter) => ({
        ...prevFilter,
        q: searchValue,
      }));
      onSearch(); // Trigger the parent-provided search handler
    }
  };

  return (
    <div className="font-body-medium bg-white p-5 pb-0 overflow-hidden shadow-[0_2px_20px_0_rgba(0,0,0,0.05)]">
      <div className="flex !justify-between w-full pb-4">
        <p
          className={`cursor-pointer font-subtitle-large text-light-neutral-text-medium w-full`}
        >
          اعضای گروه
        </p>
        <CButton
          mode="outline"
          size="sm"
          leftIcon={<Plus />}
          width={130}
          onClick={() => navigate("/admin/groups/detail/add-user")}
        >
          عضو جدید
        </CButton>
      </div>
      <div className="flex flex-row gap-[16px]">
        <CInput
          id={"q"}
          name={"q"}
          inset={true}
          headingIcon={<MagnifyingGlass />}
          size={"md"}
          validation={"none"}
          inputProps={{ onKeyDown: handleSearch }}
          direction={"rtl"}
          placeholder={placeholder}
          onChange={(e) => setSearchValue(e.target.value)}
          className={"flex-1 !mb-0"}
        />
        <CButton
          type={"submit"}
          onClick={() => {
            setFilter((prevFilter) => ({
              ...prevFilter,
              q: searchValue,
            }));
            onSearch();
          }}
          size={"md"}
          className={"[direction:rtl] [width:150px!important]"}
          disabled={loading}
        >
          جست‌وجو
        </CButton>
      </div>
      <div className="flex pt-1 gap-4">
        {!isUserList && (
          <div className="py-2 flex gap-5">
            <UserRole status={filter?.status} onChange={handleStatusChange} />
            <GroupsStatus
              status={filter?.status}
              onChange={handleStatusChange}
            />
            <DateFilter
              handleDateChange={handleDateChange}
              selectedDateRange={filter?.created_at}
              title="تاریخ افزوده شدن به گروه:"
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default GpViewSearchbar;
