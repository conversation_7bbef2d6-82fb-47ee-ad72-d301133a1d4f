import React, { useState } from "react";
import { Card } from "components/ui/Card";
import SelectType from "./SelectType";
import SelectPlatform from "./SelectPlatform";
import { CButton } from "components/ui/CButton";
import { useNavigate } from "react-router-dom";
import { useCompareStore } from "store/compareStore";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";

const StepOne = ({ nextStep = () => {} }) => {
  const [platform, setPlatform] = useState("");
  const [type, setType] = useState("");
  const setCompare = useCompareStore((state) => state.setCompare);
  const navigate = useNavigate();
  const handleType = (type) => {
    setType(type);
    setCompare({ type });
  };
  const handlePlatform = (platform) => {
    setPlatform(platform);
    setCompare({ platform });
  };

  const breadcrumbList = [
    { title: "مقایسه", link: `/app/compare/list` },
    { title: "مقایسه جدید" },
  ];
  useBreadcrumb(breadcrumbList);
  // useEffect(() => {
  //   setBreadcrumb(breadcrumbList);
  // }, [alertData]);

  return (
    <div className="flex flex-col items-center justify-center pt-12">
      <div className="flex flex-col gap-4 w-fit">
        <Card>
          <div className="flex flex-col gap-6 p-10">
            <SelectType handleChange={handleType} />
            <SelectPlatform handleChange={handlePlatform} />
            <div className="flex gap-6 items-center justify-end">
              <CButton
                role="neutral"
                onClick={() => navigate("/app/compare/list")}
              >
                انصراف
              </CButton>
              <CButton readOnly={!(!!type && !!platform)} onClick={nextStep}>
                ادامه
              </CButton>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default StepOne;
