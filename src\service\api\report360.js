import useFetch from "../index";

class Report360 {
  create(data) {
    return useFetch.post("/api/v1/report360/", data);
  }

  update(id, data) {
    return useFetch.put(`/api/v1/report360/${id}/`, data);
  }

  get(params = {}) {
    return useFetch.get("/api/v1/report360/", { params });
  }

  getById(id) {
    return useFetch.get(`/api/v1/report360/${id}/`);
  }

  remove(id) {
    return useFetch.delete(`/api/v1/report360/${id}/`);
  }

  image(formData) {
    return useFetch.post("/api/v1/report360/image/", formData);
  }
}
export default new Report360();
