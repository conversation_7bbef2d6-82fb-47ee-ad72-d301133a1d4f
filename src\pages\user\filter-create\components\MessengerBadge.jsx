import PropTypes from "prop-types";

const MessengerBadge = ({ children, name, selected, height, dir, onClick }) => {
  return (
    <div
      className="w-full rounded-lg border border-light-neutral-border-low-rest flex items-center gap-4 justify-center cursor-pointer"
      style={{
        ...(selected
          ? { backgroundColor: "#B4ABE34D", borderColor: "#B4ABE34D" }
          : {}),
        height: height,
        flexDirection: dir,
      }}
      onClick={onClick}
    >
      <div>{children}</div>
      <span className={height > 70 ? "font-body-medium" : "font-body-small"}>
        {name}
      </span>
    </div>
  );
};

MessengerBadge.propTypes = {
  children: PropTypes.node,
  name: PropTypes.string.isRequired,
  selected: PropTypes.bool,
  height: PropTypes.number,
  dir: PropTypes.string,
  onClick: PropTypes.func,
};

export default MessengerBadge;
