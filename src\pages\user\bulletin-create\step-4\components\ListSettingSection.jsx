import { useCallback, useEffect, useState } from "react";
import { DndProvider } from "react-dnd";
import { useBulletinStore } from "store/bulletinStore.js";
import Accordion from "components/ui/Accordion.jsx";
import SocialMediaCard from "./SocialMediaCard.jsx";
import { HTML5Backend } from "react-dnd-html5-backend";
import Toggle from "./Toggle.jsx";

const ListSettingSection = () => {
  const setBulletin = useBulletinStore((state) => state.setBulletin);
  const { metadata } = useBulletinStore((state) => state.bulletin);
  const [showToc, setShowToc] = useState(metadata?.has_toc || false);

  const [cards, setCards] = useState([
    { id: 1, title: "اینستاگرام", media: "instagram" },
    { id: 2, title: "تلگرام", media: "telegram" },
    { id: 3, title: "ایکس", media: "twitter" },
    { id: 4, title: "سایت‌های خبری", media: "news" },
    // { id: 5, title: "بله", media: "bale" },
    // { id: 6, title: "ایتا", media: "eitaa" },
  ]);

  useEffect(() => {
    const sortedCards = [...cards].sort(
      (a, b) =>
        metadata?.ordered_platform?.indexOf(a.media) -
        metadata?.ordered_platform.indexOf(b.media),
    );
    setCards(sortedCards);
  }, [metadata]);

  const handleChange = useCallback(
    (data) => {
      const help = JSON.parse(JSON.stringify(metadata));
      help["ordered_platform"] = data.map((item) => item.media);
      setBulletin({ metadata: { ...help } });
    },
    [metadata],
  );

  const moveCard = useCallback(
    (fromIndex, toIndex) => {
      const updatedCards = [...cards];
      const [movedCard] = updatedCards.splice(fromIndex, 1);
      updatedCards.splice(toIndex, 0, movedCard);
      setCards(updatedCards);
      handleChange(updatedCards);
    },
    [cards],
  );

  return (
    <DndProvider backend={HTML5Backend}>
      <Accordion
        isOpen={false}
        showBox={false}
        Header={<div className="font-subtitle-large">تنظیمات فهرست</div>}
      >
        <div className="flex flex-col gap-6">
          <Toggle
            title={"نمایش فهرست"}
            name="has_toc"
            key="has_toc"
            onChange={setShowToc}
          />
          {showToc && (
            <>
              <div className="flex flex-col gap-2">
                <span className="font-body-medium">ترتیب نمایش محتوا</span>
                <p className="font-overline-large text-light-neutral-text-medium">
                  از طریق کشیدن و رها کردن هر کدام از موارد ترتیب نمایش را به
                  دلخواه تغییر دهید
                </p>
              </div>
              <div className="flex flex-col gap-2">
                {cards.map((item, index) => (
                  <SocialMediaCard
                    key={item.id}
                    id={item.id}
                    media={item.media}
                    socialMediaTitle={item.title}
                    index={index}
                    moveCard={moveCard}
                  />
                ))}
              </div>
            </>
          )}
        </div>
      </Accordion>
    </DndProvider>
  );
};

export default ListSettingSection;
