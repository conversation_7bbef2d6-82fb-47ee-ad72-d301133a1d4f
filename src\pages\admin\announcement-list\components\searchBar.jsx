import { MagnifyingGlass } from "@phosphor-icons/react";
import { CButton } from "components/ui/CButton";
import { CInput } from "components/ui/CInput";
import DateFilter from "pages/admin/ticket-list/components/SearchBar/DateFilter";
import SectionDropdown from "pages/admin/ticket-list/components/SearchBar/SectionDropdown";
import StatusDropdown from "pages/admin/ticket-list/components/SearchBar/StatusDropdown";
import formatDate from "pages/admin/user-list/utils";
import { useState } from "react";
import AnnounceStatus from "./announceStatus";

const SearchBar = ({
  ticketStatus,
  filter,
  setFilter,
  loading = false,
  onSearch = () => {},
}) => {
  const [searchValue, setSearchValue] = useState(filter.q || "");

  const handleDateChange = (dates) => {
    const { from, to } = dates;
    setFilter((prevFilter) => ({
      ...prevFilter,
      created_at: { date_from: formatDate(from), date_to: formatDate(to) },
    }));
  };
  const handleSectionChange = (section) => {
    setFilter((prevFilter) => ({
      ...prevFilter,
      section,
    }));
  };
  const handleStatusChange = (status) => {
    setFilter((prevFilter) => ({
      ...prevFilter,
      status,
    }));
  };

  const handleSearch = async (event) => {
    if (event && event.key === "Enter") {
      setFilter((prevFilter) => ({
        ...prevFilter,
        q: searchValue,
      }));
      onSearch(); // Trigger the parent-provided search handler
    }
  };

  return (
    <div className="font-body-medium h-full bg-white p-5 overflow-hidden rounded-lg shadow-[0_2px_20px_0_rgba(0,0,0,0.05)]">
      <div className="flex flex-row gap-[16px]">
        <CInput
          id={"q"}
          name={"q"}
          inset={true}
          headingIcon={<MagnifyingGlass />}
          size={"md"}
          validation={"none"}
          inputProps={{ onKeyDown: handleSearch }}
          direction={"rtl"}
          placeholder={
            "عنوان اطلاعیه / نام نویسنده / منتشر کننده را جست‌وجو کنید"
          }
          onChange={(e) => setSearchValue(e.target.value)}
          className={"flex-1 !mb-0"}
        />
        <CButton
          type={"submit"}
          onClick={() => {
            setFilter((prevFilter) => ({
              ...prevFilter,
              q: searchValue,
            }));
            onSearch();
          }}
          size={"md"}
          className={"[direction:rtl] [width:150px!important]"}
          disabled={loading}
        >
          جست‌وجو
        </CButton>
      </div>
      <div className="flex pt-1 gap-4">
        <DateFilter
          handleDateChange={handleDateChange}
          selectedDateRange={filter?.created_at}
        />
        <AnnounceStatus status={filter?.status} onChange={handleStatusChange} />
      </div>
    </div>
  );
};

export default SearchBar;
