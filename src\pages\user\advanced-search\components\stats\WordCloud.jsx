import PropTypes from "prop-types";
import ReactWordcloud from "react-wordcloud";
import { SpinnerGap } from "@phosphor-icons/react";
import { Card } from "components/ui/Card";
import { CTabs } from "components/ui/CTabs";
import PLATFORMS from "constants/platforms.js";
import useSearchStore from "store/searchStore.js";
import ExportMenu from "components/ExportMenu";

const WordCloud = ({
  words,
  loading,
  type,
  setType,
  platform,
  time,
  series,
}) => {
  const { setQuery } = useSearchStore();

  return (
    <Card className={"flex-col"}>
      <div className={"flex w-full justify-between p-2 border-b-2"}>
        <div className={"flex"}>
          {platform !== PLATFORMS.NEWS && (
            <CTabs
              hasIcon={false}
              tabArray={[
                {
                  id: "keywords",
                  title: "کلمات",
                },
                {
                  id: "hashtags",
                  title: "هشتگ‌ها",
                },
              ]}
              activeTab={type}
              onChange={(x) => setType(x)}
            />
          )}
        </div>
        <div className="flex gap-4">
          <h3 className={"font-subtitle-large text-right"}>
            {type === "hashtags" ? "ابر هشتگ‌های پرتکرار" : "ابر کلمات پرتکرار"}
          </h3>
          <ExportMenu
            chartSelector=".words-container"
            fileName={type === "hashtags" ? "hashtags" : "keywords"}
            series={series}
            time={time}
            excelHeaders={[
              type === "hashtags" ? "Hashtag" : "Keyword",
              "Count",
            ]}
            onError={(error) => console.error("Export error:", error)}
            menuItems={["PNG", "JPEG", "Excel"]}
          />
        </div>
      </div>
      {loading ? (
        <div className="w-full h-[365px] flex justify-center items-center">
          <SpinnerGap size={40} className="animate-spin" />
        </div>
      ) : // {words.length === 1 ? }
      words?.length === 0 ? (
        <div className="h-[300px] flex items-center justify-center font-subtitle-medium">
          داده ای برای نمایش وجود ندارد
        </div>
      ) : (
        <div
          className={
            "flex w-full text-center justify-center items-center h-[300px] overflow-hidden"
          }
        >
          <ReactWordcloud
            options={{
              rotations: 1,
              rotationAngles: [0],
              enableTooltip: true,
              deterministic: false,
              fontFamily: "iranyekan",
              fontSizes: [14, 54],
              padding: 10,
              colors: [
                "#1CB0A5",
                "#EFA9B4",
                "#85AEFA",
                "#FEBE8C",
                "#AD88C6",
                "#99BC85",
              ],
              tooltipOptions: { theme: "light", arrow: true },
            }}
            words={words}
            callbacks={{
              onWordClick: (x) => setQuery(x.text),
            }}
          />
        </div>
      )}
    </Card>
  );
};

WordCloud.propTypes = {
  words: PropTypes.array,
  loading: PropTypes.bool.isRequired,
  type: PropTypes.oneOf(["hashtags", "keywords"]),
  setType: PropTypes.func.isRequired,
  platform: PropTypes.string,
};

export default WordCloud;
