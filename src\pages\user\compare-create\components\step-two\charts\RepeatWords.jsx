import { useState, useEffect, memo } from "react";
import HorizontalBar from "components/Charts/HorizontalBar.jsx";
import { useCompareStore } from "store/compareStore.js";
import { Cloud, FunnelSimple, SpinnerGap } from "@phosphor-icons/react";
import { Card } from "components/ui/Card.jsx";
import ReactWordcloud from "react-wordcloud";
import PropTypes from "prop-types";
import CLUSTER_COLORS from "constants/colors.js";
import { CTabs } from "components/ui/CTabs.jsx";
import advanceSearch from "service/api/advanceSearch.js";
import { preprocessWord, toPersianNumber } from "utils/helper.js";
import Title from "./Title.jsx";
import { buildRequestData } from "utils/requestData.js";

const RepeatWords = ({ type, compareType }) => {
  const { fields, platform, date } = useCompareStore((state) => state.compare);
  const [categories, setCategories] = useState([]);
  const [wordCloudActiveTab, setWordCloudActiveTab] = useState("cluster");
  const [wordClouds, setWordClouds] = useState([]);
  const [info, setInfo] = useState([]);
  const [loading, setLoading] = useState(false);
  const [fieldsCleared, setFieldsCleared] = useState([]); // New state for fieldsCleared

  // Update fieldsCleared only when relevant field properties change
  useEffect(() => {
    const newFieldsCleared = fields.filter((value) =>
      compareType === "profile" ? value?.id || value?.user_id : value?.q,
    );
    // Compare newFieldsCleared with current fieldsCleared
    const hasChanged =
      newFieldsCleared.length !== fieldsCleared.length ||
      newFieldsCleared.some((field, index) => {
        const prevField = fieldsCleared[index] || {};
        return compareType === "profile"
          ? field.id !== prevField.id || field.user_id !== prevField.user_id
          : field.q !== prevField.q;
      });

    if (hasChanged) {
      console.log("fieldsCleared updated:", newFieldsCleared);
      setFieldsCleared(newFieldsCleared);
    }
  }, [fields, compareType]);

  const fetchProfileData = async (index, abortController) => {
    const filters = {
      date,
      platform,
      sources: fieldsCleared[index]?.id
        ? [fieldsCleared[index]?.id.toString()]
        : fieldsCleared[index]?.user_id
          ? [fieldsCleared[index]?.user_id.toString()]
          : [],
    };

    const infoQuery = buildRequestData(filters, "cloud", 30);
    const response = await advanceSearch.search(infoQuery, abortController);

    return response.data.data?.[platform];
  };

  const fetchTopicData = async (index, abortController) => {
    const query = fieldsCleared[index].q;
    const filters = {
      date,
      platform,
      q: query,
    };
    const infoQuery = buildRequestData(filters, "cloud");
    const response = await advanceSearch.search(infoQuery, abortController);
    return response.data.data?.[platform];
  };

  const updateState = (result, index) => {
    setWordClouds((prev) => {
      const updatedWordClouds = [...prev];
      updatedWordClouds[index] =
        result?.map(({ word, key, count }) => ({
          text: preprocessWord(word || key),
          value: count,
        })) || [];
      return updatedWordClouds;
    });

    setInfo((prev) => {
      const updatedInfo = [...prev];
      updatedInfo[index] = result;
      return updatedInfo;
    });

    setCategories((prev) => {
      const updatedCategories = [...prev];
      updatedCategories[index] = result
        ?.slice(0, 10)
        ?.map((value) => value.word || value.key);
      return updatedCategories;
    });
  };

  const getData = async (index, abortController) => {
    setLoading(true);
    try {
      const result =
        compareType === "profile"
          ? await fetchProfileData(index, abortController)
          : await fetchTopicData(index, abortController);

      if (!abortController.signal.aborted) {
        updateState(result, index);
      }
    } catch (error) {
      if (!abortController.signal.aborted) {
        console.log(error);
      }
    } finally {
      if (!abortController.signal.aborted) setLoading(false);
    }
  };

  // Fetch data when fieldsCleared or date changes
  useEffect(() => {
    const abortController = new AbortController();
    console.log("Fetch effect triggered with:", {
      fieldsCleared,
      date,
      platform,
    });
    if (fieldsCleared.length > 0) {
      setWordClouds(new Array(fieldsCleared.length).fill([]));
      fieldsCleared.forEach((field, index) => getData(index, abortController));
    }

    return () => abortController.abort(); // Cleanup request on component unmount or update
  }, [fieldsCleared, date, platform]);

  const dataColors = {
    yellow: ["#F7A912"],
    blue: ["#6D72E5"],
    green: ["#1DCEA3"],
    purple: ["#DB6DE5"],
  };

  const colorAssignments = [];
  fieldsCleared.forEach((_, index) => {
    if (index % 4 === 0) {
      colorAssignments.push(dataColors.green[index % dataColors.green.length]);
    } else if (index % 4 === 1) {
      colorAssignments.push(dataColors.blue[index % dataColors.blue.length]);
    } else if (index % 4 === 2) {
      colorAssignments.push(
        dataColors.purple[index % dataColors.purple.length],
      );
    } else {
      colorAssignments.push(
        dataColors.yellow[index % dataColors.yellow.length],
      );
    }
  });

  const onClusterTabChange = (name) => {
    setWordCloudActiveTab(name);
  };

  if (loading) {
    return (
      <div className="flex">
        <Card
          className={`flex flex-col gap-4 ${
            fieldsCleared.length < 4
              ? "w-[83%]"
              : fieldsCleared.length === 4
                ? "w-full"
                : null
          }`}
        >
          <div className="!flex w-full h-80 justify-center items-center">
            <SpinnerGap size={40} className="animate-spin" />
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex">
      {fieldsCleared.length > 0 && (
        <Card className="px-0">
          <div className="w-full flex flex-col gap-6">
            <div className="flex flex-row gap-2 w-full justify-between">
              <Title title="کلمات پر تکرار"></Title>
              <div className="flex" id={"repeatWordsTabs"}>
                <CTabs
                  tabArray={[
                    {
                      id: "wordCloud",
                      title: "ابر کلمات",
                      icon: Cloud,
                    },
                    {
                      id: "cluster",
                      title: "نمودار",
                      icon: FunnelSimple,
                    },
                  ]}
                  activeTab={wordCloudActiveTab}
                  onChange={onClusterTabChange}
                />
              </div>
            </div>
            <div
              className="grid divide-x-[1px] divide-x-reverse"
              style={{
                gridTemplateColumns: `repeat(${fieldsCleared.length}, 1fr)`,
              }}
            >
              {wordCloudActiveTab === "wordCloud" &&
                fieldsCleared.map((item, index) => (
                  <div className={"flex flex-1 responsive-svg"} key={index}>
                    <ReactWordcloud
                      options={{
                        rotations: 1,
                        rotationAngles: [0],
                        enableTooltip: true,
                        deterministic: false,
                        fontFamily: "iranyekan",
                        fontSizes: [14, 54],
                        padding: 10,
                        colors: CLUSTER_COLORS,
                        tooltipOptions: { theme: "light", arrow: true },
                      }}
                      words={wordClouds[index] || []}
                      callbacks={{
                        getWordTooltip: (word) => {
                          const countInPersian = toPersianNumber(word.value);
                          if (word.text.endsWith("#")) {
                            return `${word.text?.slice(
                              0,
                              word.text.length - 1,
                            )} (${countInPersian})`;
                          }
                          return `${word.text} (${countInPersian})`;
                        },
                      }}
                    />
                  </div>
                ))}

              {wordCloudActiveTab === "cluster" &&
                fieldsCleared.map((item, index) => (
                  <HorizontalBar
                    colors={[colorAssignments[index]]}
                    data={item}
                    key={item?.id}
                    info={info[index]}
                    categories={categories[index]}
                    loading={loading}
                    type={type}
                    minWidth={"15rem"}
                  />
                ))}
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

RepeatWords.propTypes = {
  type: PropTypes.string.isRequired,
  compareType: PropTypes.string.isRequired,
};

export default memo(RepeatWords);
