import clsx from "clsx";

const TicketState = ({ title, state }) => {
  const stepStateClassName = clsx(
    "font-body-small rounded-[4px] px-1 w-20 text-center",
    {
      "text-light-inform-text-rest bg-light-inform-background-highlight":
        state === "open",
      "text-light-warning-text-rest bg-light-warning-background-highlight":
        state === "resolved" || state === "replied",
      "text-light-success-text-rest bg-light-success-background-highlight":
        state === "closed",
    }
  );
  return <div className={stepStateClassName}>{title}</div>;
};

export default TicketState;
