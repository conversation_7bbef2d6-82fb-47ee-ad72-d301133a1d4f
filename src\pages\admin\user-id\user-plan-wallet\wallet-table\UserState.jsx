import clsx from "clsx";

const UserState = ({ state, title }) => {
  const stepStateClassName = clsx(
    "font-body-small rounded-[4px] px-1 w-20 text-center",
    {
      "text-light-error-text-rest bg-light-error-background-highlight":
        state === "نا موفق",
      "text-light-success-text-rest bg-light-success-background-highlight":
        state === "موفق",
    }
  );
  return <div className={stepStateClassName}>{title}</div>;
};

export default UserState;
