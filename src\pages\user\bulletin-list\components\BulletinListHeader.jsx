import { Info, Plus, Question } from "@phosphor-icons/react";
import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import Drawer from "components/Drawer";
import { useBulletinStore } from "store/bulletinStore.js";
import BulletinInfo from "./BulletinInfo";
import { CButton } from "components/ui/CButton";

const BulletinListHeader = () => {
  const [showMore, setShowMore] = useState(false);
  const navigate = useNavigate();

  const resetBulletin = useBulletinStore((state) => state.resetBulletin);

  return (
    <div className="pt-6 mb-4">
      <div className="flex flex-row-reverse items-center gap-2">
        <div className="w-fit" onClick={() => setShowMore(true)}>
          <CButton
            mode="outline"
            size="sm"
            role="neutral"
            className="gap-2 !h-10"
          >
            <Info size={17} className="text-light-neutral-text-medium" />
            <p className="text-light-neutral-text-medium font-button-medium">
              بولتن چیست
            </p>
          </CButton>
        </div>
        <CButton
          rightIcon={<Plus />}
          width={130}
          className={"[direction:ltr]"}
          mode="outline"
          onClick={() => {
            resetBulletin();
            navigate("/app/bulletin/create");
          }}
        >
          بولتن جدید
        </CButton>
      </div>
      {showMore && (
        <Drawer setShowMore={setShowMore}>
          <BulletinInfo />
        </Drawer>
      )}
    </div>
  );
};

export default BulletinListHeader;
