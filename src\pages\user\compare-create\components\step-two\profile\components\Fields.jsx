import { CButton } from "components/ui/CButton.jsx";
import {
  MagnifyingGlass,
  PencilSimpleLine,
  Plus,
  TrashSimple,
} from "@phosphor-icons/react";
import { useCompareStore } from "store/compareStore.js";
import { ProfileSearch } from "./ProfileSearch.jsx";
import MediaBadge from "components/ui/MediaBadge.jsx";
import { useScrollPosition } from "hooks/useScrollPosition.jsx";
import { useEffect, useState } from "react";
import ToolTip from "components/ui/ToolTip.jsx";

const Fields = () => {
  const { platform, fields } = useCompareStore((state) => state.compare);
  const setCompare = useCompareStore((state) => state.setCompare);
  const colors = ["#1DCEA3", "#6D72E5", "#DB6DE5", "#F7A912"];

  const { isScrolled } = useScrollPosition();
  const [sticky, setSticky] = useState(false);

  useEffect(() => {
    setSticky(isScrolled);
  }, [isScrolled]);

  const handleAddField = () => {
    const help = JSON.parse(JSON.stringify(fields));
    setCompare({
      fields: [...help, { isEdit: true }],
    });
  };

  const handleRemoveFiled = (index) => {
    if (fields.length === 2) return;
    const help = JSON.parse(JSON.stringify(fields));
    help.splice(index, 1);
    setCompare({
      fields: [...help],
    });
  };

  const handleProfile = (x, i) => {
    const help = JSON.parse(JSON.stringify(fields));
    if (
      help[i] &&
      (x?.username ||
        x?.url ||
        x?.title ||
        x?.name ||
        x?.full_name ||
        x?.user_title)
    ) {
      help[i] = { ...x, isEdit: false };
    } else {
      help[i] = { isEdit: true };
    }

    setCompare({
      fields: [...help],
    });
  };

  const handleRemove = (index) => {
    const help = JSON.parse(JSON.stringify(fields));
    if (help.length === 2) {
      help[index] = { isEdit: true };
    } else {
      help.splice(index, 1);
    }
    setCompare({
      fields: [...help],
    });
  };

  const changeStatusEdit = (index) => {
    const help = JSON.parse(JSON.stringify(fields));
    help[index].isEdit = !help[index].isEdit;
    setCompare({
      fields: [...help],
    });
  };

  return (
    <>
      <div
        className={`${
          sticky
            ? "w-full fixed top-[60px] left-0 h-[90px] z-50 backdrop-blur"
            : "hidden"
        }`}
      ></div>
      <div
        className={`flex gap-4 items-center ${
          sticky ? "sticky top-[75px] z-50" : ""
        }`}
        id="profile-fields-box"
      >
        {fields?.map((item, index) => (
          <div
            className={`bg-white rounded-lg flex w-full font-body-medium bg-light-neutral-surface-card p-4 ${
              sticky ? "shadow-lg" : "shadow-[0_2px_20px_0_rgba(0,0,0,0.05)]"
            }`}
            style={{ borderRight: `6px solid ${colors[index]}` }}
            key={index}
          >
            {item.isEdit ? (
              <div className="flex items-center w-full gap-4">
                <div className="w-full">
                  <ProfileSearch
                    id={"resource"}
                    name={item?.username || ""}
                    inset={true}
                    size={"md"}
                    validation={"none"}
                    direction={"rtl"}
                    placeholder={"پروفایل کاربر را بنویسید"}
                    className={"flex-1 !mb-0"}
                    onChange={(x) => handleProfile(x, index)}
                    headingIcon={<MagnifyingGlass />}
                    platform={platform}
                    key={index}
                    value={item?.username}
                  />
                </div>
                {fields.length > 2 && (
                  <div
                    className="size-6 flex items-center justify-center bg-[#E1E8EF80] rounded-md cursor-pointer"
                    onClick={() => handleRemoveFiled(index)}
                  >
                    <TrashSimple size={16} />
                  </div>
                )}
              </div>
            ) : (
              <div className="flex justify-between items-center w-full gap-1">
                <ToolTip comp={item.title}>
                  <div className="flex gap-2 items-center">
                    <div className="relative">
                      <div
                        className="w-10 h-10 rounded-full ml-2"
                        style={{
                          backgroundImage: `url(${item.avatar}), url(/logo_small.png)`,
                          backgroundRepeat: "no-repeat",
                          backgroundPosition: "center center",
                          backgroundSize: "contain",
                        }}
                      ></div>
                      <span className="absolute top-6 right-0 rounded-full overflow-hidden !w-[20px] !h-[20px]">
                        <MediaBadge
                          media={platform}
                          className={"!h-[20px] !w-[20px]"}
                        />
                      </span>
                    </div>
                    <div className="flex flex-col whitespace-nowrap font-subtitle-medium overflow-hidden text-ellipsis">
                      <span>{item?.title || item?.full_name}</span>
                      {platform !== "news" && (
                        <div>
                          <span>{item?.username || item?.user_name}</span>
                          <span>@</span>
                        </div>
                      )}
                    </div>
                  </div>
                </ToolTip>

                <div className="flex gap-2 field-actions">
                  <div
                    className="size-6 flex items-center justify-center bg-[#E1E8EF80] rounded-md cursor-pointer"
                    onClick={() => changeStatusEdit(index)}
                  >
                    <PencilSimpleLine size={16} />
                  </div>
                  <div
                    className="size-6 flex items-center justify-center bg-[#E1E8EF80] rounded-md cursor-pointer"
                    onClick={() => handleRemove(index)}
                  >
                    <TrashSimple size={16} />
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
        {fields?.length < 4 && (
          <div
            id="addProfile"
            className={`flex w-[200px] font-body-medium bg-light-neutral-surface-card rounded-lg p-[18px] ${
              sticky ? "shadow-lg" : "shadow-[0_2px_20px_0_rgba(0,0,0,0.05)]"
            } shrink-0`}
          >
            <CButton
              mode="outline"
              rightIcon={<Plus size={16} />}
              className="gap-2 [direction:ltr]"
              onClick={handleAddField}
            >
              افزودن پروفایل
            </CButton>
          </div>
        )}
      </div>
    </>
  );
};

export default Fields;
