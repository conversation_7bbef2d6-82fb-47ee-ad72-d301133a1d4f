import { DownloadSimple } from "@phosphor-icons/react";
import { CButton } from "components/ui/CButton";
import ToolTip from "components/ui/ToolTip";
import PropTypes from "prop-types";
import { useEffect, useRef } from "react";

const Popup = ({ isOpen, onClose, imageSrc, url }) => {
  const popupRef = useRef(null);

  // Close the popup when clicking outside of it
  const handleOutsideClick = (event) => {
    if (popupRef.current && !popupRef.current.contains(event.target)) {
      onClose();
    }
  };

  useEffect(() => {
    if (isOpen) {
      document.addEventListener("mousedown", handleOutsideClick);
    } else {
      document.removeEventListener("mousedown", handleOutsideClick);
    }

    return () => {
      document.removeEventListener("mousedown", handleOutsideClick);
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div
        ref={popupRef}
        className="bg-white w-[50%] max-h-[80vh] p-4 rounded-lg flex flex-col justify-center overflow-hidden"
      >
        <div className="overflow-auto max-h-[70vh]">
          <img src={imageSrc} alt="Popup" className="w-full object-contain" />
        </div>
        <div className="flex justify-center !items-center pt-4 gap-4">
          <CButton
            mode="outline"
            role="error"
            onClick={onClose}
            className=" bg-red-500 text-white py-2 px-4 rounded text-center !font-button-medium !w-[8rem]"
          >
            انصراف
          </CButton>
          {url === null ? (
            <ToolTip
              comp={
                "امکان دانلود روزنامه وجود ندارد. لطفا از سایر مراجع ورزشی در بخش روزنامه‌های مشابه استفاده نمایید."
              }
              position="top"
            >
              <CButton
                type={"submit"}
                className="[direction:rtl] [width:170px!important] !py-0 !my-0 h-[10px] [display:flex] [gap:6px] border rounded-lg"
                readOnly
              >
                <DownloadSimple size={20} />
                دانلود روزنامه
              </CButton>
            </ToolTip>
          ) : (
            <button
              type={"submit"}
              size={"lg"}
              className="flex gap-2 bg-[#432FA7] text-white py-[9px] px-4 rounded-lg text-center font-button-medium !items-center"
              onClick={() => {
                const fileUrl = `${url}`;
                window.open(fileUrl);
              }}
            >
              <DownloadSimple size={20} />
              دانلود روزنامه
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

Popup.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  imageSrc: PropTypes.string,
  url: PropTypes.string,
};

export default Popup;
