import { useEffect, useState } from "react";
import Badge from "./Badge";
import {
  Broadcast,
  ChatTeardropDots,
  Eye,
  Heart,
  Repeat,
  ShareFat,
  UserFocus,
} from "@phosphor-icons/react";
import advanceSearch from "service/api/advanceSearch";
import PropTypes from "prop-types";
import { buildRequestData } from "utils/requestData";
import useSearchStore from "store/searchStore";

const Badges = () => {
  const [data, setData] = useState({});
  const [loading, setLoading] = useState(false);
  const { filters, query } = useSearchStore();

  const getData = async () => {
    setLoading(true);
    try {
      const requestData = buildRequestData(
        {
          ...filters,
          q: query,
        },
        "statistical",
      );
      const res = await advanceSearch.search(requestData);
      const platformData = {
        twitter: res?.data?.data?.twitter,
        instagram: res?.data?.data?.instagram,
        telegram: res?.data?.data?.telegram,
        eitaa: res?.data?.data?.eitaa,
        news: res?.data?.data?.news,
      };
      const stinasData = platformData[requestData.platform] || [];
      setData(stinasData);
    } catch (error) {
      setData([]);
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const selectPlatform = {
    telegram: [
      {
        title: "تعداد بازنشر",
        icon: <ShareFat color="white" />,
        color: "text-[#C70077]",
        bgColor: "bg-[#C70077]",
        count: data?.copy_count || "0",
      },
      {
        title: "تعداد بازدید",
        icon: <Eye color="white" />,
        color: "text-[#0A55E1]",
        bgColor: "bg-[#0A55E1]",
        count: Array.isArray(data)
          ? data?.find((item) => item.key === "view")?.count || "0"
          : "0",
      },
    ],
    twitter: [
      {
        title: "تعداد ریتوئیت",
        icon: <Repeat color="white" />,
        color: "text-[#3EA9F8]",
        bgColor: "bg-[#3EA9F8]",
        count: Array.isArray(data)
          ? data?.find((item) => item.key === "retweet")?.count || "0"
          : "0",
      },
      {
        title: "تعداد لایک",
        icon: <Heart color="white" />,
        color: "text-[#E0526A]",
        bgColor: "bg-[#E0526A]",
        count: Array.isArray(data)
          ? data?.find((item) => item.key === "like")?.count || "0"
          : "0",
      },
    ],
    instagram: [
      {
        title: "تعداد کامنت‌ها",
        icon: <ChatTeardropDots color="white" />,
        color: "text-[#1CB0A5]",
        bgColor: "bg-[#1CB0A5]",
        count: Array.isArray(data)
          ? data?.find((item) => item.key === "comment")?.count || "0"
          : "0",
      },
      {
        title: "تعداد لایک",
        icon: <Heart color="white" />,
        color: "text-[#E0526A]",
        bgColor: "bg-[#E0526A]",
        count: Array.isArray(data)
          ? data?.find((item) => item.key === "like")?.count || "0"
          : "0",
      },
    ],
    news: [
      // {
      //   title: "تعداد بازنشر",
      //   icon: <ShareFat color="white" />,
      //   color: "text-[#C70077]",
      //   bgColor: "bg-[#C70077]",
      //   count:
      //     data?.post_count && data?.original_count
      //       ? data?.post_count - data?.original_count
      //       : Array.isArray(data)
      //       ? data?.find((item) => item.key === "post")?.count -
      //           data?.find((item) => item.key === "original")?.count || "0"
      //       : "0",
      // },
      // {
      //   title: "تعداد محتوای تولیدی",
      //   icon: <FileText color="white" />,
      //   color: "text-[#22BEBE]",
      //   bgColor: "bg-[#22BEBE]",
      //   count: Array.isArray(data)
      //     ? data?.find((item) => item.key === "original")?.count || "0"
      //     : "0",
      // },
    ],
  };

  useEffect(() => {
    getData();
  }, [filters, query]);

  return (
    <div
      className={`${
        filters.platform === "news"
          ? "flex flex-col w-full h-full"
          : "grid grid-cols-12 *:col-span-3"
      } gap-4`}
    >
      {selectPlatform[filters.platform].map((item, index) => (
        <Badge
          key={item.title}
          count={item.count}
          title={item.title}
          image={index === 0 ? "/net3.svg" : "/net4.svg"}
          color={item.color}
          bgColor={item.bgColor}
          icon={item.icon}
          loading={loading}
        />
      ))}

      <Badge
        count={
          data.post_count ||
          (Array.isArray(data) &&
            data.find((item) => item.key === "post")?.count) ||
          "0"
        }
        title="تعداد محتوای منتشر شده"
        image="/net2.svg"
        color="text-light-warning-text-rest"
        bgColor="bg-light-warning-text-rest"
        icon={<Broadcast color="white" />}
        platform={filters.platform}
        loading={loading}
      />
      <Badge
        count={
          data.agency_count ||
          (Array.isArray(data) &&
            data.find((item) => item.key === "source")?.count) ||
          "0"
        }
        title="تعداد منابع انتشار"
        image="/net1.svg"
        color="text-[#AE2EEC]"
        bgColor="bg-[#AE2EEC]"
        platform={filters.platform}
        icon={<UserFocus color="white" />}
        loading={loading}
      />
    </div>
  );
};

Badges.propTypes = {
  platform: PropTypes.string,
  req_data: PropTypes.object,
  query: PropTypes.string,
};

export default Badges;
