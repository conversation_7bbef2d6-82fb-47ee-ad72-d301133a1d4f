import { Field, Form, Formik } from "formik";
import { CInput } from "components/ui/CInput";
import { bulletinStepOneSchema } from "utils/validationSchemas.js";
import { useBulletinStore } from "store/bulletinStore.js";
import DropDown from "./DropDown.jsx";
import TimeDropDown from "./TimeDropDown.jsx";
import SelectDay from "./SelectDay.jsx";
import { CButton } from "components/ui/CButton";
import useSearchStore from "store/searchStore.js";
import { useEffect, useState } from "react";
import Bulletin from "service/api/bulletin.js";
import { CheckCircle } from "@phosphor-icons/react";
import { notification } from "utils/helper.js";
import { ToastContainer } from "react-toastify";

const Step1 = () => {
  const { setShowFilterList, setShowSearchBox } = useSearchStore();
  const [bulletinData, setBulletinData] = useState(null);
  useEffect(() => {
    setShowFilterList(false);
    setShowSearchBox(false);
  }, []);

  const setBulletin = useBulletinStore((state) => state.setBulletin);
  const {
    type,
    title,
    description,
    period,
    custom_period,
    custom_period_time,
    id,
  } = useBulletinStore((state) => state.bulletin);

  const [day, time] = new Date()
    .toLocaleString("en-IR", {
      hour: "2-digit",
      minute: "2-digit",
      weekday: "long",
      hour12: false,
    })
    .split(" ");

  const handleSubmit = async (param) => {
    try {
      if (type === "auto") {
        let data = {
          title: param.title.trim(),
          description: param.description,
          period: period,
          custom_period_time: custom_period_time,
        };

        if (custom_period) data.custom_period = custom_period;
        const response = id
          ? await Bulletin.updatePeriodic(id, data)
          : await Bulletin.submitPeriodic(data);

        if (response.status === 200) {
          setBulletin({
            step: 2,
            title: param.title.trim(),
            description: param.description,
          });
        } else if (response.status === 201) {
          if (!response?.data?.data?.id) return;
          setBulletin({
            id: response?.data?.data?.id,
            step: 2,
            title: param.title.trim(),
            description: param.description,
          });
        } else {
          return notification.error(
            response.data.message,
            <CheckCircle className="text-light-error-text-rest" size={26} />,
          );
        }
      } else {
        const data = {
          title: param.title.trim(),
          description: param.description,
        };
        const response = id
          ? await Bulletin.updateManual(id, data)
          : await Bulletin.submitManual(data);

        if (response.status === 200) {
          setBulletin({
            step: 2,
            title: param.title.trim(),
            description: param.description,
          });
        } else if (response.status === 201) {
          if (!response?.data?.data?.id) return;
          setBulletin({
            id: response?.data?.data?.id,
            step: 2,
            title: param.title.trim(),
            description: param.description,
          });
        } else {
          return notification.error(
            "نام انتخاب شده تکراری است!",
            <CheckCircle className="text-light-error-text-rest" size={26} />,
          );
        }
      }

      return true;
    } catch (e) {
      console.error(e);
      if (e?.response?.data?.code === 403) {
        return notification.error(
          e.response.data.message,
          <CheckCircle className="text-light-error-text-rest" size={26} />,
        );
      } else {
        return notification.error(
          e.response.data.message,
          <CheckCircle className="text-light-error-text-rest" size={26} />,
        );
      }
    }
  };

  const handlePeriod = ({ value }) => {
    setBulletin({ period: value });
  };

  const handleCustom_period_time = (time) => {
    setBulletin({ custom_period_time: time });
  };

  const handleCustomPeriod = (days) => {
    setBulletin({ custom_period: days });
  };

  const selectPeriodValue = {
    one: { text: "هر روز", value: "one" },
    seven: { text: "هر هفته (شروع از امروز)", value: "seven" },
    ten: { text: "هر ۱۰ روز (شروع از امروز)", value: "ten" },
    thirty: { text: "هر ماه (شروع از امروز)", value: "thirty" },
    custom: { text: "تعیین دستی روزها", value: "custom" },
  };

  useEffect(() => {
    setBulletin({
      period: "one",
      custom_period_time: time,
    });
  }, []);

  // useEffect(() => {
  //   const fetchData = async () => {
  //     try {
  //       const response = await Bulletin.getBulletinById(id);
  //       setBulletinData(response.data.data);
  //     } catch (e) {
  //       console.error(e);
  //     }
  //   };
  //
  //   fetchData();
  // }, [id]);

  return (
    <div className="h-full w-full pt-6 [direction:rtl]">
      <Formik
        initialValues={{
          title: bulletinData?.title || title,
          description: bulletinData?.description || description,
        }}
        enableReinitialize
        validationSchema={bulletinStepOneSchema}
        onSubmit={handleSubmit}
      >
        {({
          handleChange,
          handleBlur,
          isValid,
          dirty,
          initialValues,
          values,
          isSubmitting,
        }) => (
          <Form className="flex-1 bg-white mx-auto w-1/2 h-full p-6 rounded-lg">
            <Field
              id={"title"}
              name={"title"}
              component={CInput}
              size={"lg"}
              validation={"none"}
              direction={"rtl"}
              title={"عنوان بولتن"}
              placeholder="برای مثال"
              onChange={handleChange("title")}
              onBlur={handleBlur("title")}
              className="w-full font-overline-large"
              value={initialValues.title}
              // inputProps={{ readOnly: state }}
            />
            <div className="mb-6">
              <label
                htmlFor="description"
                className="font-overline-large mb-2 inline-block"
              >
                توضیحات
              </label>
              <Field
                id={"description"}
                name={"description"}
                as="textarea"
                placeholder="برای مثال این بولتن"
                onChange={handleChange("description")}
                onBlur={handleBlur("description")}
                className="w-full h-40 rounded-md p-4 font-body-large outline-none border border-light-neutral-border-medium-rest"
              />
            </div>
            <label className="flex gap-2 items-center mb-8">
              <input
                type="checkbox"
                className="size-5"
                checked={type === "auto"}
                name={"type"}
                onChange={(e) =>
                  setBulletin({ type: e.target.checked ? "auto" : "manual" })
                }
                disabled={!!id}
              />
              <span className="font-body-medium">
                این بولتن به صورت دوره‌ای و خودکار تولید شود
              </span>
            </label>

            {type === "auto" && (
              <div className="flex flex-col gap-8 mb-8">
                <div className="flex gap-6">
                  <div className="flex flex-col gap-2 w-full">
                    <span className="font-overline-large">
                      دوره تکرار (شروع از امروز)
                    </span>
                    <DropDown
                      subsets={[
                        { text: "هر روز", value: "one" },
                        { text: "هر هفته (شروع از امروز)", value: "seven" },
                        { text: "هر ۱۰ روز (شروع از امروز)", value: "ten" },
                        { text: "هر ماه (شروع از امروز)", value: "thirty" },
                        { text: "تعیین دستی روزها", value: "custom" },
                      ]}
                      handleChange={handlePeriod}
                      initialValue={selectPeriodValue[period]}
                    />
                  </div>
                  <div className="flex flex-col gap-2 w-full">
                    <span className="font-overline-large">ساعت بررسی</span>
                    <TimeDropDown
                      handleChange={handleCustom_period_time}
                      initialHour={
                        custom_period_time?.split(":")[0] || time.split(":")[0]
                      }
                      initialMinite={
                        custom_period_time?.split(":")[1] || time.split(":")[1]
                      }
                    />
                  </div>
                </div>
                {period === "custom" && (
                  <SelectDay
                    initialValue={custom_period || day.toLowerCase()}
                    handleChange={handleCustomPeriod}
                  />
                )}
              </div>
            )}
            <div className="flex flex-row-reverse">
              <div className="w-[200px]">
                <CButton
                  className="[direction:ltr]"
                  disabled={isSubmitting}
                  readOnly={
                    type === "auto" && period === "custom"
                      ? !(
                          ((isValid && dirty) || values.title) &&
                          custom_period?.length
                        )
                      : !((isValid && dirty) || values.title)
                  }
                  size="md"
                  type="submit"
                >
                  ادامه
                </CButton>
              </div>
            </div>
          </Form>
        )}
      </Formik>

      <ToastContainer />
    </div>
  );
};

export default Step1;
