import { memo, useRef } from "react";
import TopicFields from "./components/TopicFields";
import { useCompareStore } from "store/compareStore";
import StatisticalInfo from "../charts/StatisticalInfo.jsx";
import ReleaseChartContainer from "../charts/ReleaseProcess.jsx";
import Content from "../charts/Content.jsx";
import RepeatWords from "../charts/RepeatWords.jsx";
import RepeatHashtags from "../charts/RepeatHashtag.jsx";
import Sentiment from "../charts/Sentiment.jsx";
import GenderChart from "../charts/GenderChart.jsx";
import Category from "../charts/Category.jsx";
import BestResource from "../charts/bestResources.jsx";
import SolidGuage from "../charts/SolidGuage.jsx";
import Header from "../Header.jsx";
import { useReactToPrint } from "react-to-print";
import PropTypes from "prop-types";
import Entities from "../charts/entities";
import StatisticalContent from "../charts/StatisticalContent";

const TopicCompare = ({ isEdit = false }) => {
  const { platform } = useCompareStore((state) => state.compare);
  const contentRef = useRef();
  const pageStyle = `
  @page {
    size: A3 landscape;
    margin: 0mm 5mm;
   }
  body {
    direction: rtl !important;
  }
  -webkit-print-color-adjust: exact !important; 
  color-adjust: exact !important; 
  print-color-adjust: exact !important;
`;

  const reactToPrintFn = useReactToPrint({ contentRef, pageStyle });

  return (
    <div className={"p-6 pt-0 flex flex-col gap-2"} ref={contentRef}>
      <div id="topic-compare-header">
        <Header handlePrint={reactToPrintFn} isEdit={isEdit} />
      </div>
      <div className="flex flex-col gap-2">
        <TopicFields />
        <div className="flex flex-col gap-4">
          {platform !== "news" && <StatisticalInfo />}
          {platform === "news" ? (
            <>
              <div
                className="flex gap-4 items-center w-full"
                id="content-chart-topic"
              >
                <div className="w-2/3">
                  <ReleaseChartContainer compareType="topic" />
                </div>
                <div className="w-1/3">
                  <StatisticalContent compareType={"topic"} />
                </div>
              </div>
            </>
          ) : (
            <>
              <ReleaseChartContainer compareType="topic" />
              <Entities compareType={"topic"} tabName="person" />
              {platform !== "telegram" && (
                <div id="content-chart-topic">
                  <Content compareType={"topic"} />
                </div>
              )}
            </>
          )}
          {platform === "news" && (
            <Entities compareType={"topic"} tabName="person" />
          )}
          {/* <div id="topic-repeated-words-chart">
            <RepeatWords type={"text"} compareType="topic" />
          </div> */}
          {platform !== "news" && (
            <RepeatHashtags type={"hashtag"} compareType="topic" />
          )}
          {platform !== "news" && platform !== "telegram" && (
            <div id="topic-sentiment-chart">
              <Sentiment compareType="topic" />
            </div>
          )}
          {/* {platform !== "news" && platform !== "telegram" && (
            <div id="topic-gender-chart">
              <GenderChart />{" "}
            </div>
          )}
          {platform !== "news" && platform !== "telegram" && <SolidGuage />} */}
          <div id="topic-category-chart">
            <Category compareType="topic" />
          </div>
          <Entities compareType={"topic"} tabName="organ" />
          <BestResource />
        </div>
      </div>
    </div>
  );
};

TopicCompare.propTypes = {
  isEdit: PropTypes.bool,
};

export default memo(TopicCompare);
