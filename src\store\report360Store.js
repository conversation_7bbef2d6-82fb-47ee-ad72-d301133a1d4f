import { create } from "zustand";

// const oneMonthInMillis = 30 * 24 * 60 * 60 * 1000;
const threeMonthsInMillis = 90 * 24 * 60 * 60 * 1000;

const report360DefaultValue = {
  id: "",
  step: 1,
  type: "",
  title: "",
  date: {
    from: new Date(new Date().getTime() - threeMonthsInMillis),
    to: new Date(),
  },
  profiles: [],
  currentProfileId: null,
  profile: {},
  location: {},
  entity: {},
  isFromTopSource: false,
};
export const useReport360Store = create((set, get) => ({
  report: report360DefaultValue,
  wordCloudData: {},

  getCurrentProfile: () => {
    const state = get();
    const currentId = state.report.currentProfileId;
    return currentId ? state.report.profiles[currentId] || {} : {};
  },

  setReport: (param) =>
    set((state) => {
      const newReport = { ...state.report, ...param };

      if (param.profile) {
        const profileId =
          param.profile.id ||
          param.profile.user_name ||
          param.profile.key ||
          "default";
        newReport.profiles = {
          ...state.report.profiles,
          [profileId]: {
            ...state.report.profiles[profileId],
            ...param.profile,
          },
        };
        newReport.currentProfileId = profileId;
        newReport.profile = newReport.profiles[profileId];
      }

      return { report: newReport };
    }),

  resetReport: () => set(() => ({ report: report360DefaultValue })),

  updateReport: (param) =>
    set((state) => {
      const newReport = {
        ...state.report,
        ...param,
        location: { ...state.report.location, ...(param.location ?? {}) },
        entity: { ...state.report.entity, ...(param.entity ?? {}) },
      };

      if (param.profile) {
        const currentId = state.report.currentProfileId;
        if (currentId) {
          newReport.profiles = {
            ...state.report.profiles,
            [currentId]: {
              ...state.report.profiles[currentId],
              ...param.profile,
            },
          };
          newReport.profile = newReport.profiles[currentId];
        } else {
          newReport.profile = { ...state.report.profile, ...param.profile };
        }
      }

      return { report: newReport };
    }),

  addProfile: (profileData) =>
    set((state) => {
      const profileId =
        profileData.id || profileData.user_name || profileData.key || "default";
      const newProfiles = {
        ...state.report.profiles,
        [profileId]: { ...profileData },
      };

      return {
        report: {
          ...state.report,
          profiles: newProfiles,
          currentProfileId: profileId,
          profile: newProfiles[profileId],
        },
      };
    }),

  setCurrentProfile: (profileId) =>
    set((state) => {
      const profile = state.report.profiles[profileId];
      if (!profile) return state;

      return {
        report: {
          ...state.report,
          currentProfileId: profileId,
          profile: profile,
        },
      };
    }),

  removeProfile: (profileId) =>
    set((state) => {
      const newProfiles = { ...state.report.profiles };
      delete newProfiles[profileId];

      let newCurrentId = state.report.currentProfileId;
      let newProfile = state.report.profile;

      if (state.report.currentProfileId === profileId) {
        const remainingIds = Object.keys(newProfiles);
        newCurrentId = remainingIds.length > 0 ? remainingIds[0] : null;
        newProfile = newCurrentId ? newProfiles[newCurrentId] : {};
      }

      return {
        report: {
          ...state.report,
          profiles: newProfiles,
          currentProfileId: newCurrentId,
          profile: newProfile,
        },
      };
    }),

  clearAllProfiles: () =>
    set((state) => ({
      report: {
        ...state.report,
        profiles: {},
        currentProfileId: null,
        profile: {},
      },
    })),

  setEntitiesData: (tabName, data) =>
    set((state) => ({
      wordCloudData: { ...state.wordCloudData, [tabName]: data },
    })),
}));
