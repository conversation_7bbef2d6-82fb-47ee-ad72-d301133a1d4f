import { useEffect, useState } from "react";
import Divider from "components/ui/Divider";
import Doughnut from "components/Charts/Doughnut";
import { fixPercentToShow } from "utils/helper";
import advanceSearch from "service/api/advanceSearch";
import { sentiment } from "utils/selectIcon";
import { SpinnerGap } from "@phosphor-icons/react";
import { useMeasure } from "react-use";
import PropTypes from "prop-types";
import PLATFORMS from "constants/platforms.js";
import useSearchStore from "store/searchStore.js";
import { buildRequestData } from "utils/requestData";
import ExportMenu from "components/ExportMenu/index.jsx";

const Sentiment = ({ showDataLabels }) => {
  const { filters, query } = useSearchStore();
  const [data, setData] = useState({});
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [ref, { width }] = useMeasure();

  const getData = async () => {
    setLoading(true);
    try {
      const requestData = buildRequestData(
        {
          ...filters,
          q: query,
        },
        "sentiments"
      );
      const res = await advanceSearch.search(requestData);
      let platformData;
      switch (filters.platform) {
        case "twitter":
          platformData = res.data.data.twitter;
          break;
        case "instagram":
          platformData = res.data.data.instagram;
          break;
        case "telegram":
          platformData = res.data.data.telegram;
          break;
        case "eitaa":
          platformData = res.data.data.eitaa;
          break;
        default:
          platformData = res.data.data.news;
      }
      const converted = {};
      platformData.forEach((item) => {
        converted[item.key] = item.count / res?.data?.data?.total;
      });
      setData(converted);
      setTotal(res?.data?.data?.total);
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  // Prepare series and time for ExportMenu
  const series = [
    {
      name: "Sentiment",
      data: [data?.positive, data?.neutral, data?.negative].map((value) =>
        value ? value * 100 : 0
      ),
      time: ["مثبت", "خنثی", "منفی"],
    },
  ];

  const time = ["مثبت", "خنثی", "منفی"];

  useEffect(() => {
    if (filters.platform === PLATFORMS.NEWS) return;
    getData();
  }, [query, filters]);

  if (filters.platform === PLATFORMS.NEWS) return <></>;

  return (
    <div className="sentiment-container" ref={ref}>
      <div className="flex items-center pb-4 justify-between">
        <ExportMenu
          chartSelector=".sentiment-container"
          fileName="sentiment-data"
          series={series}
          time={time}
          excelHeaders={["Sentiment", "Percentage"]}
          onError={(error) => console.error("Export error:", error)}
          menuItems={["PNG", "JPEG", "Excel"]}
        />
        <p className="font-subtitle-large text-right">تحلیل احساسات</p>
      </div>
      <Divider />
      {loading ? (
        <div className="w-full h-[365px] flex justify-center items-center">
          <SpinnerGap size={40} className="animate-spin" />
        </div>
      ) : Object.keys(data).length === 0 ? (
        <div className="h-[270px] flex items-center justify-center font-subtitle-medium">
          داده ای برای نمایش وجود ندارد
        </div>
      ) : (
        <Doughnut
          showDataLabels={showDataLabels}
          colLayout
          name="gender"
          minWidth="100px"
          height={365}
          data={[
            {
              name: "مثبت",
              y: data?.positive,
            },
            {
              name: "خنثی",
              y: data?.neutral,
            },
            {
              name: "منفی",
              y: data?.negative,
            },
          ]}
          legendFormatter={function () {
            return `<div dir="rtl" style="display:grid;grid-template-columns:1fr 1fr;font-family:iranyekan,serif;width:${
              width - 50
            }px;padding:2px;">
<span style="font-size:16px">
${fixPercentToShow(this.y) || ((data[0]?.count / total) * 100).toFixed(2)}
</span>
<div
style="justify-self:right"
>
<div style="display:flex;gap:4px">
<span style="color:${this.color};font-size:14px">${this.name}</span> 
<img src=${sentiment[this.name]}  alt="sentimentIcon"/>
</div>
</div>
</div>`;
          }}
          tooltipFormatter={function () {
            return `<div style="display:flex;flex-direction:column;gap:8px;text-align:center;font-family:iranyekan"><div>${
              this.key
            }</div><div>${fixPercentToShow(this.y)}</div></div>`;
          }}
          colors={["#1CB0A5", "#00000080", "#E0526A"]}
        />
      )}
    </div>
  );
};

Sentiment.propTypes = {
  platform: PropTypes.string,
  req_data: PropTypes.object,
  query: PropTypes.string,
  showDataLabels: PropTypes.bool,
};

export default Sentiment;
