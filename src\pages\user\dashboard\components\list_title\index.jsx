import { useState } from "react";
import { CaretLeft } from "@phosphor-icons/react";
import { useNavigate } from "react-router-dom";
import ToolTip from "../tooltip";
import useSearchStore from "store/searchStore.js";

const ListTile = ({ data }) => {
  const navigate = useNavigate();
  const [showToolTip, setShowToolTip] = useState(false);
  const { setCluster, saveState, setQuery } = useSearchStore();

  return (
    <div className="flex justify-between [direction:rtl] hover:cursor-pointer">
      <div
        className="font-subtitle-large text-light-neutral-text-high relative"
        onMouseEnter={() => setShowToolTip(true)}
        onMouseLeave={() => setShowToolTip(false)}
      >
        <div
          className="flex flex-row "
          onClick={() => {
            setCluster({
              name: data?.param || "",
              counts: data?.counts || [],
              keywords: data?.keywords || [],
              total: data?.total_counts || 0,
            });

            setQuery(`${data?.keywords?.join(" OR ")}`);
            saveState();
            return navigate("/app/advanced-search");
          }}
        >
          <span>
            مهم‌ترین مطالب مرتبط با دسته {data?.keywords[0].replace("#", "")}
          </span>
          {/* <p className="flex gap-1">
            {"("}
            <span>{data?.keywords[1]}</span>
            <span>,</span>
            <span>{data?.keywords[2]}</span> <span>,...</span>
            {")"}
          </p> */}
        </div>
        {showToolTip && (
          <div className="absolute bottom-8">
            <ToolTip title={data?.keywords[0]}>
              {data?.keywords.slice(0, 5).map((keyword) => (
                <p key={keyword}>{keyword}</p>
              ))}
            </ToolTip>
          </div>
        )}
      </div>

      <div
        className="flex items-center gap-1 text-light-neutral-text-large cursor-pointer"
        onClick={() => {
          setCluster({
            name: data?.param || "",
            counts: data?.counts || [],
            keywords: data?.keywords || [],
            total: data?.total_counts || 0,
          });

          setQuery(`${data?.keywords?.join(" OR ")}`);
          saveState();
          return navigate("/app/advanced-search");
        }}
      >
        <span className="font-overline-medium">بیشتر</span>
        <CaretLeft size={12} />
      </div>
    </div>
  );
};

export default ListTile;
