import { User } from "@phosphor-icons/react";
import { CButton } from "components/ui/CButton";
import { CInput } from "components/ui/CInput";
import Divider from "components/ui/Divider";
import { Field, Form, Formik } from "formik";
import { useBreadcrumb } from "hooks/useBreadcrumb";
import { useState } from "react";
import { updateProfileSchema } from "utils/validationSchemas";

const UserInfoSettings = () => {
  const [tabs, setTabs] = useState([
    "اطلاعات کاربری",
    "تنظیمات دسترسی",
    "اقدامات",
  ]);
  const [activeTab, setActiveTab] = useState("اطلاعات کاربری");
  const breadcrumbList = [
    { title: "کاربران", link: "/admin/user/list" },
    { title: "اطلاعات کاربر پرهام", link: "/admin/user/list" },
    { title: "تنظیمات کاربری" },
  ];
  useBreadcrumb(breadcrumbList);
  const [initialValues, setInitialValues] = useState({
    first_name: "",
    last_name: "",
    username: "",
    email: "",
    phone_number: "",
    description: "",
  });
  const handleSubmit = () => {
    console.log();
  };

  return (
    <div className="flex justify-center items-center">
      <div className="bg-light-neutral-surface-card w-[605px] p-6 pt-0 rounded-b-[8px]">
        <div className="flex items-center gap-6 mb-7">
          <div className="rounded-full w-[120px] h-[120px] flex items-center justify-center bg-light-neutral-background-medium">
            <User className="text-light-primary-text-rest" size={35} />
          </div>
          <div>
            <div className="flex items-center gap-4 mb-4">
              <p className="font-headline-medium">username.username</p>
              <span className="font-body-small text-light-success-text-rest bg-light-success-background-highlight px-3 py-1 rounded-[3px]">
                فعال
              </span>
            </div>
            <div className="flex items-center gap-4">
              <p className="font-body-medium text-light-neutral-text-medium">
                تاریخ عضویت
              </p>
              <p className="font-body-medium">۱۲:۳۴:۵۴ - ۱۴۰۲/۱۱/۰۱</p>
            </div>
          </div>
        </div>
        {/* Form */}
        <Formik
          initialValues={initialValues}
          validationSchema={updateProfileSchema}
          onSubmit={handleSubmit}
          enableReinitialize={true}
        >
          {({ handleChange, handleBlur, values }) => (
            <Form className="flex flex-col gap-4">
              <div className="flex flex-col w-full justify-end gap-1 font-body-medium text-light-neutral-text-high">
                <div className="flex flex-row w-full gap-6 mt-7">
                  <Field
                    id="first_name"
                    name="first_name"
                    component={CInput}
                    size="lg"
                    validation="none"
                    direction="rtl"
                    title="نام"
                    placeholder="نام کاربر"
                    onChange={handleChange}
                    onBlur={handleBlur}
                    className="w-1/2 font-overline-large"
                    value={values.first_name}
                  />
                  <Field
                    id="last_name"
                    name="last_name"
                    component={CInput}
                    size="lg"
                    validation="none"
                    direction="rtl"
                    title="نام خانوادگی"
                    placeholder="نام خانوادگی کاربر"
                    onChange={handleChange}
                    onBlur={handleBlur}
                    className="w-1/2 font-overline-large"
                    value={values.last_name}
                  />
                </div>
                <div className="flex flex-row w-full gap-6">
                  <Field
                    id="phone_number"
                    name="phone_number"
                    component={CInput}
                    size="lg"
                    validation="none"
                    direction="rtl"
                    title="شماره موبایل"
                    placeholder="۰۹۱۲۳۴۵۶۷۸۹"
                    onChange={handleChange}
                    onBlur={handleBlur}
                    className="w-1/2 font-overline-large"
                    value={values.phone_number}
                  />
                  <Field
                    id="email"
                    name="email"
                    component={CInput}
                    size="lg"
                    validation="none"
                    direction="rtl"
                    title="ایمیل"
                    placeholder="<EMAIL>"
                    onChange={handleChange}
                    onBlur={handleBlur}
                    className="w-1/2 font-overline-large"
                    value={values.email}
                  />
                </div>
                <div className="flex flex-col w-full">
                  <label
                    htmlFor="description"
                    className="font-overline-large mb-2 inline-block"
                  >
                    توضیحات
                  </label>
                  <Field
                    id="description"
                    name="description"
                    as="textarea"
                    placeholder="هرگونه توضیحات در مورد کاربر"
                    onChange={handleChange}
                    onBlur={handleBlur}
                    className="w-full h-40 rounded-md p-4 font-body-large outline-none border border-light-neutral-border-medium-rest"
                    value={values.description}
                  />
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-full">
                  <CButton role="neutral">انصراف</CButton>
                </div>
                <div className="w-full">
                  <CButton type="submit">ذخیره</CButton>
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default UserInfoSettings;
