import { useEffect, useState } from "react";
import Divider from "components/ui/Divider";
import advanceSearch from "service/api/advanceSearch";
import { SpinnerGap } from "@phosphor-icons/react";
import Sunburst from "components/Charts/Sunburst";
import { toPersianNumber } from "utils/helper";
import PropTypes from "prop-types";
import useSearchStore from "store/searchStore.js";
import { buildRequestData } from "utils/requestData";
import { SUBJECT_CATEGORIES } from "constants/subject-category.js";
import ExportMenu from "components/ExportMenu/index.jsx";

const Category = () => {
  const { filters, query } = useSearchStore();
  const [data, setData] = useState([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);

  const colors = [
    "#87D7F0",
    "#47DBB6",
    "#FF868B",
    "#4EA1FA",
    "#A6F377",
    "#FF9EEF",
    "#BEA4F4",
    "#ab8bee",
    "#8b61e3",
  ];

  const getData = async () => {
    setLoading(true);
    try {
      const requestData = buildRequestData(
        {
          ...filters,
          q: query,
        },
        "categories"
      );
      const res = await advanceSearch.search(requestData);

      const platformData = res?.data?.data?.[filters.platform] || [];
      const total = res?.data?.data?.total || 0;
      const help = [];
      platformData?.forEach((item, index) => {
        const name =
          SUBJECT_CATEGORIES.filter((x) => x.value === item.key)[0]?.label ||
          "---";
        help.push({
          id: `1.${index + 1}`,
          parent: "0.0",
          name: name,
          value: item?.count / total,
        });
      });
      setData({ original: platformData, help });
      setTotal(total);
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // Prepare series and time for ExportMenu
  const series =
    data?.original?.map((item, index) => ({
      name:
        SUBJECT_CATEGORIES.filter((x) => x.value === item.key)[0]?.label ||
        `Category ${index + 1}`,
      data: item.data?.map((x) => x.value * 100) || [item.count / total],
      time: item.data?.map((x) => x.name) || [
        SUBJECT_CATEGORIES.filter((x) => x.value === item.key)[0]?.label ||
          "---",
      ],
    })) || [];

  const time =
    data?.original?.flatMap(
      (item) =>
        item.data?.map((x) => x.name) || [
          SUBJECT_CATEGORIES.filter((x) => x.value === item.key)[0]?.label ||
            "---",
        ]
    ) || [];

  useEffect(() => {
    getData();
  }, [query, filters]);

  return (
    <div className="category-container">
      <div className="flex items-center pb-4 justify-between">
        <ExportMenu
          chartSelector=".category-container"
          fileName="category-data"
          series={series}
          time={time}
          excelHeaders={["Category", ...series.map((s) => s.name)]}
          onError={(error) => console.error("Export error:", error)}
          menuItems={["PNG", "JPEG", "Excel"]}
        />
        <p className="font-subtitle-large text-right">دسته‌بندی موضوعی محتوا</p>
      </div>
      <Divider />
      {loading ? (
        <div className="w-full h-[430px] flex justify-center items-center">
          <SpinnerGap size={40} className="animate-spin" />
        </div>
      ) : data?.original?.length === 0 ? (
        <div className="h-[430px] flex items-center justify-center font-subtitle-medium">
          داده ای برای نمایش وجود ندارد
        </div>
      ) : (
        <div className="h-[430px] w-full flex justify-end">
          <div className="flex flex-1 justify-center">
            <Sunburst data={data?.help || []} />
          </div>
          <div className="w-1/3 overflow-auto scrollbar-thin flex flex-col gap-4 p-4">
            {data?.original?.map((item, index) => (
              <div className="flex flex-col gap-4" key={index}>
                <div className="flex justify-between font-body-large py-1">
                  <span>
                    {toPersianNumber(((item.count / total) * 100).toFixed(1)) +
                      "%"}
                  </span>
                  <div className="flex gap-2 items-center">
                    <span>
                      {SUBJECT_CATEGORIES.filter((x) => x.value === item.key)[0]
                        ?.label || "---"}
                    </span>
                    <div
                      className="size-3 rounded-full"
                      style={{ backgroundColor: colors[index] }}
                    ></div>
                  </div>
                </div>
                {item.data?.map((x, i) => (
                  <div
                    className="flex justify-between font-body-medium mr-5"
                    key={i}
                  >
                    <span>
                      {toPersianNumber((x.value * 100).toFixed(1)) + " %"}
                    </span>
                    <span>{x.name}</span>
                  </div>
                ))}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

Category.propTypes = {
  platform: PropTypes.string,
  req_data: PropTypes.object,
  query: PropTypes.string,
};

export default Category;
