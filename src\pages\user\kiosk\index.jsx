import { MagnifyingGlass } from "@phosphor-icons/react";
import { CButton } from "components/ui/CButton";
import { CInput } from "components/ui/CInput";
import "./style.css";
import { useEffect, useMemo, useState } from "react";
import newsPaper from "service/api/newsPaper";
import RenderSwiperSection from "./components/renderSwiperSection";
import "swiper/swiper-bundle.css";
import { useNavigate } from "react-router-dom";
import DateCategoryFilters from "./components/dateCategoryFilters";
import { useBreadcrumb } from "hooks/useBreadcrumb";
import "./style.css";
import { Card } from "components/ui/Card";
import debounce from "lodash.debounce";

const Kiosk = () => {
  const [cards, setCards] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchParams, setSearchParams] = useState({
    searchValue: "",
    selectedCategories: [],
    fromDate: null,
    toDate: null,
    exact_match: false,
  });
  const [topNewsPapers, setTopNewsPapers] = useState([]);
  const [dateModal, setDateModal] = useState(false);
  const [categoryModal, setCategoryModal] = useState(false);
  const [loaded, setLoaded] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState([]);
  const [selectedDateRange, setSelectedDateRange] = useState({
    from: null,
    to: null,
  });

  const [selectedCategories, setSelectedCategories] = useState([]);
  useBreadcrumb([{ title: "پیشخوان" }]);
  const categoryMap = {
    ورزشی: "sport",
    اقتصادی: "economic",
    بین‌الملل: "international",
    سایر: "other",
  };

  const categories = Object.keys(categoryMap);

  const navigate = useNavigate();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await newsPaper.get();
        setCards(response.data.data);
        setLoading(false);
      } catch (err) {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  useEffect(() => {
    const fetchTopNews = async () => {
      try {
        const response = await newsPaper.getTop();
        setTopNewsPapers(response.data.data);
        setLoading(false);
      } catch (err) {
        setLoading(false);
      }
    };
    fetchTopNews();
  }, []);

  const handleCategoryChange = (category) => {
    setSelectedCategories((prev) => {
      const isSelected = prev?.includes(category);
      const newSelected = isSelected
        ? prev?.filter((c) => c !== category)
        : [...prev, category];

      return newSelected;
    });
  };

  useEffect(() => {
    const englishValues = selectedCategories.map((cat) => categoryMap[cat]);

    setSearchParams((prev) => ({
      ...prev,
      selectedCategories: englishValues,
    }));
  }, [selectedCategories]);

  const handleDateChange = (dates) => {
    const { from, to } = dates;
    setSearchParams((prev) => ({
      ...prev,
      fromDate: from,
      toDate: to,
    }));
    setSelectedDateRange({ from, to });
  };

  const handleSearch = (event) => {
    if (event && event.key === "Enter") {
      navigate(`/app/newspaper/newspaper-results`, { state: searchParams });
    }
  };

  const fetchSuggestions = useMemo(
    () =>
      debounce(async (newValue) => {
        if (newValue.length > 2) {
          try {
            const params = { q: newValue };
            const suggest = await newsPaper.searchSuggest(params);
            console.log(
              "suggest?.data?.data?.suggest",
              suggest?.data?.data?.suggest
            );

            setSuggestions(suggest?.data?.data?.suggest);
            setShowSuggestions(true);
          } catch (error) {
            console.error("Error fetching suggestions:", error);
            setShowSuggestions(false);
          }
        } else {
          setShowSuggestions(false);
        }
      }, 1000),
    []
  );

  useEffect(() => {
    return () => fetchSuggestions.cancel();
  }, [fetchSuggestions]);

  const handleInputChange = (e) => {
    const newValue = e.target.value.trim();

    setSearchParams((prev) => ({
      ...prev,
      searchValue: newValue,
    }));

    if (newValue.length === 0) {
      setShowSuggestions(false);
      return;
    }

    fetchSuggestions(newValue);
  };

  const handleSuggestionClick = (suggest) => {
    setSearchParams((prev) => ({
      ...prev,
      searchValue: suggest,
    }));

    setShowSuggestions(false);
  };

  useEffect(() => {
    setLoaded(true);
  }, []);

  const economicCards = cards.find((card) => card.economic)?.economic || [];
  const socialCards = cards.find((card) => card.social)?.social || [];
  const sportCards = cards.find((card) => card.sport)?.sport || [];
  const internationalCards =
    cards.find((card) => card.international)?.international || [];

  if (loading) return <div>Loading...</div>;

  return (
    <>
      <div className="font-body-medium mx-5 h-full bg-white p-5 overflow-hidden rounded-lg shadow-md z-[-1]">
        <div className="flex flex-row gap-[16px]">
          <CInput
            id={"q"}
            name={"q"}
            inset={true}
            headingIcon={<MagnifyingGlass />}
            size={"lg"}
            validation={"none"}
            inputProps={{ onKeyDown: handleSearch }}
            direction={"rtl"}
            placeholder={"نام روزنامه را جست‌و‌جو کنید"}
            onChange={handleInputChange}
            value={searchParams.searchValue}
            className={"flex-1 !mb-0"}
          />
          <CButton
            type={"submit"}
            onClick={() =>
              navigate(`/app/newspaper/newspaper-results`, {
                state: searchParams,
              })
            }
            size={"lg"}
            className={"[direction:rtl] [width:150px!important]"}
          >
            جست‌و‌جو
          </CButton>
        </div>
        <div
          className={`absolute left-0 mx-5 right-0 z-10 p-4 bg-gray-100 rounded-lg shadow-md origin-top transition-transform duration-700 ${
            showSuggestions ? "scale-y-100" : "scale-y-0"
          }`}
          style={{ transformOrigin: "top" }}
        >
          <ul>
            {suggestions?.map((suggest, index) => (
              <li
                key={index}
                className="p-2 hover:bg-gray-200 cursor-pointer"
                onClick={() => handleSuggestionClick(suggest)}
              >
                {suggest}
              </li>
            ))}
          </ul>
        </div>
        <div className="pt-1">
          <DateCategoryFilters
            dateModal={dateModal}
            setDateModal={setDateModal}
            handleDateChange={handleDateChange}
            categoryModal={categoryModal}
            setCategoryModal={setCategoryModal}
            categories={categories}
            selectedCategories={selectedCategories}
            handleCategoryChange={handleCategoryChange}
            selectedDateRange={selectedDateRange}
            setSearchParams={setSearchParams}
          />
        </div>
      </div>

      <div style={{ fontFamily: "iranyekan" }} className="pt-4">
        <RenderSwiperSection
          category="economic"
          cards={economicCards}
          title="اقتصادی"
        />
        <RenderSwiperSection
          category="sport"
          cards={sportCards}
          title="ورزشی"
        />
        <RenderSwiperSection
          category="international"
          cards={internationalCards}
          title="بین‌الملل"
        />
        <Card className="flex flex-col gap-6 px-5 !w-[96.5%] mb-5 mx-auto border-r-4 h-full">
          <h2 className="font-bold border-r-8 pr-2 mr-5 py-1 border-[#6F5CD1]">
            برترین منابع
          </h2>
          <div className="flex justify-center gap-[6%]">
            {topNewsPapers?.slice(0, 6).map((data, index) => (
              <div
                className={`flex flex-col justify-between items-center cursor-pointer card-animation ${
                  loaded ? `card-delay-${(index % 4) + 1}` : ""
                }`}
                key={index}
              >
                <div>
                  <img
                    src={`${data?.logo_image}`}
                    alt={`Cover for ${data?.agency}`}
                    className="circle-cards border-2 border-[#cccccc] p-2 bg-[#ffffff] flex justify-center items-center rounded-[130px] shadow-xl transition-shadow duration-300 hover:shadow-xl"
                    onClick={() =>
                      navigate(
                        `/app/newspaper/special-newspaper/${data?.id}/${data?.agency}`
                      )
                    }
                  />
                  <span className="flex circle-cards-text justify-center font-body-medium font-bold py-3">
                    {data?.agency}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </Card>
        <RenderSwiperSection
          category="other"
          cards={socialCards}
          title="سایر ( سیاسی، اجتماعی، فرهنگی )"
        />
      </div>
    </>
  );
};

export default Kiosk;
