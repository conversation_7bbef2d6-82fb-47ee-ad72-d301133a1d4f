import { useBreadcrumb } from "hooks/useBreadcrumb";
import UserData from "./components/UserData";
import UserPlanState from "./components/UserPlanState";
import UserPlanHistory from "./components/UserPlanHistory";
import UserWallet from "./components/UserWallet";
import UserSecuritySettings from "./components/UserSecuritySettings";
import UserActivitiesTable from "./components/user-activities-table";
import ReportCardBox from "./components/ReportCardBox";
import reportboxInfo from "./constants/reportboxInfo";

const AdminUserDetails = () => {
  const breadcrumbList = [
    { title: "کاربران", link: "/admin/user/list" },
    { title: "اطلاعات کاربر پرهام" },
  ];
  useBreadcrumb(breadcrumbList);

  return (
    <>
      <div className="grid grid-cols-12  mx-auto gap-4 lg:px-6">
        <div className="flex items-center gap-4 justify-center col-span-12">
          {reportboxInfo?.slice(0, 5)?.map((item) => (
            <ReportCardBox
              key={item?.title}
              title={item?.title}
              count={item?.count}
              icon={item?.icon}
              color={item?.color}
              variant={item?.variant}
            />
          ))}
        </div>
        <div className="flex items-center gap-4 justify-center col-span-12">
          {reportboxInfo?.slice(5, 10)?.map((item) => (
            <ReportCardBox
              key={item?.title}
              title={item?.title}
              count={item?.count}
              icon={item?.icon}
              color={item?.color}
              variant={item?.variant}
            />
          ))}
        </div>
        <div className="col-span-4 row-span-2 bg-light-neutral-surface-card rounded-md">
          <UserData />
        </div>
        <div className="col-span-4 bg-light-neutral-surface-card rounded-md">
          <UserPlanState />
        </div>
        <div className="col-span-4 bg-light-neutral-surface-card rounded-md">
          <UserWallet />
        </div>
        <div className="col-span-4 bg-light-neutral-surface-card rounded-md">
          <UserPlanHistory />
        </div>
        <div className="col-span-4 bg-light-neutral-surface-card rounded-md">
          <UserSecuritySettings />
        </div>
        <div className="col-span-12 bg-light-neutral-surface-card rounded-md">
          <UserActivitiesTable />
        </div>
      </div>
    </>
  );
};

export default AdminUserDetails;
