import clsx from "clsx";

const BulletinCard = ({
  image,
  caption,
  name = "not-set",
  handleClick,
  checked = false,
}) => {
  return (
    <div
      // className="flex items-center justify-center cursor-pointer"
      onClick={() => handleClick(name, !checked)}
      className={clsx(
        "border flex items-center justify-center relative rounded-lg cursor-pointer",
        checked
          ? "border-light-primary-border-rest"
          : "border-light-neutral-border-low-rest",
      )}
      // onClick={(e) => handleChange(value)}
    >
      <input
        type="checkbox"
        className="size-[13.5px] absolute top-[6px] right-[6px]"
        onChange={() => handleClick(name, !checked)}
        checked={checked}
      />
      <div className="flex flex-col gap-4 items-center justify-center bg-light-neutral-surface-card rounded-lg w-[254px] h-[180px]">
        <img src={image} alt={caption} />
        <p className="font-body-medium [direction:rtl]">{caption}</p>
      </div>
    </div>
  );
};

export default BulletinCard;
