import AlertCard from "./AlertCard";
const List = ({ data, setRerender, setLoading }) => {
  const selectMedia = {
    telegram: "telegram",
    bale: "bale",
    eitaa: "eitaa",
  };

  return (
    <div className="flex flex-col gap-4">
      {data.map((item) => (
        <AlertCard
          media={selectMedia[item.messenger]}
          key={item.id}
          data={item}
          alert_id={item.id}
          setRerender={setRerender}
          setLoading={setLoading}
        />
      ))}
    </div>
  );
};

export default List;
