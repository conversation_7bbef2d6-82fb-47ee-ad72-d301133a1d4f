import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";

const PoliticalAgeAverageRadialChart = ({
  data,
  categories,
  colors,
  tooltipFormatter,
}) => {
  const options = {
    colors,
    chart: {
      type: "column",
      inverted: true,
      polar: true,
    },
    credits: { enabled: false },
    title: {
      text: null,
    },
    subtitle: {
      text: null,
    },
    tooltip: {
      headerFormat: "",
      backgroundColor: "#efefef",
      textAlign: "center",
      useHTML: true,
      shadow: false,
      formatter: tooltipFormatter,
    },
    pane: {
      size: "75%",
      innerSize: "10%",
      endAngle: 270,
    },
    xAxis: {
      tickInterval: 1,
      labels: {
        align: "right",
        useHTML: true,
        allowOverlap: true,
        step: 1,
        y: 3,
        x:-25,
        style: {
          fontSize: "13px",
          fontFamily: "iranyekan",
        },
      },
      lineWidth: 0,
      gridLineWidth: 0,
      categories,
    },
    yAxis: {
      lineWidth: 0,
      tickInterval: 25,
      reversedStacks: false,
      endOnTick: true,
      showLastLabel: true,
      gridLineWidth: 0,
      labels: {
        enabled: false, // Disable numbers on y-axis
      },
    },
    plotOptions: {
      column: {
        stacking: "normal",
        borderWidth: 0.5,
        pointPadding: 0,
        groupPadding: 0.15,
        borderRadius: "50%",
      },
    },
    legend: {
      enabled: false,
    },
    series: data,
  };
  return <HighchartsReact highcharts={Highcharts} options={options} />;
};

export default PoliticalAgeAverageRadialChart;
