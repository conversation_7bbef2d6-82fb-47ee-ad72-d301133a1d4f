export const setLanguage = (platform, lang, set) => {
  set((l) => {
    const copy = JSON.parse(JSON.stringify(l));
    copy.platform[platform].languages = [...lang];
    return copy;
  });
};

export const setCategorie = (platform, category, set) => {
  set((l) => {
    const copy = JSON.parse(JSON.stringify(l));
    copy.platform[platform].categories = [...category];
    return copy;
  });
};

export const setHashtag = (platform, hashtags, set) => {
  set((l) => {
    const copy = JSON.parse(JSON.stringify(l));
    copy.platform[platform].hashtags = [...hashtags];
    return copy;
  });
};

export const setContainCopy = (platform, contain_copy, set) => {
  set((l) => {
    const copy = JSON.parse(JSON.stringify(l));
    copy.platform[platform].contain_copy = contain_copy;
    return copy;
  });
};

export const setSentiment = (platform, sentiment, set) => {
  set((l) => {
    const copy = JSON.parse(JSON.stringify(l));
    copy.platform[platform].sentiment = sentiment;
    return copy;
  });
};

export const setGender = (platform, gender, set) => {
  set((l) => {
    const copy = JSON.parse(JSON.stringify(l));
    copy.platform[platform].gender = gender;
    return copy;
  });
};
