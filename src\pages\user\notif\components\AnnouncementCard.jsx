import { Alarm, NewspaperClipping } from "@phosphor-icons/react";
import AlertBtn from "./AlertBtn";
import BulletinBtn from "components/NotificationsInDrawer/BulletinBtn";
import { parseTimeToPersian } from "utils/helper";

const AnnouncementCard = ({
  type = "alert",
  title = "",
  description = "",
  read = false,
  date,
  link,
  id,
  readNotifHandler,
  showMarkAsSeen = true,
}) => {
  return (
    <div className={`${read ? "bg-gray-200" : ""} py-4 flex items-center gap-2 px-3 rounded-md`}>
      <div
        className={`size-8 bg-light-neutral-surface-highlight rounded-lg border-2 ${
          read
            ? "border-light-neutral-surface-highlight"
            : "border-light-warning-border-rest"
        } flex items-center justify-center`}
      >
        {type === "alert" ? (
          <Alarm size={18} color="#00000080" />
        ) : (
          <NewspaperClipping size={18} color="#00000080" />
        )}
      </div>
      <div className="flex justify-between items-center gap-2 w-full">
        <div>
          <div
            className={`${!read ? "font-subtitle-medium" : "font-body-medium"}`}
          >
            {title}
          </div>
          <div className="font-overline-medium">{parseTimeToPersian(date)}</div>
        </div>
        <div>
          <div className="flex flex-row-reverse">
            {type === "alert" ? (
              <AlertBtn read={read} link={link} id={id} />
            ) : (
              <BulletinBtn
                id={`${id}`}
                link={link}
                showMarkAsSeen={showMarkAsSeen}
                readNotifHandler={() => readNotifHandler(id)}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnnouncementCard;
