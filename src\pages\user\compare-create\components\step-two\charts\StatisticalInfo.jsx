// src/components/StatisticalInfo.jsx
import { memo, useEffect, useState } from "react";
import { Card } from "components/ui/Card.jsx";
import { useCompareStore } from "store/compareStore.js";
import { Eye, Upload, UsersThree, SpinnerGap } from "@phosphor-icons/react";
import BasicBar from "./BasicBar.jsx";
import advanceSearch from "service/api/advanceSearch.js";
import { buildRequestData } from "utils/requestData.js";
import { shortener } from "utils/helper.js";
import ExportMenu from "components/ExportMenu/index.jsx";

const StatisticalInfo = () => {
  const { fields, platform, date } = useCompareStore((state) => state.compare);
  const [data, setData] = useState([]);
  const [selectPlatform, setSelectPlatform] = useState({
    telegram: [],
    twitter: [],
    instagram: [],
    news: [],
  });
  const [loading, setLoading] = useState(false);
  const colors = ["#1DCEA2", "#6D72E5", "#DB6DE5", "#F7A912"];
  const [fieldsCleared, setFieldsCleared] = useState([]);

  useEffect(() => {
    console.log("StatisticalInfo rendered");
  }, []);

  // Update fieldsCleared only when fields.q changes
  useEffect(() => {
    const newFieldsCleared = fields.filter((value) => value.q);
    const hasChanged =
      newFieldsCleared.length !== fieldsCleared.length ||
      newFieldsCleared.some(
        (field, index) => field.q !== (fieldsCleared[index]?.q || "")
      );

    if (hasChanged) {
      console.log("fieldsCleared updated:", newFieldsCleared);
      setFieldsCleared(newFieldsCleared);
    }
  }, [fields]);

  const newsSpecificData = (fieldsCleared) => [
    {
      title: "تعداد روزنامه‌ها",
      icon: <Eye color="white" />,
      color: "text-[#0A55E1]",
      data: data
        ?.map((item, index) => {
          if (item.key === "source") {
            return {
              name: fieldsCleared[index]?.q,
              data: [Number(item.count)],
              color: colors[index],
              title: "تعداد روزنامه‌ها",
            };
          }
          return null;
        })
        .filter((item) => item !== null),
    },
  ];

  const getData = async () => {
    if (loading) return;
    console.log("getData called with:", { fieldsCleared, platform, date });
    setLoading(true);
    try {
      const reqsQueries = fieldsCleared.map((field) => {
        const filterObject = { ...field.filters, date, platform, q: field?.q };
        const query = buildRequestData(filterObject, "statistical");
        return advanceSearch.search(query);
      });

      const response = await Promise.allSettled(reqsQueries);
      const result = response.map((res) => res?.value?.data?.data?.[platform]);
      console.log("getData setting data:", result);
      setData(result);
    } catch (error) {
      console.error("Error fetching data: ", error);
    } finally {
      setLoading(false);
    }
  };

  const createCountData = (data, platform, fieldsCleared) => {
    const generateData = (title, field, icon, color, customData = null) => {
      const generatedData = customData
        ? customData
        : data
            .map((item, index) => {
              return item
                ?.map((i) => {
                  const fieldName = field.replace("_count", "");
                  const keyMatch = fieldName.trim() === i.key.trim();

                  if (keyMatch) {
                    return {
                      name: fieldsCleared[index]?.q,
                      data: [i.count],
                      color: colors[index],
                      title,
                    };
                  }
                  return null;
                })
                .filter(Boolean);
            })
            .flat();

      return {
        title,
        icon,
        color,
        data: generatedData,
      };
    };

    const viewsPerPost = (newsAgencyCount, postCount) =>
      postCount > 0 ? newsAgencyCount / postCount : 0;

    const platformData = {
      twitter: [
        generateData(
          "پست‌ها",
          "post_count",
          <Upload color="white" />,
          "text-[#C70077]"
        ),
        generateData(
          "لایک‌ها",
          "like_count",
          <UsersThree color="white" />,
          "text-[#C70077]"
        ),
        generateData(
          "ریتوئیت‌ها",
          "retweet_count",
          <Eye color="white" />,
          "text-[#0A55E1]"
        ),
        generateData(
          "تعداد منابع انتشار",
          "source_count",
          <Eye color="white" />,
          "text-[#0A55E1]"
        ),
      ],
      telegram: [
        generateData(
          "محتوای منتشر شده",
          "post_count",
          <Eye color="white" />,
          "text-[#0A55E1]"
        ),
        generateData(
          "تعداد منابع انتشار",
          "source",
          <Eye color="white" />,
          "text-[#0A55E1]"
        ),
        generateData(
          "تعداد بازدید",
          "view_count",
          <Eye color="white" />,
          "text-[#0A55E1]"
        ),
      ],
      instagram: [
        generateData(
          "کامنت‌ها",
          "comment_count",
          <Eye color="white" />,
          "text-[#0A55E1]"
        ),
        generateData(
          "لایک",
          "like_count",
          <Eye color="white" />,
          "text-[#0A55E1]"
        ),
        generateData(
          "محتوای منتشر شده",
          "post_count",
          <Eye color="white" />,
          "text-[#0A55E1]"
        ),
      ],
      news: [
        generateData(
          "خبر منتشر شده",
          "original_count",
          <Eye color="white" />,
          "text-[#0A55E1]"
        ),
        generateData(
          "مجموع بازدید",
          "post_count",
          <Eye color="white" />,
          "text-[#0A55E1]"
        ),
        {
          title: "بازدید به پست",
          icon: <Eye color="white" />,
          color: "text-[#0A55E1]",
          data: data.map((item, index) => {
            const averageViews = viewsPerPost(
              item.news_agency_count,
              item.post_count
            );
            return {
              name: fieldsCleared[index]?.q,
              data: [averageViews],
              color: colors[index],
              title: "میانگین بازدید به پست",
            };
          }),
        },
      ],
    };

    return [
      ...(platformData[platform] || []),
      ...(platform === "news" ? newsSpecificData(fieldsCleared) : []),
    ];
  };

  useEffect(() => {
    console.log("Data effect triggered with:", { data, platform, fields });
    if (data.length > 0) {
      setSelectPlatform((prev) => {
        const newValue = {
          ...prev,
          [platform]: createCountData(data, platform, fieldsCleared),
        };
        console.log("selectPlatform updated:", newValue);
        return newValue;
      });
    }
  }, [data, platform, fieldsCleared]);

  // Fetch data when fieldsCleared changes
  useEffect(() => {
    console.log("Fetch effect triggered with:", {
      fieldsCleared,
      date,
      platform,
    });
    if (fieldsCleared.length > 0) {
      getData(fieldsCleared);
    }
  }, [fieldsCleared, date, platform]);

  const renderLegend = (fieldsCleared) => {
    return data
      ?.slice()
      .reverse()
      .map((item, index) => (
        <div
          key={index}
          className="flex items-center gap-2 mb-2 font-body-small"
        >
          <span>
            {shortener(fieldsCleared[data.length - 1 - index]?.q, 14)}
          </span>
          <div
            className="w-4 h-1 flex"
            style={{
              backgroundColor: colors[data.length - 1 - index],
              borderRadius: "20%",
            }}
          ></div>
        </div>
      ));
  };

  // Prepare series and time for ExportMenu
  const series =
    selectPlatform[platform]?.map((item) => ({
      name: item.title,
      data: item.data?.flatMap((d) => d.data) || [],
      time: item.data?.map((d) => d.name) || [],
    })) || [];

  const time = fieldsCleared.map((field) => field.q) || [];

  if (!fields.filter((user) => user.id || user.q).length) {
    return null;
  }

  return (
    <div className="flex compare-statistical-container">
      <Card className="!p-6">
        <div className="gap-3 w-full">
          <div className="pb-3 flex items-center justify-between">
            <h6 className="font-subtitle-large">اطلاعات آماری</h6>
            <ExportMenu
              chartSelector=".compare-statistical-container"
              fileName="statistical-info"
              series={series}
              time={time}
              excelHeaders={["Name", ...series.map((s) => s.name)]}
              onError={(error) => console.error("Export error:", error)}
              menuItems={["PNG", "JPEG", "Excel"]}
            />
          </div>
          <div className="gap-2 w-full">
            {loading ? (
              <div className="w-full h-[400px] flex justify-center items-center">
                <SpinnerGap size={40} className="animate-spin" />
              </div>
            ) : (
              data.length > 0 && (
                <>
                  <Card className="!p-0 w-[45%] h-[17%] flex items-center justify-center gap-3 rounded-lg !shadow-none">
                    {selectPlatform[platform]?.map((item, i) => (
                      <BasicBar
                        key={i}
                        data={item?.data}
                        title={item?.title}
                        icon={item?.icon}
                      />
                    ))}
                  </Card>
                  <div className="flex gap-5 pt-3">
                    {renderLegend(fieldsCleared)}
                  </div>
                </>
              )
            )}
          </div>
        </div>
      </Card>
    </div>
  );
};

export default memo(StatisticalInfo);
