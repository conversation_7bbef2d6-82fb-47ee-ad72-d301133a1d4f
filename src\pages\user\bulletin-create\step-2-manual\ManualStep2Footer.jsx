import { to<PERSON>ersian<PERSON><PERSON>ber } from "utils/helper";
import BulletinBadge from "../components/BulletinBadge";
import { useBulletinStore } from "store/bulletinStore.js";
import PLATFORMS from "constants/platforms.js";
import { useEffect } from "react";
import { InstagramLogo, TelegramLogo, XLogo, Rss } from "@phosphor-icons/react";
import useSearchStore from "store/searchStore.js";

const ManualStep2Footer = () => {
  const { contentManual, existingContent } = useBulletinStore(
    (state) => state.bulletin,
  );
  const setBulletin = useBulletinStore((state) => state.setBulletin);
  const { setFilters, setQuery } = useSearchStore();

  const platforms = ["twitter", "telegram", "instagram", "news"];

  const handleDelete = (query, platform) => {
    const help = JSON.parse(JSON.stringify(contentManual));
    help[platform] = help[platform].filter((item) => item.q !== query);
    setBulletin({ contentManual: help });
  };

  return (
    <div className="font-body-medium flex items-center flex-wrap gap-3">
      {platforms.map((platform, index) => {
        return contentManual?.[platform]?.map(
          (chips) =>
            chips?.q_content?.length > 0 && (
              <BulletinBadge
                key={chips.q + platform}
                icon={
                  platform === PLATFORMS.TWITTER
                    ? XLogo
                    : platform === PLATFORMS.TELEGRAM
                      ? TelegramLogo
                      : platform === PLATFORMS.INSTAGRAM
                        ? InstagramLogo
                        : platform === PLATFORMS.NEWS
                          ? Rss
                          : null
                }
                iconColor={"#fff"}
                iconClassName={
                  platform === PLATFORMS.TELEGRAM
                    ? "bg-[#0084C7]"
                    : platform === PLATFORMS.INSTAGRAM
                      ? "bg-[#E64787]"
                      : platform === PLATFORMS.TWITTER
                        ? "bg-[#000]"
                        : platform === PLATFORMS.NEWS
                          ? "bg-[#ECA213]"
                          : null
                }
                title={
                  chips.totalCount
                    ? toPersianNumber(
                        `${chips.q} (${chips.q_content.length} از ${
                          chips.totalCount
                        })`,
                      )
                    : toPersianNumber(`${chips.q} (${chips.q_content.length})`)
                }
                hasDeleteIcon
                iconClickHandler={() => {
                  handleDelete(chips.q, platform);
                }}
                clickHandler={() => {
                  setBulletin({ existingContent: chips.q_content });
                  setTimeout(() => {
                    setQuery(chips.q);
                    setFilters({ platform: platform });
                  }, 100);
                }}
              />
            ),
        );
      })}
    </div>
  );
};

export default ManualStep2Footer;
