import { CaretLeft, Diamond } from "@phosphor-icons/react";
import { Link, useLocation } from "react-router-dom";

const UserPlanHistory = () => {
  const { pathname } = useLocation();
  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <p className="font-subtitle-large">تاریخچه پلن کاربر</p>
        <Link
          to={`/admin/user/list/${
            pathname.split("/").slice(-1)[0]
          }/planSettings`}
          className="font-button-small text-light-primary-background-rest rounded-md transition hover:bg-light-primary-background-highlight cursor-pointer flex items-center justify-center p-2"
        >
          نمایش همه
          <CaretLeft />
        </Link>
      </div>

      <div className="flex items-center justify-between cursor-pointer transition-all hover:bg-light-neutral-background-medium px-3 py-1 rounded-md">
        <div className="grid gap-2">
          <div className="flex items-center gap-2">
            <Diamond />
            <p className="font-body-large">برنزی</p>
          </div>
          <p className="font-body-medium text-light-neutral-text-medium">
            ۱۲:۳۴:۵۴ - ۱۴۰۲/۱۱/۰۱
          </p>
        </div>

        <div className="flex items-center gap-2">
          <span className="font-body-small text-light-success-text-rest bg-light-success-background-highlight px-2 py-1 rounded-[3px]">
            موفق
          </span>
          <CaretLeft />
        </div>
      </div>

      <div className="flex items-center justify-between cursor-pointer transition-all hover:bg-light-neutral-background-medium px-3 py-1 rounded-md">
        <div className="grid gap-2">
          <div className="flex items-center gap-2">
            <Diamond />
            <p className="font-body-large">برنزی</p>
          </div>
          <p className="font-body-medium text-light-neutral-text-medium">
            ۱۲:۳۴:۵۴ - ۱۴۰۲/۱۱/۰۱
          </p>
        </div>

        <div className="flex items-center gap-2">
          <span className="font-body-small text-light-success-text-rest bg-light-success-background-highlight px-2 py-1 rounded-[3px]">
            موفق
          </span>
          <CaretLeft />
        </div>
      </div>

      <div className="flex items-center justify-between cursor-pointer transition-all hover:bg-light-neutral-background-medium px-3 py-1 rounded-md">
        <div className="grid gap-2">
          <div className="flex items-center gap-2">
            <Diamond />
            <p className="font-body-large">برنزی</p>
          </div>
          <p className="font-body-medium text-light-neutral-text-medium">
            ۱۲:۳۴:۵۴ - ۱۴۰۲/۱۱/۰۱
          </p>
        </div>

        <div className="flex items-center gap-2">
          <span className="font-body-small text-light-success-text-rest bg-light-success-background-highlight px-2 py-1 rounded-[3px]">
            موفق
          </span>
          <CaretLeft />
        </div>
      </div>
    </div>
  );
};

export default UserPlanHistory;
