import { Eye, FileArrowDown, Info } from "@phosphor-icons/react";
import ToolTip from "components/ui/ToolTip";
import UserState from "./UserState";
import { Link } from "react-router-dom";

const Table = ({ data }) => {
  return (
    <>
      <div
        className="grid grid-cols-7 hover:bg-light-neutral-surface-highlight py-2 cursor-pointer font-body-medium"
        //   onClick={() => navigate(`/admin/ticket/list/${data?.id}`)}
      >
        <div>پایه</div>
        <div>تمدید</div>
        <div>۱۴۰۲/۱۱/۰۱ - ۱۲:۳۴</div>
        <div className="flex items-center gap-1">
          <p>نام مدیر سیناپس</p>
          <ToolTip comp={"رول کاربر"}>
            <Info size={16} className="text-light-neutral-text-low" />
          </ToolTip>
        </div>
        <div>
          <UserState title={"موفق"} state={"موفق"} />
        </div>
        <div>۱۵۰٬۰۰۰٬۰۰۰</div>

        <div className="font-body-medium flex gap-4">
          <Link
            to={`/admin/user/list/${data?.id}`}
            className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
          >
            <ToolTip comp="نمایش جزئیات">
              <Eye size={16} />
            </ToolTip>
          </Link>
          {/* <div className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer">
                <ToolTip comp="ویرایش">
                  <PencilSimpleLine size={16} />
                </ToolTip>
              </div> */}
          <div className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer">
            <ToolTip comp="دانلود">
              <FileArrowDown size={16} />
            </ToolTip>
          </div>
        </div>
      </div>
    </>
  );
};

export default Table;
