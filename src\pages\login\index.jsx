import LoginForm from "./components/login-form";
import loginImg from "../../assets/images/login-img.png";
import HelpMenu from "./components/help-menu/index.jsx";

function Login() {
  const handleLogin = (values, actions) => {
    // Your login logic here
    // Example: Validate username and password
    if (
      values.username === "your_username" &&
      values.password === "your_password"
    ) {
      // Successful login, redirect or perform actions
      actions.resetForm();
      console.log("Logged in successfully");
    } else {
      actions.setFieldError("username", "نام کاربری یا کلمه عبور اشتباه است");
    }
  };

  return (
    <div className={"bg-light-neutral-surface-zero"}>
      <div
        className={"flex flex-row container m-auto h-screen overflow-hidden "}
      >
        <div
          className={
            "flex flex-col w-full sm:w-5/12 justify-center bg-light-neutral-surface-card pt-16 pb-6 px-8"
          }
        >
          <LoginForm handleLogin={handleLogin} />
          <div className={"end-0 text-center"}>
            <HelpMenu />
          </div>
        </div>
        <div className={"w-7/12 hidden sm:block p-20"}>
          <div
            className={"size-full bg-center bg-contain	bg-no-repeat"}
            style={{
              backgroundImage: `url(${loginImg})`,
            }}
          ></div>
        </div>
      </div>
    </div>
  );
}

export default Login;
