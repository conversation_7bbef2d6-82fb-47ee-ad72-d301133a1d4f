import ToolTip from "components/ui/ToolTip.jsx";
import { parseNumber, toPersianNumber } from "utils/helper.js";

const InfoCard = ({
  bgColor = "#EDEDF3",
  text = "",
  textIcon,
  textIconToolTipText,
  value,
  valueIcon,
  valueIsNumber = true,
}) => {
  return (
    <div
      className="w-full h-11 rounded-lg flex items-center justify-between px-2"
      style={{ backgroundColor: bgColor }}
    >
      <div className="flex items-center gap-2">
        <span className="font-body-small text-light-neutral-text-medium">
          {text}
        </span>
        {textIcon ? (
          <div>
            <ToolTip comp={textIconToolTipText}>{textIcon}</ToolTip>
          </div>
        ) : (
          <></>
        )}
      </div>
      <div className="flex items-start gap-1">
        <span className="font-body-medium">
          {valueIsNumber
            ? toPersianNumber(parseNumber(value))
            : toPersianNumber(value)}
        </span>
        <div>{valueIcon}</div>
      </div>
    </div>
  );
};

export default InfoCard;
