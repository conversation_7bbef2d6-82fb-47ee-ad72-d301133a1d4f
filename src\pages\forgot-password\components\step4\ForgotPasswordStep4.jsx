import logoImg from "assets/images/logo_main.png";
import PropTypes from "prop-types";
import { CButton } from "components/ui/CButton.jsx";
import { CaretLeft, CheckFat } from "@phosphor-icons/react";
import { useNavigate } from "react-router-dom";

const ForgotPasswordStep4 = () => {
  const navigate = useNavigate();

  setTimeout(() => {
    navigate("/login");
  }, 5000);

  return (
    <>
      <img
        className={"top-0 w-auto m-auto mt-6 h-[96px]"}
        src={logoImg}
        alt="logo"
      />

      <div
        className={
          "flex flex-col justify-center items-center m-auto mt-0 px-10"
        }
      >
        <CheckFat color={"green"} size={85} />
        <p className={"font-paragraph-medium mb-8 mt-10 text-center"}>
          رمز عبور شما با موفقیت تغییر کرد. تا لحظاتی دیگر به صفحه ورود منتقل
          خواهید شد و با نام کاربری و رمز عبور جدید وارد سامانه شوید
        </p>

        <div className="flex justify-end w-full mb-32">
          <CButton
            rightIcon={<CaretLeft />}
            size={"lg"}
            onClick={() => navigate("/login")}
          >
            تایید و ادامه
          </CButton>
        </div>
      </div>
    </>
  );
};

export default ForgotPasswordStep4;
