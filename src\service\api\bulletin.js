import useFetch from "../index";

class Bulletin {
  //   verify(data) {
  //     return useFetch.post("/api/v1/alert/verify/", data);
  //   }
  submitPeriodic(data) {
    return useFetch.post("/api/v1/bulletin/periodic/", data);
  }
  updatePeriodic(id, data) {
    return useFetch.put(`/api/v1/bulletin/periodic/${id}/`, data);
  }
  submitManual(data) {
    return useFetch.post("/api/v1/bulletin/", data);
  }
  updateManual(id, data) {
    return useFetch.put(`/api/v1/bulletin/${id}/`, data);
  }
  get() {
    return useFetch.get("/api/v1/bulletin/");
    // return useFetch.get("/api/v1/alert/get/");
  }
  getById(id, partial) {
    return useFetch.get(`/api/v1/bulletin/${id}/?partial=${partial}`);
    // return useFetch.get(`/api/v1/alert/get/${id}/`);
  }
  remove(id) {
    return useFetch.delete(`/api/v1/bulletin/${id}/`);
  }
  //   remove(id) {
  //     return useFetch.delete(`/api/v1/alert/item/${id}/`);
  //     // return useFetch.post("/api/v1/alert/remove/", data);
  //   }
  status(id) {
    return useFetch.patch(`/api/v1/bulletin/periodic/status/${id}/`);
    // return useFetch.post("/api/v1/alert/status/", data);
  }
  editBulletin(id, data) {
    return useFetch.put(`/api/v1/bulletin/${id}/`, data);
  }
  editPeriodicBulletin(id, data) {
    return useFetch.put(`/api/v1/bulletin/periodic/${id}/`, data);
  }
  //   update(id,data) {
  //     return useFetch.put(`/api/v1/alert/item/${id}/`,data);
  //     // return useFetch.post("/api/v1/alert/update/", data);
  //   }

  uploadFile(data) {
    return useFetch.post("/api/v1/bulletin/image/", data);
  }
}
export default new Bulletin();
