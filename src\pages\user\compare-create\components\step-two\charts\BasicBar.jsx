import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import PropTypes from "prop-types";
import { useCompareStore } from "store/compareStore.js";
import { memo, useEffect, useState } from "react";
import { formatShortNumber, shortener, toPersianNumber } from "utils/helper.js";

const BasicBar = ({ categories = [], data = [], title }) => {
  const [num, setNum] = useState(0);
  const { fields } = useCompareStore((state) => state.compare);

  const chartOptions = {
    chart: {
      type: "column",
      height: "100%",
      backgroundColor: "#F0F2F6",
      style: {
        borderRadius: "8px",
      },
    },
    exporting: {
      buttons: {
        contextButton: {
          menuItems: ["downloadPNG"],
          x: 0,
          y: 20,
        },
      },
    },
    title: {
      text: title ? title : null,
      align: "right",
      style: {
        fontSize: "15px",
      },
    },
    legend: {
      enabled: false,
    },
    credits: {
      enabled: false,
    },
    tooltip: {
      enabled: true,
      shared: true,
      useHTML: true,
      formatter: function () {
        let tooltipHTML = `<div style="font-family:'iranyekan',serif;font-size: 11px; direction: rtl">`;
        this.points.forEach((point) => {
          tooltipHTML += `
            <div style="display: flex; align-items: center; margin-bottom: 5px;">
              <div style="width: 10px; height: 10px; border-radius: 50%; background-color: ${
                point.color
              }; margin-left: 3px;"></div>
              <span>${shortener(point.series.name, 12)}:</span> 
              <span style="color: #333; margin-right: 5px;">${toPersianNumber(
                formatShortNumber(point.y),
              )}</span>
            </div>`;
        });
        tooltipHTML += "</div>";
        return tooltipHTML;
      },
    },
    xAxis: {
      categories: categories ? categories : null,
      crosshair: true,
      accessibility: {
        description: "categories",
      },
      visible: false,
    },
    yAxis: {
      min: 0,
      title: {
        text: null,
      },
      labels: {
        formatter: function () {
          return toPersianNumber(formatShortNumber(this.value));
        },
      },
    },
    plotOptions: {
      column: {
        borderWidth: 0,
      },
      series: {
        enableMouseTracking: true,
        borderRadius: {
          radius: 11,
        },
      },
    },
    series: data,
  };

  useEffect(() => {
    setNum((l) => l + 1);
  }, [fields]);

  return (
    <div className="w-full">
      <HighchartsReact
        highcharts={Highcharts}
        options={chartOptions}
        key={num}
      />
    </div>
  );
};

BasicBar.propTypes = {
  categories: PropTypes.array,
  data: PropTypes.array.isRequired,
  title: PropTypes.string.isRequired,
};

export default memo(BasicBar);
