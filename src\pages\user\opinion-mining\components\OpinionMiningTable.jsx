import {
  <PERSON><PERSON><PERSON><PERSON>,
  Eye,
  PencilSimpleLine,
  ShareNetwork,
  TrashSimple,
  WarningCircle,
} from "@phosphor-icons/react";
import DeletePopUp from "components/ui/DeletePopUp";
import ToolTip from "components/ui/ToolTip";
import { useState } from "react";
import { Link, useNavigate } from "react-router-dom"; // Import useNavigate
import { ToastContainer } from "react-toastify";
import opinion from "service/api/opinion";
import {
  notification,
  parseTimeToPersianSummary,
  shortener,
} from "utils/helper";

const OpinionMiningTable = ({ data, getReports }) => {
  const [showRemove, setShowRemove] = useState(false);
  const [isSharePopupOpen, setIsSharePopupOpen] = useState(false);
  const [selectedId, setSelectedId] = useState(null);
  const [reportData, setReportData] = useState([]);
  const navigate = useNavigate(); // Initialize useNavigate

  const handleDeleteClick = async (id) => {
    try {
      const res = await opinion.deleteOpinionReport(id);
      notification.success(
        res?.data?.message,
        <CheckCircle size={25} className="text-light-success-text-rest" />
      );
    } catch (error) {
      notification.error(
        error?.response?.data?.message,
        <WarningCircle size={25} className="text-light-error-text-rest" />
      );
    } finally {
      setShowRemove(false);
      setSelectedId(null);
      getReports();
    }
  };

  const getReportData = async (id) => {
    try {
      const res = await opinion.getOpinionReport(id);
      const fetchedData = res?.data?.data;
      setReportData(fetchedData);
      // Navigate to the edit page with the fetched data
      navigate(`/app/opinion-mining/list/thoughts/${data?.title}`, {
        state: fetchedData,
      });
    } catch (error) {
      notification.error(
        "خطایی رخ داد",
        <WarningCircle size={25} className="text-light-error-text-rest" />
      );
    }
  };

  return (
    <>
      <div className="grid grid-cols-6 hover:bg-light-neutral-surface-highlight py-2 cursor-pointer">
        <div className="flex font-body-medium">
          {shortener(data?.title, 22)}
        </div>
        <div className="flex font-body-medium">
          {data?.opinion_type === "systematic" ? "سیستمی" : "سفارشی"}
        </div>
        <div className="flex font-body-medium">توئیتر</div>
        <div className="font-body-medium">
          {parseTimeToPersianSummary(data?.created_at)}
        </div>
        <div className="font-body-medium">
          {parseTimeToPersianSummary(data?.updated_at)}
        </div>
        <div className={`w-40 flex gap-1 font-body-medium`}>
          <div className="w-56">
            <div className="flex gap-4">
              {data?.opinion_type !== "systematic" && (
                <div
                  onClick={() => getReportData(data?.id)} // Trigger getReportData on click
                  className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
                >
                  <ToolTip comp="ویرایش">
                    <PencilSimpleLine size={16} />
                  </ToolTip>
                </div>
              )}
              {/* <Link
                className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
                to={`/app/opinion-mining/list/thoughts/${data?.title}`}
                state={data}
              > */}
              <div
                className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
                onClick={
                  () => getReportData(data?.id)
                  // navigate(`/app/opinion-mining/list/thoughts/${data?.title}`, {
                  //   state: data,
                  // })
                }
              >
                <ToolTip comp="نمایش جزئیات">
                  <Eye size={16} />
                </ToolTip>
              </div>
              <div
                className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
                onClick={() => {
                  setSelectedId(data?.id);
                  setShowRemove(true);
                }}
              >
                <ToolTip comp="حذف">
                  <TrashSimple size={16} />
                </ToolTip>
              </div>
              <div
                className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
                onClick={() => setIsSharePopupOpen(true)}
              >
                <ToolTip comp="اشتراک گذاری">
                  <ShareNetwork size={16} />
                </ToolTip>
              </div>
            </div>
          </div>
        </div>
      </div>
      <ToastContainer />
      <DeletePopUp
        title="آیا مطمئن هستید؟"
        description={"در صورت حذف این گزارش، امکان بازیابی آن وجود ندارد"}
        onClose={() => {
          setShowRemove(false);
          setSelectedId(null);
        }}
        submitHandler={() => handleDeleteClick(selectedId)}
        isOpen={showRemove}
      />
    </>
  );
};

export default OpinionMiningTable;
