import { Field, Form, Formik } from "formik";
import { useState } from "react";
import { phoneVerifyOTPSchema } from "utils/validationSchemas";
import { CButton } from "components/ui/CButton";
import { COtpInput } from "components/ui/COtpInput";
import { useNavigate } from "react-router-dom";
import authService from "service/api/authService";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";

const VerifyEmail = ({ profile, setStep }) => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const sendEmailNo = async (otp) => {
    setIsLoading(true);
    try {
      const res = await authService.verifyEmail({
        email: profile?.email,
        token: otp,
      });
      setStep(2);
    } catch (error) {
      console.log(error);
    }
    setIsLoading(false);
  };
  const handleSubmit = async (values, { setSubmitting }) => {
    sendEmailNo(values.otp);
    setSubmitting(false);
  };
  const breadcrumbList = [
    { title: "پروفایل کاربری", link: "/app/user/profile" },
    { title: "احراز ایمیل" },
  ];
  useBreadcrumb(breadcrumbList);
  return (
    <div className="flex flex-col items-center mt-4 gap-4 min-h-screen ">
      <div className="w-[570px] bg-light-neutral-surface-card rounded-lg">
        <Formik
          initialValues={{ otp: "" }}
          validationSchema={phoneVerifyOTPSchema}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting, setFieldValue }) => (
            <Form className="font-body-medium flex-1 bg-white mx-auto w-full h-full max-w-[520px] overflow-hidden">
              <h2 className="font-subtitle-large text-gray-800 mt-8 mb-5 text-right">
                احراز آدرس ایمیل قدیم
              </h2>
              <p className={"font-paragraph-medium mb-5"}>
                هم‌اکنون برای شماره <EMAIL> یک ایمیل حاوی کد ۶
                رقمی ارسال می‌شود.
              </p>
              <Field
                id={"otp"}
                name={"otp"}
                component={COtpInput}
                size={"lg"}
                validation={"none"}
                direction={"ltr"}
                title={"کد ۶ رقمی"}
                onChange={(value) => {
                  setFieldValue("otp", value);
                }}
              />
              <div className="flex items-center pb-5 justify-end gap-4">
                <div className="w-[100px]">
                  <CButton
                    size={"lg"}
                    role={"neutral"}
                    onClick={() => navigate("/app/user/profile")}
                  >
                    انصراف
                  </CButton>
                </div>
                <div className="w-[200px]">
                  <CButton type={"submit"} size={"lg"} disabled={isSubmitting}>
                    ادامه
                  </CButton>
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};
export default VerifyEmail;
