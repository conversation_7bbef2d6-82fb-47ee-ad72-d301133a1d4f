import { CInput } from "components/ui/CInput";
import AnnouncementAttach from "../announcementAttach";
import FontSettings from "pages/user/bulletin-create/step-4/components/FontSettings";
import ToggleInput from "components/ui/ToggleInput";
import { CButton } from "components/ui/CButton";
import { useState } from "react";
import { PARSED_BASE_HIGHLIGHT } from "constants/bulletin-colors";
import { Image } from "@phosphor-icons/react";

const Step1 = ({ setStep, step }) => {
  const [isToggleOn, setIsToggleOn] = useState(false);
  const [textStyle, setTextStyle] = useState({
    fontSize: 16,
    fontWeight: "normal",
    fontStyle: "normal",
    textDecoration: "none",
    bold: true,
    italic: false,
    underlined: false,
    color: "#000000",
    text_highlight: "auto",
    textAlign: "right",
  });

  const handleStyleChange = (style) => {
    setTextStyle((prev) => ({
      ...prev,
      fontWeight:
        style.bold !== undefined
          ? style.bold
            ? "bold"
            : "normal"
          : prev.fontWeight,
      fontStyle:
        style.italic !== undefined
          ? style.italic
            ? "italic"
            : "normal"
          : prev.fontStyle,
      textDecoration:
        style.underlined !== undefined
          ? style.underlined
            ? "underline"
            : "none"
          : prev.textDecoration,
      fontSize: style.font_size !== undefined ? style.font_size : prev.fontSize,
      textAlign:
        style.alignment !== undefined ? style.alignment : prev.textAlign,
      color: style.text_color !== undefined ? style.text_color : prev.color,
      text_highlight: style.text_highlight
        ? PARSED_BASE_HIGHLIGHT[style.text_highlight] || style.text_highlight
        : prev.text_highlight,
    }));
  };

  const handleToggleChange = (time) => {
    setIsToggleOn(!isToggleOn);
  };

  return (
    <>
      <div>
        <CInput
          title="عنوان اطلاعیه"
          placeholder="عنوان اطلاعیه را در این‌جا بنویسید"
        />
        <AnnouncementAttach
          description={"عکس شاخص (اختیاری)"}
          description2={"بهترین سایز عکس ۱۰۰۰ در ۳۰۰ است"}
          fullWidth
          boxIcon={Image}
          imageOnly={true}
        />
      </div>
      <div className="flex flex-col gap-1">
        <label htmlFor="" className="font-overline-large">
          متن اطلاعیه
        </label>
        <div className="border border-[#9198ad] p-2 rounded-lg">
          <div className="relative">
            <div className="flex justify-center">
              <FontSettings
                handleChange={handleStyleChange}
                initialValue={{ font_size: 11 }}
              />
            </div>
            <textarea
              className="w-full h-44 resize-none rounded-md p-1.5 font-body-large outline-none bg-transparent"
              placeholder="متن اطلاعیه را به طور کامل بنویسید"
              style={{
                fontSize: `${textStyle.fontSize}px`,
                fontWeight: textStyle.fontWeight,
                fontStyle: textStyle.fontStyle,
                textDecoration: textStyle.textDecoration,
                textAlign: textStyle.textAlign,
                color: textStyle.color,
                backgroundColor:
                  textStyle.text_highlight &&
                  textStyle.text_highlight !== "auto"
                    ? textStyle.text_highlight
                    : "transparent",
              }}
            />
          </div>
        </div>
      </div>
      <div className="px-2">
        <ToggleInput onChange={handleToggleChange} title={"فایل ضمیمه دارم"} />
      </div>
      {isToggleOn && (
        <AnnouncementAttach
          description={"عکس شاخص (اختیاری)"}
          description2={"بهترین سایز عکس ۱۰۰۰ در ۳۰۰ است"}
          fullWidth
          boxIcon={Image}
          allowMultiple={true}
        />
      )}
      <div className="flex gap-6 w-full font-button-medium">
        <div className="bg-light-neutral-background-medium cursor-pointer rounded-lg w-full h-10 flex items-center justify-center">
          انصراف
        </div>
        <CButton type={"submit"} onClick={() => setStep(step + 1)}>
          ادامه
        </CButton>
      </div>
    </>
  );
};

export default Step1;
