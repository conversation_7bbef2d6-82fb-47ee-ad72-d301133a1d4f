const SentimentBadge = ({ children, text, color, percent }) => {
  return (
    <div className="flex items-center gap-9">
      <div
        className="flex gap-1 items-center py-2 rounded-lg"
        style={{
          color,
        }}
      >
        {children}
        <span className="font-body-medium">{text}</span>
      </div>
      <span className="font-body-large">{percent}</span>
    </div>
  );
};

export default SentimentBadge;

{
  /* <div
className="flex flex-col items-center py-2 px-4 rounded-lg"
style={{
  color: fill ? "white" : color,
  backgroundColor: fill ? color : "white",
}}
>
{children}
<span>{text}</span>
</div> */
}
