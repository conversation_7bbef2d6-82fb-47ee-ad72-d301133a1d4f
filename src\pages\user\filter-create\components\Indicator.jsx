import Container from "./Container";
import PropTypes from "prop-types";
const Indicator = ({ status, step, children, title }) => {
  return (
    <Container status={status} step={step} title={title}>
      {children}
    </Container>
  );
};

Indicator.propTypes = {
  status: PropTypes.number.isRequired,
  step: PropTypes.number.isRequired,
  children: PropTypes.node.isRequired,
  title: PropTypes.string.isRequired,
};

export default Indicator;
