import PioneerUsersTable from "./PioneerUsersTable";
import { Card } from "components/ui/Card.jsx";

const PioneerUsers = () => {
  const data = [
    {
      id: 1,
      title: "منبع 1",
      accountId: "@Parham-ab",
      connections: "هشدار ۱",
      age: 25,
    },
    {
      id: 2,
      title: "منبع 2",
      accountId: "@Parham-ab",
      connections: "هشدار ۲",
      age: 30,
    },
    {
      id: 3,
      title: "منبع 3",
      accountId: "@Parham-ab",
      connections: "هشدار ۲",
      age: 20,
    },
    {
      id: 4,
      title: "منبع 3",
      accountId: "@Parham-ab",
      connections: "هشدار ۲",
      age: 20,
    },
    {
      id: 5,
      title: "منبع 3",
      accountId: "@Parham-ab",
      connections: "هشدار ۲",
      age: 20,
    },
    {
      id: 6,
      title: "منبع 3",
      accountId: "@Parham-ab",
      connections: "هشدار ۲",
      age: 20,
    },
    {
      id: 7,
      title: "منبع 3",
      accountId: "@Parham-ab",
      connections: "هشدار ۲",
      age: 20,
    },
  ];

  return (
    <Card className="flex flex-col gap-2 h-full">
      <p className="font-subtitle-large">آخرین فیلترها</p>
      <PioneerUsersTable data={data} />
    </Card>
  );
};

export default PioneerUsers;
