import { CaretLeft } from "@phosphor-icons/react";
import { shortener, toPersianNumber } from "utils/helper";
import IncreasingTrend from "assets/images/waveAnalytics/IncreasingTrend.png";
import FutureTrend from "assets/images/waveAnalytics/FutureTrend.png";
import DecreasingTrend from "assets/images/waveAnalytics/DecreasingTrend.png";
import StaticTrend from "assets/images/waveAnalytics/StaticTrend.png";
import { CButton } from "components/ui/CButton";
import Divider from "components/ui/Divider";
import GaugeChart from "react-gauge-chart";
import { Link } from "react-router-dom";

const WaveCardInfo = ({
  type,
  badgeList,
  isNew = true,
  impact,
  title,
  id,
  elements,
  date,
  query_start_time,
  query_end_time,
  selectedNode,
}) => {
  return (
    <>
      <div
        className={`bg-light-neutral-surface-card px-2 py-1 rounded-lg h-fit border-2 ${
          selectedNode === title ? "border-[#9d80ff]" : ""
        }`}
      >
        <div className="flex items-center justify-between mb-1 pt-1">
          <div className="flex items-center">
            <div
              className={`${
                type === "increasing"
                  ? "bg-[#F60000]"
                  : type === "decreasing"
                  ? "bg-[#1270F4]"
                  : type === "trend"
                  ? "bg-[#E9A506]"
                  : type === "neutral"
                  ? "bg-[#00A184]"
                  : ""
              } w-7 h-3 rotate-90`}
            ></div>
            <img
              src={
                type === "increasing"
                  ? IncreasingTrend
                  : type === "decreasing"
                  ? DecreasingTrend
                  : type === "neutral"
                  ? StaticTrend
                  : type === "trend"
                  ? FutureTrend
                  : null
              }
              width={31}
              alt="type"
            />
            <p className="font-subtitle-large font-normal px-2">{title}</p>
            <span
              className={`font-body-small px-3 rounded-md ${
                isNew
                  ? "bg-light-success-background-highlight text-light-success-background-rest"
                  : "bg-light-warning-background-highlight text-light-warning-background-rest"
              }`}
            >
              {isNew ? "جدید" : "تکراری"}
            </span>
          </div>

          <p className="font-body-small text-light-neutral-text-medium pl-4">
            ضریب تاثیرگذاری
          </p>
        </div>

        <div className="flex items-start justify-between">
          <div className="flex items-center flex-wrap gap-1 pb-3 px-3 w-80">
            {badgeList?.slice(0, 4)?.map((item) => (
              <p
                className="font-body-small bg-light-neutral-background-medium px-2 py-1 rounded-lg"
                key={item}
              >
                {shortener(item, 15)}
              </p>
            ))}
          </div>
          <div className="w-28 mb-1">
            <GaugeChart
              animDelay={0}
              animateDuration={2500}
              textColor="#000"
              id="gauge-chart4"
              nrOfLevels={9}
              arcPadding={0.05}
              arcWidth={0.3}
              cornerRadius={3}
              percent={impact}
              colors={["#f7432f", "#0bd61c"]}
              hideText
            />
            <span className="font-body-medium flex items-center justify-center">
              {impact
                ? `% ${toPersianNumber((impact * 100).toFixed(0))}`
                : `% ${toPersianNumber(0)}`}
            </span>
          </div>
        </div>
        <Divider />

        <div className="flex items-center justify-end gap-3 my-1">
          <Link
            to={`/app/opinion-mining/list/thoughts/${title.split("#")[1]}`}
            state={{
              title,
              elements,
              date,
              query_start_time,
              query_end_time,
            }}
            className="w-fit"
          >
            <CButton size="sm" role="neutral">
              افکارسنجی
            </CButton>
          </Link>
          <Link
            to={`/app/wave-analysis/list/${title.split("#")[1]}`}
            state={{
              title,
              elements,
              query_start_time,
              query_end_time,
              date,
              id,
            }}
            className="w-fit"
          >
            <CButton size="sm" rightIcon={<CaretLeft />}>
              تحلیل کامل
            </CButton>
          </Link>
        </div>
      </div>
    </>
  );
};

export default WaveCardInfo;
