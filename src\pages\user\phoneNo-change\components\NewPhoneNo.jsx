import { Field, Form, Formik } from "formik";
import { useContext, useEffect, useState } from "react";
import { emailVerifyOTPSchema } from "utils/validationSchemas";
import { CButton } from "components/ui/CButton";
import { COtpInput } from "components/ui/COtpInput";
import { CheckCircle } from "@phosphor-icons/react";
import { CInput } from "components/ui/CInput";
import authService from "service/api/authService";
import DisabledOTP from "components/ui/DisabledOTP";
import { notification, toPersianNumber } from "utils/helper";
import AuthContext from "context/auth-context";

const NewPhoneNo = ({ setStep }) => {
  const { loadProfile } = useContext(AuthContext);
  const [isLoading, setIsLoading] = useState(false);
  const [phoneNoValue, setPhoneNoValue] = useState("");
  const [currentStep, setCurrentStep] = useState(1);
  const sendEmail = async () => {
    setIsLoading(true);
    try {
      const res = await authService.changePhoneNo({
        phone: phoneNoValue,
      });
      setCurrentStep(2);
      notification.success(
        `کد تایید برای شماره ${toPersianNumber(
          phoneNoValue,
        )} ارسال شد و تا لحظاتی دیگر آن را دریافت خواهید کرد`,
        <CheckCircle className="text-light-success-text-rest" size={26} />,
      );
    } catch (error) {
      console.log(error);
    }
    setIsLoading(false);
  };
  const handleSubmit = async (values, { setSubmitting }) => {
    setIsLoading(true);
    try {
      const res = await authService.verifyPhoneNo({
        phone: phoneNoValue,
        token: values?.otp,
      });
      setCurrentStep(2);
    } catch (error) {
      console.log(error);
    }
    setSubmitting(false);
    setStep(3);
    loadProfile();
  };

  return (
    <div className="flex flex-col items-center mt-4 gap-4 min-h-screen">
      <div className="w-[570px] bg-light-neutral-surface-card rounded-lg">
        <div className="max-w-lg m-auto">
          <h2 className="font-subtitle-large text-gray-800 mt-8 mb-5 text-right">
            تعیین شماره موبایل جدید
          </h2>
          <p className={"font-overline-large pb-1"}>۱. شماره موبایل جدید</p>
          <CInput
            caption={
              "شماره موبایل جدید را وارد کرده و سپس روی «دریافت کد تایید» کلیک کنید"
            }
            placeholder={"برای مثال ۰۹۱۲۳۴۵۶۷۸"}
            direction="ltr"
            customActionText={"دریافت کد تایید"}
            customAction={sendEmail}
            value={phoneNoValue}
            onChange={(e) => setPhoneNoValue(e.target.value)}
          />
        </div>
        <Formik
          initialValues={{ otp: "" }}
          validationSchema={emailVerifyOTPSchema}
          onSubmit={handleSubmit}
          // enableReinitialize={true}
        >
          {({ isSubmitting, setFieldValue }) => (
            <Form className="font-body-medium flex-1 bg-white mx-auto w-full h-full max-w-lg overflow-hidden">
              <p
                className={"font-overline-large pb-1"}
                style={{ opacity: currentStep === 2 ? 1 : 0.5 }}
              >
                ۲. کد تایید شماره جدید
              </p>
              <Field
                id={"otp"}
                name={"otp"}
                component={currentStep === 2 ? COtpInput : DisabledOTP}
                size={"lg"}
                validation={"none"}
                direction={"ltr"}
                onChange={(value) => {
                  setFieldValue("otp", value);
                }}
              />
              <div className="flex items-center pb-5 justify-end">
                <div className="w-[200px]">
                  <CButton
                    type={"submit"}
                    size={"lg"}
                    disabled={isSubmitting}
                    // role={currentStep === 1 ? "neutral" : ""}
                    readOnly={currentStep === 1}
                  >
                    ادامه
                  </CButton>
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default NewPhoneNo;
