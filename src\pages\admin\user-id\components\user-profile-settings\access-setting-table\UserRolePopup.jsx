import { CaretDown, Check } from "@phosphor-icons/react";

const UserRolePopup = ({
  isDropdownOpen,
  handleSelectOption,
  selectedOption,
  options,
  setIsDropdownOpen,
}) => {
  return (
    <>
      <div className="w-full font-body-medium my-7 relative select-none">
        <label>نوع کاربر</label>
        <div
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
          className="outline-none border border-1 rounded-md p-2 cursor-pointer my-1 flex justify-between items-center"
        >
          <p>{selectedOption || "انتخاب کنید"}</p>
          <CaretDown
            size={20}
            className={`transition-all ${isDropdownOpen ? "rotate-180" : ""}`}
          />
        </div>

        {isDropdownOpen && (
          <div className="absolute top-full left-0 w-full border border-gray-300 rounded-md bg-white shadow-md z-10  overflow-auto">
            {options?.map((option, index) => (
              <div
                key={index}
                onClick={() => handleSelectOption(option)}
                className={`p-2 cursor-pointer flex items-center justify-between ${
                  selectedOption === option
                    ? "bg-light-primary-background-highlight"
                    : "hover:bg-gray-100"
                }`}
              >
                <p>{option}</p>
                {option === selectedOption && <Check size={17} />}
              </div>
            ))}
          </div>
        )}
      </div>
    </>
  );
};

export default UserRolePopup;
