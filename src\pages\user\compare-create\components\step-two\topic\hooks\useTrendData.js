import { useEffect, useState } from "react";
import dashboard from "service/api/dashboard";

const useTrendData = (platform) => {
  const [trendData, setTrendData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadClusterTitles = async () => {
      setIsLoading(true);
      try {
        const { data } = await dashboard.getData({ platform }, true);
        setTrendData(data?.data || []);
      } catch (err) {
        console.error(err);
        setError(err);
      } finally {
        setIsLoading(false);
      }
    };

    loadClusterTitles();
  }, [platform]);

  return { trendData, isLoading, error };
};

export default useTrendData;
