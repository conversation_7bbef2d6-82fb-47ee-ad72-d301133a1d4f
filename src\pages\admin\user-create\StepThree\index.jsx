import { CButton } from "components/ui/CButton";
import PlanBadge from "./components/PlanBadge";
import { useState } from "react";
import PlanOptionsCard from "./components/PlanOptionsCard";

const StepThree = ({ setStep }) => {
  const [selectedPlan, setSelectedPlan] = useState(null);
  return (
    <div>
      <div className="grid gap-4 bg-light-neutral-surface-card rounded-[8px] p-6">
        <p className="font-subtitle-large">انتخاب پلن</p>
        <div className="flex items-center gap-6 justify-center my-4">
          <PlanBadge
            title={"بدون طرح"}
            isSelected={selectedPlan === "بدون طرح"}
            onSelect={() => setSelectedPlan("بدون طرح")}
          />
          <PlanBadge
            title={"سازمانی"}
            isSelected={selectedPlan === "سازمانی"}
            onSelect={() => setSelectedPlan("سازمانی")}
          />
          <PlanBadge
            title={"پیشرفته"}
            isSelected={selectedPlan === "پیشرفته"}
            onSelect={() => setSelectedPlan("پیشرفته")}
          />
          <PlanBadge
            title={"حرفه‌ای"}
            isSelected={selectedPlan === "حرفه‌ای"}
            onSelect={() => setSelectedPlan("حرفه‌ای")}
          />
          <PlanBadge
            title={"پایه"}
            isSelected={selectedPlan === "پایه"}
            onSelect={() => setSelectedPlan("پایه")}
          />
        </div>

        {selectedPlan && (
          <>
            <PlanOptionsCard selectedPlan={selectedPlan} />
          </>
        )}
        <div className="flex items-center gap-2">
          <div className="w-full" onClick={() => setStep(2)}>
            <CButton role="neutral">مرحله قبل</CButton>
          </div>
          <div className="w-full" onClick={() => setStep(4)}>
            <CButton readOnly={!selectedPlan}>مرحله بعد</CButton>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StepThree;
