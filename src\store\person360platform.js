import { create } from "zustand";

const usePlatformDataStore = create((set) => ({
  platformData: {
    news: {
      releaseProcess: null,
      resourcesInfo: null,
      latestContent: null,
      entities: null,
      thematicCategory: null,
      topSource: null,
    },
    telegram: {
      releaseProcess: null,
      resourcesInfo: null,
      latestContent: null,
      entities: null,
      thematicCategory: null,
      sentiment: null,
      repeatHashtags: null,
      topSource: null,
      advertise: null,
    },
    instagram: {
      releaseProcess: null,
      resourcesInfo: null,
      latestContent: null,
      entities: null,
      thematicCategory: null,
      sentiment: null,
      repeatHashtags: null,
      topSource: null,
    },
    twitter: {
      releaseProcess: null,
      resourcesInfo: null,
      latestContent: null,
      entities: null,
      thematicCategory: null,
      sentiment: null,
      repeatHashtags: null,
      topSource: null,
      contentTypeDistribution: null,
    },
  },
  chartData: {
    AllPlatformContent: {
      twitter: null,
      instagram: null,
      telegram: null,
      news: null,
    },
    OverviewProcess: {
      twitter: null,
      instagram: null,
      telegram: null,
      news: null,
    },
  },
  setPlatformData: (platform, dataType, data) =>
    set((state) => ({
      platformData: {
        ...state.platformData,
        [platform]: {
          ...state.platformData[platform],
          [dataType]: data,
        },
      },
    })),
  clearPlatformData: (platform, dataType) =>
    set((state) => ({
      platformData: {
        ...state.platformData,
        [platform]: {
          ...state.platformData[platform],
          [dataType]: null,
        },
      },
    })),
  clearAllData: () =>
    set({
      platformData: {
        news: {
          releaseProcess: null,
          resourcesInfo: null,
          latestContent: null,
          entities: null,
          thematicCategory: null,
          topSource: null,
        },
        telegram: {
          releaseProcess: null,
          resourcesInfo: null,
          latestContent: null,
          entities: null,
          thematicCategory: null,
          sentiment: null,
          repeatHashtags: null,
          topSource: null,
          advertise: null,
        },
        instagram: {
          releaseProcess: null,
          resourcesInfo: null,
          latestContent: null,
          entities: null,
          thematicCategory: null,
          sentiment: null,
          repeatHashtags: null,
          topSource: null,
        },
        twitter: {
          releaseProcess: null,
          resourcesInfo: null,
          latestContent: null,
          entities: null,
          thematicCategory: null,
          sentiment: null,
          repeatHashtags: null,
          topSource: null,
          contentTypeDistribution: null,
        },
      },
      chartData: {
        AllPlatformContent: {
          twitter: null,
          instagram: null,
          telegram: null,
          news: null,
        },
        OverviewProcess: {
          twitter: null,
          instagram: null,
          telegram: null,
          news: null,
        },
      },
    }),
  updateChartData: (chartType, platform, data) =>
    set((state) => ({
      chartData: {
        ...state.chartData,
        [chartType]: {
          ...state.chartData[chartType],
          [platform]: data,
        },
      },
    })),
}));

export default usePlatformDataStore;
