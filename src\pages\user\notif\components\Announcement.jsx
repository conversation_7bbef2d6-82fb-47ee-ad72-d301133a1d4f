import { useEffect, useState } from "react";
import AnnouncementCard from "./AnnouncementCard";
import SegmentTab from "components/ui/SegmentTab";
import notification from "service/api/notification";
import queryString from "query-string";
import Paginate from "components/ui/Paginate";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";

const Announcement = () => {
  const breadcrumbList = [{ title: "اعلانات" }];
  useBreadcrumb(breadcrumbList);

  const [segment, setSegment] = useState("all");
  const [page, setPage] = useState(1);
  const [announce, setAnnounce] = useState([]);
  const [totalAnnounce, setTotalAnnounce] = useState(0);

  const reloadData = async () => {
    try {
      const query = {
        page,
        count: 10,
        type: "announcement",
        status: "all",
        segment,
      };
      const response = await notification.get(query);
      setAnnounce(response?.data?.data?.notifications || []);
      setTotalAnnounce(response?.data?.data?.total_notify || 0);
    } catch (e) {
      console.log(e);
      setAnnounce([]);
      setTotalAnnounce(0);
    }
  };

  useEffect(() => {
    reloadData();
  }, [segment, page]);

  const readNotifHandler = async (id) => {
    try {
      await notification.markAsReadNotification(id);
      const query = queryString.stringify({
        page: 1,
        count: 10,
        type: "announcement",
        segment,
        status: "all",
      });
      notification
        .get(query)
        .then((res) => {
          setAnnounce(res.data.data.notifications);
        })
        .catch((e) => console.log(e));
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <div className="flex-1 bg-white shadow-[0px_2px_20px_0px_#0000000D] rounded-lg px-4 py-6 relative h-full flex flex-col justify-between">
      <div className="flex-1">
        <div className="py-2">
          <SegmentTab
            segmentArray={[
              { title: "همه اعلانات", id: "all" },
              { title: "هشدارها", id: "alert" },
              { title: "بولتن‌ها", id: "bulletin" },
            ]}
            activeTab={segment}
            onChange={(id) => {
              setPage(1);
              setSegment(id);
            }}
          />
        </div>
        <div className="divide-y-2 divide-gray-300">
          {announce?.map(
            ({ description, created_at, unread, link, id, segment, title }) => (
              <AnnouncementCard
                key={id}
                type={segment}
                date={created_at}
                title={title}
                description={description}
                read={!unread}
                link={link}
                id={id}
                showMarkAsSeen={false}
                readNotifHandler={() => readNotifHandler(id)}
              />
            ),
          )}
        </div>
      </div>

      <div className="flex justify-center">
        <Paginate
          page={page}
          setPage={setPage}
          dataCount={totalAnnounce}
          per_page={10}
        />
      </div>
    </div>
  );
};

export default Announcement;
