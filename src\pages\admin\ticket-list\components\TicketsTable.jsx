import { useNavigate } from "react-router-dom";
import TicketState from "./TicketState";
import { ToastContainer } from "react-toastify";
import { parseTimeToPersianSummary } from "utils/helper";

const TicketsTable = ({ data }) => {
  const navigate = useNavigate();
  return (
    <>
      <div
        className="grid grid-cols-6 hover:bg-light-neutral-surface-highlight py-2 cursor-pointer"
        onClick={() => navigate(`/admin/ticket/list/${data?.id}`)}
      >
        <div className="flex font-body-medium">{data?.ticket_id}</div>
        <div className="flex font-body-medium">{data?.user}</div>
        <div className="flex font-body-medium">{data?.title}</div>
        <div className="font-body-medium">
          {parseTimeToPersianSummary(data?.updated_at)}
        </div>

        <div className="font-body-medium">
          <TicketState
            title={
              data?.ticket_state === "open"
                ? "در‌ حال‌ بررسی"
                : data?.ticket_state === "closed"
                ? "پایان‌ یافته"
                : data?.ticket_state === "resolved" ||
                  data?.ticket_state === "replied"
                ? "منتظر ‌پاسخ"
                : ""
            }
            state={data?.ticket_state}
          />
        </div>

        <div className={`w-40 flex gap-1 font-body-medium`}>
          <span>
            <TicketState
              title={
                data?.ticket_section === "support"
                  ? "پشتیبانی"
                  : data?.ticket_section === "technical"
                  ? "فنی"
                  : data?.ticket_section === "finances"
                  ? "مالی‌و‌اداری"
                  : ""
              }
            />
          </span>
        </div>
      </div>
      <ToastContainer />
    </>
  );
};

export default TicketsTable;
