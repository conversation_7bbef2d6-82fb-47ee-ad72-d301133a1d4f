import { Eye, TrashSimple } from "@phosphor-icons/react";
import ToolTip from "components/ui/ToolTip";
import UserState from "./UserState";
import {
  parseTimeToPersian,
  parseTimeToPersianSummary,
  toPersianNumber,
} from "utils/helper";
import { Link } from "react-router-dom";

function maskFourthDigit(phoneNumber) {
  let phoneString = phoneNumber.toString();
  if (phoneString.length >= 4) {
    return phoneString.slice(8) + "***" + phoneString.slice(0, 5);
  }
  return phoneString;
}
const Table = ({ data }) => {
  return (
    <>
      <div
        className="grid grid-cols-9 hover:bg-light-neutral-surface-highlight py-2 cursor-pointer"
        //   onClick={() => navigate(`/admin/ticket/list/${data?.id}`)}
      >
        <div className="flex items-center gap-1 font-body-medium">
          <input type="checkbox" name="" id="" />
          <p>{data?.username}</p>
        </div>
        <div className="flex font-body-medium">
          {data?.first_name + data?.last_name}
        </div>
        <div className="flex font-body-medium">
          {maskFourthDigit(`${toPersianNumber(data?.phone_number)}`)}
        </div>
        <div className="font-body-medium">
          {data?.role == "Regular"
            ? "کاربر عادی"
            : data?.role == "Manager"
              ? "مدیر سازمان"
              : "پشتیبانی"}
        </div>
        <div className="font-body-medium">{data?.userPlan}</div>
        <div className="font-body-medium">
          {parseTimeToPersian(data?.last_login)}
        </div>
        <div className="font-body-medium">
          {parseTimeToPersianSummary(data?.register_at)}
        </div>
        <div className="font-body-medium">
          <UserState
            title={
              data?.status === "فعال"
                ? "فعال"
                : data?.status === "غیرفعال"
                  ? "غیرفعال"
                  : data?.status === "غیرفعال" || data?.status === "حذف‌شده"
                    ? "حذف‌شده"
                    : ""
            }
            state={data?.status}
          />
        </div>{" "}
        <div className="font-body-medium">
          <div className="flex gap-4">
            <Link
              to={`/admin/user/list/${data?.id}`}
              className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
            >
              <ToolTip comp="نمایش جزئیات">
                <Eye size={16} />
              </ToolTip>
            </Link>
            {/* <div className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer">
                <ToolTip comp="ویرایش">
                  <PencilSimpleLine size={16} />
                </ToolTip>
              </div> */}
            <div className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer">
              <ToolTip comp="حذف">
                <TrashSimple size={16} />
              </ToolTip>
            </div>
          </div>
        </div>
        <div className={`w-40 flex gap-1 font-body-medium`}>
          <span>
            {/* <TicketState
                title={
                  data?.ticket_section === "support"
                    ? "پشتیبانی"
                    : data?.ticket_section === "technical"
                    ? "فنی"
                    : data?.ticket_section === "finances"
                    ? "مالی‌و‌اداری"
                    : ""
                }
              /> */}
          </span>
        </div>
      </div>
    </>
  );
};

export default Table;
