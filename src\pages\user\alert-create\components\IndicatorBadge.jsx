import { toPersianNumber } from "utils/helper";

const IndicatorBadge = ({ children, step, status }) => {
  return (
    <div className="flex items-center gap-4">
      <div
        className="grid place-items-center font-body-medium size-6 border border-light-neutral-border-medium-rest text-light-neutral-text-medium rounded-lg"
        style={{
          ...(status == step
            ? { color: "#000000", borderColor: "#4D36BF" }
            : status > step
            ? {
                color: "#000000",
                borderColor: "#19A399",
                backgroundColor: "#73EBE24D",
              }
            : {}),
        }}
      >
        {toPersianNumber(step)}
      </div>
      {status >= step && <p className="font-body-medium">{children}</p>}
    </div>
  );
};

export default IndicatorBadge;
