import TextWithBullet from "components/TextWithBullet";

const StoreFilterGuide = () => {
  return (
    <div className="font-body-medium">
      <h3 className="mb-4">راهنما</h3>
      <div className="list-disc list-inside flex flex-col gap-4">
        <TextWithBullet>
          برای تنظیم دقیق هشدار خود، ابتدا بستر (های) مورد نظر خود را انتخاب
          کنید.
        </TextWithBullet>
        <TextWithBullet>
          سپس کلمه کلیدی، عبارت یا ترکیبی منطقی از کلمات کلیدی را در باکس "کلمات
          کلیدی" وارد نمایید. توجه داشته باشید که این هشدار بر اساس کلمات تعیین
          شده در این مرحله فیلتر خواهد شد.
        </TextWithBullet>
        <TextWithBullet>
          اگر تمایل به اعمال فیلترهای بیشتر برای هشدار خود دارید می‌توانید از
          طریق دکمه «تنظیمات پیشرفته»، هشداری دقیق‌تر را با توجه به فیلدهای
          متفاوت فیلتر ایجاد نمایید.
        </TextWithBullet>
        <TextWithBullet>
          شما می‌توانید با استفاده از عملوندهای منطقی + و - و * یک ترکیب از
          کلمات کلیدی را جهت فیلتر نتایج ایجاد کنید.عملوند + به معنای وجود قطعی
          ترکیب کلمات، عملوند - به معنای عدم وجود قطعی کلمات و عملوند * برای شرط
          وجود حداقلی یک کلمه در ترکیب کلمات به کار می‌رود.
        </TextWithBullet>
      </div>
    </div>
  );
};

export default StoreFilterGuide;
