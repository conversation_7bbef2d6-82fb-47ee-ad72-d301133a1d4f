import InfoProfile from "./InfoProfile.jsx";
import { useCompareStore } from "store/compareStore.js";
import { Card } from "components/ui/Card.jsx";
import Title from "./Title.jsx";

const Info = () => {
  const { fields } = useCompareStore((state) => state.compare);

  return (
    <div className="flex">
      {fields?.filter((value) => value?.user_name || value.id).length > 0 && (
        <Card className="px-0">
          <div className="w-full flex flex-col gap-6">
            <Title title="شناسنامه"></Title>
            <div
              className="grid divide-x-[1px] divide-x-reverse"
              style={{ gridTemplateColumns: `repeat(${fields.length},1fr)` }}
            >
              {fields.map((item) => {
                if (item?.user_name) {
                  return <InfoProfile data={item} key={item.user_id} />;
                }
                return <div key={item?.id}></div>;
              })}
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default Info;
