import {
  ArrowsClockwise,
  DownloadSimple,
  FileXls,
  TrashSimple,
} from "@phosphor-icons/react";
import { CButton } from "components/ui/CButton";
import Divider from "components/ui/Divider";
import ToolTip from "components/ui/ToolTip";
import AttachmentBox from "pages/user/ticket-create/components/AttachmentBox";
import BatchesTable from "./BatchesTable";
const AttachFileHeader = ({ attachments, setAttachments }) => {
  return (
    <>
      <div className="grid grid-cols-12 mx-auto px-6 gap-5">
        <div className="col-span-8 container">
          <div className="bg-light-neutral-surface-card rounded-[8px] p-6 mb-6">
            <div className="flex items-center justify-between">
              <p className="font-body-small">
                لطفا مبتنی «فایل نمونه»٬ اطلاعات خود را تکمیل کرده و سپس آن را
                آپلود کنید.
              </p>

              <div className="w-fit">
                <CButton mode="outline" leftIcon={<DownloadSimple />}>
                  دانلود فایل نمونه
                </CButton>
              </div>
            </div>
            <div className="my-6">
              <Divider />
            </div>
            <div>
              {!attachments?.length > 0 && (
                <AttachmentBox
                  description={"در صورت لزوم فایل را بارگذاری کنید (اختیاری)"}
                  description2={"حداکثر حجم مجاز: ۱۰ مگابایت"}
                  fullWidth
                  boxIcon={FileXls}
                  accept=".csv,.xls,.png"
                  onChange={(files) => {
                    setAttachments(files);
                  }}
                  multiple
                />
              )}
              <div className="mt-4 flex flex-wrap gap-4">
                {attachments?.map((file, index) => (
                  <>
                    <div className="flex items-center gap-5">
                      <div className="flex items-center gap-1">
                        <FileXls
                          className="text-light-neutral-text-low"
                          size={30}
                        />
                        <p className="font-button-medium">
                          نام فایل آپلود شده ۲۳۴۶
                        </p>
                      </div>

                      <div className="flex items-center gap-2">
                        <div
                          className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer w-fit"
                          onClick={() => setAttachments([])}
                        >
                          <ToolTip comp="حذف">
                            <TrashSimple size={16} />
                          </ToolTip>
                        </div>
                        <div className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer w-fit">
                          <ToolTip comp="بروزرسانی">
                            <ArrowsClockwise size={16} />
                          </ToolTip>
                        </div>
                      </div>
                    </div>
                  </>
                ))}
              </div>
            </div>
          </div>
          <BatchesTable />
        </div>

        <div className="col-span-4 container leading-8 font-body-medium pr-3">
          <p>راهنما</p>
          <ul className="list-disc">
            <li>
              فایل نمونه را دانلود کنید و براساس آن اطلاعات مد نظر خود را وارد
              کنید.
            </li>
            <li>تکمیل بخش‌های نام کاربری و شماره موبایل اجباری است.</li>
            <li>نام کاربری و شماره موبایل را به زبان انگلیسی وارد کنید.</li>
            <li>سپس فایل خود را آپلود کنید.</li>
            <li>
              پس از بررسی سیستم٬ اطلاعات فایل شما به شما نمایش داده خواهد شد.
            </li>
            <li>پس از بررسی و اصلاح موارد دکمه ثبت را وارد کنید.</li>
          </ul>
        </div>
      </div>
    </>
  );
};

export default AttachFileHeader;
