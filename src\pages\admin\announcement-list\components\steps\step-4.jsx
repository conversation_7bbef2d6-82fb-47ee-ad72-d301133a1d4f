import {
  EnvelopeSimple,
  FileImage,
  FilePdf,
  FileXls,
  MicrosoftWordLogo,
  UsersFour,
} from "@phosphor-icons/react";
import { Card } from "components/ui/Card";
import { CButton } from "components/ui/CButton";
import fox from "../../../../../assets/images/announcement/fox.jpg";

const Step4 = ({ setStep, step, selectedCard }) => {
  const getFileIcon = (index) => {
    const icons = [
      { icon: <FileImage size={34} color="#90EE90" />, label: "Image File" },
      { icon: <FilePdf size={34} color="#FF0000" />, label: "PDF File" },
      {
        icon: <MicrosoftWordLogo size={34} color="#0000FF" />,
        label: "Word File",
      },
      { icon: <FileXls size={34} color="#008000" />, label: "Excel File" },
    ];

    return icons[index % icons.length];
  };
  return (
    <Card className="w-full flex justify-center">
      <div className="w-full flex flex-col justify-center">
        <div className="flex justify-center">
          <EnvelopeSimple weight="duotone" size={85} />
        </div>
        <div>
          <h1 className="font-subtitle-large text-center pt-6">
            آیا می‌خواهید اطلاعیه را ارسال کنید؟
          </h1>
        </div>
        <div className="m-2 rounded-md bg-[#EDEDF3] w-full mt-5 p-3">
          <h1 className="flex justify-start font-subtitle-large">
            تخفیف به مناسبت تولد سیناپس
          </h1>
          <img
            src={fox}
            className="h-[176px] object-cover object-mid my-5 w-full"
          />
          <p className="font-body-large">
            متن اطلاعیه به طور کامل در اینجا نوشته شده که برای مثال به مناسبت
            تولد سیناپس تخفیف ۱۰۰ درصدی به هر کی که تا امشب فلان کار رو بکنه.
            متن اطلاعیه به طور کامل در اینجا نوشته شده که برای مثال به مناسبت
            تولد سیناپس تخفیف ۱۰۰ درصدی به هر کی که تا امشب فلان کار رو بکنه.
            متن اطلاعیه به طور کامل در اینجا نوشته شده که برای مثال به مناسبت
            تولد سیناپس تخفیف ۱۰۰ درصدی به هر کی که تا امشب فلان کار رو بکنه.
            متن اطلاعیه به طور کامل در اینجا نوشته شده که برای مثال به مناسبت
            تولد سیناپس تخفیف ۱۰۰ درصدی به هر کی که تا امشب فلان کار رو بکنه.
            متن اطلاعیه به طور کامل در اینجا نوشته شده که برای مثال به مناسبت
            تولد سیناپس تخفیف ۱۰۰ درصدی به هر کی که تا امشب فلان کار رو بکنه.
          </p>
          <div className="flex gap-3 mt-5">
            {Array(4)
              .fill(null)
              .map((_, index) => {
                const { icon, label } = getFileIcon(index);

                return (
                  <div
                    key={index}
                    className="relative flex flex-col items-center justify-center w-36 h-20 border bg-white border-gray-300 rounded-lg p-2"
                  >
                    <div className="text-gray-600">{icon}</div>
                    <p className="font-body-medium text-center truncate w-full mt-1">
                      {label}
                    </p>
                  </div>
                );
              })}
          </div>
        </div>
        <div className="m-2 rounded-md bg-[#EDEDF3] flex justify-between w-full mt-5 p-3">
          <span className="font-subtitle-large">مخاطب هدف</span>
          {selectedCard === 5 || selectedCard === 6 || selectedCard === 0 ? (
            <div className="flex gap-2 items-center">
              <span className="font-body-large">همه‌ی کاربران</span>
              <UsersFour size={20} />
            </div>
          ) : (
            <div></div>
          )}
        </div>
        <div className="flex gap-6 w-full font-button-medium pt-5">
          <div
            className="bg-light-neutral-background-medium cursor-pointer rounded-lg w-full h-10 flex items-center justify-center"
            onClick={() => setStep(step - 1)}
          >
            گام قبل
          </div>
          <CButton type={"submit"}>انتشار اطلاعیه</CButton>
        </div>
      </div>
    </Card>
  );
};

export default Step4;
