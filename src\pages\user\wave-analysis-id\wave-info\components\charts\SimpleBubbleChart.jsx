import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
const SimpleBubbleChart = ({ monthOptions, yearOptions, selectedYear }) => {
  return (
    <div>
      {selectedYear ? (
        <div>
          <HighchartsReact highcharts={Highcharts} options={monthOptions} />
        </div>
      ) : (
        <HighchartsReact highcharts={Highcharts} options={yearOptions} />
      )}
    </div>
  );
};

export default SimpleBubbleChart;
