import { useState } from "react";
import ColumnAndLineChart from "./charts/ColumnAndLineChart";
import { ArrowUUpLeft } from "@phosphor-icons/react";

const ContentDistributionByAccountAge = ({ fetchedContentAge }) => {
  const [selectedYear, setSelectedYear] = useState(false);
  return (
    <>
      <div
        className="bg-light-neutral-surface-card rounded-lg p-3 h-full"
        style={{
          boxShadow: "0px 2px 20px 0px #0000000D",
        }}
      >
        <div className="flex items-center justify-between mb-5">
          <div className="flex items-center gap-1">
            <p className="font-subtitle-large">
              توزیع محتوای منتشر شده بر اساس قدمت حساب‌های کاربری
            </p>
          </div>
          {selectedYear && (
            <div
              onClick={() => setSelectedYear(false)}
              className="flex flex-row-reverse items-center gap-2 font-body-medium cursor-pointer"
            >
              <p>بازگشت به نمودار سالیانه</p>
              <ArrowUUpLeft size={19} />
            </div>
          )}
        </div>
        {fetchedContentAge?.total?.post ? (
          <ColumnAndLineChart
            fetchedContentAge={fetchedContentAge}
            selectedYear={selectedYear}
            setSelectedYear={setSelectedYear}
          />
        ) : (
          <div className="py-44 text-center font-body-bold-large text-light-neutral-text-medium">
            موردی یافت نشد!
          </div>
        )}
      </div>
    </>
  );
};

export default ContentDistributionByAccountAge;
