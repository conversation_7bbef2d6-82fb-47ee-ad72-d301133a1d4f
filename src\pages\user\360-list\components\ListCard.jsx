import React, { useState } from "react";
import MediaBadge from "components/ui/MediaBadge";
import { Eye, Faders, ShareNetwork, TrashSimple } from "@phosphor-icons/react";
import { useNavigate } from "react-router-dom";
import ToolTip from "components/ui/ToolTip";
import { parseTimeToPersian } from "utils/helper";
import PropTypes from "prop-types";
import report360 from "service/api/report360.js";
import Popup from "components/ui/PopUp.jsx";
import DeletePopUp from "components/ui/DeletePopUp.jsx";

const ListCard = ({
  id,
  title,
  type,
  subject,
  remove360,
  subjectPlatform,
  created_at,
  render,
  is360 = false,
}) => {
  const navigate = useNavigate();

  const [showRemove, setShowRemove] = useState(false);
  const [isSharePopupOpen, setIsSharePopupOpen] = useState(false);

  const removeCompare = async () => {
    try {
      await report360.remove(id);
      render();
    } catch (error) {
      console.log(error);
    }
  };

  const viewCompare = async () => {
    try {
      // navigate(`/app/report-360/edit`, {
      //   state: { id },
      // });
    } catch (error) {
      console.log(error);
    }
  };

  const view360 = async () => {
    try {
      navigate(`/app/report-360/edit`, {
        state: { id },
      });
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <>
      <div className="grid grid-cols-5 justify-between hover:bg-light-neutral-surface-highlight rounded p-1 items-center">
        <span>{title}</span>
        {/*<span>{type === "profile" ? "پروفایلی" : "موضوعی"}</span>*/}
        <span>{type}</span>
        <span className={"flex flex-row gap-2 items-center justify-start"}>
          {subjectPlatform && (
            <MediaBadge media={subjectPlatform} className={""} />
          )}
          <span>{subject}</span>
        </span>

        <span>
          {created_at ? parseTimeToPersian(created_at).split("-")[1] : ""}
        </span>

        <div className="flex gap-4">
          <div
            className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
            onClick={() => navigate(`/app/filter/access/list/${id}`)}
          >
            <ToolTip comp="دسترسی ها">
              <Faders size={16} />
            </ToolTip>
          </div>
          <div
            className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
            onClick={is360 ? view360 : viewCompare}
          >
            <ToolTip comp="نمایش جزئیات">
              <Eye size={16} />
            </ToolTip>
          </div>
          <div
            className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
            onClick={() => setShowRemove(true)}
          >
            <ToolTip comp="حذف">
              <TrashSimple size={16} />
            </ToolTip>
          </div>
          <div
            className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
            onClick={() => setIsSharePopupOpen(true)}
          >
            <ToolTip comp="اشتراک گذاری">
              <ShareNetwork size={16} />
            </ToolTip>
          </div>
        </div>
      </div>
      <Popup
        isOpen={isSharePopupOpen}
        onClose={() => setIsSharePopupOpen(!isSharePopupOpen)}
        title={"اشتراک‌گذاری"}
        isSharePopup
        width="352px"
        currentId={id}
        urlToShare={`#`}
        shareShortenedLink={"#"}
      />

      <DeletePopUp
        onClose={() => setShowRemove(false)}
        isOpen={showRemove === id}
        submitHandler={() => (remove360 ? remove360(id) : removeCompare)}
        title="آیا مطمئن هستید؟"
        description="در صورت حذف این گزارش، امکان بازیابی آن وجود ندارد"
      />
    </>
  );
};

ListCard.propTypes = {
  id: PropTypes.number,
  title: PropTypes.string,
  type: PropTypes.string,
  subject: PropTypes.string,
  subjectPlatform: PropTypes.string,
  created_at: PropTypes.string,
  render: PropTypes.func.isRequired,
  remove360: PropTypes.func,
  is360: PropTypes.bool,
};

export default ListCard;
