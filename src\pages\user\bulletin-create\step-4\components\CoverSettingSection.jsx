import PropTypes from "prop-types";
import InputWithEdit from "./InputWithEdit.jsx";
import Accordion from "components/ui/Accordion.jsx";
import CalendarInput from "./CalendarInput.jsx";
import UploadBoxInput from "./UploadBoxInput.jsx";

const CoverSettingSection = ({
  isOpen = false,
  selectedInput,
  setSelectedInput,
}) => {
  return (
    <Accordion
      isOpen={isOpen}
      showBox={false}
      Header={
        <div className="font-subtitle-large">تنظیمات صفحه اصلی (جلد)</div>
      }
    >
      <div className="grid grid-cols-2 gap-12">
        <InputWithEdit
          FontSettingsPosition="right"
          handleClick={(id) =>
            setSelectedInput((l) => (l === "cover_title" ? "" : id))
          }
          title="عنوان اصلی"
          name="cover_title"
          selected={selectedInput === "cover_title"}
          placeholder={"عنوان اصلی بولتن"}
        />
        <InputWithEdit
          FontSettingsPosition="left"
          handleClick={(id) =>
            setSelectedInput((l) => (l === "cover_subtitle" ? "" : id))
          }
          title="عنوان فرعی"
          name="cover_subtitle"
          selected={selectedInput === "cover_subtitle"}
          placeholder={"عنوان فرعی بولتن"}
        />
      </div>

      <div className="grid grid-cols-2 gap-12 my-8">
        <InputWithEdit
          FontSettingsPosition="right"
          handleClick={(id) =>
            setSelectedInput((l) => (l === "creator" ? "" : id))
          }
          title="ایجاد کننده"
          name="creator"
          selected={selectedInput === "creator"}
          placeholder={"نام شخص یا سازمان"}
        />
        <CalendarInput name={"cover_has_date_time"} title={"تاریخ ایجاد"} />
      </div>

      <div className="grid grid-cols-2 gap-24 my-8">
        <UploadBoxInput
          key={"cover_image"}
          name={"cover_image"}
          title={"فایل کاور"}
          description="کلیک کنید یا انتقال دهید"
        />

        <UploadBoxInput
          key={"logo_image"}
          name={"logo_image"}
          title={"فایل لوگو"}
          description="کلیک کنید یا انتقال دهید"
        />
      </div>
    </Accordion>
  );
};

CoverSettingSection.propTypes = {
  isOpen: PropTypes.bool,
  selectedInput: PropTypes.string,
  setSelectedInput: PropTypes.func,
  title: PropTypes.string,
};

export default CoverSettingSection;
