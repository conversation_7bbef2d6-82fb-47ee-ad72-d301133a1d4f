import { useBulletinStore } from "store/bulletinStore";
import { InstagramLogo, Rss, TelegramLogo, XLogo } from "@phosphor-icons/react";
import EitaaLogo from "components/ui/EitaaLogo";
import { toPersianNumber } from "utils/helper";
import Chips from "../components/Chips";

const FooterInHeader = () => {
  const { chart } = useBulletinStore((state) => state.bulletin);
  // const help = JSON.parse(JSON.stringify(chart));
  // delete help.filter;
  // const parsedChart = Object.entries(help);
  // console.log(parsedChart);
  const selectMedia = {
    telegram: {
      logo: <TelegramLogo size={10} color="white" />,
      bgColor: "#0084C7",
      mediaName: "تلگرام",
    },
    twitter: {
      // logo: <TwitterLogo size={10} color="white" />,
      logo: <XLogo size={10} color="white" />,
      // bgColor: "#54ADEE",
      bgColor: "#000000",
      mediaName: "ایکس",
    },
    instagram: {
      logo: <InstagramLogo size={10} color="white" />,
      bgColor: "#E64787",
      mediaName: "اینستاگرام",
    },
    news: {
      logo: <Rss size={10} color="white" />,
      bgColor: "#ECA213",
      mediaName: "خبرگزاری",
    },
    eitaa: {
      logo: <EitaaLogo size={10} color="white" />,
      bgColor: "#E67920",
      mediaName: "ایتا",
    },
    // bale: {
    //   logo: <BaleLogoWhite size={10} color="white" />,
    //   bgColor: "#41A59C",
    //   mediaName: "بله",
    // },
  };
  return (
    <div className="flex gap-4">
      {!!chart.platform?.twitter && (
        <Chips>
          <div className="flex gap-2">
            <div
              style={{ backgroundColor: selectMedia.twitter?.bgColor }}
              className="size-[18px] rounded-full flex items-center justify-center"
            >
              {selectMedia.twitter?.logo}
            </div>
            <div className="font-body-small flex gap-1">
              <span>
                {toPersianNumber(chart.platform.twitter?.chart_type?.length)}
              </span>
              <span>نمودار</span>
            </div>
          </div>
        </Chips>
      )}

      {!!chart.platform?.telegram && (
        <Chips>
          <div className="flex gap-2">
            <div
              style={{ backgroundColor: selectMedia.telegram?.bgColor }}
              className="size-[18px] rounded-full flex items-center justify-center"
            >
              {selectMedia.telegram?.logo}
            </div>
            <div className="font-body-small flex gap-1">
              <span>
                {toPersianNumber(chart.platform.telegram?.chart_type?.length)}
              </span>
              <span>نمودار</span>
            </div>
          </div>
        </Chips>
      )}

      {!!chart.platform?.instagram && (
        <Chips>
          <div className="flex gap-2">
            <div
              style={{ backgroundColor: selectMedia.instagram?.bgColor }}
              className="size-[18px] rounded-full flex items-center justify-center"
            >
              {selectMedia.instagram?.logo}
            </div>
            <div className="font-body-small flex gap-1">
              <span>
                {toPersianNumber(chart.platform.instagram?.chart_type?.length)}
              </span>
              <span>نمودار</span>
            </div>
          </div>
        </Chips>
      )}

      {!!chart.platform?.news && (
        <Chips>
          <div className="flex gap-2">
            <div
              style={{ backgroundColor: selectMedia.news?.bgColor }}
              className="size-[18px] rounded-full flex items-center justify-center"
            >
              {selectMedia.news?.logo}
            </div>
            <div className="font-body-small flex gap-1">
              <span>
                {toPersianNumber(chart.platform.news?.chart_type?.length)}
              </span>
              <span>نمودار</span>
            </div>
          </div>
        </Chips>
      )}
    </div>
  );
};

export default FooterInHeader;
