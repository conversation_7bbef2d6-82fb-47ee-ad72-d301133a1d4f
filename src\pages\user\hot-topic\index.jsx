import { useCallback, useEffect, useMemo, useState } from "react";
import SummaryCardTopic from "./components/SummaryCardTopic";
import TextSlicer from "components/TextSlicer";
import advanceSearch from "service/api/advanceSearch";
import DropDown from "components/ui/DropDown";
import { preprocessWord, query<PERSON>and<PERSON>, toPersianNumber } from "utils/helper";
import Loading from "components/ui/Loading";
import Paginate from "components/ui/Paginate";
import CardsLayout from "layout/CardsLayout";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";
import useSearchStore from "store/searchStore.js";
import { PATHS } from "constants/index.js";
import MasterpageLayout from "layout/masterpage-layout.jsx";
import { DownloadExcelButton } from "components/DownloadExcelButton/index.jsx";
import ReactWordcloud from "react-wordcloud";
import CLUSTER_COLORS from "constants/colors.js";
import { CTabs } from "components/ui/CTabs.jsx";
import { Cloud, Layout } from "@phosphor-icons/react";
import { Card } from "components/ui/Card.jsx";
import CardLoading from "components/ui/CardLoading.jsx";
import TreeMap from "components/Charts/TreeMap.jsx";
import { ToastContainer } from "react-toastify";
import PropTypes from "prop-types";

const WordCloudCard_Header = ({
  wordCloudActiveTab,
  onClusterTabChange,
  title,
}) => {
  return (
    <div className={"flex flex-row gap-2 w-full justify-between p-2"}>
      <div className={"flex"}>
        <CTabs
          tabArray={[
            {
              id: "wordCloud",
              title: "ابر کلمات",
              icon: Cloud,
            },
            {
              id: "cluster",
              title: "دسته‌بندی",
              icon: Layout,
            },
          ]}
          activeTab={wordCloudActiveTab}
          onChange={onClusterTabChange}
        />
      </div>
      <div className={"flex justify-end align-middle items-center"}>
        <h3
          className={
            "font-subtitle-medium text-light-neutral-text-high [direction:rtl]"
          }
        >
          مهمترین مطالب مرتبط با {title}
        </h3>
      </div>
    </div>
  );
};

WordCloudCard_Header.propTypes = {
  wordCloudActiveTab: PropTypes.string,
  onClusterTabChange: PropTypes.func,
  title: PropTypes.string,
};

const WordCloudCard = ({ loading, clusterData }) => {
  const [wordCloudActiveColors, setWordCloudActiveColors] =
    useState(CLUSTER_COLORS);
  const [wordCloudActiveTab, setWordCloudActiveTab] = useState("wordCloud");
  const [wordCloud, setWordCloud] = useState([]);

  const treeData = useMemo(() => {
    const help = [];

    clusterData?.meta?.keys?.slice(0, 9).forEach((item, index) => {
      help.push({
        id: `id_${index}`,
        name: item.word,
        color: CLUSTER_COLORS[index],
        value: item.count,
        x: item.count,
      });
    });
    return help;
  }, [clusterData]);

  const reloadWordCloud = (data) => {
    setWordCloudActiveColors(CLUSTER_COLORS);
    return setWordCloud(
      data?.meta?.keys?.map(({ word, count }) => {
        return { text: preprocessWord(word), value: count };
      }) || [],
    );
  };

  useEffect(() => {
    reloadWordCloud(clusterData);
  }, [clusterData]);

  const onClusterTabChange = (name) => {
    setWordCloudActiveTab(name);
  };

  return (
    <Card className={"flex-col mb-8"}>
      <WordCloudCard_Header
        wordCloudActiveTab={wordCloudActiveTab}
        onClusterTabChange={onClusterTabChange}
        title={clusterData?.meta?.keys[0]?.word || ""}
      />

      <div
        className={
          "relative flex w-full text-center justify-center items-center h-[300px] overflow-hidden"
        }
      >
        {wordCloudActiveTab === "wordCloud" && (
          <div className={"flex flex-row w-full h-full"}>
            {loading ? (
              <CardLoading />
            ) : (
              <>
                <div className={"flex flex-1 responsive-svg"}>
                  <ReactWordcloud
                    // size={[isSidebarOpened ? 1550 : 1700, 300]}
                    options={{
                      rotations: 1,
                      rotationAngles: [0],
                      enableTooltip: true,
                      deterministic: false,
                      fontFamily: "iranyekan",
                      fontSizes: [14, 54],
                      padding: 10,
                      colors: wordCloudActiveColors,
                      tooltipOptions: { theme: "light", arrow: true },
                    }}
                    words={wordCloud}
                    callbacks={{
                      getWordTooltip: (word) => {
                        if (word.text.endsWith("#")) {
                          return `${word.text.slice(
                            0,
                            word.text.length - 1,
                          )} (${word.value})`;
                        }
                        return `${word.text} (${word.value})`;
                      },
                    }}
                  />
                </div>
              </>
            )}
          </div>
        )}
        {wordCloudActiveTab === "cluster" && (
          <div
            className={
              "flex justify-center align-middle items-center w-full h-full"
            }
          >
            <TreeMap treeData={treeData} handleClick={() => {}} />
          </div>
        )}
      </div>
    </Card>
  );
};

WordCloudCard.propTypes = {
  loading: PropTypes.bool,
  clusterData: PropTypes.object,
};

const HotTopic = () => {
  const { filters, query, cluster, loadState } = useSearchStore();

  useEffect(() => {
    loadState();
  }, []);

  const breadcrumbList = [
    { title: `خانه`, link: PATHS.basePath + PATHS.dashboard },
    {
      title: `${
        cluster.name === "all" ? "همه دسته ها" : `دسته‌بندی ${cluster.name}`
      }`,
    },
  ];
  const setBreadCrumb = useBreadcrumb(breadcrumbList);

  const [topics, setTopics] = useState({});
  const [sort, setSort] = useState("");
  const [sort_type, setSort_type] = useState("نزولی");
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [words, setWords] = useState([]);

  const med = {
    telegram: ["میزان بازدید", "زمان انتشار"],
    twitter: ["بازنشر", "تعداد لایک", "زمان انتشار"],
    instagram: ["تعداد لایک", "تعداد نظرات", "زمان انتشار"],
    news: ["تعداد نظرات", "زمان انتشار"],
    eitaa: ["میزان بازدید", "زمان انتشار"],
  };

  const convertPerfilterToEn = {
    نزولی: "desc",
    صعودی: "asc",
    "زمان انتشار": "time",
    "میزان بازدید": "view_count",
    "تعداد لایک": "like_count",
    بازنشر: "copy_count",
    "تعداد نظرات": "",
  };

  useEffect(() => {
    getTopics(page, "desc", convertPerfilterToEn[sort]);
    setWords(
      cluster?.keywords?.map((item, index) => {
        return { word: item, count: cluster?.counts[index] };
      }),
    );
    setBreadCrumb([
      { title: `خانه`, link: PATHS.basePath + PATHS.dashboard },
      {
        title: `${
          cluster.name === "all" ? "همه دسته ها" : `دسته‌بندی ${cluster.name}`
        }`,
      },
    ]);
  }, [cluster, query]);

  const getTopics = useCallback(
    async (page, sort_type, sort) => {
      setLoading(true);
      try {
        const q_ = cluster?.keywords[0]?.includes("#")
          ? cluster?.keywords[0].slice(1)
          : cluster.keywords[0];
        const responseStinas = await advanceSearch.search({
          // q: base_url + "&q=" + q_,
          q: queryHandler(
            cluster?.baseUrl,
            "&count=12&q=" + q_,
            page,
            sort_type,
            sort,
          ),
        });
        setTopics(JSON.parse(responseStinas.data.data).result);
      } catch (error) {
        console.log(error);
      }
      setLoading(false);
    },
    [cluster, query],
  );

  useEffect(() => {
    getTopics(
      page,
      convertPerfilterToEn[sort_type],
      convertPerfilterToEn[sort],
    );
    setPage(1);
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, [sort_type, sort]);

  // const countLoadingOffset = useRef();
  useEffect(() => {
    getTopics(
      page,
      convertPerfilterToEn[sort_type],
      convertPerfilterToEn[sort],
    );
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, [page]);

  return (
    <MasterpageLayout>
      <div className={"flex-1"}>
        <WordCloudCard
          clusterData={{ meta: { keys: words } }}
          clusterListActiveTab={{ name: "all", index: 1 }}
          setClusterListActiveTab={() => {}}
          loading={false}
        />

        {loading ? (
          <Loading />
        ) : (
          <div>
            <div className="bg-transparent flex justify-between items-center mb-4 [direction:rtl]">
              <div className="flex items-center gap-4">
                <DropDown
                  title="نمایش بر اساس"
                  subsets={med[filters.platform]}
                  selected={sort}
                  setSelected={setSort}
                />
                <div className="font-overline-medium text-light-neutral-text-medium flex gap-2">
                  <div className="flex gap-2">
                    نمایش
                    <div>
                      <span>{toPersianNumber(page * 16)}</span>-
                      <span>{toPersianNumber((page - 1) * 16 + 1)}</span>
                    </div>
                    از
                    <span>{toPersianNumber(topics?.meta?.total || 0)}</span>
                    محتوا
                  </div>
                  <div>-</div>
                  <div className="flex gap-2">
                    صفحه
                    <span>{toPersianNumber(page)}</span>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-4">
                <DownloadExcelButton platform={filters.platform}>
                  دانلود خروجی اکسل
                </DownloadExcelButton>
              </div>
            </div>
            <CardsLayout>
              {topics?.data?.map((item) => (
                <div className="w-full" key={item.id}>
                  <SummaryCardTopic media={filters.platform} data={item}>
                    <TextSlicer>{item.description}</TextSlicer>
                  </SummaryCardTopic>
                </div>
              ))}
            </CardsLayout>
            <Paginate
              page={page}
              setPage={setPage}
              dataCount={topics?.meta?.total?.toString()}
              per_page={10}
            />
          </div>
        )}
      </div>
      <ToastContainer />
    </MasterpageLayout>
  );
};
export default HotTopic;
