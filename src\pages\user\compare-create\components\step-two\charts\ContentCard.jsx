import React, { useEffect, useRef, useState } from "react";
import Doughnut from "components/Charts/Doughnut.jsx";
import {
  fixPercentToShow,
  formatShortNumber,
  shortener,
  toPersianNumber,
} from "utils/helper.js";
import { useCompareStore } from "store/compareStore.js";
import PropTypes from "prop-types";

const ContentCard = ({ title, name, data, titleOffset, className = "" }) => {
  const { fields } = useCompareStore((state) => state.compare);
  const chartRef = useRef(null);
  const [legendWidth, setLegendWidth] = useState("auto");

  useEffect(() => {
    if (chartRef.current) {
      // Set the legend width equal to the chart's width
      setLegendWidth(`${chartRef.current.offsetWidth - 20}`);
    }
  }, [data]);

  const fieldsCleared = fields.filter(
    (field) => field.q || field.username || field.id,
  );
  if ((title, data.filter((x) => !!x.y).length === 0)) return null;
  const colors = ["#1DCEA3", "#6D72E5", "#DB6DE5", "#F7A912"];

  return (
    <div ref={chartRef} className={`w-full flex justify-center ${className}`}>
      <Doughnut
        titleOffset={titleOffset}
        showDataLabels={false}
        colLayout
        name={name}
        title={title}
        titleFontSize={13}
        data={data}
        legendFormatter={function () {
          const index = this.index;
          return `<div
      dir="rtl"
      style="display:grid;grid-template-columns:1fr 1fr;font-family:iranyekan,serif;width:${legendWidth}px;padding:2px ${
        legendWidth * 0.18
      }px;"
    >
      <div style="display:grid;grid-template-columns:1fr 1fr;">
        <span style="font-size:14px;direction: rtl;">${toPersianNumber(
          formatShortNumber(this.x),
        )} <span style="font-size: 10px;">محتوا</span></span>
      </div>
      <div style="justify-self:right">
        <div style="display:flex;align-items:center;gap:4px">
          <span style="font-size:12px; direction: rtl;">${shortener(
            fieldsCleared[index]?.q ||
              fieldsCleared[index]?.username ||
              fieldsCleared[index]?.user_name ||
              fieldsCleared[index]?.full_name,
            14,
          )}</span>
          <div style="width:12px;height:12px;background-color:${
            this.color
          };border-radius:100%;"></div>
        </div>
      </div>
    </div>`;
        }}
        tooltipFormatter={function () {
          return `<div style="display:flex;flex-direction:column;gap:8px;text-align:center;font-family:iranyekan,serif;  direction: rtl;"><div>${shortener(
            fieldsCleared[this.colorIndex]?.q ||
              fieldsCleared[this.colorIndex]?.username ||
              fieldsCleared[this.colorIndex]?.user_name ||
              fieldsCleared[this.colorIndex]?.full_name,
            14,
          )}</div><div>${fixPercentToShow(this.percentage / 100)}</div></div>`;
        }}
        colors={colors}
      />
    </div>
  );
};

ContentCard.propTypes = {
  title: PropTypes.string,
  name: PropTypes.string,
  data: PropTypes.array,
  titleOffset: PropTypes.number,
  className: PropTypes.string,
};

export default React.memo(ContentCard);
