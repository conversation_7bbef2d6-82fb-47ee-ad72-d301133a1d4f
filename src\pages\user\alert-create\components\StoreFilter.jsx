import { useEffect, useState } from "react";
import SearchInput from "./SearchInput";
import AlertAndFilterDetails from "components/alertAndFilterDetails";
import { CButton } from "components/ui/CButton.jsx";

const StoreFilter = ({ setStatus, setAlertData }) => {
  const [filterData, setFilterData] = useState({});
  useEffect(() => {
    if (filterData.params) {
      setAlertData((l) => {
        return {
          ...l,
          q: filterData.params.q,
          platform: filterData.params.platform,
        };
      });
    }
  }, [filterData]);
  return (
    <div className="mt-12 flex flex-col gap-4">
      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-2">
          <span className="font-overline-large">فیلترها</span>
          <SearchInput setFilterData={setFilterData} />
        </div>

        {filterData.params && (
          <div className="bg-light-neutral-surface-highlight p-6 rounded-lg">
            <AlertAndFilterDetails
              data={{ ...filterData, ...filterData.params }}
              showReceivingPlatform={false}
            />
          </div>
        )}
      </div>

      <div className="flex flex-row-reverse mt-12">
        <CButton
          type={"submit"}
          className={`!w-[200px]`}
          onClick={() => {
            if (filterData.params.q?.length > 2) {
              setStatus(5);
            }
          }}
        >
          ادامه
        </CButton>
        <button
          className="w-[100px] h-10 flex justify-center items-center text-light-primary-text-rest font-button-medium"
          onClick={() => {
            setStatus(2);
          }}
        >
          مرحله قبل
        </button>
      </div>
    </div>
  );
};

export default StoreFilter;
