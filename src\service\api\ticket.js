import useFetch from "../index";

class Ticket {
  // ADMIN
  createAmin(data) {
    return useFetch.post("/api/admin/v1/ticket/", data);
  }
  getAllAdminTickets() {
    return useFetch.get("/api/admin/v1/ticket/");
  }
  getAdminTicket(id) {
    return useFetch.get(`/api/admin/v1/ticket/${id}/`);
  }
  searchTicket(data, query = {}) {
    return useFetch.post(
      `/api/admin/v1/ticket/search/?${new URLSearchParams(query).toString()}`,
      data
    );
  }
  updateAttachmentAdmin(ticket_id, msg_id, data) {
    return useFetch.put(
      `/api/admin/v1/ticket/${ticket_id}/messages/${msg_id}/attachment/`,
      data
    );
  }
  sendTicketAdmin(id, data) {
    return useFetch.put(`/api/admin/v1/ticket/${id}/`, data);
  }
  getUsernamesList(data) {
    return useFetch.post(
      `${import.meta.env.VITE_AI_SERVICE_URL}/api/qac`,
      data
    );
  }
  getUsernameList(data) {
    return useFetch.post(`/api/admin/v1/account/search/`, data);
  }
  toggleAdminTicketState(id, data) {
    return useFetch.patch(`/api/admin/v1/ticket/${id}/`, data);
  }
  // USER
  getAll() {
    return useFetch.get("/api/v1/ticket/");
  }
  get(id) {
    return useFetch.get(`/api/v1/ticket/${id}/`);
  }
  getNextPage(id, page) {
    return useFetch.get(`/api/v1/ticket/${id}/?${page}`);
  }
  create(data) {
    return useFetch.post("/api/v1/ticket/", data);
  }
  updateAttachment(ticket_id, msg_id, data) {
    return useFetch.put(
      `/api/v1/ticket/${ticket_id}/messages/${msg_id}/attachment/`,
      data
    );
  }
  sendTicket(id, data) {
    return useFetch.put(`/api/v1/ticket/${id}/`, data);
  }
  toggleTicketState(id, data) {
    return useFetch.patch(`/api/v1/ticket/${id}/`, data);
  }
}
export default new Ticket();
