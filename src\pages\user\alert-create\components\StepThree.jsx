import SettingBadge from "./SettingBadge";
import NewFilter from "./NewFilter";
import StoreFilter from "./StoreFilter";

const StepThree = ({
  setStatus,
  selectFilter,
  setSelectFiter,
  alertData,
  setAlertData,
}) => {
  return (
    <div className="p-6 w-full">
      <div className="grid grid-cols-2 gap-4">
        <div
          onClick={() => setSelectFiter("new-filter")}
          className={"cursor-pointer"}
        >
          <SettingBadge
            text="تنظیم فیلتر جدید"
            src="/Setting.png"
            selected={selectFilter === "new-filter"}
          />
        </div>
        <div
          onClick={() => setSelectFiter("store-filter")}
          className={"cursor-pointer"}
        >
          <SettingBadge
            text="انتخاب از میان فیلترهای از پیش‌ساخته شده"
            src="/Funnel.png"
            selected={selectFilter === "store-filter"}
          />
        </div>
      </div>

      {selectFilter === "new-filter" ? (
        <NewFilter
          alertData={alertData}
          setAlertData={setAlertData}
          setStatus={setStatus}
        />
      ) : (
        selectFilter === "store-filter" && (
          <StoreFilter setStatus={setStatus} setAlertData={setAlertData} />
        )
      )}
    </div>
  );
};

export default StepThree;
