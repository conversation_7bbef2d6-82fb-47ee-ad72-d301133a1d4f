import queryString from "query-string";
export const generateStatsQuery = (
  platform,
  type,
  object,
  filterObject = {},
) => {
  const selectType = {
    info: {
      twitter: "info",
      instagram: "info",
      telegram: "info",
      news: "info",
    },
    process: {
      twitter: "posts/tweet_process",
      instagram: "posts/share_process",
      telegram: "info/process",
      news: "posts/publish_process",
    },
    subjects: {
      twitter: "subjects",
      instagram: "subjects",
      telegram: "subjects",
      news: "subjects",
    },
    by_subject: {
      twitter: "users/by_subject",
      instagram: "users/by_subject",
      telegram: "channels/by_subject",
      news: "agencies/by_subject",
    },
    gender: {
      twitter: "posts/gender",
      instagram: "posts/gender",
      telegram: "",
      news: "",
    },
    age: {
      twitter: "posts/age_classification",
      instagram: "posts/age_classification",
      telegram: "",
      news: "",
    },
    category: {
      twitter: "posts/category/chart",
      instagram: "posts/category/chart",
      telegram: "posts/category/chart",
      news: "posts/category/chart",
    },
    sentiment: {
      twitter: "posts/sentiment",
      instagram: "posts/sentiment",
      telegram: "posts/sentiment",
      news: "",
    },
  };
  const selectOtherOption = {
    twitter: "&source=original",
    instagram: "&min_advert_noise=0&max_advert_noise=0.15",
    telegram: "",
    news: "",
  };

  let query = `search/api/v1/search?api_path=/${platform}/${selectType[type][platform]}`;
  if (queryString.stringify(object)) {
    query += `&${queryString.stringify(object, { skipNull: true, arrayFormat: "index", skipEmptyString: true, encode: true })}${selectOtherOption[platform]}`;
  }

  if (filterObject.language && filterObject.language?.includes("fa"))
    query += "&langs[0]=fa";
  if (filterObject.keywords) {
    for (let i = 0; i < filterObject.keywords?.length; i++) {
      query += `&extracted_phrases[${i}]=${filterObject.keywords[i]}`;
    }
  }
  if (filterObject.hashtags) {
    for (let i = 0; i < filterObject.hashtags?.length; i++) {
      query += `&extracted_hashtags[${i}]=${filterObject.hashtags[i]}`;
    }
  }
  if (filterObject.reshare) {
    if (filterObject.reshare.includes("republish")) query += "&source=copy";
    if (filterObject.reshare.includes("publish")) query += "&source=original";
  }
  if (filterObject.sentiment) {
    if (filterObject.sentiment.includes("positive"))
      query += "&sentiment_positive_min=50&sentiment_positive_max=100";
    if (filterObject.sentiment.includes("negative"))
      query += "&sentiment_negative_min=50&sentiment_negative_max=100";
    if (filterObject.sentiment.includes("neutral"))
      query += "&sentiment_neutral_min=50&sentiment_neutral_max=100";
  }
  if (filterObject.subjectCategory) {
    let categoryIndex = 0;
    let subCategoryIndex = 0;
    if (filterObject.subjectCategory.includes("varzeshi"))
      query += `&categories[${categoryIndex++}]=Varzeshi&sub_categories[${subCategoryIndex++}]=KoshtiVaVazneBardari&sub_categories[${subCategoryIndex++}]=SaiereHozeha&sub_categories[${subCategoryIndex++}]=FootballIran&sub_categories[${subCategoryIndex++}]=FootballJahan&sub_categories[${subCategoryIndex++}]=Razmi&sub_categories[${subCategoryIndex++}]=ToopVaTour`;
    if (filterObject.subjectCategory.includes("eghtesadi"))
      query += `&categories[${categoryIndex++}]=Eghtesadi&sub_categories[${subCategoryIndex++}]=EghtesadKalanVaBoodje&sub_categories[${subCategoryIndex++}]=TaavonVaEshteghal&sub_categories[${subCategoryIndex++}]=Boors&sub_categories[${subCategoryIndex++}]=BimeVaBank&sub_categories[${subCategoryIndex++}]=RahVaMaskan&sub_categories[${subCategoryIndex++}]=SanatTejaratBazargani&sub_categories[${subCategoryIndex++}]=KeshavarziVaOmooreDam&sub_categories[${subCategoryIndex++}]=NaftVaEnergy&sub_categories[${subCategoryIndex++}]=EghtesadeBinolmelal`;
    if (filterObject.subjectCategory.includes("honar"))
      query += `&categories[${categoryIndex++}]=FarhangHonarVaResane&sub_categories[${subCategoryIndex++}]=DiniVaMazhabi&sub_categories[${subCategoryIndex++}]=RadioVaTV&sub_categories[${subCategoryIndex++}]=CinemaVaTheatre&sub_categories[${subCategoryIndex++}]=MousighiVaHonarhaieTajasomi`;
    if (filterObject.subjectCategory.includes("international"))
      query += `&categories[${categoryIndex++}]=Beinolmelal&sub_categories[${subCategoryIndex++}]=AmricaVaEurope&sub_categories[${subCategoryIndex++}]=SharghAsiaVaOghianousie&sub_categories[${subCategoryIndex++}]=GharbAsia&sub_categories[${subCategoryIndex++}]=Africa`;
    if (filterObject.subjectCategory.includes("ejtemaie"))
      query += `&categories[${categoryIndex++}]=Ejtemaee&sub_categories[${subCategoryIndex++}]=Salamat&sub_categories[${subCategoryIndex++}]=AmouzeshVaParvaresh&sub_categories[${subCategoryIndex++}]=HoghoughiVaGhazaee&sub_categories[${subCategoryIndex++}]=EntezamiVaHavades&sub_categories[${subCategoryIndex++}]=MohitZistVaGardeshgari&sub_categories[${subCategoryIndex++}]=Shahri&sub_categories[${subCategoryIndex++}]=ZananVaJavanan&sub_categories[${subCategoryIndex++}]=RefahVaAsibhaieEjtemaee`;
    if (filterObject.subjectCategory.includes("siasi"))
      query += `&categories[${categoryIndex++}]=Siasi&sub_categories[${subCategoryIndex++}]=EmamVaRahbari&sub_categories[${subCategoryIndex++}]=AhzabVaTashakolha&sub_categories[${subCategoryIndex++}]=AmniatiVaDefaee&sub_categories[${subCategoryIndex++}]=Dolat&sub_categories[${subCategoryIndex++}]=Majles&sub_categories[${subCategoryIndex++}]=TashakolhaieDaneshghai`;
    if (filterObject.subjectCategory.includes("elmi"))
      query += `&categories[${categoryIndex++}]=ElmiVaDaneshghai&sub_categories[${subCategoryIndex++}]=Amouzesh&sub_categories[${subCategoryIndex++}]=ElamVaFanavariIran&sub_categories[${subCategoryIndex++}]=ElamVaFanavariJahan`;
    if (filterObject.subjectCategory.includes("roozmare"))
      query += `&categories[${categoryIndex++}]=Roozmare&sub_categories[${subCategoryIndex++}]=Roozmare`;
  }

  if (filterObject.gender) {
    if (filterObject.gender.includes("male")) query += "&gender[0]=male";
    if (filterObject.gender.includes("female")) query += "&gender[0]=female";
  }

  if (filterObject.sources) {
    for (let i = 0; i < filterObject.sources?.length; i++) {
      query += `&resource_ids[${i}]=${filterObject.sources[i]?.id}&post_resource_ids[${i}]=${filterObject.resource[i]?.id}`;
    }
  }

  if (filterObject.impact && filterObject.impact[0] !== "all") {
    for (let i = 0; i < filterObject.impact?.length; i++)
      query += `&resource_influence_categories[${i}]=${filterObject.impact[i]}`;
  }

  return query;
};
