import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { shortener, toPersianNumber } from "utils/helper";

const RadialBar = ({
  data,
  legendEnabled = false,
  legendFormatter = () => {},
}) => {
  
  const selectColor = ["#201089", "#2916B6", "#462EE5", "#8171EF", "#C1B8FA"];

  const options = {
    chart: {
      type: "column",
      inverted: true,
      polar: true,
      height: 350,
      backgroundColor: "transparent",
      spacing: [-40, 0, -40, 0],
      margin: [0, 0, 0, 0],
      padding: [0, 0, 0, 0],
    },
    title: {
      text: null,
      align: "left",
    },
    pane: {
      size: "100%",
      innerSize: "20%",
      endAngle: 270,
    },
    credits: {
      enabled: false,
    },
    legend: {
      enabled: legendEnabled,
      useHTML: true,
      labelFormatter: legendFormatter,
    },
    xAxis: {
      tickInterval: 1,
      labels: {
        align: "right",
        useHTML: true,
        allowOverlap: true,
        step: 1,
        y: 3,
        style: {
          fontSize: "12px",
          fontFamily: "iranyekan",
        },
      },
      lineWidth: 0,
      gridLineWidth: 0,
      categories: data?.map((item) =>
        item?.username ? "@" + item?.username : item.title
      ),
    },
    yAxis: {
      lineWidth: 0,
      tickInterval: 1,
      reversedStacks: false,
      endOnTick: true,
      showLastLabel: true,
      gridLineWidth: 0,
      visible: false,
    },
    tooltip: {
      headerFormat: "",
      backgroundColor: "#efefef",
      textAlign: "center",
      useHTML: true,
      shadow: false,
      formatter: function () {
        return `<div style="display:flex;flex-direction:column;gap:8px;text-align:center;font-family:iranyekan,serif"><div>${
          this.key
        }</div><div>${toPersianNumber(this.y)}</div></div>`;
      },
    },
    plotOptions: {
      column: {
        stacking: "normal",
        borderWidth: 0,
        pointPadding: 0,
        groupPadding: 0.15,
        borderRadius: "50%",
      },
    },
    series: [
      {
        name: "media count",
        data: data?.map((item, index) => {
          return {
            name: item.username,
            y: +item.y,
            color: selectColor[index % selectColor.length],
          };
        }),
      },
    ],
  };
  return <HighchartsReact highcharts={Highcharts} options={options} />;
};

export default RadialBar;
