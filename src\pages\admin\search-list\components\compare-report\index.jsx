import {
  <PERSON>,
  Heart,
  Repeat,
  Upload,
  User,
  UserList,
  ReadCv<PERSON>ogo,
  Clock,
  UserPlus,
  UserMinus,
} from "@phosphor-icons/react";
import CardDetails from "./CardDetails";
import PlatformDistribution from "./PlatformDistribution";
import TopicGrouping from "./TopicGrouping";
import ContentShareFlow from "./ContentShareFlow";
import PioneerUsers from "./PioneerUsers";
import ReportWordCloud from "./ReportWordCloud.jsx";

const CompareReport = ({ filter }) => {
  const cardDetails = [
    {
      id: 0,
      title: "تعداد کل جست‌‌وجو‌ها",
      count: "۲.۴M",
      icon: ReadCvLogo,
      color: "#6F5CD1",
      variant: "#6F5CD126",
    },
    {
      id: 2,
      title: "تعداد کاربران دارای جست‌‌وجو",
      count: "۲.۴M",
      icon: UserPlus,
      color: "#1CB0A5",
      variant: "#DDF3F2",
    },
    {
      id: 3,
      title: "تعداد کاربران بدون جست‌‌وجو",
      count: "۲.۴M",
      icon: UserMinus,
      color: "#ED5C74",
      variant: "#FDE7EA",
    },
  ];

  return (
    <div className="grid grid-cols-12 gap-4">
      {cardDetails?.map((item) => (
        <div className="col-span-4" key={item?.id}>
          <CardDetails
            title={item?.title}
            count={item?.count}
            color={item?.color}
            variant={item?.variant}
            icon={item?.icon}
          />
        </div>
      ))}
      <div className={"col-span-12 grid grid-cols-12 gap-4"}>
        <div className="col-span-4">
          <PlatformDistribution filter={filter} />
        </div>
        <div className="col-span-8">
          <TopicGrouping filter={filter} />
        </div>
      </div>

      <div className={"col-span-12 grid grid-cols-12 gap-4"}>
        <div className="col-span-12">
          <ContentShareFlow filter={filter} />
        </div>
      </div>
      <div className={"col-span-12 grid grid-cols-12 gap-4"}>
        <div className="col-span-7">
          <ReportWordCloud filter={filter} />
        </div>
        <div className="col-span-5">
          <PioneerUsers filter={filter} />
        </div>
      </div>
    </div>
  );
};

export default CompareReport;
