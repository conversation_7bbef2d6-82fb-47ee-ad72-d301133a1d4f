import { parseNumber } from "utils/helper";
import { SpinnerGap } from "@phosphor-icons/react";
import PropTypes from "prop-types";

const Badge = ({
  title,
  count,
  image,
  icon,
  color,
  bgColor,
  loading,
  platform,
}) => {
  return (
    <div
      className={`col-span-6 rounded-lg bg-white ${platform === "news" ? "h-52" : "h-32"} shadow-[0px_2px_20px_0px_#0000000D] flex justify-between`}
    >
      <div
        className="w-full [direction:rtl] flex flex-col gap-2 p-4 justify-center"
        style={{
          background: `url(${image})`,
          backgroundRepeat: "no-repeat",
          backgroundSize: "contain",
        }}
      >
        <div className="flex gap-2 items-center">
          <div
            className={`size-6 rounded-[4px] grid place-items-center ${bgColor}`}
          >
            {icon}
          </div>
          <p className="font-body-small text-light-neutral-text-medium">
            {title}
          </p>
        </div>
        <div
          className={`w-full h-16 font-title-medium flex items-center ${color}`}
        >
          {loading ? (
            <SpinnerGap size={26} className="animate-spin" />
          ) : (
            parseNumber(count)
          )}
        </div>
      </div>
    </div>
  );
};

Badge.propTypes = {
  title: PropTypes.string,
  count: PropTypes.number,
  image: PropTypes.string,
  icon: PropTypes.element,
  color: PropTypes.string,
  bgColor: PropTypes.string,
  loading: PropTypes.bool,
  platform: PropTypes.oneOf(["news", "social"]),
};

export default Badge;
