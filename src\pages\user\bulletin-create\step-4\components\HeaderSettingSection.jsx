import { useState } from "react";
import PropTypes from "prop-types";
import { useBulletinStore } from "store/bulletinStore.js";
import InputWithEdit from "./InputWithEdit.jsx";
import Accordion from "components/ui/Accordion.jsx";
import UploadBoxInput from "./UploadBoxInput.jsx";
import Toggle from "./Toggle.jsx";
import CalendarInput from "./CalendarInput.jsx";

const HeaderSettingSection = ({
  isOpen = false,
  selectedInput,
  setSelectedInput,
}) => {
  const setBulletin = useBulletinStore((state) => state.setBulletin);
  const { metadata } = useBulletinStore((state) => state.bulletin);

  const [headerHasDateTime, setHeaderHasDateTime] = useState(
    metadata?.header_has_date_time,
  );

  return (
    <Accordion
      isOpen={false}
      showBox={false}
      Header={<div className="font-subtitle-large">تنظیمات سربرگ و پانویس</div>}
    >
      <div className="grid grid-cols-2 gap-12">
        <InputWithEdit
          FontSettingsPosition="right"
          handleClick={(id) =>
            setSelectedInput((l) => (l === "header_title" ? "" : id))
          }
          value={metadata?.header_title}
          title="متن سربرگ"
          placeholder={"متن سربرگ بولتن"}
          name="header_title"
          selected={selectedInput === "header_title"}
          initialValue={{ font_size: 11 }}
        />
      </div>
      <div className="grid grid-cols-2 gap-12 my-8">
        <Toggle
          title={"درج شماره صفحه"}
          description="(در پانویس قرار می‌گیرد)"
          name="has_page_number"
          key="has_page_number"
        />
      </div>
      <div className="grid grid-cols-2 gap-12 my-8">
        <Toggle
          title={"درج تاریخ"}
          onChange={(e) => {
            if (!e) {
              const help = JSON.parse(JSON.stringify(metadata));
              help["header_has_date_time"] = "";
              setBulletin({ metadata: { ...help } });
            }
            setHeaderHasDateTime(e);
          }}
        />
        {headerHasDateTime && (
          <CalendarInput name={"header_has_date_time"} title={"تاریخ سربرگ"} />
        )}
      </div>
      <div className="grid grid-cols-2 gap-12 my-8">
        <UploadBoxInput
          key={"header_logo"}
          name="header_logo"
          title={"فایل لوگو سربرگ"}
          description={"کلیک کنید یا انتقال دهید"}
        />
      </div>
    </Accordion>
  );
};

HeaderSettingSection.propTypes = {
  isOpen: PropTypes.bool,
  selectedInput: PropTypes.string,
  setSelectedInput: PropTypes.func,
};

export default HeaderSettingSection;
