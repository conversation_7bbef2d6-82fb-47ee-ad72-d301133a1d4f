import { CaretLeft } from "@phosphor-icons/react";
import PlansHistoryTable from "./plans-history-table";
import { useBreadcrumb } from "hooks/useBreadcrumb";
import { Link, useLocation } from "react-router-dom";
import { useState } from "react";
import DeletePopUp from "components/ui/DeletePopUp";
import ChangePlanCard from "./components/ChangePlanCard";
import RevivalPlanCard from "./components/RevivalPlanCard";
import PlanSettingsStatusCard from "./components/PlanSettingsStatusCard";

const UserPlanSettings = () => {
  const [isPopupOpen, setIsPopupOpen] = useState(false);

  const [planChangeToggle, setPlanChangeToggle] = useState(false);
  const [planRevivalToggle, setPlanRevivalToggle] = useState(false);
  const [newPlan, setNewPlan] = useState("");
  const [startPlan, setStartPlan] = useState("");

  const [planPrice, setPlanPrice] = useState("");
  const [planRevivalStart, setPlanRevivalStart] = useState("");
  const [planRevivalPrice, setPlanRevivalPrice] = useState("");

  const { pathname } = useLocation();

  const userId = pathname.split("/").slice(-2)[0];
  const breadcrumbList = [
    { title: "کاربران", link: "/admin/user/list" },
    { title: "پرهام", link: `/admin/user/list/${userId}` },
    { title: "پلن کاربری" },
  ];
  useBreadcrumb(breadcrumbList);
  return (
    <>
      <div className="grid grid-cols-12 gap-2 container lg:px-4 mx-auto">
        <PlanSettingsStatusCard
          planChangeToggle={planChangeToggle}
          planRevivalToggle={planRevivalToggle}
          setPlanChangeToggle={setPlanChangeToggle}
          setPlanRevivalToggle={setPlanRevivalToggle}
          setIsPopupOpen={setIsPopupOpen}
        />

        <div className="col-span-8 row-span-2 bg-light-neutral-surface-card rounded-[8px] p-6">
          <div className="flex items-center justify-between">
            <p className="font-subtitle-large">تاریخچه پلن کاربر</p>
            <Link
              to={`/admin/user/list/${
                pathname.split("/").slice(-2)[0]
              }/planHistory`}
              className="font-button-small text-light-primary-background-rest rounded-md transition hover:bg-light-primary-background-highlight cursor-pointer flex items-center justify-center p-2"
            >
              نمایش همه
              <CaretLeft />
            </Link>
          </div>
          <PlansHistoryTable />
        </div>

        {planChangeToggle && (
          <ChangePlanCard
            setPlanChangeToggle={setPlanChangeToggle}
            setStartPlan={setStartPlan}
            setNewPlan={setNewPlan}
            setPlanPrice={setPlanPrice}
            startPlan={startPlan}
            planPrice={planPrice}
          />
        )}

        {planRevivalToggle && (
          <RevivalPlanCard
            setPlanRevivalStart={setPlanRevivalStart}
            setPlanRevivalPrice={setPlanRevivalPrice}
            setPlanRevivalToggle={setPlanRevivalToggle}
            planRevivalStart={planRevivalStart}
            planRevivalPrice={planRevivalPrice}
          />
        )}
      </div>
      <DeletePopUp
        onClose={() => setIsPopupOpen(false)}
        isOpen={isPopupOpen}
        // submitHandler={deleteNoteHandler}
        title="آیا می‌خواهید پلن کاربر را لغو کنید؟"
        description="در صورت لغو پلن٬ اعتباری به کیف پول کاربر افزوده نخواهد شد و برای پلن جدید٬ می‌بایست فرآیند خرید پلن از ابتدا آغاز شود."
      />
    </>
  );
};

export default UserPlanSettings;
