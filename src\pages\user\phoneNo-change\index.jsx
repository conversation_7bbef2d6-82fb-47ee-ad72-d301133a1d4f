import { useContext, useState } from "react";
import SuccessMessage from "components/ui/SuccessMessage";
import NewPhoneNo from "./components/NewPhoneNo";
import PhoneNoVerify from "./components/PhoneNoVerify";
import AuthContext from "context/auth-context";
import { ToastContainer } from "react-toastify";

const PhoneNoChange = () => {
  const [step, setStep] = useState(1);
  const { profile } = useContext(AuthContext);
  const renderStep = () => {
    switch (step) {
      case 1:
        return <PhoneNoVerify profile={profile} setStep={setStep} />;
      case 2:
        return <NewPhoneNo setStep={setStep} />;
      case 3:
        return (
          <SuccessMessage
            title={"شماره موبایل با موفقیت تغییر کرد"}
            description={
              "از این پس برای ورود به سامانه تحلیل از این شماره استفاده کنید."
            }
            clickHandler={() => setStep(1)}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div>
      {renderStep()}
      <ToastContainer />
    </div>
  );
};

export default PhoneNoChange;
