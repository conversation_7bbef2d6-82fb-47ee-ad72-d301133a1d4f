import { useEffect, useMemo, useState } from "react";
import { Cloud, Layout } from "@phosphor-icons/react";
import { Card } from "components/ui/Card.jsx";
import { CTabs } from "components/ui/CTabs.jsx";
import CardLoading from "components/ui/CardLoading.jsx";
import ReactWordcloud from "react-wordcloud";
import CLUSTER_COLORS from "constants/colors.js";
import TreeMap from "components/Charts/TreeMap.jsx";
import { preprocessWord } from "utils/helper.js";
import { useNavigate } from "react-router-dom";
import useSearchStore from "store/searchStore.js";
import PropTypes from "prop-types";
import PLATFORMS from "constants/platforms.js";

const WordCloudCard_Header = ({ wordCloudActiveTab, onClusterTabChange }) => {
  return (
    <div className={"flex flex-row gap-2 w-full justify-between p-2"}>
      <div className={"flex"}>
        <CTabs
          tabArray={[
            {
              id: "wordCloud",
              title: "ابر کلمات",
              icon: Cloud,
            },
            {
              id: "cluster",
              title: "دسته‌بندی",
              icon: Layout,
            },
          ]}
          activeTab={wordCloudActiveTab}
          onChange={onClusterTabChange}
        />
      </div>
      <div className={"flex justify-end align-middle items-center"}>
        <h3
          className={
            "font-subtitle-medium text-light-neutral-text-high [direction:rtl]"
          }
        >
          موضوعات مهم روز
        </h3>
      </div>
    </div>
  );
};

WordCloudCard_Header.propTypes = {
  wordCloudActiveTab: PropTypes.string,
  onClusterTabChange: PropTypes.func,
};

export const WordCloudCard = ({
  loading,
  clusterData,
  clusterListActiveTab,
  setClusterListActiveTab,
}) => {
  const navigate = useNavigate();
  const { setQuery, setCluster, saveState, filters } = useSearchStore();

  const [wordCloudActiveColors, setWordCloudActiveColors] =
    useState(CLUSTER_COLORS);
  const [wordCloudActiveTab, setWordCloudActiveTab] = useState("wordCloud");
  const [wordCloud, setWordCloud] = useState([]);
  const [word, setWord] = useState("");

  const treeData = useMemo(() => {
    const help = [];

    clusterData?.clusters?.slice(0, 9).forEach((item, index) => {
      help.push({
        id: `id_${index}`,
        name: item.param,
        color: CLUSTER_COLORS[index],
        value: item.total_counts,
        x: item.total_counts,
      });
      item.keywords.forEach((x, i) => {
        help.push({
          id: `id_${index}_${i}`,
          parent: `id_${index}`,
          name: x,
          value: item.counts[i],
          x: item.counts[i],
          color: CLUSTER_COLORS[index],
        });
      });
    });
    return help;
  }, [clusterData]);

  const reloadWordCloud = (data, clusterName, clusterIndex = null) => {
    if (clusterName === "all") {
      setWordCloudActiveColors(CLUSTER_COLORS);
      return setWordCloud(
        data?.meta?.keys?.map(({ key, count }) => {
          return { text: preprocessWord(key), value: count };
        }) || [],
      );
    }

    setWordCloudActiveColors([CLUSTER_COLORS[clusterIndex]]);
    return setWordCloud(
      data.clusters[clusterIndex]?.keywords.map((item, i) => {
        return {
          text:
            filters.platform === PLATFORMS.NEWS
              ? preprocessWord(item)
              : preprocessWord(item, true),
          value: data.clusters[clusterIndex]?.counts[i],
        };
      }) || [],
    );
  };

  useEffect(() => {
    reloadWordCloud(clusterData, "all");
    onClusterActiveTabChange("all", -1);
    setCluster({ baseUrl: clusterData.meta?.base_url || "" });
  }, [clusterData]);

  const onClusterActiveTabChange = (name, index) => {
    setClusterListActiveTab({ name, index });
    reloadWordCloud(clusterData, name, index);
  };

  const handleTreeMapClick = (data) => {
    if (data.type === "parent") {
      onClusterActiveTabChange(data.name, Number(data.id));
    }
    if (data.type === "child") {
      setCluster({ name: clusterListActiveTab?.name });
      setQuery(data?.name);
      saveState();
      return navigate("/app/advanced-search");
    }
  };

  const onClusterTabChange = (name) => {
    setWordCloudActiveTab(name);
    setClusterListActiveTab({ name: "all", index: -1 });
    onClusterActiveTabChange("all", -1);
  };

  useEffect(() => {
    if (word.length > 2) {
      setCluster({ name: clusterListActiveTab?.name });
      setQuery(word.replace("#", ""));
      saveState();
      // return navigate("/app/hot-word");
      return navigate("/app/advanced-search");
    }
  }, [word]);

  return (
    <Card className={"flex-col mb-8"}>
      <WordCloudCard_Header
        wordCloudActiveTab={wordCloudActiveTab}
        onClusterTabChange={onClusterTabChange}
      />

      <div
        className={
          "relative flex w-full text-center justify-center items-center h-[300px] overflow-hidden"
        }
      >
        {wordCloudActiveTab === "wordCloud" && (
          <div className={"flex flex-row w-full h-full"}>
            {loading ? (
              <CardLoading />
            ) : (
              <>
                <div className={"flex flex-1 responsive-svg"}>
                  <ReactWordcloud
                    // size={[isSidebarOpened ? 1550 : 1700, 300]}
                    options={{
                      rotations: 1,
                      rotationAngles: [0],
                      enableTooltip: true,
                      deterministic: false,
                      fontFamily: "iranyekan",
                      fontSizes: [14, 54],
                      padding: 10,
                      colors: wordCloudActiveColors,
                      tooltipOptions: { theme: "light", arrow: true },
                    }}
                    words={wordCloud}
                    callbacks={{
                      getWordTooltip: (word) => {
                        if (word.text.endsWith("#")) {
                          return `${word.text.slice(
                            0,
                            word.text.length - 1,
                          )} (${word.value})`;
                        }
                        return `${word.text} (${word.value})`;
                      },
                      onWordClick: (x) => {
                        setWord(x.text);
                      },
                    }}
                  />
                </div>
                <div
                  className={
                    "flex w-[250px] pl-2 border-l [direction:rtl] overflow-y-scroll scrollbar-thin"
                  }
                >
                  <ul className={"w-full text-right"}>
                    <li
                      className={`flex border-r-4 rounded-l-[8px] border-[#cccccc] h-[50px] justify-start items-center cursor-pointer my-1 pr-2
                     ${
                       clusterListActiveTab.name === "all"
                         ? "bg-light-primary-background-highlight font-body-bold-large"
                         : "hover:bg-light-neutral-surface-highlight"
                     }`}
                      onClick={() => onClusterActiveTabChange("all")}
                    >
                      همه دسته‌‌ها
                    </li>
                    {clusterData?.clusters?.slice(0, 9).map((item, index) => (
                      <li
                        key={index}
                        className={`flex border-r-4 rounded-l-[8px] h-[50px] justify-start items-center cursor-pointer my-1 pr-2
                     ${
                       clusterListActiveTab.name === item.param
                         ? "bg-light-primary-background-highlight font-body-bold-large"
                         : "hover:bg-light-neutral-surface-highlight"
                     }`}
                        style={{ borderColor: CLUSTER_COLORS[index] }}
                        onClick={() =>
                          onClusterActiveTabChange(item.param, index)
                        }
                      >
                        {item.param}
                      </li>
                    ))}
                  </ul>
                </div>
              </>
            )}
          </div>
        )}
        {wordCloudActiveTab === "cluster" && (
          <div
            className={
              "flex justify-center align-middle items-center w-full h-full"
            }
          >
            <TreeMap treeData={treeData} handleClick={handleTreeMapClick} />
          </div>
        )}
      </div>
    </Card>
  );
};

WordCloudCard.propTypes = {
  loading: PropTypes.bool.isRequired,
  clusterData: PropTypes.object.isRequired,
  clusterListActiveTab: PropTypes.object.isRequired,
  setClusterListActiveTab: PropTypes.func.isRequired,
};
