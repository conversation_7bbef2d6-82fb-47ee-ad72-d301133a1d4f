import { useContext } from "react";
import { Navigate, useSearchParams } from "react-router-dom";
import AuthContext from "./context/auth-context";
import PropTypes from "prop-types";

const RouteGuard = ({ Component, role = "Regular" }) => {
  const { profile } = useContext(AuthContext);
  const [searchParams] = useSearchParams();
  let redirectionPath = "";
  if (!profile) {
    redirectionPath = `/login${
      searchParams.get("redirect_after_login")
        ? `?redirect_url=${window.location.href}`
        : ""
    }`;
  } else if (role !== "Regular" && profile.role === "Regular") {
    redirectionPath = `/app`;
  }

  return redirectionPath ? (
    <Navigate replace to={redirectionPath} />
  ) : (
    <Component />
  );
};

RouteGuard.propTypes = {
  Component: PropTypes.elementType,
  role: PropTypes.string,
};

export default RouteGuard;
