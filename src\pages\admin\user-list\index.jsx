import "./style.css";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";
import { CButton } from "components/ui/CButton.jsx";
import { Plus } from "@phosphor-icons/react";
import { Card } from "components/ui/Card.jsx";
import List from "./components/List.jsx";
import { useNavigate } from "react-router-dom";
import SearchBar from "pages/admin/user-list/components/SearchBar/index.jsx";
import { useState } from "react";
import formatDate from "pages/admin/user-list/utils.js";
import ManagementTable from "./components/management-table";

function AdminUserList() {
  const navigate = useNavigate();
  const breadcrumbList = [{ title: "کاربران" }];
  useBreadcrumb(breadcrumbList);

  const [loading, setLoading] = useState(false);

  const RULES = {
    REGISTER_AT: "register_at",
    LOGIN_AT: "login_at",
    ROLE: "role",
    USERNAME: "username",
    PHONE: "phone",
    EMAIL: "email",
  };
  const DIRECTION = {
    ASC: "asc",
    DESC: "desc",
  };

  // const [filter, setFilter] = useState({
  //   q: "",
  //   register_at: {
  //     date_from: formatDate(
  //       new Date(
  //         new Date(new Date().getTime() - sixMonthInMillis).setHours(0, 0, 0, 0)
  //       )
  //     ),
  //     date_to: formatDate(new Date()),
  //   },
  //   login_at: {
  //     date_from: formatDate(
  //       new Date(
  //         new Date(new Date().getTime() - sixMonthInMillis).setHours(0, 0, 0, 0)
  //       )
  //     ),
  //     date_to: formatDate(new Date()),
  //   },
  //   sort_rules: [{ field: RULES.REGISTER_AT, direction: DIRECTION.DESC }],
  //   page: 1,
  //   page_size: 10,
  // });
  const oneYearInMillis = 12 * 30 * 24 * 60 * 60 * 1000;
  const [filter, setFilter] = useState({
    q: "",
    register_at: {
      date_from: formatDate(new Date(new Date().getTime() - oneYearInMillis)),
      date_to: formatDate(new Date(new Date().getTime() + 24 * 60 * 60 * 1000)),
    },
    sort_rules: [
      {
        field: "login_at",
        direction: "desc",
      },
      {
        field: "register_at",
        direction: "desc",
      },
    ],
  });
  return (
    <div className="px-6 flex flex-col gap-4 h-full">
      {/* <div className="flex justify-end">
        <div className="flex items-center gap-7">
          <div className="[direction:ltr] w-[200px] ">
            <CButton
              rightIcon={<Plus color="white" size={18} />}
              size="lg"
              className="flex gap-2"
              onClick={() => {
                // resetReport();
                navigate("/admin/user/create");
              }}
            >
              کاربر جدید
            </CButton>
          </div>
        </div>
      </div> */}

      <SearchBar filter={filter} setFilter={setFilter} loading={loading} />
      {/* <Card>
        <div className="flex flex-col gap-4 w-full">
          <span className="font-subtitle-medium">لیست کاربران</span>
          <div className="grid grid-cols-7 justify-between *:font-overline-medium *:text-light-neutral-text-medium">
            <span>نام کاربری</span>
            <span>ایمیل</span>
            <span>شماره موبایل</span>
            <span>تاریخ ایجاد</span>
            <span>آخرین بازدید</span>
            <span>نقش کاربر</span>
            <span className={"text-center"}>اقدامات</span>
          </div>
          <div>
            <List
              filter={filter}
              setFilter={setFilter}
              loading={loading}
              setLoading={setLoading}
            />
          </div>
        </div>
      </Card> */}

      <ManagementTable filter={filter} setFilter={setFilter} />
    </div>
  );
}

export default AdminUserList;
