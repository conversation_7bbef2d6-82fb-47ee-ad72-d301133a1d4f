import useFetch from "../index";

class Filter {
  new(data) {
    return useFetch.post("/api/v1/queries/query-search/", data);
  }
  get() {
    return useFetch.get("/api/v1/queries/query-search/");
  }
  getById(id) {
    return useFetch.get(`/api/v1/queries/query-search/${id}/`);
  }
  remove(id) {
    return useFetch.delete(`/api/v1/queries/query-search/${id}/`);
  }
  update(id, data) {
    return useFetch.put(`/api/v1/queries/query-search/${id}/`, data);
  }
}
export default new Filter();
