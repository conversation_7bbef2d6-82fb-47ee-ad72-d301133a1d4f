import clsx from "clsx";

const StatsCard = ({ text, pic, value, checked = false, handleChange }) => {
  return (
    <div
      className={clsx(
        "border h-32 flex items-center justify-center relative rounded-lg cursor-pointer",
        checked
          ? "border-light-primary-border-rest"
          : "border-light-neutral-border-low-rest",
      )}
      onClick={(e) => handleChange(value)}
    >
      <input
        type="checkbox"
        className="size-[13.5px] absolute top-[6px] right-[6px]"
        onChange={(e) => handleChange(value)}
        checked={checked}
      />
      <div className="flex items-center flex-col gap-4">
        <div>
          <img src={pic} />
        </div>
        <div className="font-body-medium">{text}</div>
      </div>
    </div>
  );
};

export default StatsCard;
