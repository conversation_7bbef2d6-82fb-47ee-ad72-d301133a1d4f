import { Hash, Info } from "@phosphor-icons/react";
import { CButton } from "components/ui/CButton";
import { DateInput } from "components/ui/DateInput";
import Divider from "components/ui/Divider";
import { TagInput } from "components/ui/TagInput";
import SettingBadge from "pages/admin/user-create/components/SettingBadge";
import { useState } from "react";
import NewFilter from "./components/NewFilter";
import SearchInput from "pages/user/alert-create/components/SearchInput";
const StepTwo = ({ setStep, opinionMiningData, setOpinionMiningData }) => {
  const [selectFilter, setSelectFilter] = useState("");
  const [filterList, setFilterList] = useState([]);
  const handleChange = (selectedDate) => {
    setFilterList(selectedDate);
    // saveState();
  };
  const handleDropdownChange = (selectedItems) => {
    const selectedFilterItem = filterList?.find(
      (filter) => filter.id === selectedItems[0]
    );
    setSelectedFilters(selectedFilterItem);
  };

  return (
    <>
      <div className="rounded-md bg-light-neutral-surface-card p-4">
        <p className="font-subtitle-large mb-6">تنظیم فیلترها</p>

        <div className="grid grid-cols-2 gap-4">
          <div
            onClick={() => {
              setSelectFilter("new-filter");
              setOpinionMiningData({ ...opinionMiningData, type: "new" });
            }}
            className={"cursor-pointer"}
          >
            <SettingBadge
              text="تنظیم فیلتر جدید"
              src="/Setting.png"
              selected={selectFilter === "new-filter"}
            />
          </div>
          <div
            onClick={() => {
              setSelectFilter("store-filter");
              setOpinionMiningData({
                ...opinionMiningData,
                type: "pregenerated",
              });
            }}
            className={"cursor-pointer"}
          >
            <SettingBadge
              text="انتخاب از میان فیلترهای از پیش‌ساخته شده"
              src="/Funnel.png"
              selected={selectFilter === "store-filter"}
            />
          </div>
        </div>

        {selectFilter === "new-filter" ? (
          <>
            <div className="mt-6">
              <NewFilter
                disabledNews
                disabledInstagram
                disabledTelegram
                alertData={""}
                setAlertData={() => {}}
                setStatus={1}
              />
            </div>

            <div className="mt-8">
              <p className="font-subtitle-large mb-5">بازه زمانی جستجو</p>
              <div className={"w-full flex font-body-small"}>
                <DateInput
                  id={"date"}
                  name={"date"}
                  inset={true}
                  size={"lg"}
                  validation={"none"}
                  direction={"rtl"}
                  className={"flex-1"}
                  field={{}}
                  form={{ errors: [], touched: [] }}
                  onChange={(selectedDate) => {
                    setOpinionMiningData({
                      ...opinionMiningData,
                      date_range: {
                        from_date: selectedDate.from,
                        to_date: selectedDate.to,
                      },
                    });
                  }}
                  dateButtonsClassName={'!flex-row-reverse'}
                  value={new Date()}
                />
              </div>
            </div>
            <Divider className={"my-6"} />

            <div className="">
              <div className="flex items-center gap-1 mb-5">
                <Hash size={19} className="text-light-neutral-text-low" />
                <p className="font-subtitle-large">هشتگ‌های کلیدی</p>
                <Info size={19} className="text-light-neutral-text-low" />
              </div>
              <div className={"w-full flex"}>
                <TagInput
                  id={"hashtag"}
                  name={"hashtag"}
                  inset={true}
                  size={"lg"}
                  direction={"rtl"}
                  placeholder={"عبارت هشتگ مورد نظر را بنویسید"}
                  caption={
                    "بعد از نوشتن هر کلمه از Enter استفاده کنید. نیازی به استفاده از # نیست"
                  }
                  // initialTags={filters.hashtags}
                  className={"flex-1"}
                  onChange={(data) => {
                    setOpinionMiningData({
                      ...opinionMiningData,
                      hashtags: data,
                    });
                  }}
                />
              </div>
            </div>
            {/* <div className="flex items-center gap-4 mt-4">
              <div className="w-full" onClick={() => setStep(1)}>
                <CButton role="neutral">مرحله قبل</CButton>
              </div>
              <div className="w-full" onClick={() => setStep(3)}>
                <CButton role="neutral" mode="outline">
                  مرحله بعد
                </CButton>
              </div>
            </div> */}
          </>
        ) : selectFilter === "store-filter" ? (
          <div className="my-5">
            <>
              <div className="mt-12 flex flex-col gap-4">
                <div className="flex flex-col gap-4">
                  <div className="flex flex-col gap-2">
                    <span className="font-overline-large">فیلترها</span>
                    <SearchInput setFilterData={setFilterList} />
                  </div>
                </div>
              </div>
            </>
            <Divider className={"my-6"} />

            <div className="mt-8">
              <p className="font-subtitle-large mb-5">بازه زمانی جستجو</p>
              <div className={"w-full flex font-body-small"}>
                <DateInput
                  id={"date"}
                  name={"date"}
                  inset={true}
                  size={"lg"}
                  validation={"none"}
                  direction={"rtl"}
                  className={"flex-1"}
                  field={{}}
                  form={{ errors: [], touched: [] }}
                  onChange={(selectedDate) =>
                    setOpinionMiningData({
                      ...opinionMiningData,
                      date_range: {
                        from_date: selectedDate.from,
                        to_date: selectedDate.to,
                      },
                    })
                  }
                  value={new Date()}
                />
              </div>
            </div>
          </div>
        ) : (
          ""
        )}
        <div className="flex items-center gap-4 mt-6">
          <div className="w-full" onClick={() => setStep(1)}>
            <CButton role="neutral">مرحله قبل</CButton>
          </div>
          <div className="w-full">
            <CButton readOnly={!selectFilter} onClick={() => setStep(3)}>
              مرحله بعد
            </CButton>
          </div>
        </div>
      </div>
    </>
  );
};

export default StepTwo;
