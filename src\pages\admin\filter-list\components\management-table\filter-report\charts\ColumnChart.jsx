import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { toPersianNumber } from "utils/helper";

const ColumnChart = ({ xAxisCategory, seriesColor }) => {
  const options = {
    chart: {
      type: "column",
    },
    title: {
      text: null,
    },
    subtitle: {
      text: null,
    },
    credits: {
      enabled: false,
    },
    xAxis: {
      categories: xAxisCategory,
      crosshair: true,
      labels: {
        formatter: function () {
          return toPersianNumber(this.value);
        },
      },
    },
    yAxis: {
      min: 0,
      title: {
        text: null,
      },
      labels: {
        formatter: function () {
          return toPersianNumber(this.value);
        },
      },
      gridLineDashStyle: "dash",
    },
    tooltip: {
      enabled: true,
      shared: true,
      useHTML: true,
      style: {
        fontFamily: "IranYekan",
      },
      formatter: function () {
        let tooltipHTML = `<div style="font-family:'iranyekan',serif;font-size: 11px; direction: rtl;">`;
        this.points.forEach((point) => {
          tooltipHTML += `
              <div style="display: flex; align-items: center; margin-bottom: 5px;">
                <div style="width: 10px; height: 10px; border-radius: 50%; background-color: ${
                  point.color
                }; margin-left: 3px;"></div>
                <strong style="color: #333; margin-right: 5px;">${toPersianNumber(
                  point.y
                )}%</strong>
              </div>`;
        });
        tooltipHTML += "</div>";
        return tooltipHTML;
      },
    },
    legend: {
      enabled: false,
    },
    series: [
      {
        name: "Data",
        data: [5, 3, 4, 7, 2, 3, 5, 6, 6],
        color: seriesColor,
      },
    ],
    plotOptions: {
      column: {
        borderRadius: 8, // Top corners radius
      },
    },
  };

  return (
    <div>
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};

export default ColumnChart;
