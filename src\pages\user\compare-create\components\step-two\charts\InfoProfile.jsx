import React, { useEffect, useState } from "react";
import InfoCard from "./InfoCard.jsx";

import {
  ChatTeardropDots,
  Eye,
  GenderIntersex,
  Heart,
  SpinnerGap,
  Upload,
  User,
  UsersThree,
} from "@phosphor-icons/react";

import { useCompareStore } from "store/compareStore.js";
import advanceSearch from "service/api/advanceSearch.js";

const InfoProfile = ({ data }) => {
  const { platform, date } = useCompareStore((state) => state.compare);
  console.log(data);
  const [info, setInfo] = useState(data);
  const [loading, setLoading] = useState(false);
  const post = {
    text: "پست",
    value: info.post_count,
    valueIcon: <Upload size={18} color="black" />,
  };
  const like = {
    text: "لایک",
    value: info.like_count,
    valueIcon: <Heart size={18} color="#E0526A" weight="fill" />,
    bgColor: "#E0526A33",
  };
  const genderSelected = {
    male: "مرد",
    female: "زن",
    org: "سازمان",
  };
  const gender = {
    text: "جنسیت",
    value: genderSelected[info.gender?.label],
    valueIcon: <GenderIntersex size={18} color="#00000080" />,
    valueIsNumber: false,
  };
  const follower = {
    text: "فالوور",
    value: info.follower_count,
    valueIcon: <UsersThree size={18} color="#00000080" />,
  };
  const following = {
    text: "فالووینگ",
    value: info.following_count,
    valueIcon: <User size={18} color="#00000080" />,
  };
  const member = {
    text: "عضو (ممبر)",
    value: info.member_count,
    valueIcon: <UsersThree size={18} color="#DB9100" />,
    bgColor: "#DB910033",
  };
  const view = {
    text: "مجموع بازدید",
    value: info.view_count,
    valueIcon: <Eye size={18} color="#00000080" />,
  };
  const selectPlatform = {
    twitter: [
      { ...like },
      {
        text: "توئیت",
        value: info.tweet_count,
        valueIcon: <Upload size={18} color="#54ADEE" />,
        bgColor: "#54ADEE33",
      },
      { ...following },
      { ...follower },
      { ...gender },
      {
        text: "لایک به توئیت",
        value:
          info.like_count > 0 && info.post_count > 0
            ? (info.like_count / info.post_count).toFixed()
            : "0",
      },
    ],
    instagram: [
      { ...like },
      {
        text: "کامنت",
        value: info.comment_count,
        valueIcon: <ChatTeardropDots size={18} color="#1CB0A5" weight="fill" />,
        bgColor: "#1CB0A533",
      },
      { ...post },
      { ...gender },
      { ...following },
      { ...follower },
    ],
    telegram: [
      {
        ...member,
      },
      {
        text: "بازدید به پست",
        value:
          info.view_count > 0 && info.post_count > 0
            ? (info.view_count / info.post_count).toFixed()
            : "0",
        valueIcon: <Eye size={18} color="#256EF6" weight="fill" />,
        bgColor: "#256EF633",
      },
      { ...post },
      { ...view },
    ],
  };

  useEffect(() => {
    const abortController = new AbortController();
    const getInfo = async (id, abortController) => {
      setLoading(true);
      try {
        const infoQuery = `search/api/v1/search?api_path=/${platform}/info&langs[0]=fa&from=${parseInt(
          new Date(date.from).getTime() / 1000
        )}&to=${parseInt(
          new Date(date.to).getTime() / 1000
        )}&resource_ids[0]=${id}`;
        const response = await advanceSearch.search(
          { q: infoQuery },
          abortController
        );
        if (!abortController.signal.aborted) {
          const newData = JSON.parse(response?.data?.data)?.result?.data;
          if (newData) {
            setInfo((l) => ({
              ...l,
              ...newData,
            }));
          }
        }
      } catch (error) {
        if (!abortController.signal.aborted) {
          console.log(error);
        }
      } finally {
        if (!abortController.signal.aborted) {
          setLoading(false); // Ensure loading is set to false after request
        }
      }
    };

    getInfo(info.id, abortController);

    return () => {
      abortController.abort();
    };
  }, [date]);

  if (loading) {
    return (
      <div className="w-full h-40 flex justify-center items-center">
        <SpinnerGap size={40} className="animate-spin" />
      </div>
    );
  }

  return (
    <div className="w-full grid grid-cols-2 gap-4 px-6">
      {selectPlatform[platform]?.map(
        ({ value, valueIcon, text, bgColor, valueIsNumber, textIcon }) => (
          <InfoCard
            value={value}
            valueIcon={valueIcon}
            text={text}
            textIcon={textIcon}
            textIconToolTipText="میانگین تعداد لایک به ازای هر توئیت"
            bgColor={bgColor}
            valueIsNumber={valueIsNumber}
          />
        )
      )}
    </div>
  );
};

export default React.memo(InfoProfile);
