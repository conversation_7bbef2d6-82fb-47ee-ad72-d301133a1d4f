import TextWithBullet from "components/TextWithBullet";
const StepOneGuide = () => {
  return (
    <div className="font-body-medium">
      <h3 className="mb-4">راهنما</h3>
      <div className="flex flex-col gap-4">
        <TextWithBullet>
          <span>جهت تشخیص هشدار برای آن یک نام تعیین کنید.</span>
        </TextWithBullet>
        <TextWithBullet>
          <span>
            توصیه می‌شود برای این هشدار توضیحات مختصری ثبت کنید تا هدف از ایجاد
            این هشدار مشخص شود.
          </span>
        </TextWithBullet>
        <TextWithBullet>
          <span>تکمیل فیلد توضیحات اجباری</span>
          <span className="underline underline-offset-4"> نیست. </span>
        </TextWithBullet>
        <TextWithBullet>
          <span>
            در صورتی که بخواهید می‌توانید این موارد را پس از ثبت ویرایش کنید.
          </span>
        </TextWithBullet>
      </div>
    </div>
  );
};

export default StepOneGuide;
