import { ArrowsCounterClockwise } from "@phosphor-icons/react";
import { CButton } from "components/ui/CButton";
import Loading from "components/ui/Loading";
import { useState } from "react";
import Wrapper from "./Wrapper";
import Empty from "./Empty";
import Table from "./Table";
import { toPersianNumber } from "utils/helper";
import Paginate from "components/ui/Paginate";
import { ToastContainer } from "react-toastify";

const UserActivitiesTable = ({ filter, setFilter }) => {
  const [loading, setLoading] = useState(false);
  const [table, setTable] = useState([{}, {}, {}, {}, {}, {}, {}]);
  const [tableItemsLimit, setTableItemsLimit] = useState(20);
  const [page, setPage] = useState(1);
  const [tabs, setTabs] = useState([
    "هشدارها",
    "فیلتر",
    "بولتن",
    "مقایسه",
    "گزارش ۳۶۰",
  ]);
  const [activeTab, setActiveTab] = useState("هشدارها");
  // const sixMonthInMillis = 6 * 30 * 24 * 60 * 60 * 1000;
  // const [filter, setFilter] = useState({
  //   q: "",
  //   register_at: {
  //     date_from: formatDate(new Date(new Date().getTime() - sixMonthInMillis)),
  //     date_to: formatDate(new Date(new Date().getTime() + 24 * 60 * 60 * 1000)),
  //   },
  //   sort_rules: [
  //     {
  //       field: "login_at",
  //       direction: "desc",
  //     },
  //     {
  //       field: "register_at",
  //       direction: "desc",
  //     },
  //   ],
  // });
  // get users
  // const getUsers = async () => {
  //   setLoading(true);
  //   try {
  //     let newFiler = {};
  //     if (filter?.q.trim() !== "") newFiler.q = filter.q;
  //     newFiler.register_at = filter?.register_at;
  //     newFiler.sort_rules = filter?.sort_rules;

  //     let newQuery = {};
  //     newQuery.page = page;
  //     newQuery.page_size = tableItemsLimit;
  //     // newQuery.status =
  //     //   activeTab == "همه"
  //     //     ? "all"
  //     //     : activeTab == "کاربران فعال"
  //     //     ? "active_users"
  //     //     : activeTab == "کاربران غیرفعال"
  //     //     ? "inactive_users"
  //     //     : "managers";
  //     newQuery.role = filter?.role;

  //     const {
  //       data: { data },
  //     } = await userService.getUserList(newFiler, newQuery);
  //     setTable(data?.users);
  //   } catch (error) {
  //     console.log(error);
  //   }
  //   setLoading(false);
  // };
  // useEffect(() => {
  //   getUsers();
  // }, [page, tableItemsLimit, activeTab, filter]);

  return (
    <div className="bg-light-neutral-surface-card rounded-md p-4">
      {/* tabs */}
      <div className="flex items-center justify-between">
        <div className="flex items-center font-body-large gap-4">
          {tabs?.map((title) => (
            <p
              key={title}
              className={`cursor-pointer ${
                title === activeTab
                  ? "border-b-2 border-light-primary-text-rest text-black"
                  : "text-light-neutral-text-medium"
              }`}
              onClick={() => setActiveTab(title)}
            >
              {title}
            </p>
          ))}
        </div>
        <div className="flex items-center gap-2">
          <div className="w-fit" onClick={() => getUsers()}>
            <CButton mode="outline" size="sm">
              <ArrowsCounterClockwise size={18} />
            </CButton>
          </div>
        </div>
      </div>
      <hr className="mt-1" />
      {/* table */}
      <>
        {loading ? (
          <Loading />
        ) : (
          <>
            <div className="flex flex-col h-full w-full">
              <Wrapper filter={filter} setFilter={setFilter}>
                {table.length > 0 ? (
                  table?.map((item) => (
                    <>
                      <Table key={item.id} data={item} />
                    </>
                  ))
                ) : (
                  <Empty />
                )}
              </Wrapper>
              <div className="flex items-center justify-between mt-7">
                <div className="flex items-center gap-3">
                  <p className="font-body-medium">نمایش تعداد رکورد در صفحه:</p>
                  <div className="flex items-center gap-4">
                    <div
                      className={`rounded-md cursor-pointer font-body-small w-8 h-8 flex items-center justify-center ${
                        tableItemsLimit == 20
                          ? "bg-light-primary-background-highlight"
                          : ""
                      }`}
                      onClick={() => setTableItemsLimit(20)}
                    >
                      {toPersianNumber("20")}
                    </div>
                    <div
                      className={`rounded-md cursor-pointer font-body-small w-8 h-8 flex items-center justify-center ${
                        tableItemsLimit == 50
                          ? "bg-light-primary-background-highlight"
                          : ""
                      }`}
                      onClick={() => setTableItemsLimit(50)}
                    >
                      {toPersianNumber("50")}
                    </div>
                    <div
                      className={`rounded-md cursor-pointer font-body-small w-8 h-8 flex items-center justify-center ${
                        tableItemsLimit == 100
                          ? "bg-light-primary-background-highlight"
                          : ""
                      }`}
                      onClick={() => setTableItemsLimit(100)}
                    >
                      {toPersianNumber("100")}
                    </div>
                  </div>
                </div>
                <div className="">
                  <Paginate
                    page={page}
                    setPage={setPage}
                    dataCount={100}
                    per_page={10}
                  />
                </div>
              </div>
            </div>
          </>
        )}
      </>
      <ToastContainer />
    </div>
  );
};

export default UserActivitiesTable;
