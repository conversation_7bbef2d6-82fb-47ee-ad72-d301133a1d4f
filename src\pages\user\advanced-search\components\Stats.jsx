import Badges from "./stats/Badges";
import Content from "./stats/Content";
import Category from "./stats/Category";
import Sentiment from "./stats/Sentiment";
import Gender from "./stats/Gender";
import Age from "./stats/Age";
import BestSource from "./stats/BestSource";
import Words from "./stats/Words";
import useSearchStore from "store/searchStore.js";
import PLATFORMS from "constants/platforms.js";
import Offensive from "pages/user/advanced-search/components/stats/Offensive.jsx";
import Advertise from "pages/user/advanced-search/components/stats/Advertise.jsx";

const Stats = () => {
  const { filters, query } = useSearchStore();

  return (
    <>
      {filters.platform === PLATFORMS.NEWS ? (
        <div className="grid grid-cols-12 gap-4 mt-4">
          <div className="col-span-8 bg-white rounded-lg p-6 shadow-[0px_2px_20px_0px_#0000000D]">
            <Content />
          </div>
          <div className="col-span-4">
            <Badges />
          </div>
          <div className="col-span-12 bg-white rounded-lg p-6 shadow-[0px_2px_20px_0px_#0000000D]">
            <Category
              platform={filters.platform}
              req_data={filters}
              query={query}
            />
          </div>
          <div className="col-span-12 bg-white rounded-lg p-6 shadow-[0px_2px_20px_0px_#0000000D]">
            <BestSource />
          </div>
          <div className="col-span-12">
            <Words />
          </div>
        </div>
      ) : filters.platform === PLATFORMS.TWITTER ? (
        <div className="grid grid-cols-12 gap-4 mt-4">
          <div className="col-span-12">
            <Badges />
          </div>
          <div className="col-span-8 bg-white rounded-lg p-6 shadow-[0px_2px_20px_0px_#0000000D]">
            <Content />
          </div>
          <div
            className={
              "col-span-4 bg-white rounded-lg p-6 shadow-[0px_2px_20px_0px_#0000000D]"
            }
          >
            <Sentiment />
          </div>
          <div className="col-span-12 bg-white rounded-lg p-6 shadow-[0px_2px_20px_0px_#0000000D]">
            <Category
              platform={filters.platform}
              req_data={filters}
              query={query}
            />
          </div>
          <div className="col-span-8 bg-white rounded-lg p-6 shadow-[0px_2px_20px_0px_#0000000D]">
            <BestSource />
          </div>
          <div className="col-span-4 bg-white rounded-lg p-6 shadow-[0px_2px_20px_0px_#0000000D]">
            <Offensive />
          </div>
          <div className="col-span-12">
            <Words />
          </div>
        </div>
      ) : filters.platform === PLATFORMS.TELEGRAM ? (
        <div className="grid grid-cols-12 gap-4 mt-4">
          <div className="col-span-12">
            <Badges />
          </div>
          <div className="col-span-8 bg-white rounded-lg p-6 shadow-[0px_2px_20px_0px_#0000000D]">
            <Content />
          </div>
          <div
            className={
              "col-span-4 bg-white rounded-lg p-6 shadow-[0px_2px_20px_0px_#0000000D]"
            }
          >
            <Sentiment />
          </div>
          <div className="col-span-4 bg-white rounded-lg p-6 shadow-[0px_2px_20px_0px_#0000000D]">
            <Advertise />
          </div>
          <div className="col-span-8 bg-white rounded-lg p-6 shadow-[0px_2px_20px_0px_#0000000D]">
            <Category
              platform={filters.platform}
              req_data={filters}
              query={query}
            />
          </div>
          <div className="col-span-8 bg-white rounded-lg p-6 shadow-[0px_2px_20px_0px_#0000000D]">
            <BestSource />
          </div>
          <div className="col-span-4 bg-white rounded-lg p-6 shadow-[0px_2px_20px_0px_#0000000D]">
            <Offensive />
          </div>
          <div className="col-span-12">
            <Words />
          </div>
        </div>
      ) : filters.platform === PLATFORMS.INSTAGRAM ? (
        <div className="grid grid-cols-12 gap-4 mt-4">
          <div className="col-span-12">
            <Badges />
          </div>
          <div className="col-span-8 bg-white rounded-lg p-6 shadow-[0px_2px_20px_0px_#0000000D]">
            <Content />
          </div>
          <div
            className={
              "col-span-4 bg-white rounded-lg p-6 shadow-[0px_2px_20px_0px_#0000000D]"
            }
          >
            <Sentiment />
          </div>
          <div className="col-span-4 bg-white rounded-lg p-6 shadow-[0px_2px_20px_0px_#0000000D]">
            <Advertise />
          </div>
          <div className="col-span-8 bg-white rounded-lg p-6 shadow-[0px_2px_20px_0px_#0000000D]">
            <Category
              platform={filters.platform}
              req_data={filters}
              query={query}
            />
          </div>
          <div className="col-span-8 bg-white rounded-lg p-6 shadow-[0px_2px_20px_0px_#0000000D]">
            <BestSource />
          </div>
          <div className="col-span-4 bg-white rounded-lg p-6 shadow-[0px_2px_20px_0px_#0000000D]">
            <Offensive />
          </div>
          <div className="col-span-12">
            <Words />
          </div>
        </div>
      ) : null}
    </>
  );
};

export default Stats;
