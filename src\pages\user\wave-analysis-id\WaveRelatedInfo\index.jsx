import SummaryCard from "components/SummaryCard";
import DropDown from "components/ui/DropDown";
import Paginate from "components/ui/Paginate";
import { useState } from "react";
import { shortener } from "utils/helper";

const WaveRelatedInfo = ({ data, setPage, page, total }) => {
  return (
    <div className="mt-3 px-4 grid grid-cols-12 gap-3">
      {data?.length > 0 ? (
        data?.map((item) => (
          <div
            className="col-span-4 bg-light-neutral-surface-card rounded-lg"
            key={item.id}
          >
            <SummaryCard data={item} media={"twitter"} showHeaderMenu={true}>
              <p className="font-body-medium text-light-neutral-text-high min-h-4 text-justify">
                {shortener(item?.text, 200)}
              </p>
            </SummaryCard>
          </div>
        ))
      ) : (
        <p className="text-center col-span-12 font-body-bold-large">
          هیچ محتوایی یافت نشد!
        </p>
      )}
      <div className="col-span-12">
        <Paginate
          page={page}
          setPage={setPage}
          dataCount={total}
          per_page={10}
        />
      </div>
    </div>
  );
};

export default WaveRelatedInfo;
