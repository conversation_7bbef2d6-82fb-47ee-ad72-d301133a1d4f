import React, { useState } from "react";
import MediaBadge from "components/ui/MediaBadge";
import { Eye, Faders, TrashSimple } from "@phosphor-icons/react";
import { useNavigate } from "react-router-dom";
import ToolTip from "components/ui/ToolTip";
import { parseTimeToPersian, toPersianNumber } from "utils/helper";
import compare from "service/api/compare";
import DeletePopUp from "components/ui/DeletePopUp.jsx";

const ListCard = ({
  id,
  title,
  column_num,
  comparison_type,
  comparison_platform,
  created_at,
  render,
}) => {
  const [showRemove, setShowRemove] = useState(false);
  const navigate = useNavigate();

  const removeCompare = async () => {
    try {
      await compare.remove(id);
      render();
    } catch (error) {
      console.log(error);
    }
  };

  const viewCompare = async () => {
    try {
      navigate(`/app/compare/edit/${id}`);
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <>
      <div className="grid grid-cols-6 justify-between hover:bg-light-neutral-surface-highlight rounded p-1 items-center">
        <span>{title}</span>
        <span>{comparison_type === "profile" ? "پروفایلی" : "موضوعی"}</span>
        <span>{toPersianNumber(column_num)}</span>
        <MediaBadge media={comparison_platform} />
        <span>
          {created_at ? parseTimeToPersian(created_at).split("-")[1] : ""}
        </span>

        <div className="flex gap-4">
          <div
            className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
            onClick={() => navigate(`/app/compare/access/list/${id}`)}
          >
            <ToolTip comp="دسترسی ها">
              <Faders size={16} />
            </ToolTip>
          </div>
          <div
            className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
            onClick={viewCompare}
          >
            <ToolTip comp="نمایش جزئیات">
              <Eye size={16} />
            </ToolTip>
          </div>
          <div
            className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
            onClick={() => setShowRemove(true)}
          >
            <ToolTip comp="حذف">
              <TrashSimple size={16} />
            </ToolTip>
          </div>
        </div>
      </div>

      <DeletePopUp
        onClose={() => setShowRemove(false)}
        isOpen={showRemove}
        submitHandler={removeCompare}
        title="آیا مطمئن هستید؟"
        description="در صورت حذف این مقایسه، امکان بازیابی آن وجود ندارد"
      />
    </>
  );
};

export default ListCard;
