import {
  Check<PERSON>ir<PERSON>,
  FileArrowDown,
  MagnifyingGlass,
  Plus,
  SpinnerGap,
} from "@phosphor-icons/react";
import { CButton } from "components/ui/CButton";
import { useState, useEffect } from "react";
import GraphContent from "./GraphContent";
import Drawer from "components/Drawer";
import NodeTypeCard from "../GraphSettings/components/NodeTypeCard";
import { CInput } from "components/ui/CInput";
import { useRelationStore } from "store/relationDetectStore";
import { buildRequestData } from "utils/requestData";
import nodeTypeData from "../GraphSettings/constants/nodeType";
import SourceSelect from "./SourceSelect";
import PersonSelect from "./PersonSelect";
import OrgSelect from "./OrgSelect";
import PlaceSelect from "./PlaceSelect";
import { notification } from "utils/helper";
import { ToastContainer } from "react-toastify";
import Filters from "./Filters";
import advanceSearch from "service/api/advanceSearch";
import relationDetect from "service/api/relationDetect";
import { useLocation, useNavigate } from "react-router-dom";

const GraphPreview = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [entityVal, setEntityVal] = useState("");
  const [selectedNode, setSelectedNode] = useState(1);
  const [data, setData] = useState([]);
  const [nodesData, setNodesData] = useState([]);
  const [personNodesData, setPersonNodesData] = useState([]);
  const [organNodesData, setOrganNodesData] = useState([]);
  const [placeNodesData, setPlaceNodesData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [finalGraphData, setFinalGraphData] = useState([]);
  const nodeDetails = useRelationStore((state) => state.relation.nodeDetails);
  const setNodeDetailsBulk = useRelationStore(
    (state) => state?.setNodeDetailsBulk
  );
  const setType = useRelationStore((state) => state?.setType);
  const edgeDetails = useRelationStore((state) => state.relation.edgeDetails);
  const location = useLocation();
  const navigate = useNavigate();
  useEffect(() => {
    if (nodeDetails.length > 0) {
      generateGraphRelation();
    }
  }, [nodeDetails]);

  const edgeTypeRenderer = {
    1: <SourceSelect nodesData={nodesData} data={data} setData={setData} />,
    2: (
      <PersonSelect
        personNodesData={personNodesData}
        data={data}
        setData={setData}
      />
    ),
    3: (
      <OrgSelect
        organNodesData={organNodesData}
        data={data}
        setData={setData}
      />
    ),
    4: (
      <PlaceSelect
        placeNodesData={placeNodesData}
        data={data}
        setData={setData}
      />
    ),
  };

  const clearSearchData = () => {
    setNodesData([]);
    setPersonNodesData([]);
    setOrganNodesData([]);
    setPlaceNodesData([]);
  };

  const search = async () => {
    if (entityVal.length < 2) {
      return;
    }
    setLoading(true);
    clearSearchData();
    try {
      const filterObject = {
        platform: selectedNode === 1 ? "twitter" : "all",
        q: entityVal,
        page: 1,
        rows: 10,
        date: {
          from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 24 hours ago
          to: new Date(Date.now()), // Now
        },
      };
      const req = buildRequestData(
        filterObject,
        selectedNode === 1 ? "search_in_source" : "similar_phrases"
      );
      const responseStinas = await advanceSearch?.search(
        req,
        false,
        selectedNode === 2
          ? { cloud_type: "person" }
          : selectedNode === 3
          ? { cloud_type: "organ" }
          : selectedNode === 4
          ? { cloud_type: "location" }
          : ""
      );
      const parsedResponse = responseStinas?.data?.data?.["twitter"];
      selectedNode === 1
        ? setNodesData(parsedResponse)
        : selectedNode === 2
        ? setPersonNodesData(responseStinas?.data?.data?.phrases)
        : selectedNode === 3
        ? setOrganNodesData(responseStinas?.data?.data?.phrases)
        : setPlaceNodesData(responseStinas?.data?.data?.phrases);
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  const searchNodeHandler = () => search();

  function separateData(data) {
    const person = [];
    const source = [];
    const organization = [];
    const location = [];

    data.forEach((item) => {
      const entry = item.title || item.user_name || "";

      if (item.id.startsWith("person-")) {
        person.push(entry);
      } else if (item.id.startsWith("organization-")) {
        organization.push(entry);
      } else if (item.id.startsWith("location-")) {
        location.push(entry);
      } else {
        source.push(entry);
      }
    });

    return { person, source, organization, location };
  }
  const generateGraphRelation = async () => {
    try {
      const separatedData = separateData(nodeDetails);
      const res = await relationDetect?.generateGraph({
        relation: separatedData,
        connection_mode: edgeDetails?.relationType,
        relation_type: edgeDetails?.relationIndex,
        node_weight: edgeDetails?.nodeWeight,
        start_date:
          edgeDetails?.timeSettings?.from || location?.state?.date?.start_date,
        end_date:
          edgeDetails?.timeSettings?.to || location?.state?.date?.end_date,
      });
      setFinalGraphData(res?.data?.data);

      navigate("/app/relation-detect/results", {
        state: {
          finalGraphData: res?.data?.data?.params,
        },
      });
      // navigate("/app/relation-detect/results", {
      //   state: {
      //     data: res?.data?.data,
      //   },
      // });
    } catch (error) {
      notification.error(
        "حد اقل دو گره را انتخاب کنید",
        <CheckCircle size={20} className="text-light-error-background-rest" />
      );
    }
  };
  const printScreenHandler = () => {};

  return (
    <>
      <Filters
        printScreenHandler={printScreenHandler}
        finalGraphData={finalGraphData}
      />
      <div className="px-4 mt-4">
        <div
          className="bg-light-neutral-surface-card rounded-lg p-3 h-full font-button-medium"
          style={{
            boxShadow: "0px 2px 20px 0px #0000000D",
          }}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <p className="font-subtitle-large">گراف شناسایی روابط</p>
              <p className="bg-light-inform-background-highlight text-light-inform-background-rest px-3 py-1 rounded-md font-body-small">
                ارتباط غیرمستقیم
              </p>
              <p className="bg-light-inform-background-highlight text-light-inform-background-rest px-3 py-1 rounded-md font-body-small">
                یک ماه گذشته
              </p>
            </div>
            <div className="flex items-center gap-6">
              <div className="w-fit">
                <CButton
                  onClick={() => setIsSidebarOpen((prev) => !prev)}
                  className="gap-1"
                  leftIcon={<Plus size={18} />}
                  mode="outline"
                >
                  افزودن گره جدید
                </CButton>
              </div>
              <div className="w-fit">
                <CButton
                  className="gap-1"
                  rightIcon={<FileArrowDown size={18} />}
                  mode="outline"
                >
                  خروجی گراف
                </CButton>
              </div>
            </div>
          </div>

          <GraphContent finalGraphData={finalGraphData} />

          <div className="flex items-center gap-7 font-body-medium p-3">
            <div className="flex items-center gap-2">
              <div className="border border-[#1DCEA3] w-6"></div>
              <p>ریپلای</p>
            </div>
            <div className="flex items-center gap-2">
              <div className="border border-[#DB6DE5] w-6"></div>
              <p>کوت</p>
            </div>
            <div className="flex items-center gap-2">
              <div className="border border-[#FCB353] w-6"></div>
              <p>ریتوئیت</p>
            </div>
            <div className="flex items-center gap-2">
              <div className="border border-[#6D72E5] w-6"></div>
              <p>منشن</p>
            </div>
            <div className="flex items-center gap-2">
              <div className="border border-black w-6"></div>
              <p>بیش از یک نوع ارتباط</p>
            </div>
          </div>
        </div>
      </div>

      {isSidebarOpen && (
        <Drawer setShowMore={setIsSidebarOpen}>
          <div className="">
            <p className="font-subtitle-large">افزودن گره جدید</p>
            <div className="font-body-large">
              <p className="mb-2 pt-6">نوع گره را انتخاب کنید</p>
              <div className="flex items-center justify-around gap-2 my-4">
                {nodeTypeData?.map((item) => (
                  <div
                    key={item.id}
                    onClick={() => {
                      setSelectedNode(item.id);
                      setType(item?.type);
                    }}
                  >
                    <NodeTypeCard
                      classname="!w-[112px] !h-[112px]"
                      img={item.img}
                      title={item?.type === "source" ? "منبع" : item.title}
                      isSelected={selectedNode === item.id}
                    />
                  </div>
                ))}
              </div>
              <div className="mt-10 mb-3">
                <p className="-mb-2">موجودیت را جست‌وجو کنید</p>
                <div className="flex items-center gap-2">
                  <CInput
                    value={entityVal}
                    onChange={(e) => setEntityVal(e?.target?.value)}
                    inputProps={{
                      onKeyPress: (e) => {
                        if (e.key === "Enter") {
                          searchNodeHandler();
                        }
                      },
                    }}
                    headingIcon={<MagnifyingGlass />}
                    placeholder="نام موجودیت مورد نظر را برای جست‌وجو وارد کنید"
                    className="w-full pt-5"
                  />
                  <div className="w-20">
                    <CButton
                      readOnly={!entityVal.trim() || loading}
                      onClick={searchNodeHandler}
                    >
                      جست‌وجو
                    </CButton>
                  </div>
                </div>
              </div>
              {loading ? (
                <div className="flex justify-center mb-10">
                  <SpinnerGap size={26} className="animate-spin" />
                </div>
              ) : (
                entityVal.trim() && edgeTypeRenderer[selectedNode]
              )}
              <CButton
                leftIcon={<Plus size={18} className="ml-2" />}
                readOnly={data.length < 1}
                onClick={() => {
                  setNodeDetailsBulk(data);
                  setIsSidebarOpen(false);
                }}
              >
                افزودن گره
              </CButton>
            </div>
          </div>
        </Drawer>
      )}
      <ToastContainer />
    </>
  );
};

export default GraphPreview;
