import { useBreadcrumb } from "hooks/useBreadcrumb";
import OpinionMiningTable from "./components/OpinionMiningTable";
import Loading from "components/ui/Loading";
import Wrapper from "./components/Wrapper";
import Empty from "./components/Empty";
import { ToastContainer } from "react-toastify";
import { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { CButton } from "components/ui/CButton";
import { Info, Plus, WarningCircle } from "@phosphor-icons/react";
import opinion from "service/api/opinion";
import { notification } from "utils/helper";
import useOpinionMiningStore from "store/opinionMining";
import Drawer from "components/Drawer";
import OpinionMiningGuide from "./components/OpinionMiningGuide";

const OpinionMining = () => {
  const { clearOpinionMiningData } = useOpinionMiningStore();
  const [loading, setLoading] = useState(false);
  const [isOpenDrawer, setIsOpenDrawer] = useState(false);
  const [reports, setReports] = useState([]);
  const breadcrumbList = [{ title: "گزارش افکارسنجی" }];
  useBreadcrumb(breadcrumbList);
  const getReports = async () => {
    try {
      const res = await opinion.getOpinionReports();
      setReports(res?.data?.data?.user);
    } catch (error) {
      notification.error(
        "خطایی رخ داد",
        <WarningCircle size={25} className="text-light-error-text-rest" />
      );
    }
  };
  useEffect(() => {
    clearOpinionMiningData();
    getReports();
  }, []);
  return (
    <>
      {loading ? (
        <Loading />
      ) : (
        <>
          <div className="flex flex-col h-full w-full px-6">
            <div className="flex items-center gap-2 justify-end mb-4">
              <Link to={"/app/opinion-mining/create"}>
                <CButton
                  rightIcon={<Plus />}
                  width={155}
                  className={"[direction:ltr]"}
                  mode="outline"
                >
                  گزارش جدید
                </CButton>
              </Link>
              <CButton
                mode="outline"
                size="sm"
                role="neutral"
                width={155}
                className="gap-2 !h-10"
                onClick={() => setIsOpenDrawer(true)}
              >
                <Info size={17} className="text-light-neutral-text-medium" />
                <p className="text-light-neutral-text-medium font-button-medium">
                  راهنمای افکار‌سنجی
                </p>
              </CButton>
            </div>
            <Wrapper>
              {reports.length > 0 ? (
                reports?.map((item) => (
                  <OpinionMiningTable
                    key={item.id}
                    data={item}
                    getReports={getReports}
                  />
                ))
              ) : (
                <Empty />
              )}
            </Wrapper>
          </div>
        </>
      )}
      {isOpenDrawer && (
        <Drawer setShowMore={setIsOpenDrawer}>
          <OpinionMiningGuide />
        </Drawer>
      )}
      <ToastContainer />
    </>
  );
};

export default OpinionMining;
