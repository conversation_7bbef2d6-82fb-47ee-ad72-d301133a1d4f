import ColumnChart from "./charts/ColumnChart";

const TopicGrouping = ({ topGroupingData }) => {
  const getPersianCategory = (key) => {
    const categoryMap = {
      culture_art: "فرهنگ و هنر",
      politic: "سیاسی",
      security_defense: "امنیت و دفاع",
      dailylife: "روزمره",
      social: "اجتماعی",
      economic: "اقتصادی",
      religion: "مذهب",
      science_tech_health: "علم و فناوری",
      sport: "ورزشی",
    };
    return categoryMap[key] || key;
  };

  const xAxisCategory =
    topGroupingData?.twitter?.map((item) => getPersianCategory(item.key)) || [];

  return (
    <div
      className="bg-light-neutral-surface-card rounded-lg p-3"
      style={{
        boxShadow: "0px 2px 20px 0px #0000000D",
      }}
    >
      <div className="flex items-center gap-1">
        <p className="font-subtitle-large">دسته بندی موضوعی</p>
      </div>

      <ColumnChart
        data={topGroupingData?.twitter?.map((item) =>
          parseFloat(((item.count / topGroupingData.total) * 100).toFixed(2))
        )}
        xAxisCategory={xAxisCategory}
        seriesColor={"#1DCEA3"}
      />
    </div>
  );
};

export default TopicGrouping;
