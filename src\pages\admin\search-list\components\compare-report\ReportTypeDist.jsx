import ColumnChart from "./charts/ColumnChart";
import HorizontalBar from "components/Charts/HorizontalBar.jsx";
import { Card } from "components/ui/Card.jsx";
import MultipleBar from "components/Charts/MultipleBar.jsx";
import HighchartsReact from "highcharts-react-official";
import Highcharts from "highcharts";
import {
  fixPercentToShow,
  formatShortNumber,
  toPersianNumber,
} from "utils/helper.js";
import MediaBadge from "components/ui/MediaBadge.jsx";

const ReportTypeDist = () => {
  const info = [
    {
      y: 200,
      name: "گزارش‌های موجودیت",
      key: "گزارش‌های موجودیت",
      color: "#432FA733",
    },
    {
      y: 500,
      name: "گزارش‌های مکان‌محور",
      key: "گزارش‌های مکان‌محور",
      color: "#432FA7",
    },
    {
      y: 300,
      name: "گزارش‌های منبع",
      key: "گزارش‌های منبع",
      color: "#432FA780",
    },
  ];

  const chartOptions = {
    chart: {
      type: "column",
      height: 350,
      width: 360,
    },
    title: {
      text: null,
      align: "right",
      enabled: false,
    },
    subtitle: {
      text: null,
    },
    xAxis: {
      visible: false,
      title: {
        text: null,
      },
    },
    yAxis: {
      min: 0,
      title: {
        text: null, // You can set a title here if you want one
        align: "right",
      },
      labels: {
        enabled: true, // Changed to true to show y-axis labels
        overflow: "justify",
        formatter: function () {
          return toPersianNumber(this.value);
        },
      },
      gridLineWidth: 1, // Set to 1 if you want to show grid lines
    },
    tooltip: {
      enabled: true,
      shared: true,
      useHTML: true,
      style: {
        fontFamily: "IranYekan",
      },
      formatter: function () {
        let tooltipHTML = `<div style="font-family:'iranyekan',serif;font-size: 11px; direction: rtl;">`;
        this.points.forEach((point) => {
          tooltipHTML += `
            <div style="display: flex; align-items: center; margin-bottom: 5px;">
              <div style="width: 10px; height: 10px; border-radius: 50%; background-color: ${
                point.color
              }; margin-left: 3px;"></div>
              <strong style="color: #333; margin-right: 5px; margin-left: 3px;">${toPersianNumber(
                point.y,
              )}</strong> گزارش
            </div>`;
        });
        tooltipHTML += "</div>";
        return tooltipHTML;
      },
    },
    plotOptions: {
      column: {
        pointPadding: 0,
        groupPadding: 0.2,
        borderWidth: 0,
      },
      series: {
        enableMouseTracking: true,
        borderRadius: {
          radius: 8,
        },
      },
    },
    legend: {
      enabled: false,
    },
    credits: {
      enabled: false,
    },
    series: [
      {
        name: "سال 1403",
        data: info,
      },
    ],
  };

  return (
    <Card className="flex flex-col gap-2 h-full">
      <div className="flex items-center gap-1">
        <p className="font-subtitle-large">
          کاربرانی که بیشترین گزارش ۳۶۰ را ثبت کرده اند
        </p>
      </div>
      <div className={"flex flex-row justify-between items-center gap-10"}>
        <div dir="ltr" className="flex flex-col gap-2 mt-4 px-4 w-full">
          {info
            .sort((a, b) => b.y - a.y)
            .map((item) => (
              <div key={item.key} className="flex items-center justify-between">
                <span className="text-lg">{toPersianNumber(item.y)}</span>
                <div className="flex-1 flex items-center gap-2 justify-end">
                  <span>{item.name}</span>
                  <span
                    className={`w-3 h-3 rounded-full`}
                    style={{ backgroundColor: item.color }}
                  ></span>
                  {/*<MediaBadge media={item.key} />*/}
                </div>
              </div>
            ))}
        </div>
        <div className={`flex-1`}>
          <HighchartsReact highcharts={Highcharts} options={chartOptions} />
        </div>
      </div>
    </Card>
  );
};

export default ReportTypeDist;
