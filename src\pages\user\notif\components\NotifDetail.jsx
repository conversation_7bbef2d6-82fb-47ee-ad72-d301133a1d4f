import { useEffect, useState } from "react";
import notification from "service/api/notification";
import { useParams } from "react-router-dom";
import { parseTimeToPersian } from "utils/helper";

const NotifDetail = () => {
  const { id } = useParams();
  const [data, setData] = useState({});

  useEffect(() => {
    notification
      .getById(id)
      .then((res) => setData(res.data.data))
      .catch((e) => console.log(e));
  }, [id]);
  return (
    <div className="flex flex-col gap-6 ml-4">
      <div className="bg-white w-full p-6 rounded-lg shadow-[0px_2px_20px_0px_#0000000D] flex flex-col gap-2">
        <div className="flex justify-between">
          <h2 className="font-subtitle-medium">{data.title}</h2>
          <span className="font-overline-medium text-light-neutral-text-medium">
            {parseTimeToPersian(data.created_at)}
          </span>
        </div>
        <div
          className="font-body-medium"
          dangerouslySetInnerHTML={{ __html: data.description }}
        ></div>
      </div>
    </div>
  );
};

export default NotifDetail;
