import { useState } from "react";
import DropDown from "./DropDown";
import { convertToEinglihsNumbers, toPersianNumber } from "utils/helper";
import PropTypes from "prop-types";

const media = {
  telegram: [
    { text: "زمان انتشار", value: "date" },
    { text: "میزان بازدید", value: "view" },
  ],
  twitter: [
    { text: "زمان انتشار", value: "date" },
    { text: "تعداد لایک", value: "like" },
    { text: "ریتوئیت", value: "retweet" },
  ],
  instagram: [
    { text: "زمان انتشار", value: "date" },
    { text: "میزان بازدید", value: "view" },
  ],
  news: [
    { text: "زمان انتشار", value: "date" },
    // { text: "تعداد نظرات", value: "comment_count" },
  ],
};

const MultipleFilter = ({
  platform,
  handleChange = (platform, stat) => {},
  value,
  subsets = [],
  disabled = false,
}) => {
  const [selectedValue, setSelectedValue] = useState(
    media[platform].filter((item) => item.value === value.sort)[0],
  );

  const handleNum = (number) => {
    if (isNaN(number)) return value.content_count;
    if (number > 50) return 50;
    return +number;
  };

  const handleNumber = (number) => {
    const help = JSON.parse(JSON.stringify(value));
    help.content_count = handleNum(number);
    handleChange(platform, help);
  };

  const handleSort = (sort) => {
    const help = JSON.parse(JSON.stringify(value));
    help.sort = sort.value;
    handleChange(platform, help);
    setSelectedValue(sort);
  };

  const handleOrder = (order) => {
    const help = JSON.parse(JSON.stringify(value));
    help.order = order.value;
    handleChange(platform, help);
  };

  return (
    <div>
      <div className="flex gap-2">
        <div className="w-full">
          <div
            className={`border border-light-neutral-border-low-rest rounded-lg flex gap-2 items-center justify-between h-10 px-4 py-2 w-full ${
              disabled
                ? "bg-light-neutral-background-low border-light-neutral-border-disable"
                : "bg-light-neutral-background-medium"
            }`}
          >
            <span className="font-button-medium shrink-0">تعداد محتوا</span>
            <input
              type="text"
              className="outline-none bg-transparent w-full font-body-large text-light-neutral-text-medium"
              dir="ltr"
              onChange={(e) =>
                handleNumber(convertToEinglihsNumbers(e.target.value))
              }
              value={toPersianNumber(value.content_count)}
              disabled={disabled}
            />
          </div>
        </div>

        <div className="w-full">
          <DropDown
            subsets={subsets}
            value={selectedValue}
            title="انتخاب براساس"
            handleChange={(x) => handleSort(x)}
            disabled={disabled}
          />
        </div>
        <div className="w-full">
          <DropDown
            subsets={[
              { text: "بیشترین", value: "asc" },
              { text: "کمترین", value: "desc" },
            ]}
            title="ترتیب نمایش"
            handleChange={(x) => handleOrder(x)}
            disabled={disabled}
          />
        </div>
      </div>
      <div></div>
    </div>
  );
};

MultipleFilter.propTypes = {
  platform: PropTypes.string,
  handleChange: PropTypes.func,
  value: PropTypes.object,
  subsets: PropTypes.array,
  disabled: PropTypes.bool,
};

export default MultipleFilter;
