import useFetch from "../index";

class AuthService {
  loginUser(data) {
    return useFetch.post("/api/v1/account/login/", data);
  }

  forgotPassword(data) {
    return useFetch.post("/api/v1/account/forgot-password/", data);
  }

  confirm(data) {
    return useFetch.put("/api/v1/account/forgot-password/", data);
    // return useFetch.post('/api/v1/account/forgot-password/confirm', data);
  }

  changePassword(data) {
    return useFetch.put("/api/v1/account/change-password/", data);
    // return useFetch.post('/api/v1/account/change-password', data);
  }

  getProfile() {
    return useFetch.get("/api/v1/account/profile/");
  }

  updateProfile(data) {
    return useFetch.put("/api/v1/account/profile/", data);
  }

  verifyPhoneNo(data) {
    return useFetch.put("/api/v1/account/change-phone/", data);
  }
  verifyEmail(data) {
    return useFetch.put("/api/v1/account/change-email/", data);
  }
  changePhoneNo(data) {
    return useFetch.post("/api/v1/account/change-phone/", data);
  }
  changeEmail(data) {
    return useFetch.post("/api/v1/account/change-email/", data);
  }
}
export default new AuthService();
