import { useState } from "react";
import { useBreadcrumb } from "hooks/useBreadcrumb";
import { ticketGuideLis } from "./constants/ticketGuideLis";
import { CInput } from "components/ui/CInput";
import { CButton } from "components/ui/CButton";
import DropDown from "./components/DropDown";
import ticket from "service/api/ticket";
import {
  File,
  WarningCircle,
  XCircle,
  FileZip,
  FilePdf,
  FileTxt,
  FileDoc,
  MicrosoftExcelLogo,
} from "@phosphor-icons/react";
import { notification, shortener } from "utils/helper";
import { ToastContainer } from "react-toastify";
import AttachmentBox from "./components/AttachmentBox";
import { useNavigate } from "react-router-dom";

const TicketCreate = () => {
  const [ticketTitle, setTicketTitle] = useState("");
  const [ticketDescription, setTicketDescription] = useState("");
  const [ticketSection, setTicketSection] = useState("support");
  const [attachments, setAttachments] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  const breadcrumbList = [
    { title: "تیکت‌", link: "/app/ticket/list" },
    { title: "تیکت جدید" },
  ];
  useBreadcrumb(breadcrumbList);

  const ticketCreate = async () => {
    setIsLoading(true);

    try {
      const data = {
        title: ticketTitle,
        description: ticketDescription,
        ticket_section: ticketSection,
      };
      const response = await ticket.create(data);
      const ticketId = response?.data?.data?.ticket?.id;
      const messageId = response?.data?.data?.messages?.id;

      if (attachments && attachments.length > 0) {
        const formData = new FormData();
        attachments.forEach((file) => {
          formData.append("files", file);
        });
        await ticket.updateAttachment(ticketId, messageId, formData);
      }

      notification.success(
        `تیکت شما ثبت شد و در اسرع وقت کارشناسان سیناپس به آن پاسخ خواهند داد`,
        <WarningCircle className="text-light-success-text-rest" />,
      );
      navigate(`/app/ticket/list/${ticketId}`);
    } catch (error) {
      console.log(error);
    }
    setIsLoading(false);
  };

  const handleDeleteFile = (index) => {
    const newAttachments = attachments.filter((_, i) => i !== index);
    setAttachments(newAttachments);
  };

  const handleImageClick = (file) => {
    setSelectedImage(URL.createObjectURL(file));
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedImage(null);
  };

  return (
    <div className="container px-3 grid grid-cols-12 gap-10 mt-8">
      <div className="col-span-8 bg-light-neutral-surface-card p-6 rounded-[8px]">
        <p className="font-subtitle-large mb-8">ساخت تیکت جدید</p>
        <div className="grid grid-cols-12 gap-5">
          <div className="col-span-6">
            <CInput
              value={ticketTitle}
              onChange={(e) => setTicketTitle(e.target.value)}
              title={"عنوان تیکت"}
              placeholder={"عنوان تیکت را بنویسید"}
              validation="error"
            />
          </div>
          <div className="col-span-6">
            <DropDown
              subsets={[
                { text: "واحد پشتیبانی", value: "support" },
                { text: "واحد فنی", value: "technical" },
                { text: "واحد مالی و اداری", value: "finances" },
              ]}
              title="واحد مربوطه"
              handleChange={(e) => setTicketSection(e?.value)}
              disabled={false}
            />
          </div>
        </div>

        <label htmlFor="" className="font-overline-large">
          متن پیام
        </label>
        <textarea
          value={ticketDescription}
          onChange={(e) => setTicketDescription(e.target.value)}
          className="w-full h-40 rounded-md p-1.5 font-body-large outline-none border border-light-neutral-border-medium-rest mt-1"
          placeholder="توضیحات خود را کامل و شفاف در اینجا بنویسید"
        ></textarea>

        <div className="mb-5 mt-1">
          {/* <AttachmentBox
            description={"در صورت لزوم فایل را بارگذاری کنید (اختیاری)"}
            description2={"حداکثر حجم مجاز: ۱۰ مگابایت"}
            fullWidth
            boxIcon={File}
            onChange={(files) => {
              const totalFiles = attachments.length + files.length;

              if (totalFiles > 8) {
                notification.error(
                  "تعداد فایل‌های آپلود شده باید حداکثر ۸ عدد باشد!"
                );
                return;
              }
              const filteredFiles = files.filter(
                (newFile) =>
                  !attachments.some(
                    (existingFile) => existingFile.name === newFile.name
                  )
              );
              setAttachments((prev) => [...prev, ...filteredFiles]);
            }}
            multiple
          /> */}
          <AttachmentBox
            description={"در صورت لزوم فایل را بارگذاری کنید (اختیاری)"}
            description2={"حداکثر حجم مجاز: ۱۰ مگابایت"}
            fullWidth
            boxIcon={File}
            onChange={(files) => {
              const validExtensions = [
                ".jpeg",
                ".jpg",
                ".png",
                ".docx",
                ".pdf",
                ".doc",
                ".csv",
                ".xls",
                ".txt",
                ".zip",
              ];
              const invalidFiles = files.filter((file) => {
                const fileExtension = file.name
                  .slice(file.name.lastIndexOf("."))
                  .toLowerCase();
                return !validExtensions.includes(fileExtension);
              });
              if (invalidFiles.length > 0) {
                notification.error(
                  `فرمت فایل‌های زیر معتبر نیست: ${invalidFiles
                    .map((file) => file.name)
                    .join(", ")}`,
                );
                return;
              }
              const totalFiles = attachments.length + files.length;
              if (totalFiles > 8) {
                notification.error(
                  "تعداد فایل‌های آپلود شده باید حداکثر ۸ عدد باشد!",
                );
                return;
              }
              const totalSize =
                attachments.reduce((acc, file) => acc + file.size, 0) +
                files.reduce((acc, file) => acc + file.size, 0);
              if (totalSize > 10 * 1024 * 1024) {
                notification.error(
                  "حجم کل فایل‌ها نباید بیشتر از ۱۰ مگابایت باشد!",
                );
                return;
              }
              const filteredFiles = files.filter(
                (newFile) =>
                  !attachments.some(
                    (existingFile) => existingFile.name === newFile.name,
                  ),
              );
              setAttachments((prev) => [...prev, ...filteredFiles]);
            }}
            multiple
          />

          <div className="mt-4 flex flex-wrap gap-4 items-center">
            {attachments.map((file, index) => (
              <div
                key={index}
                className="flex flex-col items-center relative w-24 h-24 border rounded my-3"
              >
                {file.type?.startsWith("image/") ? (
                  <img
                    src={URL.createObjectURL(file)}
                    alt={file.name}
                    className="w-full h-full max-h-20 object-cover cursor-pointer"
                    onClick={() => handleImageClick(file)}
                  />
                ) : file.type?.endsWith("pdf") ? (
                  <FilePdf
                    size={96}
                    className="w-full h-full flex items-center justify-center"
                  />
                ) : file.type?.endsWith("application/x-zip-compressed") ? (
                  <FileZip
                    size={96}
                    className="flex items-center justify-center"
                  />
                ) : file.type?.endsWith("csv") ? (
                  <MicrosoftExcelLogo
                    size={96}
                    className="flex items-center justify-center"
                  />
                ) : file.type?.endsWith("xls") ? (
                  <MicrosoftExcelLogo
                    size={96}
                    className="flex items-center justify-center"
                  />
                ) : file.type?.endsWith(".sheet") ? (
                  <MicrosoftExcelLogo
                    size={96}
                    className="flex items-center justify-center"
                  />
                ) : file.type?.endsWith("text/plain") ? (
                  <FileTxt
                    size={96}
                    className="flex items-center justify-center"
                  />
                ) : file.type?.endsWith("docx") ? (
                  <FileDoc
                    size={96}
                    className="flex items-center justify-center"
                  />
                ) : file.type?.endsWith("doc") ? (
                  <FileDoc
                    size={96}
                    className="flex items-center justify-center"
                  />
                ) : file.type?.endsWith("document") ? (
                  <FileDoc
                    size={96}
                    className="flex items-center justify-center"
                  />
                ) : (
                  <File
                    size={96}
                    className="flex items-center justify-center"
                  />
                )}
                <p className="font-body-bold-small">
                  {shortener(file?.name, 10)}
                </p>
                <button
                  onClick={() => handleDeleteFile(index)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full"
                >
                  <XCircle size={20} />
                </button>
              </div>
            ))}
          </div>
        </div>
        <CButton
          onClick={ticketCreate}
          readOnly={!ticketTitle || !ticketDescription}
          disabled={isLoading}
        >
          ارسال تیکت
        </CButton>
      </div>
      <div className="col-span-4">
        <div className="font-body-medium ">
          {ticketGuideLis?.map((item) => (
            <ul key={item} className="py-2">
              <li className="list-disc text-justify">{item}</li>
            </ul>
          ))}
        </div>
        <div className="font-body-medium mt-14">
          <ul className="py-2">
            <li className="list-disc text-justify py-2">
              <span className="font-body-bold-medium pl-1">واحد پشتیبانی:</span>
              در صورتی که مشکلی در شیوه کاربری سامانه، طریقه‌ای استفاده از یک
              صفحه خاص یا مواردی از این قبیل را دارید، این واحد را انتخاب
              نمایید.
            </li>
            <li className="list-disc text-justify py-2">
              <span className="font-body-bold-medium pl-1">واحد فنی:</span>
              اگر مشکل شما از نوع ناسازگاری داده‌ها مانند کاهش داده‌ها یا وجود
              داده‌های نامرتبط، مشاهده نتایج اشتباه در یک گزارش یا صفحه، عملکرد
              اشتباه یک ویژگی در سامانه و موارد مشابه است، لطفا این گزینه را
              انتخاب کنید.
            </li>
            <li className="list-disc text-justify py-2">
              <span className="font-body-bold-medium pl-1">واحد مالی:</span>
              در صورت وجود مشکل در پرداخت مبلغ رسید، کیف پول کاربری اطلاع‌رسانی
              در مورد پرداخت‌های انجام شده و سایر موارد از این دست می‌باشد، این
              واحد را انتخاب کنید.
            </li>
          </ul>
        </div>
      </div>
      <ToastContainer />

      {isModalOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          onClick={handleCloseModal}
        >
          <div
            className="relative p-4 rounded"
            onClick={(e) => e.stopPropagation()}
          >
            <img
              src={selectedImage}
              alt="Preview"
              className="max-w-96 max-h-max-w-96"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default TicketCreate;
