import { useCallback, useEffect, useRef, useState } from "react";
import SummaryCard from "components/SummaryCard";
import TextSlicer from "components/TextSlicer/index.jsx";
import useSearchStore from "store/searchStore";
import PLATFORMS from "constants/platforms";
import advanceSearch from "service/api/advanceSearch";
import Paginate from "components/ui/Paginate";
import Loading from "components/ui/Loading";
import { ToastContainer } from "react-toastify";
import { toPersianNumber } from "utils/helper";
import { useBulletinStore } from "store/bulletinStore.js";
import SORT_TYPE from "constants/sort-type.js";
import DropDown from "components/ui/DropDown.jsx";
import { buildRequestData, transformResponseData } from "utils/requestData.js";

const Step2Manual = () => {
  const { content, contentManual, existingContent } = useBulletinStore(
    (state) => state.bulletin,
  );
  const setBulletin = useBulletinStore((state) => state.setBulletin);

  const [selectAllValue, setSelectAllValue] = useState(false);
  const [badgePlatform, setBadgePlatform] = useState(PLATFORMS.TWITTER);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [sort, setSort] = useState({ fa: "زمان انتشار", en: "date" });
  const [sort_type, setSort_type] = useState("نزولی");
  const [dataArray, setDataArray] = useState([]);
  const [transformedDataArray, setTransformedDataArray] = useState([]);
  const [dataCount, setDataCount] = useState(0);

  const {
    filters,
    setFilters,
    query,
    setShowFilterList,
    setIsFilterListOpen,
    setShowSearchBox,
    setSearchBoxConfig,
  } = useSearchStore();

  useEffect(() => {
    setSelectAllValue(false);
    setShowFilterList(true);
    setShowSearchBox(true);
    setIsFilterListOpen(true);
    setSearchBoxConfig({ disableScroll: true });
  }, []);

  const countLoadingOffset = useRef();

  const handleQuery = useCallback(() => {
    return buildRequestData(
      {
        ...filters,
        q: query,
      },
      "advance",
    );
  }, [filters, query, page, sort, sort_type]);

  const resetPageNumber = () => page !== 1 && setPage(1);

  const resetSort = () => setSort({ fa: "زمان انتشار", en: "date" });
  const clearDataArray = () => {
    setDataArray([]);
    setTransformedDataArray([]);
    setDataCount(0);
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };
  const getSearchData = async (overwrite = false) => {
    if (loading) return null;
    if (!query || query === "") {
      clearDataArray();
      return null;
    }
    setLoading(true);
    try {
      const parsedQueryToString = handleQuery();
      const responseStinas = await advanceSearch.search(parsedQueryToString);

      const stinasData = responseStinas?.data?.data;
      const transformedData = transformResponseData(
        stinasData?.[filters?.platform] || [],
      );
      if (overwrite) {
        setDataArray(existingContent);
        setTransformedDataArray(transformResponseData(existingContent));
      } else {
        setDataArray(stinasData?.[filters?.platform]);
        setTransformedDataArray(transformedData);
        setDataCount(stinasData?.total);
      }
      setBulletin({ existingContent: [] });
      setLoading(false);
    } catch (error) {
      console.log(error);
      setLoading(false);
    }
  };

  useEffect(() => {
    resetPageNumber();
    resetSort();

    switch (filters.platform) {
      case PLATFORMS.INSTAGRAM:
        setBadgePlatform(PLATFORMS.INSTAGRAM);
        return;
      case PLATFORMS.TELEGRAM:
        setBadgePlatform(PLATFORMS.TELEGRAM);
        return;
      case PLATFORMS.NEWS:
        setBadgePlatform(PLATFORMS.NEWS);
        return;
      case PLATFORMS.TWITTER:
        setBadgePlatform(PLATFORMS.TWITTER);
        return;
      default:
        setBadgePlatform(filters.platform);
        return;
    }
  }, [filters.platform]);

  useEffect(() => {
    // Avoid calling getSearchData multiple times on platform change
    let debounceTimer;
    clearDataArray();

    const handleDataFetch = async () => {
      clearTimeout(debounceTimer); // Clear previous timer

      debounceTimer = setTimeout(async () => {
        await getSearchData(existingContent.length > 0);
        scrollToTop(); // Call getSearchData once after a short delay
      }, 50); // Debounce with 50ms delay
    };

    handleDataFetch();

    return () => clearTimeout(debounceTimer); // Cleanup timer on unmount
  }, [filters, query]);

  useEffect(() => {
    setFilters({ page: page });
  }, [page]);

  useEffect(() => {
    resetPageNumber();
    setFilters({ sort: sort.en, page: 1 });
  }, [sort]);

  useEffect(() => {
    const existingItemIndex = contentManual?.[filters.platform]?.findIndex(
      (x) => x.q === query,
    );

    setSelectAllValue(
      dataArray?.every((aItem) =>
        contentManual?.[filters.platform]?.[existingItemIndex]?.q_content.some(
          (bItem) => bItem.id === aItem.id,
        ),
      ),
    );
  }, [transformedDataArray, dataArray, contentManual]);

  const handleSelect = (item, checked) => {
    const help = JSON.parse(JSON.stringify(contentManual));

    const existingItemIndex = help?.[filters.platform]?.findIndex(
      (x) => x.q === query,
    );

    if (checked) {
      if (existingItemIndex !== -1) {
        const existingContent =
          help?.[filters.platform]?.[existingItemIndex]?.q_content;

        // Check if a similar item already exists in q_content
        const itemIndexInContent = existingContent.findIndex(
          (content) => JSON.stringify(content) === JSON.stringify(item),
        );

        if (itemIndexInContent !== -1) {
          // Remove the existing item if found
          existingContent.splice(itemIndexInContent, 1);
        } else {
          // Add the item if not found
          existingContent.push(item);
        }

        help[filters.platform][existingItemIndex].totalCount = dataCount;
        help[filters.platform][existingItemIndex].q_content = existingContent;
        setBulletin({ contentManual: help });
        return;
      }

      help[filters.platform].push({
        q: query,
        totalCount: dataCount,
        from_date: filters.date?.from?.toISOString(),
        to_date: filters.date?.to?.toISOString(),
        content_count: dataArray.length,
        q_content: [item], // Start with the current item
      });

      setBulletin({ contentManual: help });

      help.q = query;
      return true;
    }

    const existingContent =
      help[filters.platform]?.[existingItemIndex]?.q_content;

    // Check if a similar item already exists in q_content
    const itemIndexInContent = existingContent.findIndex(
      (content) => JSON.stringify(content) === JSON.stringify(item),
    );

    if (itemIndexInContent !== -1) {
      // Remove the existing item if found
      existingContent.splice(itemIndexInContent, 1);
    } else {
      existingContent.push(item);
    }

    help[filters.platform][existingItemIndex].totalCount = dataCount;
    help[filters.platform][existingItemIndex].q_content = existingContent;
    setBulletin({ contentManual: help });
  };

  const handleSelectAll = (items, checked) => {
    const help = JSON.parse(JSON.stringify(contentManual));

    const existingItemIndex = help?.[filters.platform]?.findIndex(
      (x) => x.q === query,
    );

    if (checked) {
      // Handle checked case: add all items to q_content
      if (existingItemIndex !== -1) {
        const existingContent =
          help[filters.platform][existingItemIndex].q_content;

        items.forEach((item) => {
          const itemIndexInContent = existingContent.findIndex(
            (content) => JSON.stringify(content) === JSON.stringify(item),
          );

          if (itemIndexInContent === -1) {
            // Add the item if not found
            existingContent.push(item);
          }
        });

        help[filters.platform][existingItemIndex].totalCount = dataCount;
        help[filters.platform][existingItemIndex].q_content = existingContent;
        setBulletin({ contentManual: help });
      } else {
        // If no existing entry with the same query, create a new entry
        help[filters.platform].push({
          q: query,
          totalCount: dataCount,
          from_date: filters.date?.from?.toISOString(),
          to_date: filters.date?.to?.toISOString(),
          content_count: dataArray.length,
          q_content: items, // Add all items to q_content
        });

        help.q = query;

        setBulletin({ contentManual: help });
      }
    } else {
      // Handle unchecked case: remove all items from q_content
      if (existingItemIndex !== -1) {
        let existingContent =
          help[filters.platform][existingItemIndex].q_content;

        items.forEach((item) => {
          const itemIndexInContent = existingContent.findIndex(
            (content) => JSON.stringify(content) === JSON.stringify(item),
          );

          if (itemIndexInContent !== -1) {
            // Remove the item if found
            existingContent.splice(itemIndexInContent, 1);
          }
        });

        // If q_content becomes empty after removal, remove the entire entry
        if (existingContent.length === 0) {
          help[filters.platform].splice(existingItemIndex, 1);
        } else {
          help[filters.platform][existingItemIndex].q_content = existingContent;
          help[filters.platform][existingItemIndex].totalCount = dataCount;
        }
        setBulletin({ contentManual: help });
      }
    }
  };

  return (
    <>
      <div className="min-h-screen w-full flex  flex-1 flex-col pb-32">
        {dataArray?.length > 0 && (
          <div className="flex flex-row-reverse justify-between w-full font-body-medium bg-light-neutral-surface-card rounded-[8px] px-4 py-6 shadow-[0_2px_20px_0_rgba(0,0,0,0.05)] ">
            <div className="flex flex-row-reverse items-center gap-3">
              <input
                name="selectAllBulletins"
                id="selectAllBulletins"
                type="checkbox"
                className="size-6"
                style={{ accentColor: "#343330" }}
                checked={selectAllValue}
                onChange={(e) => {
                  handleSelectAll(dataArray, e.target.checked);
                }}
              />
              <label htmlFor="selectAllBulletins" className="font-body-large">
                انتخاب همه (این صفحه)
              </label>
            </div>

            <div className="flex flex-row-reverse items-center gap-4">
              <div className="flex items-center gap-2 font-overline-medium text-light-neutral-text-medium">
                <p>{toPersianNumber(dataCount)} محتوا</p>
              </div>

              <div className="w-52 [direction:rtl]">
                <DropDown
                  title="نمایش بر اساس"
                  subsets={SORT_TYPE[filters.platform].map((item) => item.fa)}
                  selected={sort.fa}
                  setSelected={(value) => {
                    setSort(
                      SORT_TYPE[filters.platform].filter(
                        (item) => item.fa === value,
                      )[0],
                    );
                  }}
                />
              </div>
            </div>
          </div>
        )}
        <>
          {loading ? (
            <Loading height={countLoadingOffset?.current?.clientHeight + 24} />
          ) : (
            <>
              {transformedDataArray && transformedDataArray.length === 0 ? (
                <div
                  className={
                    "flex w-full flex-col justify-center items-center min-h-[350px]"
                  }
                >
                  <div
                    className={
                      "w-full h-[50px] bg-[url(/empty_folder.png)] bg-no-repeat bg-contain bg-center"
                    }
                  ></div>
                  <p
                    className={
                      "font-body-large text-light-neutral-text-high mt-3"
                    }
                  >
                    هنوز محتوایی جست‌وجو نشده است
                  </p>
                  <p
                    className={
                      "font-body-medium text-light-neutral-text-medium mt-3"
                    }
                  >
                    لطفا کلمات موردنظر خود را جست‌و‌جو کنید
                  </p>
                </div>
              ) : (
                <div>
                  {transformedDataArray?.map((item, index) => (
                    <div
                      key={item?.id}
                      className="md:col-span-6 col-span-12 bg-light-neutral-surface-card rounded-lg mt-5"
                    >
                      <SummaryCard
                        data={item}
                        media={badgePlatform}
                        showHeaderMenu={true}
                        setMyBookmarksLists={() => {}}
                        selectable
                        isSelected={contentManual[filters.platform]?.some(
                          (bulletin) =>
                            bulletin.q_content.some((x) => x.id === item.id),
                        )}
                        handleSelect={(checked) =>
                          handleSelect(dataArray[index], checked)
                        }
                      >
                        <TextSlicer length={200}>
                          {item?.description || item?.content}
                        </TextSlicer>
                      </SummaryCard>
                    </div>
                  ))}

                  <div className="[direction:rtl]">
                    <Paginate
                      page={page}
                      setPage={setPage}
                      dataCount={dataCount}
                      per_page={10}
                    />
                  </div>
                </div>
              )}
            </>
          )}
        </>
      </div>
      <ToastContainer />
    </>
  );
};

export default Step2Manual;
