import { useBreadcrumb } from "hooks/useBreadcrumb";
import { CButton } from "components/ui/CButton";
import PoliticalSpectrumContribution from "./components/PoliticalSpectrumContribution.jsx";
import PoliticalFactionEmotions from "./components/PoliticalFactionEmotions.jsx";
import EmotionAnalysisPercentage from "./components/EmotionAnalysisPercentage.jsx";
import PoliticalSentimentAnalysis from "./components/PoliticalSentimentAnalysis.jsx";
import SentimentAnalysisPercentage from "./components/SentimentAnalysisPercentage.jsx";
import AgeClassification from "./components/AgeClassification.jsx";
import AgePercentageClassification from "./components/AgePercentageClassification.jsx";
import PoliticalGenderAverageAnalysis from "./components/PoliticalGenderAverageAnalysis.jsx";
import PoliticalAgeAverageAnalysis from "./components/PoliticalAgeAverageAnalysis.jsx";
import PoliticalParties from "./components/PoliticalParties";
import { CheckCircle, FloppyDisk, WarningCircle } from "@phosphor-icons/react";
import NetworkGraphCategoryChart from "../wave-analysis-id/wave-info/components/charts/NetworkGraphCategoryChart.jsx";
import { useLocation } from "react-router-dom";
import DateFilter from "./components/DateFilter.jsx";
import opinion from "service/api/opinion.js";
import { useEffect, useState } from "react";
import PopUp from "components/ui/PopUp.jsx";
import { CInput } from "components/ui/CInput.jsx";
import { notification } from "utils/helper.js";
import { ToastContainer } from "react-toastify";
import waveAnalytics from "service/api/waveAnalytics.js";

const WaveAnalysisThoughts = () => {
  const [sentimentData, setSentimentData] = useState([]);
  const [sentimentAnalysis, setSentimentAnalysis] = useState([]);
  const [emotionsData, setEmotionsData] = useState([]);
  const [emotionsAnalysis, setEmotionsAnalysis] = useState([]);
  const [agesData, setAgesData] = useState([]);
  const [gendersData, setGendersData] = useState([]);
  const [graphDataLoading, setGraphDataLoading] = useState(false);
  const [topSourcesData, setTopSources] = useState([]);
  const [offensiveData, setOffensiveData] = useState([]);
  const [categoriesData, setCategoriesData] = useState([]);
  const [graphData, setGraphData] = useState([]);
  const location = useLocation();
  const { title } = location.state || {};
  const today = new Date();
  const oneMonthAgo = new Date();
  oneMonthAgo.setMonth(today.getMonth() - 1);
  const [opinionMiningData, setOpinionMiningData] = useState({
    title,
    opinion_type: "systematic",
    opinion_platform: "twitter",
    date_range: {
      start_date:
        location?.query_start_time ||
        location?.state?.opinionMiningData2?.date_range?.from_date ||
        location?.state?.date_range?.from_date,
      end_date:
        location?.query_end_time ||
        location?.state?.opinionMiningData2?.date_range?.to_date ||
        location?.state?.date_range?.to_date,
    },
    hashtags:
      location?.state?.elements?.map((item) => item?.content) ||
      location?.state?.opinionMiningData2?.query?.platform?.twitter?.hashtags ||
      location?.state?.query?.params?.platform?.twitter?.hashtags ||
      [],
  });
  const breadcrumbList = [
    { title: "موج شناسی", link: "/app/wave-analysis/list" },
    { title: title || location?.state?.opinionMiningData2?.title },
  ];
  const [showPopup, setShowPopup] = useState(false);
  useBreadcrumb(breadcrumbList);
  // console.log(opinionMiningData?.date_range);
  const saveOpinionMiningHandler = async () => {
    try {
      const res = await opinion.createWaveAnalysisOpinion(opinionMiningData);
      notification.success(
        res?.data?.message,
        <CheckCircle size={25} className="text-light-success-text-rest" />
      );
    } catch (error) {
      notification.error(
        error?.response?.data?.message,
        <WarningCircle size={25} className="text-light-error-text-rest" />
      );
    } finally {
      setShowPopup(false);
    }
  };
  const fetchSentiment = async () => {
    try {
      const { data } = await waveAnalytics.sendWaveData({
        report_type: "opinion_sentiments",
        platform: "twitter",
        start_date:
          location?.query_start_time ||
          location?.state?.opinionMiningData2?.date_range?.from_date ||
          location?.state?.date_range?.from_date,
        end_date:
          location?.query_end_time ||
          location?.state?.opinionMiningData2?.date_range?.to_date ||
          location?.state?.date_range?.to_date,
        hashtags:
          location?.state?.elements?.map((item) => item?.content) ||
          location?.state?.opinionMiningData2?.query?.platform?.twitter
            ?.hashtags ||
          location?.state?.query?.params?.platform?.twitter?.hashtags ||
          [],
      });
      setSentimentData(data?.data);
    } catch (error) {
      console.log(error);
    }
  };
  const fetchEmotions = async () => {
    try {
      const { data } = await waveAnalytics.sendWaveData({
        report_type: "opinion_emotion",
        platform: "twitter",
        start_date:
          location?.query_start_time ||
          location?.state?.opinionMiningData2?.date_range?.from_date ||
          location?.state?.date_range?.from_date,
        end_date:
          location?.query_end_time ||
          location?.state?.opinionMiningData2?.date_range?.to_date ||
          location?.state?.date_range?.to_date,
        hashtags:
          location?.state?.elements?.map((item) => item?.content) ||
          location?.state?.opinionMiningData2?.query?.platform?.twitter
            ?.hashtags ||
          location?.state?.query?.params?.platform?.twitter?.hashtags ||
          [],
      });
      setEmotionsData(data?.data);
    } catch (error) {
      console.log(error);
    }
  };
  const fetchEmotionsAnalysis = async () => {
    try {
      const { data } = await waveAnalytics.sendWaveData({
        report_type: "emotion",
        platform: "twitter",
        start_date:
          location?.query_start_time ||
          location?.state?.opinionMiningData2?.date_range?.from_date ||
          location?.state?.date_range?.from_date,
        end_date:
          location?.query_end_time ||
          location?.state?.opinionMiningData2?.date_range?.to_date ||
          location?.state?.date_range?.to_date,
        hashtags:
          location?.state?.elements?.map((item) => item?.content) ||
          location?.state?.opinionMiningData2?.query?.platform?.twitter
            ?.hashtags ||
          location?.state?.query?.params?.platform?.twitter?.hashtags ||
          [],
      });
      setEmotionsAnalysis(data?.data);
    } catch (error) {
      console.log(error);
    }
  };
  const fetchSentimentAnalysis = async () => {
    try {
      const { data } = await waveAnalytics.sendWaveData({
        report_type: "sentiments",
        platform: "twitter",
        start_date:
          location?.query_start_time ||
          location?.state?.opinionMiningData2?.date_range?.from_date ||
          location?.state?.date_range?.from_date,
        end_date:
          location?.query_end_time ||
          location?.state?.opinionMiningData2?.date_range?.to_date ||
          location?.state?.date_range?.to_date,
        hashtags:
          location?.state?.elements?.map((item) => item?.content) ||
          location?.state?.opinionMiningData2?.query?.platform?.twitter
            ?.hashtags ||
          location?.state?.query?.params?.platform?.twitter?.hashtags ||
          [],
      });
      setSentimentAnalysis(data?.data);
    } catch (error) {
      console.log(error);
    }
  };
  const fetchAges = async () => {
    try {
      const { data } = await waveAnalytics.sendWaveData({
        report_type: "opinion_age",
        platform: "twitter",
        start_date:
          location?.query_start_time ||
          location?.state?.opinionMiningData2?.date_range?.from_date ||
          location?.state?.date_range?.from_date,
        end_date:
          location?.query_end_time ||
          location?.state?.opinionMiningData2?.date_range?.to_date ||
          location?.state?.date_range?.to_date,
        hashtags:
          location?.state?.elements?.map((item) => item?.content) ||
          location?.state?.opinionMiningData2?.query?.platform?.twitter
            ?.hashtags ||
          location?.state?.query?.params?.platform?.twitter?.hashtags ||
          [],
      });
      setAgesData(data?.data);
    } catch (error) {
      console.log(error);
    }
  };
  const fetchGenders = async () => {
    try {
      const { data } = await waveAnalytics.sendWaveData({
        report_type: "opinion_gender",
        platform: "twitter",
        start_date:
          location?.query_start_time ||
          location?.state?.opinionMiningData2?.date_range?.from_date ||
          location?.state?.date_range?.from_date,
        end_date:
          location?.query_end_time ||
          location?.state?.opinionMiningData2?.date_range?.to_date ||
          location?.state?.date_range?.to_date,
        hashtags:
          location?.state?.elements?.map((item) => item?.content) ||
          location?.state?.opinionMiningData2?.query?.platform?.twitter
            ?.hashtags ||
          location?.state?.query?.params?.platform?.twitter?.hashtags ||
          [],
      });
      setGendersData(data?.data);
    } catch (error) {
      console.log(error);
    }
  };
  const fetchTopSources = async () => {
    try {
      const { data } = await waveAnalytics.sendWaveData({
        report_type: "opinion_top_sources",
        platform: "twitter",
        start_date:
          location?.query_start_time ||
          location?.state?.opinionMiningData2?.date_range?.from_date ||
          location?.state?.date_range?.from_date,
        end_date:
          location?.query_end_time ||
          location?.state?.opinionMiningData2?.date_range?.to_date ||
          location?.state?.date_range?.to_date,
        hashtags:
          location?.state?.elements?.map((item) => item?.content) ||
          location?.state?.opinionMiningData2?.query?.platform?.twitter
            ?.hashtags ||
          location?.state?.query?.params?.platform?.twitter?.hashtags ||
          [],
      });
      setTopSources(data?.data);
    } catch (error) {
      console.log(error);
    }
  };
  const fetchCategories = async () => {
    try {
      const { data } = await waveAnalytics.sendWaveData({
        report_type: "opinion_categories",
        platform: "twitter",
        start_date:
          location?.query_start_time ||
          location?.state?.opinionMiningData2?.date_range?.from_date ||
          location?.state?.date_range?.from_date,
        end_date:
          location?.query_end_time ||
          location?.state?.opinionMiningData2?.date_range?.to_date ||
          location?.state?.date_range?.to_date,
        hashtags:
          location?.state?.elements?.map((item) => item?.content) ||
          location?.state?.opinionMiningData2?.query?.platform?.twitter
            ?.hashtags ||
          location?.state?.query?.params?.platform?.twitter?.hashtags ||
          [],
      });
      setCategoriesData(data?.data);
    } catch (error) {
      console.log(error);
    }
  };
  const fetchGraph = async () => {
    try {
      setGraphDataLoading(true);
      const { data } = await waveAnalytics.sendWaveData({
        report_type: "opinion_graph",
        platform: "twitter",
        start_date:
          location?.query_start_time ||
          location?.state?.opinionMiningData2?.date_range?.from_date ||
          location?.state?.date_range?.from_date,
        end_date:
          location?.query_end_time ||
          location?.state?.opinionMiningData2?.date_range?.to_date ||
          location?.state?.date_range?.to_date,
        hashtags:
          location?.state?.elements?.map((item) => item?.content) ||
          location?.state?.opinionMiningData2?.query?.platform?.twitter
            ?.hashtags ||
          location?.state?.query?.params?.platform?.twitter?.hashtags ||
          [],
      });
      setGraphData(data?.data);
      setGraphDataLoading(false);
    } catch (error) {
      console.log(error);
    }
  };
  const fetchOffensive = async () => {
    try {
      const { data } = await waveAnalytics.sendWaveData({
        report_type: "offensive",
        platform: "twitter",
        start_date:
          location?.query_start_time ||
          location?.state?.opinionMiningData2?.date_range?.from_date ||
          location?.state?.date_range?.from_date,
        end_date:
          location?.query_end_time ||
          location?.state?.opinionMiningData2?.date_range?.to_date ||
          location?.state?.date_range?.to_date,
        hashtags:
          location?.state?.elements?.map((item) => item?.content) ||
          location?.state?.opinionMiningData2?.query?.platform?.twitter
            ?.hashtags ||
          location?.state?.query?.params?.platform?.twitter?.hashtags ||
          [],
      });
      setOffensiveData(data?.data);
    } catch (error) {
      console.log(error);
    }
  };
  useEffect(() => {
    fetchSentiment();
    fetchEmotions();
    fetchAges();
    fetchGenders();
    fetchTopSources();
    fetchEmotionsAnalysis();
    fetchSentimentAnalysis();
    fetchCategories();
    fetchOffensive();
    fetchGraph();
  }, []);
  return (
    <div className="container grid grid-cols-12 px-4 mx-auto gap-4">
      <div className="col-span-12">
        <div className="bg-light-neutral-surface-card rounded-[8px] p-6 flex justify-between">
          <div className="font-button-medium">
            <DateFilter
              opinionMiningData={opinionMiningData}
              setOpinionMiningData={setOpinionMiningData}
            />
          </div>
          <div className="flex items-center gap-2">
            <div className="w-fit" onClick={() => setShowPopup(true)}>
              <CButton rightIcon={<FloppyDisk size={18} />}>
                ذخیره افکارسنجی
              </CButton>
            </div>
            {/* <div className="w-fit">
              <CButton mode="outline" rightIcon={<FilePdf size={18} />}>
                خروجی
              </CButton>
            </div> */}
          </div>
        </div>
      </div>
      <div className="col-span-12">
        <PoliticalSpectrumContribution
          graphDataLoading={graphDataLoading}
          graphData={graphData?.count}
        />
      </div>
      <div className="col-span-12">
        <div className="bg-light-neutral-surface-card rounded-[8px] p-6">
          <p className="font-subtitle-large pb-4">
            شبکه مشارکت کاربران بر اساس طیف‌های سیاسی
          </p>
          <NetworkGraphCategoryChart
            graphDataLoading={graphDataLoading}
            graphData={graphData}
          />
        </div>
      </div>
      <div className="col-span-9">
        <PoliticalFactionEmotions emotionsData={emotionsData} />
      </div>
      <div className="col-span-3">
        <EmotionAnalysisPercentage emotionsAnalysis={emotionsAnalysis} />
      </div>
      <div className="col-span-3">
        <SentimentAnalysisPercentage sentimentAnalysis={sentimentAnalysis} />
      </div>
      <div className="col-span-9">
        <PoliticalSentimentAnalysis sentimentData={sentimentData} />
      </div>
      {/* <div className="col-span-9">
        <PoliticalAgeAverageAnalysis agesData={agesData?.age_groups} />
      </div>
      <div className="col-span-3">
        <AgeClassification data={agesData?.age_aggs} />
      </div> */}
      <div className="col-span-3">
        <AgePercentageClassification gendersData={gendersData} />
      </div>
      <div className="col-span-9">
        <PoliticalGenderAverageAnalysis gendersData={gendersData} />
      </div>
      <div className="col-span-12">
        <PoliticalParties
          emotionsData={emotionsData}
          sentimentData={sentimentData}
          topSourcesData={topSourcesData}
          gendersData={gendersData}
          agesData={agesData}
          offensiveData={offensiveData}
          categoriesData={categoriesData}
          graphData={graphData.count}
        />
      </div>
      <PopUp
        isOpen={showPopup}
        onClose={() => setShowPopup(false)}
        submitHandler={saveOpinionMiningHandler}
        agreeButton="ذخیره"
        title={"ذخیره گزارش افکار سنجی"}
      >
        <p className="font-overline-large p-4">
          این گزارش را با نتایج موجود ذخیره کنید تا در آینده بتوانید به آن رجوع
          کنید.
        </p>
        <label htmlFor="" className="font-overline-large">
          عنوان گزارش
        </label>
        <CInput
          value={opinionMiningData?.title}
          onChange={(e) =>
            setOpinionMiningData((prev) => ({
              ...prev,
              title: e?.target?.value,
            }))
          }
        />
      </PopUp>
      <ToastContainer />
    </div>
  );
};

export default WaveAnalysisThoughts;
