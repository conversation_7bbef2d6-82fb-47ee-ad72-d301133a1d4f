import Divider from "components/ui/Divider";

import BulletinBadge from "./BulletinBadge.jsx";
import { InstagramLogo, Rss, TelegramLogo, XLogo } from "@phosphor-icons/react";

const BulletinDetailsContent = ({ bulletinData }) => {
  const getChartTitleInPersian = (value) => {
    switch (value) {
      case "process":
        return "نمودار روند انتشار";
      case "info":
        return "آمار کلی عددی";
      case "sentiment":
        return "تحلیل احساسات";
      case "age":
        return "دسته‌بندی سن";
      case "gender":
        return "دسته‌بندی جنسیت";
      case "category":
        return "دسته‌بندی موضوعی";
      case "top_source":
        return "برترین منابع";
      case "hashtag_list":
        return "هشتگ‌های پرتکرار";
      case "hashtag_cloud":
        return "ابرهشتگ‌های پرتکرار";
      case "word_list":
        return "لیست کلمات پرتکرار";
      case "word_cloud":
        return "ابر کلمات پرتکرار";
      case "offensive":
        return "محتوای توهین‌آمیز";
      default:
        return "عنوان نامشخص"; // Default case if the value doesn't match
    }
  };

  return (
    <>
      <div className="flex items-center">
        <p className="font-body-medium w-28">اطلاعات بولتن</p>
        <Divider />
      </div>

      <div className="flex items-center justify-between my-8">
        <p className="font-body-medium text-light-neutral-text-medium">
          عنوان بولتن
        </p>
        <p className="font-body-large">{bulletinData?.title}</p>
      </div>

      {bulletinData?.description && (
        <div className="flex items-center justify-between my-8">
          <p className="font-body-medium text-light-neutral-text-medium">
            توضیحات بولتن
          </p>
          <p className="font-body-large">{bulletinData?.description}</p>
        </div>
      )}

      <div className="flex items-center justify-between my-8">
        <p className="font-body-medium text-light-neutral-text-medium">
          نوع بولتن
        </p>
        <p className="font-body-large">
          {bulletinData?.bulletin_type === "periodic" ? "دوره‌ای" : "موردی"}
        </p>
      </div>

      <div className="flex items-center">
        <p className="font-body-medium pl-3">محتوا</p>
        <Divider />
      </div>

      <div className="flex items-start justify-between my-8">
        <p className="font-body-medium text-light-neutral-text-medium">
          محتوای انتخاب شده
        </p>
        <div className="grid grid-cols-4 gap-2 [direction:ltr]">
          {bulletinData?.content?.platform?.telegram?.map((item) => (
            <BulletinBadge
              key={item}
              icon={<TelegramLogo />}
              iconBgColor={"#0084C7"}
              title={`${bulletinData?.chart?.q || bulletinData?.title} (${
                item?.content_count
              }) `}
            />
          ))}

          {bulletinData?.content?.platform?.instagram?.map((item) => (
            <BulletinBadge
              key={item}
              icon={<InstagramLogo />}
              iconBgColor={"#E64787"}
              title={`${bulletinData?.chart?.q || bulletinData?.title} (${
                item?.content_count
              }) `}
            />
          ))}

          {bulletinData?.content?.platform?.twitter?.map((item) => (
            <BulletinBadge
              key={item}
              icon={<XLogo />}
              iconBgColor={"#000"}
              title={`${bulletinData?.chart?.q || bulletinData?.title} (${
                item?.content_count
              }) `}
            />
          ))}

          {bulletinData?.content?.platform?.news?.map((item) => (
            <BulletinBadge
              key={item}
              icon={<Rss />}
              iconBgColor={"#ECA213"}
              title={`${bulletinData?.chart?.q || bulletinData?.title} (${
                item?.content_count
              }) `}
            />
          ))}
        </div>
      </div>

      <div>
        <div className="flex items-center">
          <p className="font-body-medium pl-3">نمودار</p>
          <Divider />
        </div>
        <div className="flex items-start justify-between my-8">
          <p className="font-body-medium text-light-neutral-text-medium">
            کلمه کلیدی
          </p>

          <p className="font-body-large">{bulletinData?.chart?.q}</p>
        </div>

        {bulletinData?.chart?.platform?.twitter && (
          <div className="flex items-start justify-between my-8">
            <div className="flex items-center gap-1">
              <div
                className={`rounded-md w-[20px] h-[20px] flex items-center justify-center text-white bg-[#000]`}
              >
                <XLogo />
              </div>
              <p className="font-body-medium text-light-neutral-text-medium">
                نمودارهای ایکس
              </p>
            </div>
            <div className="flex items-center flex-wrap gap-1 w-[568PX] [direction:ltr]">
              {bulletinData?.chart?.platform?.twitter?.chart_type?.map(
                (item) => (
                  <BulletinBadge
                    key={item}
                    img={item}
                    iconBgColor={"#000"}
                    title={getChartTitleInPersian(item)}
                  />
                ),
              )}
            </div>
          </div>
        )}

        {bulletinData?.chart?.platform?.instagram && (
          <div className="flex items-start justify-between my-8">
            <div className="flex items-center gap-1">
              <div
                className={`rounded-md w-[20px] h-[20px] flex items-center justify-center text-white bg-[#E64787]`}
              >
                <InstagramLogo />
              </div>
              <p className="font-body-medium text-light-neutral-text-medium">
                نمودارهای اینستاگرام
              </p>
            </div>

            <div className="flex items-center flex-wrap gap-1 w-[568PX] [direction:ltr]">
              {bulletinData?.chart?.platform?.instagram?.chart_type?.map(
                (item) => (
                  <BulletinBadge
                    key={item}
                    img={item}
                    iconBgColor={"#E64787"}
                    title={getChartTitleInPersian(item)}
                  />
                ),
              )}
            </div>
          </div>
        )}

        {bulletinData?.chart?.platform?.telegram && (
          <div className="flex items-start justify-between my-8">
            <div className="flex items-center gap-1">
              <div
                className={`rounded-md w-[20px] h-[20px] flex items-center justify-center text-white bg-[#0084C7]`}
              >
                <TelegramLogo />
              </div>
              <p className="font-body-medium text-light-neutral-text-medium">
                نمودارهای تلگرام
              </p>
            </div>

            <div className="flex items-center flex-wrap gap-1 w-[568PX] [direction:ltr]">
              {bulletinData?.chart?.platform?.telegram?.chart_type?.map(
                (item) => (
                  <BulletinBadge
                    key={item}
                    img={item}
                    iconBgColor={"#0084C7"}
                    title={getChartTitleInPersian(item)}
                  />
                ),
              )}
            </div>
          </div>
        )}

        {bulletinData?.chart?.platform?.news && (
          <div className="flex items-start justify-between my-8">
            <div className="flex items-center gap-1">
              <div
                className={`rounded-md w-[20px] h-[20px] flex items-center justify-center text-white bg-[#ECA213]`}
              >
                <Rss />
              </div>
              <p className="font-body-medium text-light-neutral-text-medium">
                نمودارهای سایت‌های خبری
              </p>
            </div>
            <div className="flex items-center flex-wrap gap-1 w-[568PX] [direction:ltr]">
              {bulletinData?.chart?.platform?.news?.chart_type?.map((item) => (
                <BulletinBadge
                  key={item}
                  img={item}
                  title={getChartTitleInPersian(item)}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default BulletinDetailsContent;
