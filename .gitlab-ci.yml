stages:
  - build
  - deploy

workflow:
  rules:
    - if: $CI_COMMIT_TAG != null
      variables:
        TAG: $CI_COMMIT_TAG
    - if: $CI_COMMIT_TAG == null
      variables:
        TAG: $CI_COMMIT_SHORT_SHA


docker-build-stage:
  stage: build
  tags:
    - builder
  before_script:
    - echo "$HARBOR_PASSWORD" | docker login --password-stdin --username "$HARBOR_USER" -- "$HARBOR_REGISTRY"
  script:
    - docker build --network host -t $HARBOR_REGISTRY/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:$TAG -f Dockerfile.dev .
    - docker push $HARBOR_REGISTRY/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:$TAG
  only:
    - Development

stage_deploy:
  stage: deploy
  tags:
    - front-stage
  before_script:
    - echo "$HARBOR_PASSWORD" | docker login --password-stdin --username "$HARBOR_USER" -- "$HARBOR_REGISTRY"
  script:
   - echo "Stopping and removing existing container..."
   - docker stop user-panel-stage || true
   - docker rm user-panel-stage || true
   - docker pull $HARBOR_REGISTRY/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:$TAG
   - docker run -itd --name user-panel-stage -p 80:80 --restart always $HARBOR_REGISTRY/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:$TAG
  only:
    - Development

docker-build-prod:
  stage: build
  tags:
    - builder
  before_script:
    - echo "$HARBOR_PASSWORD" | docker login --password-stdin --username "$HARBOR_USER" -- "$HARBOR_REGISTRY"
  script:
    - docker build --network host -t $HARBOR_REGISTRY/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:$TAG -f Dockerfile.prod .
    - docker push $HARBOR_REGISTRY/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:$TAG
  rules:
    - if: $CI_COMMIT_TAG

.deploy_script:
  script: &deploy_script
    - >
      helm create deploy
    - >
      helm upgrade
      --namespace=analytics-team
      --set image.repository=$HARBOR_REGISTRY/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME
      --set image.tag=$TAG
      --set imagePullSecrets[0].name=harbor-auth
      --set ingress.enabled=true
      --set ingress.hosts[0].host=synappse.ir,ingress.hosts[0].paths[0].path=/,ingress.hosts[0].paths[0].pathType=Prefix
      --force
      --install
      $CI_PROJECT_NAME
      deploy

deploy_prod:
  stage: deploy
  tags:
    - builder
  script: *deploy_script
  rules:
    - if: $CI_COMMIT_TAG
