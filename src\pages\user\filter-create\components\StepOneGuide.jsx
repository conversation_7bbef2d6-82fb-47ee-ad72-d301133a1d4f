import TextWithBullet from "components/TextWithBullet";
const StepOneGuide = () => {
  return (
    <div className="font-body-medium">
      <h3 className="mb-4">راهنما</h3>
      <div className="flex flex-col gap-4">
        <TextWithBullet>
          <span>
            فیلترها را می‌توان از طریق نامشان در سایر صفحات جست‌وجو کرد بنابراین
            برای فیلتر خود عنوانی مناسب تعیین نمایید.
          </span>
        </TextWithBullet>
        <TextWithBullet>
          <span>
            توصیه می‌شود برای این فیلتر توضیحات مختصری ثبت کنید تا در صورت
            اشتراک این فیلتر برای دیگران قابل فهم باشد.
          </span>
        </TextWithBullet>
        <TextWithBullet>
          <span>تکمیل فیلد توضیحات اجباری</span>
          <span className="underline underline-offset-4"> نیست. </span>
        </TextWithBullet>
        <TextWithBullet>
          <span>
            در صورتی که بخواهید می‌توانید این موارد را پس از ثبت، ویرایش کنید.
          </span>
        </TextWithBullet>
      </div>
    </div>
  );
};

export default StepOneGuide;
