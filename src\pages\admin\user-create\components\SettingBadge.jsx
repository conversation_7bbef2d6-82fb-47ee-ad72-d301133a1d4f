const SettingBadge = ({ text, src, selected }) => {
  return (
    <div
      className="w-full h-[200px] rounded-lg border border-light-neutral-border-low-rest flex flex-col items-center gap-4 justify-center"
      style={{
        ...(selected
          ? {
              backgroundColor: "#B4ABE34D",
              borderColor: "#B4ABE34D",
              border: "2px solid #4D36BF",
            }
          : {}),
      }}
    >
      <img src={src} className="size-32" />
      <span className="font-body-medium">{text}</span>
    </div>
  );
};

export default SettingBadge;
