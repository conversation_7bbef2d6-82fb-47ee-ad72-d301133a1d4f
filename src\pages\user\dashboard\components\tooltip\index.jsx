import "./index.css";
const ToolTip = ({ children, title = "عنوان" }) => {
  return (
    <div className="tooltip border-[0.5px] border-light-neutral-border-low-rest w-[300px] p-3 rounded-lg text-light-neutral-text-high flex flex-col gap-2 bg-white relative drop-shadow-[0px_2px_20px_0px_#0000000D]">
      <div className="font-body-large">
        <span>در مجموعه</span>
        <span> {title} </span>
        <span>در رابطه با این موارد صحبت می‌شود:</span>
      </div>
      <ul className="flex flex-col gap-2 list-disc px-6">
        {children?.map((item, index) => (
          <li key={index} className="break-all font-body-medium">
            {item}
          </li>
        ))}
        {children.length > 4 && <li>...</li>}
      </ul>
      <div className="size-[10px] rotate-45 absolute bg-white -bottom-[5px] left-1/2 border-b-[0.5px] border-r-[0.5px] border-light-neutral-border-low-rest"></div>
    </div>
  );
};

export default ToolTip;
