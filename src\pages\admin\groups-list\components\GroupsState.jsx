import clsx from "clsx";

const GroupsState = ({ title, state }) => {
  const stepStateClassName = clsx(
    "font-body-small rounded-[4px] px-1 w-20 text-center",
    {
      "text-light-error-text-rest bg-light-error-background-highlight":
        state === "Inactive" || state === "Deleted",
      "text-light-warning-text-rest bg-light-warning-background-highlight":
        state === "Pending" || state === "replied",
      "text-light-success-text-rest bg-light-success-background-highlight":
        state === "Active",
    }
  );
  return <div className={stepStateClassName}>{title}</div>;
};

export default GroupsState;
