import { fixPercentToShow } from "utils/helper";
import Doughnut from "components/Charts/Doughnut.jsx";
import { useEffect, useState } from "react";
import { useMeasure } from "react-use";
import Divider from "components/ui/Divider.jsx";
import { SpinnerGap } from "@phosphor-icons/react";
import MediaBadge from "components/ui/MediaBadge.jsx";
import { Card } from "components/ui/Card.jsx";

const PlatformDistribution = ({ filter }) => {
  const [data, setData] = useState({});
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [ref, { width }] = useMeasure();

  const getData = async () => {
    setLoading(true);
    try {
      const converted = {};
      [
        { key: "twitter", count: 200 },
        { key: "telegram", count: 100 },
        { key: "instagram", count: 50 },
        { key: "news", count: 150 },
      ].forEach((item) => {
        converted[item.key] = item.count / 500;
      });

      setData(converted);
      setTotal(500);
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  useEffect(() => {
    getData();
  }, [filter]);

  const chartData = [
    { key: "twitter", name: "ایکس", y: data?.twitter, color: "#181818" },
    { key: "telegram", name: "تلگرام", y: data?.telegram, color: "#49A6D5" },
    {
      key: "instagram",
      name: "اینستاگرام",
      y: data?.instagram,
      color: "#E052B8",
    },
    { key: "news", name: "وبسایت‌های خبری", y: data?.news, color: "#ECA213" },
  ];

  return (
    <Card className="flex flex-col gap-2 h-full" ref={ref}>
      <p className="font-subtitle-large text-right">
        توزیع فیلترها به نسبت بسترها
      </p>
      {loading ? (
        <div className="w-full h-[365px] flex justify-center items-center">
          <SpinnerGap size={40} className="animate-spin" />
        </div>
      ) : Object.keys(data).length === 0 ? (
        <div className="h-[270px] flex items-center justify-center font-subtitle-medium">
          داده ای برای نمایش وجود ندارد
        </div>
      ) : (
        <div>
          <Doughnut
            showDataLabels={false}
            colLayout
            minWidth="100px"
            height={250}
            data={chartData.map(({ key, name, y }) => ({ key, name, y }))} // Pass only necessary data to Highcharts
            legendFormatter={() => ""} // Disable default legend rendering
            showInLegend={false} // Disable Highcharts legend
            tooltipFormatter={function () {
              return `<div style="display:flex;flex-direction:column;gap:8px;text-align:center;font-family:iranyekan"><div>${
                this.key
              }</div><div>${fixPercentToShow(this.y)}</div></div>`;
            }}
            colors={chartData.map((item) => item.color)}
          />
          {/* Custom Legend */}
          <div dir="ltr" className="flex flex-col gap-2 mt-4 px-4">
            {chartData.map((item) => (
              <div key={item.key} className="flex items-center justify-between">
                <span className="text-lg">{fixPercentToShow(item.y)}</span>
                <div className="flex items-center gap-2 justify-end">
                  <span style={{ color: item.color }}>{item.name}</span>
                  <MediaBadge media={item.key} />
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </Card>
  );
};

export default PlatformDistribution;
