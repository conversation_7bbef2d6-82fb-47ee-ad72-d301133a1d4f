const HighlightBaseCard = ({ color }) => {
  return (
    <div className="size-6 rounded-md bg-light-neutral-background-medium flex items-center justify-center cursor-pointer">
      <div
        className="size-[13px] flex rounded-full border-2  rotate-45"
        style={{ borderColor: color }}
      >
        <div
          className="size-full rounded-s-full"
          style={{ backgroundColor: color }}
        ></div>
        <div className="size-full"></div>
      </div>
    </div>
  );
};

export default HighlightBaseCard;
