import BulletinCard from "./BulletinCard";
const List = ({ data, setRerender, setLoading }) => {
  const selectMedia = {
    telegram: "telegram",
    bale: "bale",
    eitaa: "eitaa",
  };

  return (
    <div className="flex flex-col gap-4">
      {data?.map((item) => (
        <BulletinCard
          media={selectMedia[item.messenger]}
          key={item.id}
          data={item}
          bulletin_id={item.id}
          setRerender={setRerender}
          setLoading={setLoading}
        />
      ))}
    </div>
  );
};

export default List;
