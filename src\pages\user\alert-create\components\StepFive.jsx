import { useState } from "react";
import AlertAndFilterDetails from "components/alertAndFilterDetails";
import alert from "service/api/alert";
import { useNavigate } from "react-router-dom";
import { ToastContainer } from "react-toastify";
import { notification } from "utils/helper";
import { WarningDiamond } from "@phosphor-icons/react";
import Loading from "components/ui/Loading";
import { CButton } from "components/ui/CButton.jsx";
const StepFive = ({ alertData, selectFilter, setStatus, state }) => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const createAlert = async () => {
    setLoading(true);
    try {
      const { data } = state
        ? await alert.update(alertData.id, alertData)
        : await alert.new(alertData);
      navigate("/app/alert/list", {
        state: { sucess: true, message: data.message },
      });
    } catch (error) {
      console.log(error.response.data.message);
      notification.error(
        error?.response?.data?.message,
        <WarningDiamond size={32} className="text-light-error-text-rest" />
      );
    }
    setLoading(false);
  };

  return (
    <>
      {loading && <Loading />}
      <div className="p-6 flex flex-col gap-12">
        <AlertAndFilterDetails
          data={alertData}
          selectFilter={selectFilter}
          isFilter={false}
        />
        <div className="flex flex-row-reverse">
          <CButton
            type={"submit"}
            className={`!w-[200px]`}
            onClick={() => createAlert()}
          >
            ثبت و ذخیره
          </CButton>
          <button
            className="w-[100px] h-10 flex justify-center items-center text-light-primary-text-rest font-button-medium"
            onClick={() => {
              setStatus(1);
            }}
          >
            ویرایش
          </button>
        </div>
      </div>
      <ToastContainer />
    </>
  );
};

export default StepFive;
