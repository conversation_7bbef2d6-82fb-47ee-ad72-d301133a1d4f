import { useEffect, useState } from "react";
import Loading from "components/ui/Loading";
import { ToastContainer } from "react-toastify";
import Empty from "./components/Empty";
import Wrapper from "./components/Wrapper";
import { useBreadcrumb } from "hooks/useBreadcrumb";
import TicketsTable from "./components/TicketsTable";
import ticket from "service/api/ticket";

const TicketList = () => {
  const [isLoading, setIsLoading] = useState(false);
  const breadcrumbList = [{ title: "تیکت‌" }];
  useBreadcrumb(breadcrumbList);
  const [tickets, setTickets] = useState([]);

  const getTickets = async () => {
    setIsLoading(true);
    try {
      const {
        data: { data },
      } = await ticket.getAll();
      setTickets(data);
    } catch (error) {
      console.log(error.response.data.message);
    }
    setIsLoading(false);
  };
  useEffect(() => {
    getTickets();
  }, []);

  return (
    <>
      {isLoading ? (
        <Loading />
      ) : (
        <>
          <div className="flex flex-col h-full w-full px-6">
            <Wrapper>
              {tickets.length > 0 ? (
                tickets.map((item) => (
                  <TicketsTable key={item.id} data={item} />
                ))
              ) : (
                <Empty />
              )}
            </Wrapper>
          </div>
        </>
      )}
      <ToastContainer />
    </>
  );
};

export default TicketList;
