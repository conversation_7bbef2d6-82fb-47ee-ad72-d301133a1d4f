import StepOne from "./components/step-one/index.jsx";
import StepTwo from "./components/step-two/index.jsx";
import { useCompareStore } from "store/compareStore.js";
import PropTypes from "prop-types";
import { useCallback } from "react";

const CompareCreate = ({ isEdit = false }) => {
  const { step } = useCompareStore((state) => state.compare);
  const setCompare = useCompareStore((state) => state.setCompare);

  // Memoize the setCompare function
  const handleNextStep = useCallback(() => setCompare({ step: 2 }), []);

  return (
    <>
      {step === 1 ? (
        <StepOne nextStep={handleNextStep} />
      ) : (
        <StepTwo isEdit={isEdit} />
      )}
    </>
  );
};

CompareCreate.propTypes = {
  isEdit: PropTypes.bool,
};

export default CompareCreate;
