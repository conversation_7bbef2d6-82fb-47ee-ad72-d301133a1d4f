import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";
import StepOne from "./components/step-1";
import StepTwo from "./components/step-2";
import { useState } from "react";

const AnnouncementManagement = () => {
  const [step, setStep] = useState(1);
  const breadcrumbList = [{ title: "اطلاعیه‌ها" }];
  useBreadcrumb(breadcrumbList);

  return (
    <div className="container px-4 mx-auto mt-8">
      <div>
        {step === 1 ? (
          <StepOne setStep={setStep} />
        ) : (
          <StepTwo setStep={setStep} />
        )}
      </div>
    </div>
  );
};

export default AnnouncementManagement;
