import clsx from "clsx";

const ChangeListState = ({ title, state }) => {
  const stepStateClassName = clsx(
    "font-body-small rounded-[4px] px-1 w-20 text-center",
    {
      "text-light-error-text-rest bg-light-error-background-highlight":
        state === "Unsuccessful",
      "text-light-success-text-rest bg-light-success-background-highlight":
        state === "successful" || state === "Active",
    }
  );
  return <div className={stepStateClassName}>{title}</div>;
};

export default ChangeListState;
