import { ChartDonut, FileText } from "@phosphor-icons/react";
import Divider from "components/ui/Divider";
import { useBreadcrumb } from "hooks/useBreadcrumb";
import { useCallback, useEffect, useState } from "react";
import WaveInfo from "./wave-info";
import WaveRelatedInfo from "./WaveRelatedInfo";
import { useLocation } from "react-router-dom";
import advanceSearch from "service/api/advanceSearch.js";
import DropDown from "components/ui/DropDown";
import { buildRequestData } from "utils/requestData";
import { DownloadExcelButton } from "components/DownloadExcelButton";

const WaveAnalysisId = () => {
  const location = useLocation();
  const { id, title, date, elements, query_start_time, query_end_time } =
    location.state || {};
  const trendsDate = new Date(date);
  const [activeTab, setActiveTab] = useState("تحلیل موج");
  const [data, setData] = useState([]);
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);
  const from = new Date(
    trendsDate.getFullYear(),
    trendsDate.getMonth(),
    trendsDate.getDate()
  );
  const to = new Date(
    trendsDate.getFullYear(),
    trendsDate.getMonth(),
    trendsDate.getDate() + 1
  );
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const SORT_TYPE = {
    telegram: [
      { fa: "زمان انتشار", en: "date" },
      { fa: "بازدید", en: "view" },
    ],
    twitter: [
      { fa: "زمان انتشار", en: "date" },
      { fa: "ریتوئیت", en: "retweet" },
      { fa: "بازدید", en: "view" },
      { fa: "لایک", en: "like" },
      { fa: "بوکمارک", en: "bookmark" },
    ],
    instagram: [
      { fa: "زمان انتشار", en: "date" },
      { fa: "لایک", en: "like" },
      { fa: "نظرات", en: "comment" },
    ],
    news: [
      { fa: "زمان انتشار", en: "date" },
      // { fa: "تعداد نظرات", en: "comment_count" },
    ],
  };

  const [sort, setSort] = useState({ fa: "زمان انتشار", en: "date" });
  const breadcrumbList = [
    { title: "موج شناسی", link: "/app/wave-analysis/list" },
    { title },
  ];
  useBreadcrumb(breadcrumbList);
  const tabs = [
    { title: "تحلیل موج", icon: ChartDonut },
    {
      title: "محتوای مرتبط با موج",
      icon: FileText,
    },
  ];

  const handleQuery = useCallback(() => {
    return buildRequestData({
      page,
      sort: sort.en || "date",
      sources: [],
      platform: "twitter",
      rows: 12,
      q: "*",
      date: {
        from: query_start_time,
        to: query_end_time,
      },
      hashtags: elements.map((item) => item?.content),
    });
  }, [page, sort]);

  const fullAnalysisHandler = async () => {
    try {
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(today.getDate() - 1);

      const req = buildRequestData(
        {
          page,
          sort: sort.en || "date",
          sources: [],
          platform: "twitter",
          rows: 12,
          q: "*",
          date: {
            from: query_start_time,
            to: query_end_time,
          },
          hashtags: elements.map((item) => item?.content),

          // end_date: query_end_time,
          // report_type: "advance",
          // start_date: query_start_time,
        },
        "advance",
        12
      );

      const response = await advanceSearch.search(req);
      setData(response?.data?.data?.twitter || []);
      setTotal(response?.data?.data?.total);
    } catch (error) {
      console.log("Error fetching wave data:", error);
      setData([]);
    }
  };

  useEffect(() => {
    fullAnalysisHandler();
  }, [page, sort]);

  return (
    <>
      <div className="grid grid-cols-12 gap-4 px-6">
        <div className="col-span-12 flex items-center justify-between gap-6 font-body-medium">
          <div className="flex items-center gap-6">
            {tabs?.map((item) => {
              const IconComponent = item.icon;
              return (
                <div key={item?.title} className="flex items-center gap-3">
                  <IconComponent size={20} />
                  <p
                    className={`cursor-pointer ${
                      item?.title === activeTab
                        ? "border-b-2 border-light-primary-text-rest text-black"
                        : "text-light-neutral-text-medium"
                    }`}
                    onClick={() => setActiveTab(item?.title)}
                  >
                    {`${item?.title}`}
                  </p>
                </div>
              );
            })}
          </div>
        </div>

        <div className="col-span-12">
          <Divider />
        </div>
      </div>

      {activeTab === "تحلیل موج" ? (
        <div>
          <WaveInfo from={from} to={to} id={id} />
        </div>
      ) : activeTab === "محتوای مرتبط با موج" ? (
        <>
          <div className="flex justify-end pl-6 items-center gap-2 mt-4">
            <div className="flex justify-end w-fit ml-2 ">
              <DownloadExcelButton
                size="lg"
                platform={"twitter"}
                q={handleQuery()}
              >
                دانلود گزارش اکسل
              </DownloadExcelButton>
            </div>
            <div className="w-fit">
              <DropDown
                title="نمایش بر اساس"
                subsets={SORT_TYPE["twitter"].map((item) => item.fa)}
                selected={sort.fa}
                setSelected={(value) => {
                  setSort(
                    SORT_TYPE["twitter"].filter((item) => item.fa === value)[0]
                  );
                }}
                onOpenChange={() => {
                  setIsDropdownOpen(!isDropdownOpen);
                }}
              />
            </div>
          </div>
          <WaveRelatedInfo
            data={data}
            setPage={setPage}
            page={page}
            total={total}
          />
        </>
      ) : (
        ""
      )}
    </>
  );
};

export default WaveAnalysisId;
