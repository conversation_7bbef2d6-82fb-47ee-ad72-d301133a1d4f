import { EyeClosed, Eye } from "@phosphor-icons/react";
const Input = ({
  label,
  value,
  placeholder,
  controller,
  onFocus,
  onBlur,
  readOnly,
}) => {
  return (
    <div className="">
      <label
        className="font-overline-large text-light-neutral-text-high mr-1 mb-1 inline-block"
        htmlFor={label}
      >
        {label}
      </label>
      <div className="w-full h-12 rounded-md border border-light-neutral-border-medium-rest px-4 py-3 font-body-large flex items-center justify-between gap-4">
        <div
          onClick={() =>
            controller((l) => {
              return { ...l, showValue: !l.showValue };
            })
          }
          className="hover:cursor-pointer"
        >
          {value.showValue ? <EyeClosed size={18} /> : <Eye size={18} />}
        </div>
        <input
          id={label}
          type={value.showValue ? "text" : "password"}
          className="w-full placeholder:font-body-medium outline-none [direction:ltr]"
          placeholder={value.value || placeholder}
          value={value.value}
          onChange={(e) =>
            controller((l) => {
              return { ...l, value: e.target.value };
            })
          }
          onFocus={onFocus}
          onBlur={onBlur}
          readOnly={readOnly}
        />
      </div>
    </div>
  );
};

export default Input;
