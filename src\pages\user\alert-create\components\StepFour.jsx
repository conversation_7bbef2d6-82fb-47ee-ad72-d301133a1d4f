import { useEffect, useState } from "react";
import {
  Caret<PERSON>eft,
  InstagramLogo,
  IntersectThree,
  Rss,
  TelegramLogo,
  TwitterLogo,
} from "@phosphor-icons/react";
import <PERSON>itaaLogo from "components/ui/EitaaLogo";
import AllFilter from "components/FiltersOfPlatform/AllFilter";
import TelegramFilter from "components/FiltersOfPlatform/TelegramFilter";
import NewsFilter from "components/FiltersOfPlatform/NewsFilter";
import EitaaFilters from "components/FiltersOfPlatform/EitaaFilters";
import TwitterFilter from "components/FiltersOfPlatform/TwitterFilter";
import InstagramFilter from "components/FiltersOfPlatform/InstagramFilter";
import MediaBadge from "components/ui/MediaBadge.jsx";
import Accordion from "components/ui/Accordion.jsx";
import { CInput } from "components/ui/CInput";
import { Sliders } from "@phosphor-icons/react";
import CustomSearchDrawer from "../../../../components/CustomSearchDrawer";
import { CButton } from "components/ui/CButton.jsx";
import useSearchStore from "store/searchStore.js";
import PropTypes from "prop-types";

const StepFour = ({ alertData, setStatus, setAlertData }) => {
  const { setFilters } = useSearchStore();
  const [touchedQ, setTouchedQ] = useState(false);
  const [q, setQ] = useState(alertData?.q || "");
  const [editQ, setEditQ] = useState(false);
  const [isOpen, setIsOpen] = useState(null);
  const [showAdvancedSearchDrawer, setShowAdvancedSearchDrawer] =
    useState(false);

  useEffect(() => {
    setAlertData((l) => {
      const copy = JSON.parse(JSON.stringify(l));
      copy.q = q;
      return copy;
    });
  }, [q]);

  const selectHeader = {
    all: {
      name: "همه",
      icon: <IntersectThree className="size-4" color="#000000" />,
    },
    twitter: {
      name: "توئیتر",
      icon: <TwitterLogo className="size-4" color="#000000" />,
    },
    telegram: {
      name: "تلگرام",
      icon: <TelegramLogo className="size-4" color="#000000" />,
    },
    news: {
      name: "سایت‌های خبری",
      icon: <Rss className="size-4" color="#000000" />,
    },
    instagram: {
      name: "اینستاگرام",
      icon: <InstagramLogo className="size-4" color="#000000" />,
    },
    eitaa: { name: "ایتا", icon: <EitaaLogo color="#000000" /> },
  };

  const selectFilters = {
    all: (
      <AllFilter setData={setAlertData} initialValue={alertData.platform.all} />
    ),

    twitter: (
      <TwitterFilter
        setData={setAlertData}
        initialValue={alertData.platform.twitter}
      />
    ),
    telegram: (
      <TelegramFilter
        setData={setAlertData}
        initialValue={alertData.platform.telegram}
      />
    ),
    news: (
      <NewsFilter
        setData={setAlertData}
        initialValue={alertData.platform.news}
      />
    ),

    instagram: (
      <InstagramFilter
        setData={setAlertData}
        initialValue={alertData.platform.instagram}
      />
    ),
    eitaa: (
      <EitaaFilters
        setData={setAlertData}
        initialValue={alertData.platform.eitaa}
      />
    ),
  };

  return (
    <div className="p-6 flex flex-col gap-12">
      {editQ ? (
        <div className="shadow[0px_2px_20px_0px_#0000000D] rounded-lg bg-white p-6 flex flex-col gap-4">
          <div className="flex flex-col gap-2">
            <span className="font-overline-large">کلمات کلیدی</span>
            <CInput
              id={"q2"}
              name={"q2"}
              inset={true}
              readOnly={true}
              size={"md"}
              validation={"none"}
              direction={"rtl"}
              className={"flex-1 mt-2"}
              onFocus={() => setTouchedQ(false)}
              onBlur={() => setTouchedQ(true)}
              value={q}
              field={{}}
              onChange={(e) => {
                setQ(e.target.value);
              }}
              form={{ errors: [], touched: [] }}
              customAction={() => setShowAdvancedSearchDrawer(true)}
              customActionText={<Sliders size={20} />}
            ></CInput>
            <span className="font-body-small text-light-neutral-text-medium">
              از عملوندهای منطقی AND و OR و NOT استفاده کنید.
            </span>
            {touchedQ && q.length < 2 && (
              <span className="font-body-small text-light-error-text-rest">
                طول کلمات کلیدی حداقل باید ۲ کاراکتر باشد
              </span>
            )}
          </div>
          <div className="flex flex-row-reverse">
            <CButton
              type={"submit"}
              className={`!w-[200px]`}
              readOnly={!(alertData.q.length >= 2)}
              onClick={() => {
                if (alertData.q.length >= 2) {
                  setEditQ(false);
                }
              }}
            >
              ثبت کلمات کلیدی
            </CButton>
          </div>
        </div>
      ) : (
        <div className="shadow[0px_2px_20px_0px_#0000000D] rounded-lg bg-white p-6 flex flex-col gap-4">
          <div className="flex justify-between items-center">
            <span className="font-body-medium">کلمات کلیدی</span>
            <button
              className="flex gap-2 text-light-primary-text-rest items-center font-button-medium"
              onClick={() => setEditQ(true)}
            >
              <span>ویرایش</span>
              <CaretLeft />
            </button>
          </div>
          <div className="rounded-lg bg-light-neutral-background-low py-[6px] px-2 self-start border border-light-neutral-border-low-rest font-body-medium">
            {alertData.q}
          </div>
        </div>
      )}
      <div className="flex flex-col gap-6">
        {Object.keys(alertData.platform).map((item, index) => (
          <div
            onClick={() => {
              setFilters({ platform: item });
              setIsOpen(index);
            }}
            key={index}
          >
            <Accordion
              isOpen={index === isOpen}
              Header={
                <div className="flex gap-2">
                  <div>{<MediaBadge media={item} />}</div>
                  <div className="font-body-medium">
                    <span>فیلتر در</span> <span>{selectHeader[item].name}</span>
                  </div>
                </div>
              }
            >
              {selectFilters[item]}
            </Accordion>
          </div>
        ))}
      </div>

      <div className="flex flex-row-reverse bg-white p-6 rounded-lg">
        <CButton
          type={"submit"}
          className={`!w-[200px]`}
          onClick={() => {
            setStatus(5);
          }}
        >
          ادامه
        </CButton>
        <button
          className="w-[100px] h-10 flex justify-center items-center text-light-primary-text-rest font-button-medium"
          onClick={() => {
            setStatus(3);
          }}
        >
          مرحله قبل
        </button>
      </div>

      <div className={!showAdvancedSearchDrawer ? "hidden" : "block"}>
        <CustomSearchDrawer
          inputQuery={q}
          setShowMore={setShowAdvancedSearchDrawer}
          onSubmit={(e) => setQ(e)}
        />
      </div>
    </div>
  );
};

StepFour.propTypes = {
  setStatus: PropTypes.func,
  setAlertData: PropTypes.func,
  alertData: PropTypes.object,
};

export default StepFour;
