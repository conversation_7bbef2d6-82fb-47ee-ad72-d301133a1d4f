import { Info, Plus, Question } from "@phosphor-icons/react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import Drawer from "components/Drawer";
import FilterInfo from "./FilterInfo";
import { CButton } from "components/ui/CButton.jsx";

const FilterListHeader = () => {
  const [showMore, setShowMore] = useState(false);
  const navigate = useNavigate();

  return (
    <div className="pt-6 mb-4">
      <div className="flex items-center flex-row-reverse gap-2 font-button-large text-light-primary-text-rest">
        <div className="w-[130px]" onClick={() => setShowMore(true)}>
          <CButton
            mode="outline"
            size="sm"
            role="neutral"
            className="gap-2 !h-10"
          >
            <Info size={17} className="text-light-neutral-text-medium" />
            <p className="text-light-neutral-text-medium font-button-medium">
              فیلتر چیست
            </p>
          </CButton>
        </div>
        <CButton
          onClick={() => navigate("/app/filter/create")}
          rightIcon={<Plus />}
          width={130}
          className={"[direction:ltr]"}
          mode="outline"
        >
          فیلتر جدید
        </CButton>
      </div>
      {showMore && (
        <Drawer setShowMore={setShowMore}>
          <FilterInfo />
        </Drawer>
      )}
    </div>
  );
};

export default FilterListHeader;
