import { useEffect, useRef, useState } from "react";
import { useLayoutContext } from "context/layout-context";
import AlertListHeader from "./components/AlertListHeader";
import Wrapper from "./components/Wrapper";
import Empty from "./components/Empty";
import List from "./components/List";
import alert from "service/api/alert";
import Loading from "components/ui/Loading";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useLocation } from "react-router-dom";
import { notification } from "utils/helper";
import { CheckCircle } from "@phosphor-icons/react";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";

const AlertList = () => {
  const { state } = useLocation();
  const breadcrumbList = [{ title: "هشدار" }];
  useBreadcrumb(breadcrumbList);

  const [alerts, setAlerts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [rerender, setRerender] = useState(false);
  const countLoadingOffset = useRef();
  const getAlerts = async () => {
    setLoading(true);
    try {
      const {
        data: { data },
      } = await alert.get();
      setAlerts(data);
    } catch (error) {
      console.log(error.response.data.message);
    }
    setLoading(false);
  };

  useEffect(() => {
    if (state?.message) {
      notification.success(
        state.message,
        <CheckCircle className="text-light-success-text-rest" />,
      );
    }
    window.history.replaceState({}, "");
  }, []);

  useEffect(() => {
    getAlerts();
  }, [rerender]);

  return (
    <>
      {loading && <Loading />}
      <div className="flex flex-col w-full px-6 h-full">
        <div ref={countLoadingOffset}>
          <AlertListHeader />
        </div>
        <Wrapper>
          {alerts.length ? (
            <List
              data={alerts}
              setRerender={setRerender}
              setLoading={setLoading}
            />
          ) : (
            <Empty />
          )}
        </Wrapper>
      </div>
      <ToastContainer />
    </>
  );
};

export default AlertList;
