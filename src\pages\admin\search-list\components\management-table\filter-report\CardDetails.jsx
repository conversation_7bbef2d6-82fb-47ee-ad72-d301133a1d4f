import PropTypes from "prop-types";

const CardDetails = ({ title, count, variant, color, icon: Icon }) => {
  return (
    <div
      className="bg-light-neutral-surface-card rounded-lg px-4 py-6"
      style={{
        boxShadow: "0px 2px 20px 0px #0000000D",
      }}
    >
      <div className="flex items-start justify-between">
        <div>
          <p className="font-body-large text-light-neutral-text-medium">
            {title}
          </p>
          <p className="font-title-medium font-semibold" style={{ color }}>
            {count}
          </p>
        </div>

        <div
          className={`rounded-2xl flex items-center justify-center p-3`}
          style={{
            background: variant,
          }}
        >
          <Icon size={28} color={color} />
        </div>
      </div>
    </div>
  );
};

export default CardDetails;

CardDetails.propTypes = {
  title: PropTypes.string,
  children: PropTypes.node,
  className: PropTypes.string,
  onClick: PropTypes.func,
};
