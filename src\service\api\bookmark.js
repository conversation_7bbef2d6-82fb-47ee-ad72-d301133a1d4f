import useFetch from "../index";

class Bookmark {
  getBookmarks(page = 1, collection_title) {
    return useFetch.get(
      `/api/v1/interaction/bookmark/?page=${page}&collection_title=${collection_title}`
    );
  }
  toggleBookmark(data) {
    return useFetch.post(`/api/v1/interaction/bookmark/`, data);
  }
  pinPost(data) {
    return useFetch.patch("/api/v1/interaction/bookmark/", data);
  }
}
export default new Bookmark();
