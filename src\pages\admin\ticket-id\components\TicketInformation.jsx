import { CButton } from "components/ui/CButton";
import { useState } from "react";
import Popup from "components/ui/PopUp";
import { CheckCircle, Info, XCircle } from "@phosphor-icons/react";
import { notification } from "utils/helper";
import { parseTimeToPersian } from "utils/helper";
import { useNavigate } from "react-router-dom";
import ticket from "service/api/ticket";
import TicketState from "../../ticket-list/components/TicketState";
import { parseTimeToPersianSummary } from "utils/helper";

const TicketInformation = ({ ticketData, closed_date, isClosed }) => {
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const navigate = useNavigate();
  const closeTicketHandler = async () => {
    try {
      await ticket.toggleAdminTicketState(ticketData?.id, { status: "closed" });
      navigate("/admin/ticket/list");
      notification.info(
        `تیتک شما بسته شد`,
        <CheckCircle size={25} className="text-light-inform-text-rest" />
      );
      setIsPopupOpen(false);
    } catch (error) {
      console.log(error);
    }
  };
  return (
    <>
      <div>
        <p className="font-subtitle-medium">اطلاعات تیکت</p>
        <div className="">
          <div className="flex items-center justify-between py-2">
            <p className="font-body-medium text-light-neutral-text-medium">
              تاریخ ایجاد
            </p>
            <span className="font-body-medium">
              {parseTimeToPersian(ticketData?.created_at)}
            </span>
          </div>

          <div className="flex items-center justify-between py-2">
            <p className="font-body-medium text-light-neutral-text-medium">
              تاریخ به‌روزرسانی
            </p>
            <span className="font-body-medium">
              {parseTimeToPersian(ticketData?.updated_at)}
            </span>
          </div>

          <div className="flex items-center justify-between py-2">
            <p className="font-body-medium text-light-neutral-text-medium">
              موضوع تیکت
            </p>
            <span className="font-body-medium">{ticketData?.title}</span>
          </div>

          <div className="flex items-center justify-between py-2">
            <p className="font-body-medium text-light-neutral-text-medium">
              واحد مربوطه
            </p>
            <span className="font-body-medium">
              {ticketData?.ticket_section === "support"
                ? "پشتیبانی"
                : ticketData?.ticket_section === "technical"
                ? "فنی"
                : ticketData?.ticket_section === "finances"
                ? "مالی و اداری"
                : ""}
            </span>
          </div>
          <div className="flex items-center justify-between py-2">
            <p className="font-body-medium text-light-neutral-text-medium">
              وضعیت تیکت
            </p>
            <TicketState
              title={
                ticketData?.ticket_state === "reopen"
                  ? "در ‌حال‌ بررسی"
                  : ticketData?.ticket_state === "open"
                  ? "در‌ حال‌ بررسی"
                  : ticketData?.ticket_state === "closed"
                  ? "پایان‌ یافته"
                  : ticketData?.ticket_state === "resolved" ||
                    ticketData?.ticket_state === "replied"
                  ? "منتظر‌ پاسخ"
                  : ""
              }
              state={ticketData?.ticket_state}
            />
          </div>
        </div>

        <hr className="my-3" />

        <div className="mb-3">
          {ticketData?.ticket_state !== "closed" ? (
            <CButton
              size="sm"
              role="neutral"
              onClick={() => setIsPopupOpen(true)}
            >
              بستن تیکت
            </CButton>
          ) : (
            <div className="flex items-center gap-2 bg-light-inform-background-highlight rounded-md p-4">
              <Info size={20} className="text-light-inform-text-rest" />
              <p className="font-body-small">
                این تیکت توسط {isClosed ? "کارشناس" : "کاربر"} در تاریخ{" "}
                {parseTimeToPersianSummary(closed_date)} بسته شد
              </p>
            </div>
          )}
        </div>

        <ul className="font-body-small text-light-neutral-text-medium list-disc px-3">
          <li>
            در صورت بستن تیکت٬ پیگیری پیام‌های تیکت امکان پذیر نخواهد بود.
          </li>
          <li>
            در صورت پایان یافتن مکالمات و حل مشکل٬ تیکت می‌تواند توسط کارشناس
            پشتیبانی
          </li>
        </ul>
      </div>
      <Popup
        isOpen={isPopupOpen}
        onClose={() => setIsPopupOpen(!isPopupOpen)}
        hasCloseIcon={false}
        hasButton={false}
        // submitHandler={updateNoteHandler}
        // disabled={isLoading}
      >
        <div className="flex flex-col items-center justify-center gap-3">
          <XCircle size={30} />

          <p className="font-subtitle-large">آیا می‌خواهید تیکت را ببندید؟</p>
          <p className="font-body-large">
            در صورت بستن تیکت، پیگیری پیام‌های شما امکان‌پذیر نخواهد بود
          </p>
        </div>

        <div className="flex items-center gap-4 mt-10">
          <CButton role="neutral" onClick={() => setIsPopupOpen(false)}>
            انصراف
          </CButton>
          <CButton role="error" mode="outline" onClick={closeTicketHandler}>
            بستن تیکت
          </CButton>
        </div>
      </Popup>
    </>
  );
};

export default TicketInformation;
