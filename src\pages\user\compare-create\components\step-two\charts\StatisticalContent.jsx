import { memo, useEffect, useState } from "react";
import { useCompareStore } from "store/compareStore.js";
import { Card } from "components/ui/Card.jsx";
import { SpinnerGap } from "@phosphor-icons/react";
import advanceSearch from "service/api/advanceSearch.js";
import ContentTwitter from "./ContentTwitter.jsx";
import ContentInstagram from "./ContentInstagram.jsx";
import ContentTelegram from "./ContentTelegram.jsx";
import ContentNews from "./ContentNews.jsx";
import Title from "./Title.jsx";
import { buildRequestData } from "utils/requestData.js";
import PropTypes from "prop-types";

const StatisticalContent = ({ compareType }) => {
  const { platform, date, fields, content } = useCompareStore(
    (state) => state.compare
  );
  const setCompare = useCompareStore((state) => state.setCompare);
  const [loading, setLoading] = useState(false);
  const [fieldsCleared, setFieldsCleared] = useState([]); // New state for fieldsCleared

  // Update fieldsCleared only when relevant field properties change
  useEffect(() => {
    const newFieldsCleared = fields.filter((value) =>
      compareType === "profile" ? value.id : value.q
    );
    // Compare newFieldsCleared with current fieldsCleared
    const hasChanged =
      newFieldsCleared.length !== fieldsCleared.length ||
      newFieldsCleared.some((field, index) => {
        const prevField = fieldsCleared[index] || {};
        return compareType === "profile"
          ? field.id !== prevField.id
          : field.q !== prevField.q;
      });

    if (hasChanged) {
      console.log("fieldsCleared updated:", newFieldsCleared);
      setFieldsCleared(newFieldsCleared);
    }
  }, [fields, compareType]);

  const fetchProfileData = async (fieldsCleared, abortController) => {
    try {
      const queries = fieldsCleared.map((field) => {
        const filterObject = {
          ...field.filters,
          date,
          platform,
          sources: field?.user_id
            ? [field?.user_id.toString()]
            : field?.id
            ? [field?.id.toString()]
            : [],
        };
        const query = buildRequestData(filterObject, "statistical");
        return advanceSearch.search(query);
      });
      const responses = await Promise.allSettled(queries);
      return responses
        .filter((res) => res.status === "fulfilled")
        .map((res) => res.value?.data?.data?.[platform] || {});
    } catch (error) {
      if (!abortController.signal.aborted)
        console.error("Profile fetch error:", error);
      return [];
    }
  };

  const fetchTopicData = async (fieldsCleared, abortController) => {
    try {
      const queries = fieldsCleared.map((field) => {
        const filterObject = {
          ...field.filters,
          date,
          platform,
          q: field?.q,
        };
        const query = buildRequestData(filterObject, "statistical");
        return advanceSearch.search(query);
      });
      const responses = await Promise.allSettled(queries);
      return responses
        .filter((res) => res.status === "fulfilled")
        .map((res) => res.value?.data?.data?.[platform] || {});
    } catch (error) {
      if (!abortController.signal.aborted)
        console.error("Topic fetch error:", error);
      return [];
    }
  };

  const getData = async (fieldsCleared, abortController) => {
    if (!fieldsCleared.length) return;
    setLoading(true);
    try {
      const result =
        compareType === "profile"
          ? await fetchProfileData(fieldsCleared, abortController)
          : await fetchTopicData(fieldsCleared, abortController);

      if (!abortController.signal.aborted) {
        setCompare({
          content: result.length
            ? fieldsCleared.map((item, index) => ({
                ...item,
                ...result[index],
              }))
            : [],
        });
      }
    } catch (error) {
      if (!abortController.signal.aborted)
        console.error("Error in getData:", error);
    } finally {
      if (!abortController.signal.aborted) setLoading(false);
    }
  };

  // Fetch data when fieldsCleared or date changes
  useEffect(() => {
    const abortController = new AbortController();
    console.log("Fetch effect triggered with:", {
      fieldsCleared,
      date,
      platform,
    });
    if (fieldsCleared.length) {
      getData(fieldsCleared, abortController);
    }
    return () => abortController.abort();
  }, [fieldsCleared, date, platform]);

  const platformSelector = {
    twitter: <ContentTwitter />,
    instagram: <ContentInstagram />,
    telegram: <ContentTelegram />,
    news: <ContentNews />,
  };

  if (!fields.filter((user) => user.id || user.user_id || user.q).length) {
    return null;
  }

  return (
    <div className="flex h-[30.5rem]">
      <Card className="px-0">
        <div className="w-full flex flex-col gap-6">
          <Title title="تعداد محتوای منتشر شده"></Title>
          {loading ? (
            <div className="w-full h-[400px] flex justify-center items-center">
              <SpinnerGap size={40} className="animate-spin" />
            </div>
          ) : content.length === 0 ? (
            <div className="w-full h-60 flex justify-center pt-4 items-center text-lg text-gray-600">
              داده ای برای نمایش وجود ندارد
            </div>
          ) : (
            <div className="flex items-center justify-center gap-4">
              {platformSelector[platform]}
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

StatisticalContent.propTypes = {
  compareType: PropTypes.string.isRequired,
};

export default memo(StatisticalContent);
