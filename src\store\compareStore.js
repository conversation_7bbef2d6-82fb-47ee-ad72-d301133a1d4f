import { create } from "zustand";

const oneMonthInMillis = 30 * 24 * 60 * 60 * 1000;
const threeMonthsInMillis = 90 * 24 * 60 * 60 * 1000;

const compareDefaultValue = {
  id: "",
  step: 1,
  type: "",
  platform: "",
  title: "",
  date: {
    from: new Date(new Date().getTime() - threeMonthsInMillis),
    to: new Date(),
  },
  profile: [],
  content: [],
  fields: [
    {
      isEdit: true,
      filters: {
        sentiment: [],
        gender: ["all"],
        subjectCategory: [],
        language: [],
        sources: [],
        keywords: [],
        hashtags: [],
      },
      q: "",
      source: "",
    },
    {
      isEdit: true,
      filters: {
        sentiment: [],
        gender: ["all"],
        subjectCategory: [],
        language: [],
        sources: [],
        keywords: [],
        hashtags: [],
      },
      q: "",
      source: "",
    },
  ],
  // filters: [],
  topic: {},
  sentiment: [],
};
export const useCompareStore = create((set) => ({
  compare: compareDefaultValue,
  setCompare: (param) =>
    set((state) => ({ compare: { ...state.compare, ...param } })),
  resetCompare: () => set(() => ({ compare: compareDefaultValue })),
}));
