import { CInput } from "components/ui/CInput";
import { Field, Form, Formik } from "formik";
import { filterStepOneSchema } from "utils/validationSchemas";
import { CButton } from "components/ui/CButton.jsx";
import PropTypes from "prop-types";

const StepOne = ({ setStatus, setFilterData, filterData, state }) => {
  const handleSubmit = (data) => {
    const copy = { ...data };
    copy.title = data.title.trim();
    setFilterData((l) => {
      return { ...l, ...copy };
    });
    setStatus(2);
  };
  return (
    <div className="p-6 w-full">
      <Formik
        initialValues={{
          title: filterData.title,
          description: filterData.description,
        }}
        validationSchema={filterStepOneSchema}
        onSubmit={handleSubmit}
      >
        {({
          handleChange,
          handleBlur,
          isValid,
          dirty,
          initialValues,
          values,
        }) => (
          <Form className="flex-1 bg-white mx-auto w-full h-full overflow-hidden">
            <Field
              id={"title"}
              name={"title"}
              component={CInput}
              size={"lg"}
              validation={"none"}
              direction={"rtl"}
              title={"عنوان فیلتر"}
              placeholder="برای مثال فیلتر رشد قیمت"
              onChange={handleChange("title")}
              onBlur={handleBlur("title")}
              className="w-full font-overline-large"
              value={initialValues.title}
              inputProps={{ readOnly: state }}
            />
            <div className="mb-12">
              <label
                htmlFor="description"
                className="font-overline-large mb-2 inline-block"
              >
                {" "}
                توضیحات
              </label>
              <Field
                id={"description"}
                name={"description"}
                as="textarea"
                placeholder="برای مثال این فیلتر برای رشد قیمت ارز خاص در نظر گرفته شده است"
                onChange={handleChange("description")}
                onBlur={handleBlur("description")}
                className="w-full h-40 rounded-md p-4 font-body-large outline-none border border-light-neutral-border-medium-rest"
                // value={initialValues.description}
              />
            </div>
            <div className="flex flex-row-reverse">
              <CButton
                type={"submit"}
                className={`!w-[200px]`}
                readOnly={!((isValid && dirty) || values.title)}
              >
                ادامه
              </CButton>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

StepOne.propTypes = {
  setStatus: PropTypes.func,
  setFilterData: PropTypes.func,
  filterData: PropTypes.object,
  state: PropTypes.bool,
};

export default StepOne;
