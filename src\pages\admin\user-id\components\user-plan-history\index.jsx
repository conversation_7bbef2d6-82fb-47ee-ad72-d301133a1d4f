import { useState } from "react";
import UserPlanTable from "./plan-history-table";
import SearchBar from "./SearchBar";
import formatDate from "pages/admin/dashboard/utils";
import { useLocation } from "react-router-dom";
import { useBreadcrumb } from "hooks/useBreadcrumb";

const UserPlanHistoryPage = () => {
  const { pathname } = useLocation();
  const breadcrumbList = [
    { title: "کاربران", link: "/admin/user/list" },
    {
      title: "پلن کاربری",
      link: `/admin/user/list/${
        pathname?.split("/").slice(-2)[0]
      }/planSettings`,
    },
    { title: "تاریخچه پلن کاربر" },
  ];
  useBreadcrumb(breadcrumbList);

  const [loading, setLoading] = useState(false);
  const oneYearInMillis = 12 * 30 * 24 * 60 * 60 * 1000;
  const [filter, setFilter] = useState({
    q: "",
    register_at: {
      date_from: formatDate(new Date(new Date().getTime() - oneYearInMillis)),
      date_to: formatDate(new Date(new Date().getTime() + 24 * 60 * 60 * 1000)),
    },
    sort_rules: [
      {
        field: "login_at",
        direction: "desc",
      },
      {
        field: "register_at",
        direction: "desc",
      },
    ],
  });
  return (
    <div className="container grid gap-3 mx-auto px-5">
      <SearchBar filter={filter} setFilter={setFilter} loading={loading} />

      <UserPlanTable />
    </div>
  );
};

export default UserPlanHistoryPage;
