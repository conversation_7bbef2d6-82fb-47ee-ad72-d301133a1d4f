import IndicatorBadge from "./IndicatorBadge";
import Seprator from "./Seprator";

const Container = ({ children, status, step, title }) => {
  return (
    <div className="w-full">
      <IndicatorBadge step={step} status={status}>
        {title}
      </IndicatorBadge>
      <div className="flex gap-6 w-full">
        {step < 5 && <Seprator step={step} status={status} />}
        {status == step && (
          <div className="p-6 grid grid-cols-12 gap-12 w-full">{children}</div>
        )}
      </div>
    </div>
  );
};

export default Container;
