import React, { useState } from "react";
import { Eye, PencilSimpleLine, TrashSimple } from "@phosphor-icons/react";
import { useNavigate } from "react-router-dom";
import ToolTip from "components/ui/ToolTip";
import RemoveUser from "./RemoveUser.jsx";
import { parseTimeToPersian } from "utils/helper";
import PropTypes from "prop-types";
import RoleBadge from "pages/admin/user-list/components/RoleBadge.jsx";

const ListCard = ({
  id,
  avatar,
  username,
  fullname,
  role,
  email,
  phone,
  created_at,
  last_seen,
  render,
}) => {
  const navigate = useNavigate();

  const [showRemove, setShowRemove] = useState(false);

  const remove = async () => {
    try {
      // await user.remove(id);
      render();
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <>
      <div className="grid grid-cols-7 justify-between hover:bg-light-neutral-surface-highlight rounded p-1 items-center">
        <span>
          {username}
          {fullname !== "" && (
            <span className={"font-body-medium"}> ({fullname}) </span>
          )}
        </span>
        {/*<span>{type === "profile" ? "پروفایلی" : "موضوعی"}</span>*/}
        <span>{email}</span>
        <span>{phone}</span>

        <span>
          {created_at ? parseTimeToPersian(created_at).split("-")[1] : ""}
        </span>

        <span>
          {last_seen ? parseTimeToPersian(last_seen).split("-")[1] : ""}
        </span>
        <span className={"flex flex-row gap-2 items-center justify-start"}>
          <RoleBadge role={role} />
        </span>
        <div className="flex gap-4 justify-center">
          <div
            className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
            onClick={() => navigate(`/admin/user/view/${id}`)}
          >
            <ToolTip comp="نمایش جزئیات">
              <Eye size={16} />
            </ToolTip>
          </div>
          <div
            className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
            onClick={() => navigate(`/admin/user/edit/${id}`)}
          >
            <ToolTip comp="ویرایش">
              <PencilSimpleLine size={16} />
            </ToolTip>
          </div>
          <div
            className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
            onClick={() => setShowRemove(true)}
          >
            <ToolTip comp="حذف">
              <TrashSimple size={16} />
            </ToolTip>
          </div>
        </div>
      </div>
      {showRemove && (
        <RemoveUser removeHandler={remove} setShowRemove={setShowRemove} />
      )}
    </>
  );
};

ListCard.propTypes = {
  id: PropTypes.number,
  title: PropTypes.string,
  type: PropTypes.string,
  subject: PropTypes.string,
  subjectPlatform: PropTypes.string,
  created_at: PropTypes.string,
  render: PropTypes.func.isRequired,
};

export default ListCard;
