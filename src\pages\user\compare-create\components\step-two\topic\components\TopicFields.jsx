import { memo, useEffect, useRef, useState } from "react";
import { CButton } from "components/ui/CButton";
import {
  Funnel,
  PencilSimpleLine,
  Plus,
  Sliders,
  TrashSimple,
} from "@phosphor-icons/react";
import MediaBadge from "components/ui/MediaBadge";
import { CInput } from "components/ui/CInput";
import Drawer from "components/Drawer";
import { useCompareStore } from "store/compareStore";
import useSearchStore from "store/searchStore";
import { CompareFilterList } from "components/CompareFilterList/CompareFilterList";
import CompareSearchDrawer from "./CompareSearchDrawer";
import useTrendData from "../hooks/useTrendData.js";
import { useScrollPosition } from "hooks/useScrollPosition.jsx";
import { deepEqual, shortener } from "utils/helper.js";
import ToolTip from "components/ui/ToolTip.jsx";
import { FilterList } from "components/FilterList/FilterList.jsx";

const TopicFields = () => {
  const { searchBoxConfig, query, setIsFilterListOpen } = useSearchStore();
  const { platform, fields } = useCompareStore((state) => state.compare);
  const { setCompare } = useCompareStore();

  const [showSubmenu, setShowSubmenu] = useState([false, false, false, false]);
  const [showAdvancedSearchDrawer, setShowAdvancedSearchDrawer] =
    useState(false);
  const [showFilterSearch, setShowFilterSearch] = useState(false);
  const [inputValues, setInputValues] = useState(fields.map((item) => item.q));
  const [focusedIndex, setFocusedIndex] = useState(null);
  const [compareSearchQuery, setCompareSearchQuery] = useState();
  const submenuRefs = useRef([]);

  const { isScrolled } = useScrollPosition();
  const [sticky, setSticky] = useState(false);
  const colors = ["#1DCEA3", "#6D72E5", "#DB6DE5", "#F7A912"];

  useEffect(() => {
    setIsFilterListOpen(true);
  }, []);

  useEffect(() => {
    setSticky(isScrolled);
  }, [isScrolled]);

  const { trendData } = useTrendData(platform);
  const trendKeys = trendData.map((item) => Object.keys(item)[0]);

  const updateCompareState = (newFields) => {
    if (deepEqual(fields, newFields)) return;
    setCompare({
      fields: newFields,
    });
  };

  const updateShowSubMenuState = (value, index) => {
    const help = JSON.parse(JSON.stringify(showSubmenu));
    help[index] = !value || value === "";
    setShowSubmenu(help);
  };

  const updateInputValuesState = (value, index) => {
    const help = JSON.parse(JSON.stringify(inputValues));
    help[index] = value;
    setInputValues(help);
  };

  const handleAddField = () => {
    updateCompareState([
      ...fields,
      {
        isEdit: true,
        filters: {
          sentiment: [],
          gender: ["male"],
          subjectCategory: [],
          language: [],
          sources: [],
          keywords: [],
          hashtags: [],
        },
        q: "",
      },
    ]);
    setInputValues((prev) => [...prev, ""]);
  };

  const handleRemoveField = (index) => {
    if (fields.length === 2) return;
    const updatedFields = fields.filter((_, i) => i !== index);
    updateCompareState(updatedFields);
    setInputValues((prev) => prev.filter((_, i) => i !== index));
  };

  const handleFocus = (index) => {
    setFocusedIndex(index);
    updateShowSubMenuState(inputValues[index], index);
  };

  const handleInputChange = (index, value) => {
    updateInputValuesState(value, index);
    updateShowSubMenuState(value, index);
  };

  useEffect(() => {
    if (!query || query === "") return;
    updateInputValuesState(query, focusedIndex);
    const help = JSON.parse(JSON.stringify(fields));
    help[focusedIndex] = { ...help[focusedIndex], isEdit: true, q: query };
    updateCompareState(help);
  }, [query]);

  const handleChipClick = (chipValue, index) => {
    const selectedChipObject = trendData.find((item) => item[chipValue]);
    const selectedChipValues = selectedChipObject
      ? selectedChipObject[chipValue]
      : [];
    let inputString = "";
    if (selectedChipValues.length === 1) {
      inputString = selectedChipValues[0];
    } else if (selectedChipValues.length > 1) {
      inputString = `${selectedChipValues.join(" OR ")}`;
    }
    updateInputValuesState(inputString, index);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        submenuRefs.current?.[focusedIndex] &&
        !submenuRefs.current?.[focusedIndex].contains(event.target)
      ) {
        const updatedShowSubmenu = [...showSubmenu];
        updatedShowSubmenu[focusedIndex] = false;
        setShowSubmenu(updatedShowSubmenu);

        // setFocusedIndex(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [focusedIndex, showSubmenu]);

  const handleCompare = () => {
    if (inputValues.filter((item) => !!item).length < 2) return;
    const help = JSON.parse(JSON.stringify(fields));
    inputValues.map((item, index) => {
      if (!item || item === "") return;
      help[index] = { ...help[index], isEdit: false, q: item };
      // const updatedFields = fields.map((x, i) =>
      //   i === index ? { ...x, isEdit: true, q: item } : x,
      // );
      updateShowSubMenuState(item, index);
      updateCompareState(help);
    });
  };

  const handleEditTopic = (index) => {
    const help = JSON.parse(JSON.stringify(fields));
    help[index] = { ...help[index], isEdit: true };
    updateCompareState(help);
  };

  const handleFilterChange = (index, filter) => {
    const help = JSON.parse(JSON.stringify(fields));
    help[index] = { ...help[index], filters: filter, q: query };
    updateCompareState(help);
  };

  // useEffect(() => {
  //   const handleKeyDown = (event) => {
  //     if (event.key === "Enter") {
  //       handleCompare();
  //     }
  //   };
  //   window.addEventListener("keydown", handleKeyDown);
  //   return () => {
  //     window.removeEventListener("keydown", handleKeyDown);
  //   };
  // }, [inputValues]);

  return (
    <>
      <div
        className={`${
          sticky
            ? "w-full fixed top-[60px] left-0 h-[90px] z-50 backdrop-blur"
            : "hidden"
        }`}
      ></div>
      <div
        className={`flex gap-4 items-start ${
          sticky ? "sticky top-[75px] z-50" : ""
        }`}
        id="topic-fields-box"
      >
        {fields.map((item, index) => (
          <div
            key={index}
            ref={(el) => (submenuRefs.current[index] = el)}
            className={`bg-white rounded-lg flex w-full font-body-medium bg-light-neutral-surface-card p-4 ${
              sticky ? "shadow-lg" : "shadow-[0_2px_20px_0_rgba(0,0,0,0.05)]"
            }`}
            style={{
              padding: !item.isEdit ? "26px 16px" : "",
              borderRight: `6px solid ${colors[index]}`,
            }}
          >
            <div className={"flex flex-col w-full gap-1"}>
              <div className="flex items-center w-full gap-4">
                <div className="w-full relative">
                  {item.isEdit ? (
                    <CInput
                      id={`q-${index}`}
                      name={`q-${index}`}
                      inset={true}
                      size={"md"}
                      validation={"none"}
                      direction={"rtl"}
                      placeholder={"کلمه مورد نظر را بنویسید"}
                      className={"flex-1 !mb-0"}
                      value={inputValues[index]}
                      readOnly={searchBoxConfig.readOnly}
                      onChange={(e) => handleInputChange(index, e.target.value)}
                      onFocus={() => handleFocus(index)}
                      customAction={async () => {
                        handleFocus(index);
                        setShowFilterSearch(!showFilterSearch);
                      }}
                      customAction2={async () => {
                        handleFocus(index);
                        setCompareSearchQuery(inputValues[index]);
                        setShowAdvancedSearchDrawer(true);
                      }}
                      customActionText={
                        <Funnel
                          className="text-light-neutral-text-low"
                          size={22}
                        />
                      }
                      customActionText2={
                        !searchBoxConfig.readOnly ? (
                          <Sliders
                            className="text-light-neutral-text-low"
                            size={22}
                          />
                        ) : null
                      }
                    />
                  ) : (
                    <>
                      <div className="flex flex-row items-center justify-between">
                        <ToolTip
                          comp={inputValues[index]}
                          position={"bottom"}
                          hasNoWrap={false}
                        >
                          <div className="font-subtitle-medium max-h-[25px] [direction:ltr] text-right overflow-hidden">
                            {shortener(
                              inputValues[index] !== ""
                                ? inputValues[index]
                                : fields[index].q,
                              27,
                              "rtl",
                            )}
                          </div>
                        </ToolTip>
                        <div className="flex flex-row items-center gap-3 field-actions">
                          <div
                            className="size-6 flex items-center justify-center bg-[#E1E8EF80] rounded-md cursor-pointer mt-0"
                            onClick={() => handleEditTopic(index)}
                          >
                            <PencilSimpleLine size={16} />
                          </div>
                          {fields.length < 2 && (
                            <div
                              className="size-6 flex items-center justify-center bg-[#E1E8EF80] rounded-md cursor-pointer mt-0"
                              onClick={() => handleRemoveField(index)}
                            >
                              <TrashSimple size={16} />
                            </div>
                          )}
                        </div>
                      </div>
                    </>
                  )}
                </div>
                {fields.length > 2 && (
                  <div
                    className="size-6 flex items-center justify-center bg-[#E1E8EF80] rounded-md cursor-pointer mt-0"
                    onClick={() => handleRemoveField(index)}
                  >
                    <TrashSimple size={16} />
                  </div>
                )}
              </div>
              {focusedIndex === index && showSubmenu[index] && (
                <div className="bg-light-neutral-surface-card rounded-md p-4 items-center">
                  <p className="font-subtitle-medium rounded-md mb-4">
                    موضوعات مهم روز
                  </p>
                  <div className="flex items-center flex-wrap gap-3">
                    {trendKeys.map((chip) => (
                      <div
                        key={chip}
                        className="px-3 py-1 font-body-small bg-light-neutral-background-low border border-light-neutral-border-low-rest w-fit rounded-md cursor-pointer"
                        onClick={() => handleChipClick(chip, index)}
                      >
                        {chip}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
        <div className={!showAdvancedSearchDrawer ? "hidden" : "block"}>
          <CompareSearchDrawer
            inputQuery={compareSearchQuery}
            index={focusedIndex}
            setShowMore={setShowAdvancedSearchDrawer}
            onSubmit={(query, index) => {
              handleInputChange(index, query);
            }}
          />
        </div>
        <div className={!showFilterSearch ? "hidden" : "block"}>
          <Drawer setShowMore={setShowFilterSearch}>
            <CompareFilterList
              index={focusedIndex}
              onChange={handleFilterChange}
            />
          </Drawer>
        </div>
        <div
          id={"addTopic"}
          className={`flex gap-2 font-body-medium bg-light-neutral-surface-card rounded-lg p-[18px] shadow-[0_2px_20px_0_rgba(0,0,0,0.05)] shrink-0 ${
            fields?.length < 4 ? "w-[220px]" : "w-[120px]"
          }`}
        >
          {fields?.length < 4 && (
            <CButton
              mode="outline"
              leftIcon={<Plus size={16} />}
              className="gap-0 [direction:ltr] "
              onClick={handleAddField}
            >
              افزودن
            </CButton>
          )}
          <CButton
            mode="fill"
            className="gap-0 [direction:ltr] flex-grow"
            onClick={handleCompare}
          >
            مقایسه
          </CButton>
        </div>
      </div>
    </>
  );
};

export default memo(TopicFields);
