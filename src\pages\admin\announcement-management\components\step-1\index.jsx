import { useState } from "react";
import { CInput } from "components/ui/CInput";
import {
  File,
  FileDoc,
  FilePdf,
  FileTxt,
  FileZip,
  MicrosoftExcelLogo,
  XCircle,
} from "@phosphor-icons/react";
import { notification, shortener } from "utils/helper";
// import AttachmentBox from "./components/AttachmentBox";
import UploadBox from "components/ui/UploadBox";
import AttachmentBox from "../AttachmentBox";
import { CButton } from "components/ui/CButton.jsx";
import Editor from "../Editor";
const StepOne = ({ setStep }) => {
  const [inputTitle, setInputTitle] = useState("");
  const [attachments, setAttachments] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [editorData, setEditorData] = useState("");
  const [headerImage, setHeaderImage] = useState(false);

  const handleImageClick = (file) => {
    setSelectedImage(URL.createObjectURL(file));
    setIsModalOpen(true);
  };
  const handleDeleteFile = (index) => {
    const newAttachments = attachments.filter((_, i) => i !== index);
    setAttachments(newAttachments);
  };
  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedImage(null);
  };
  return (
    <>
      <div className="flex justify-center">
        <div className="container px-3 gap-10">
          <div className="bg-light-neutral-surface-card p-6 rounded-[8px]">
            <p className="font-subtitle-large mb-8">محتوای اطلاعیه</p>
            <CInput
              value={inputTitle}
              onChange={(e) => setInputTitle(e.target.value)}
              title={"عنوان"}
              placeholder={"عنوان را بنویسید"}
              validation="error"
            />
            <div className="flex flex-col items-start mt-8 mb-14">
              <div className="flex items-center gap-2">
                <input
                  value={headerImage}
                  onChange={() => setHeaderImage((prev) => !prev)}
                  type="checkbox"
                  name="headerImg"
                  id="headerImg"
                />
                <label
                  htmlFor="headerImg"
                  className="font-body-medium select-none"
                >
                  عکس هدر
                </label>
              </div>
              {headerImage && (
                <div className="w-full">
                  <UploadBox
                    description={"عکس هدر"}
                    shortnerLen={20}
                    fullWidth
                  />
                </div>
              )}
            </div>

            <div>
              <label className="font-body-medium select-none">
                متن اطلاعیه
              </label>
              <div className="py-2 font-body-medium">
                <Editor />
                {/* <CKEditor
                  editor={ClassicEditor}
                  data=""
                  config={{
                    toolbar: {
                      items: [
                        "undo",
                        "redo",
                        "|",
                        "fontfamily",
                        "fontsize",
                        "fontColor",
                        "fontBackgroundColor",
                        "|",
                        "bold",
                        "italic",
                        "strikethrough",
                        "subscript",
                        "superscript",
                        "code",
                        "|",
                        // "link",
                        "uploadImage",
                        "blockQuote",
                        "codeBlock",
                        "|",
                        "bulletedList",
                        "numberedList",
                        "todoList",
                      ],
                    },
                    language: {
                      ui: "en",
                      content: "ar",
                    },
                    placeholder: "متن اطلاعیه را بنویسید...",
                  }}
                  onChange={(event, editor) => {
                    const data = editor.getData();
                    setEditorData(data);
                  }}
                  onReady={(editor) => {}}
                  onBlur={(event, editor) => {}}
                  onFocus={(event, editor) => {}}
                /> */}
              </div>
            </div>

            <div className="mb-5 mt-1">
              <div className="mt-10">
                <label className="font-body-medium select-none">
                  آپلود فایل (اختیاری)
                </label>
              </div>
              <div>
                <AttachmentBox
                  description={"در صورت لزوم فایل را بارگذاری کنید (اختیاری)"}
                  description2={"حداکثر حجم مجاز: ۱۰ مگابایت"}
                  fullWidth
                  boxIcon={File}
                  onChange={(files) => {
                    const totalFiles = attachments.length + files.length;
                    if (totalFiles > 8) {
                      notification.error(
                        "تعداد فایل‌های آپلود شده باید حداکثر ۸ عدد باشد!",
                      );
                      return;
                    }
                    const filteredFiles = files.filter(
                      (newFile) =>
                        !attachments.some(
                          (existingFile) => existingFile.name === newFile.name,
                        ),
                    );
                    setAttachments((prev) => [...prev, ...filteredFiles]);
                  }}
                  multiple
                />
              </div>

              <div className="mt-4 flex flex-wrap gap-4 items-center">
                {attachments.map((file, index) => (
                  <div
                    key={index}
                    className="flex flex-col items-center relative w-24 h-24 border rounded my-3"
                  >
                    {file.type?.startsWith("image/") ? (
                      <img
                        src={URL.createObjectURL(file)}
                        alt={file.name}
                        className="w-full h-full max-h-20 object-cover cursor-pointer"
                        onClick={() => handleImageClick(file)}
                      />
                    ) : file.type?.endsWith("pdf") ? (
                      <FilePdf
                        size={96}
                        className="w-full h-full flex items-center justify-center"
                      />
                    ) : file.type?.endsWith("application/x-zip-compressed") ? (
                      <FileZip
                        size={96}
                        className="flex items-center justify-center"
                      />
                    ) : file.type?.endsWith("csv") ? (
                      <MicrosoftExcelLogo
                        size={96}
                        className="flex items-center justify-center"
                      />
                    ) : file.type?.endsWith("xls") ? (
                      <MicrosoftExcelLogo
                        size={96}
                        className="flex items-center justify-center"
                      />
                    ) : file.type?.endsWith(".sheet") ? (
                      <MicrosoftExcelLogo
                        size={96}
                        className="flex items-center justify-center"
                      />
                    ) : file.type?.endsWith("text/plain") ? (
                      <FileTxt
                        size={96}
                        className="flex items-center justify-center"
                      />
                    ) : file.type?.endsWith("docx") ? (
                      <FileDoc
                        size={96}
                        className="flex items-center justify-center"
                      />
                    ) : file.type?.endsWith("doc") ? (
                      <FileDoc
                        size={96}
                        className="flex items-center justify-center"
                      />
                    ) : file.type?.endsWith("document") ? (
                      <FileDoc
                        size={96}
                        className="flex items-center justify-center"
                      />
                    ) : (
                      <File
                        size={96}
                        className="flex items-center justify-center"
                      />
                    )}
                    <p className="font-body-bold-small">
                      {shortener(file?.name, 10)}
                    </p>
                    <button
                      onClick={() => handleDeleteFile(index)}
                      className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full"
                    >
                      <XCircle size={20} />
                    </button>
                  </div>
                ))}
              </div>
            </div>
            <CButton onClick={() => setStep(2)}>ادامه</CButton>
          </div>
        </div>
        {isModalOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onClick={handleCloseModal}
          >
            <div
              className="relative p-4 rounded"
              onClick={(e) => e.stopPropagation()}
            >
              <img
                src={selectedImage}
                alt="Preview"
                className="max-w-96 max-h-max-w-96"
              />
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default StepOne;
