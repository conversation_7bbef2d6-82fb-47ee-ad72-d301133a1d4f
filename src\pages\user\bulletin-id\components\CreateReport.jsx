import { FilePdf, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ord<PERSON>ogo } from "@phosphor-icons/react";
import { CButton } from "components/ui/CButton";
import { parseTimeToPersian } from "utils/helper";

const CreateReport = ({ bulletinData }) => {
  return (
    <>
      <div className="w-full flex flex-col gap-4 rounded-lg bg-white p-6 col-span-3 shadow-[0px_2px_20px_0px_#0000000D]">
        <p className="font-subtitle-medium">گزارش‌ ساخته شده</p>

        {bulletinData?.files?.length > 0 ? (
          bulletinData?.files?.map((item, index) => (
            <div className="flex items-center justify-between" key={index}>
              <div className="flex flex-col font-body-medium gap-7">
                {index === 0 && (
                  <span className="font-overline-medium text-light-neutral-text-medium">
                    تاریخ ایجاد
                  </span>
                )}
                <span>{parseTimeToPersian(item?.created_at)}</span>
              </div>

              <div className="flex flex-col font-body-medium gap-7">
                {index === 0 && (
                  <span className="font-overline-medium text-light-neutral-text-medium">
                    اقدامات
                  </span>
                )}
                <a
                  target="_blank"
                  rel="noreferrer"
                  href={`${item?.filename}`}
                  className="w-fit"
                >
                  <CButton
                    leftIcon={<FileZip />}
                    size="sm"
                    className="font-button-small"
                    role="neutral"
                  >
                    دانلود
                  </CButton>
                </a>
              </div>
            </div>
          ))
        ) : (
          <div className="flex items-center justify-center text-center py-10">
            <p className="font-body-medium text-light-neutral-text-medium">
              فایلی یافت نشد
            </p>
          </div>
        )}
      </div>
    </>
  );
};

export default CreateReport;
