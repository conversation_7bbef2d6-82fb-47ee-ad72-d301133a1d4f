import { Field, Form, Formik } from "formik";
import { addUserStep4Schema } from "utils/validationSchemas.js";
import { CInput } from "components/ui/CInput.jsx";
import { CButton } from "components/ui/CButton.jsx";
import PropTypes from "prop-types";
import { Warning } from "@phosphor-icons/react";
import SetPassword from "./components/SetPassword";

const StepFour = ({ setStep, setData, data }) => {
  return (
    <>
      <div className="grid gap-4 bg-light-neutral-surface-card rounded-[8px] p-6">
        <p className="font-subtitle-large">تعیین رمز عبور</p>
        <div className="flex items-start justify-start gap-2 p-4 rounded-lg bg-light-warning-background-highlight mb-4">
          <Warning className="text-light-warning-background-rest" size={20} />
          <p className="font-body-medium">
            در صورتی که رمز عبوری را تعیین نکنید٬ شماره موبایل کاربر به عنوان
            رمز عبور وی انتخاب می‌شود.
          </p>
        </div>

        {/* <CInput
          title="تکرار رمز عبور"
          placeholder="رمز عبور را دوباره بنویسید"
          type="password"
        /> */}
        <SetPassword setStep={setStep} />
      </div>
    </>
  );
};

StepFour.propTypes = {
  data: PropTypes.object,
  setData: PropTypes.func,
  setStep: PropTypes.func,
};

export default StepFour;
