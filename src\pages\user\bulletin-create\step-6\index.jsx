import { useEffect } from "react";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";
import { CButton } from "components/ui/CButton";
import CardLoading from "components/ui/CardLoading";
import { useBulletinStore } from "store/bulletinStore";
import { useCronJob } from "context/cronJob.jsx";
import { useNavigate } from "react-router-dom";
import { ToastContainer } from "react-toastify";
import { CheckFat } from "@phosphor-icons/react";

const Step6 = () => {
  // const breadcrumbList = [{ title: "بولتن" }, { title: "ایجاد بولتن" }];
  // useBreadcrumb(breadcrumbList);
  const { id, type } = useBulletinStore((state) => state.bulletin);
  const navigate = useNavigate();

  const { startJob } = useCronJob();

  useEffect(() => {
    startJob("bulletin", id);
  }, []);

  const clickHandler = () => {
    navigate("/app/bulletin/list");
  };

  return (
    <div className="flex justify-center items-center">
      <div className="flex flex-col justify-center items-center mt-16 w-[460px]">
        <div className="mb-8">
          {type === "manual" ? (
            <CardLoading />
          ) : (
            <CheckFat size={70} color={"#13ab2d"} />
          )}
        </div>
        {type === "manual" ? (
          <div className="text-center">
            <p className="font-subtitle-large">آماده‌سازی فایل بولتن</p>
            <div className="my-8">
              <p className="font-body-medium text-light-neutral-text-medium text-center [direction:rtl] leading-7">
                فرایند آماده‌سازی فایل بولتن ممکن است نیاز به چند دقیقه زمان
                داشته باشد. پس از آماده شدن فایل، امکان دانلود آن در این صفحه و
                همچنین از طریق صفحه اعلانات وجود خواهد داشت.
              </p>
              <p className="font-body-medium text-light-neutral-text-medium text-center mt-2 [direction:rtl] leading-7">
                از شکیبایی شما سپاس‌گزاریم.
              </p>
            </div>
          </div>
        ) : (
          <div className="text-center">
            <p className="font-subtitle-large">
              فرآیند ساخت بولتن دوره ای تکمیل شد
            </p>
            <p className="font-body-medium text-light-neutral-text-medium text-center my-8 [direction:rtl] leading-7">
              فرایند آماده‌سازی فایل بولتن دوره‌ای در زمان مشخص شده انجام خواهد
              شد. پس از آماده شدن فایل، امکان دانلود آن از طریق صفحه اعلانات
              وجود خواهد داشت.
            </p>
          </div>
        )}

        <div onClick={clickHandler} className="w-full">
          <CButton>متوجه شدم</CButton>
        </div>
      </div>
      <ToastContainer />
    </div>
  );
};

export default Step6;
