import { useEffect } from "react";
import "./style.css";
import { WordCloudCard } from "./components/WordCloudCard.jsx";
import { ClusteredContentsSection } from "./components/ClusteredContentsSection.jsx";
import { useDashboardData } from "./hooks/useDashboardData.jsx";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";
import useSearchStore from "store/searchStore.js";
import MasterpageLayout from "layout/masterpage-layout.jsx";
import { ToastContainer } from "react-toastify";

function Dashboard() {
  const breadcrumbList = [{ title: "در یک نگاه" }];
  useBreadcrumb(breadcrumbList);
  const { clearQuery, setShowFilterList, setShowSearchBox } = useSearchStore();
  useEffect(() => {
    clearQuery();
  }, [clearQuery]);

  const {
    clusterData,
    loading,
    clusterListActiveTab,
    setClusterListActiveTab,
  } = useDashboardData();

  useEffect(() => {
    setShowFilterList(false);
    setShowSearchBox(true);
  }, []);

  return (
    <MasterpageLayout overrideFilterSettings={true}>
      <div className={"w-full"}>
        <WordCloudCard
          loading={loading}
          clusterData={clusterData}
          clusterListActiveTab={clusterListActiveTab}
          setClusterListActiveTab={setClusterListActiveTab}
        />

        <ClusteredContentsSection
          loading={loading}
          clusterData={clusterData}
          clusterListActiveTab={clusterListActiveTab}
        />
        <ToastContainer />
      </div>
    </MasterpageLayout>
  );
}

export default Dashboard;
