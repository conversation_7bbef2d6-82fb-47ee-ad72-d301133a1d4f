import { BASE_COLORS } from "constants/bulletin-colors";

const TextColors = ({ handleColor }) => {
  return (
    <div className="w-[140px] h-[114px] [direction:ltr] flex flex-col gap-2">
      <div className="grid grid-cols-10">
        {BASE_COLORS.slice(0, 10).map((color, i) => (
          <div
            className="size-[14px] flex items-center justify-center"
            onClick={() => {
              handleColor(color);
            }}
            key={i}
          >
            <div
              className="size-[9.75px] rounded-full cursor-pointer"
              style={{
                backgroundColor: color,
                border: color === "#FFFFFF" && "0.1px solid #7F7F7F",
              }}
            ></div>
          </div>
        ))}
      </div>
      <div className="grid grid-cols-10">
        {BASE_COLORS.slice(10, 60).map((color, i) => (
          <div
            className="size-[14px] flex items-center justify-center"
            onClick={() => {
              handleColor(color);
            }}
            key={i}
          >
            <div
              className="size-[9.75px] rounded-full cursor-pointer"
              style={{ backgroundColor: color }}
            ></div>
          </div>
        ))}
      </div>
      <div className="grid grid-cols-10">
        {BASE_COLORS.slice(60, 70).map((color, i) => (
          <div
            className="size-[14px] flex items-center justify-center"
            onClick={() => {
              handleColor(color);
            }}
            key={i}
          >
            <div
              className="size-[9.75px] rounded-full cursor-pointer"
              style={{ backgroundColor: color }}
            ></div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TextColors;
