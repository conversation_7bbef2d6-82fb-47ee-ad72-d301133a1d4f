import React, { useEffect, useState } from "react";
import ListCard from "./ListCard";
import compare from "service/api/compare";
import Loading from "components/ui/Loading";
import Empty from "./Empty";
const List = () => {
  const [data, setData] = useState([]);
  const [groupData, setGroupData] = useState([]);
  const [loading, setLoading] = useState(false);
  const getData = async () => {
    setLoading(true);
    try {
      const response = await compare.get();
      setData(response.data.data.user);
      setGroupData(response.data.data.group);
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };
  useEffect(() => {
    getData();
  }, []);

  return (
    <>
      {loading && <Loading />}
      {data.length || groupData.length ? (
        <div className="flex flex-col gap-4">
          {data.map(
            ({
              id,
              title,
              comparison_platform,
              comparison_type,
              column_num,
              created_at,
            }) => (
              <ListCard
                key={id}
                id={id}
                title={title}
                comparison_platform={comparison_platform}
                comparison_type={comparison_type}
                column_num={column_num}
                created_at={created_at}
                render={getData}
              />
            ),
          )}
          {groupData.map(
            ({
              id,
              title,
              comparison_platform,
              comparison_type,
              column_num,
              created_at,
            }) => (
              <ListCard
                key={id}
                id={id}
                title={title}
                comparison_platform={comparison_platform}
                comparison_type={comparison_type}
                column_num={column_num}
                created_at={created_at}
                render={getData}
              />
            ),
          )}
        </div>
      ) : (
        <Empty />
      )}
    </>
  );
};

export default List;
