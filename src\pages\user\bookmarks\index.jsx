import React, { useEffect, useState } from "react";
import { ToastContainer } from "react-toastify";
import bookmark from "service/api/bookmark.js";
import EmptyList from "components/ui/EmptyList.jsx";
import SummaryCard from "components/SummaryCard/index.jsx";
import TextSlicer from "components/TextSlicer/index.jsx";
import Paginate from "components/ui/Paginate.jsx";
import Loading from "components/ui/Loading.jsx";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";
import ChipsCategory from "./components/ChipsCategory";
import { CButton } from "components/ui/CButton";
import { CheckCircle, Plus, WarningDiamond } from "@phosphor-icons/react";
import Popup from "components/ui/PopUp.jsx";
import noteLists from "service/api/noteLists";
import DeletePopUp from "components/ui/DeletePopUp";
import ToolTip from "components/ui/ToolTip";
import { notification } from "utils/helper";

const Bookmarks = () => {
  const breadcrumbList = [{ title: "نشان‌ شده‌ ها" }];
  useBreadcrumb(breadcrumbList);
  const [myBookmarksLists, setMyBookmarksLists] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [updater, setUpdater] = useState(false);
  const [totalItems, setTotalItems] = useState(0);
  const [createCategoryPopup, setCreateCategoryPopup] = useState(false);
  const [newCategoryValue, setNewCategoryValue] = useState({
    title: "",
    description: "",
    collection_type: "bookmark",
  });
  const [isDeletePopUpOpen, setIsDeletePopUpOpen] = useState(false);
  const [isDeletePopUpOpen2, setIsDeletePopUpOpen2] = useState(false);
  const [activeCollection, setActiveCollection] = useState("default");
  const [categories, setCategories] = useState([]);
  const [collectionId, setCollectionId] = useState(null);
  const loadBookmarks = async (page) => {
    setIsLoading(true);
    try {
      const res = await bookmark.getBookmarks(page, activeCollection);
      const result = res?.data?.data;
      setMyBookmarksLists(result?.bookmarks);
      setTotalItems(result?.total_bookmarks);
    } catch (error) {
      console.log(error);
    }
    setIsLoading(false);
  };
  const getCollections = async () => {
    try {
      const { data } = await noteLists.getCollections({
        page: 1,
        count: 10,
        collection_type: "bookmark",
      });
      setCategories(data?.data?.map((item) => item));
    } catch (error) {
      console.log(error);
    }
  };

  // const addCategoryHandler = (e) => {
  //   setNewCategoryValue((prev) => ({
  //     ...prev,
  //     [e.target.name]: e.target.value,
  //   }));
  // };
  // const handleSubmitCategory = async () => {
  //   try {
  //     await noteLists.createCollection(newCategoryValue);
  //     setActiveCollection(newCategoryValue.title);
  //     getCollections();
  //   } catch (e) {
  //     console.log(e);
  //   }
  //   setCreateCategoryPopup(false);
  //   setNewCategoryValue({
  //     title: "",
  //     description: "",
  //     collection_type: "note",
  //   });
  // };
  const deleteCollectionHandler = async () => {
    try {
      await noteLists.deleteCollection(collectionId);
      // getCollections();
      setActiveCollection("default");
    } catch (error) {
      console.log(error);
    } finally {
      setIsDeletePopUpOpen2(false);
      setIsDeletePopUpOpen(false);
      getCollections();
    }
  };
  const addCategoryHandler = (e) => {
    setNewCategoryValue((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  const handleSubmitCategory = async () => {
    try {
      const res = await noteLists.createCollection(newCategoryValue);
      setActiveCollection(newCategoryValue.title.trim());
      setCollectionId(res?.data?.data?.id);
      notification.success(
        "پوشه جدید با موفقیت ثبت شد",
        <CheckCircle className="text-light-success-text-rest" size={26} />
      );
      getCollections();
    } catch (e) {
      console.log(e.response.data);
      notification.error(
        e?.response?.data?.message || "عنوان نمیتواند خالی باشد!",
        <WarningDiamond size={32} className="text-light-error-text-rest" />
      );
    }
    setCreateCategoryPopup(false);
    setNewCategoryValue({
      title: "",
      description: "",
      collection_type: "bookmark",
    });
  };

  useEffect(() => {
    loadBookmarks(page);
    getCollections();
  }, [page, updater, activeCollection]);

  if (isLoading) return <Loading />;
  if (!myBookmarksLists.length) {
    return (
      <>
        <div className="flex items-center justify-between px-5">
          <div className="flex items-center flex-wrap gap-2">
            {categories?.map((item) => (
              <React.Fragment key={item?.id}>
                {item?.description ? (
                  <ToolTip comp={item?.description}>
                    <ChipsCategory
                      title={item?.title === "default" ? "پیشفرض" : item?.title}
                      onClick={() => {
                        setActiveCollection(item?.title);
                        setCollectionId(item?.id);
                      }}
                      isActive={activeCollection === item?.title}
                    />
                  </ToolTip>
                ) : (
                  <ChipsCategory
                    title={item?.title === "default" ? "پیشفرض" : item?.title}
                    onClick={() => {
                      setActiveCollection(item?.title);
                      setCollectionId(item?.id);
                    }}
                    isActive={activeCollection === item?.title}
                  />
                )}
              </React.Fragment>
            ))}
          </div>
          <div className="flex items-center gap-2">
            {activeCollection !== "default" && (
              <div className="w-fit" onClick={() => setIsDeletePopUpOpen(true)}>
                <CButton className="flex items-center gap-1" mode="outline">
                  <p>حذف پوشه</p>
                </CButton>
              </div>
            )}
            <div
              className="w-fit"
              onClick={() => setCreateCategoryPopup(!createCategoryPopup)}
            >
              <CButton className="flex items-center gap-1">
                <p>افزودن پوشه</p>
                <Plus />
              </CButton>
            </div>
          </div>
        </div>
        <EmptyList
          title={"هیچ محتوایی در لیست نشان‌شده‌ها قرار ندارد"}
          description={
            "شما می‌توانید هر محتوایی را به لیست نشان‌شده‌ خود اضافه کنید"
          }
        />
        <Popup
          isOpen={createCategoryPopup}
          title="افزودن پوشه"
          hasButton={false}
          onClose={() => setCreateCategoryPopup(!createCategoryPopup)}
        >
          <input
            className="border border-light-neutral-border-medium-rest rounded-md w-full p-2 mt-1 p-y-[13px] font-body-large mb-4"
            value={newCategoryValue?.title.trim()}
            onChange={addCategoryHandler}
            name="title"
            placeholder="نام دسته‌بندی را وارد کنید..."
          />
          <textarea
            className="border border-light-neutral-border-medium-rest rounded-md w-full p-2 mt-1 p-y-[13px] font-body-large"
            rows={4}
            value={newCategoryValue?.description}
            onChange={addCategoryHandler}
            name="description"
            placeholder="توضیحات دسته بندی را وارد کنید..."
          />
          <div className="flex items-center justify-between gap-4 mt-4 font-button-large">
            <div className="w-full">
              <CButton
                className="bg-light-neutral-background-medium rounded-lg p-3 w-[100px]"
                role="neutral"
                onClick={() => setCreateCategoryPopup(!createCategoryPopup)}
              >
                انصراف
              </CButton>
            </div>
            <div className="w-full" onClick={handleSubmitCategory}>
              <CButton readOnly={!newCategoryValue.title}>ثبت</CButton>
            </div>
          </div>
        </Popup>
        <DeletePopUp
          onClose={() => setIsDeletePopUpOpen(false)}
          isOpen={isDeletePopUpOpen}
          submitHandler={deleteCollectionHandler}
          title="آیا می‌خواهید مجموعه را حذف کنید؟"
          description="در صورت حذف این مجموعه تمام محتوای موجود در آن حذف شده قابل بازیابی نخواهد بود."
        />
        <ToastContainer />
      </>
    );
  }

  return (
    <>
      <div className="h-full flex flex-col">
        <div className="flex items-center justify-between px-5">
          <div className="flex items-center flex-wrap gap-2">
            {categories?.map((item) => (
              <React.Fragment key={item?.id}>
                {item?.description ? (
                  <ToolTip comp={item?.description}>
                    <ChipsCategory
                      title={item?.title === "default" ? "پیشفرض" : item?.title}
                      onClick={() => {
                        setActiveCollection(item?.title);
                        setCollectionId(item?.id);
                      }}
                      isActive={activeCollection === item?.title}
                    />
                  </ToolTip>
                ) : (
                  <ChipsCategory
                    title={item?.title === "default" ? "پیشفرض" : item?.title}
                    onClick={() => {
                      setActiveCollection(item?.title);
                      setCollectionId(item?.id);
                    }}
                    isActive={activeCollection === item?.title}
                  />
                )}
              </React.Fragment>
            ))}
          </div>
          <div className="flex items-center gap-2">
            <div className="w-fit" onClick={() => setIsDeletePopUpOpen2(true)}>
              {activeCollection !== "default" && (
                <CButton className="flex items-center gap-1" mode="outline">
                  <p>حذف پوشه</p>
                </CButton>
              )}
            </div>
            <div
              className="w-fit"
              onClick={() => setCreateCategoryPopup(!createCategoryPopup)}
            >
              <CButton className="flex items-center gap-1">
                <p>افزودن پوشه</p>
                <Plus />
              </CButton>
            </div>
          </div>
        </div>
        <div className="container px-5 mx-auto grid grid-cols-12 gap-3 mt-5">
          {myBookmarksLists?.map((item) => (
            <div
              key={item?.id}
              className="md:col-span-6 col-span-12 bg-light-neutral-surface-card rounded-lg"
            >
              <SummaryCard
                data={item?.content || item.text}
                media={item?.platform}
                showHeaderMenu={true}
                showBookMark
                hasNotes={!!item?.note?.text}
                setMyBookmarksLists={setMyBookmarksLists}
                is_pin={item?.bookmark?.is_pin}
                setUpdater={setUpdater}
              >
                <TextSlicer length={200}>
                  {item?.content?.description ||
                    item?.content?.content ||
                    item?.content?.text}
                </TextSlicer>
              </SummaryCard>
            </div>
          ))}
          <ToastContainer />
        </div>
        <div>
          <Paginate
            page={page}
            setPage={setPage}
            dataCount={totalItems}
            per_page={10}
          />
        </div>
      </div>
      <Popup
        isOpen={createCategoryPopup}
        onClose={() => setCreateCategoryPopup(!createCategoryPopup)}
        title="افزودن پوشه"
        submitHandler={handleSubmitCategory}
      >
        <input
          className="border border-light-neutral-border-medium-rest rounded-md w-full p-2 mt-1 p-y-[13px] font-body-large mb-4"
          value={newCategoryValue?.title.trim()}
          onChange={addCategoryHandler}
          name="title"
          placeholder="نام دسته‌بندی را وارد کنید..."
        />
        <textarea
          className="border border-light-neutral-border-medium-rest rounded-md w-full p-2 mt-1 p-y-[13px] font-body-large"
          rows={4}
          value={newCategoryValue?.description}
          onChange={addCategoryHandler}
          name="description"
          placeholder="توضیحات دسته بندی را وارد کنید..."
        />
      </Popup>
      <DeletePopUp
        onClose={() => setIsDeletePopUpOpen2(false)}
        isOpen={isDeletePopUpOpen2}
        submitHandler={() => deleteCollectionHandler()}
        title="آیا می‌خواهید این مجموعه را حذف کنید؟"
      />
    </>
  );
};

export default Bookmarks;
