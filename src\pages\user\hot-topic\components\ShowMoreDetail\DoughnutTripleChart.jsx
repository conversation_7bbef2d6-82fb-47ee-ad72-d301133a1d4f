import { Chart as ChartJS, ArcElement, <PERSON><PERSON><PERSON>, <PERSON> } from "chart.js";
import { Doughn<PERSON> } from "react-chartjs-2";
import { toPersianNumber } from "utils/helper";

ChartJS.register(ArcE<PERSON>, Tooltip, Legend);

export function DoughnutTripleChart({ dataPercent }) {
  const data = {
    datasets: [
      {
        label: "",
        data: [
          dataPercent.sentiment_negative * 100,
          dataPercent.sentiment_positive * 100,
          dataPercent.sentiment_neutral * 100,
        ],
        backgroundColor: ["#E0526A", "#1CB0A5", "#E1E8EFCC"],
        borderColor: ["white", "white", "white"],
        borderWidth: 4,
        borderRadius: 8,
        hoverBorderColor: "white",
      },
    ],
  };

  const getOrCreateTooltip = (chart) => {
    let tooltipEl = chart.canvas.parentNode.querySelector("div");

    if (!tooltipEl) {
      tooltipEl = document.createElement("div");
      tooltipEl.style.background = "rgba(0, 0, 0, 0.7)";
      tooltipEl.style.borderRadius = "3px";
      tooltipEl.style.opacity = 1;
      tooltipEl.style.pointerEvents = "none";
      tooltipEl.style.position = "absolute";
      tooltipEl.style.transform = "translate(50%,0 )";
      tooltipEl.style.transition = "all .1s ease";

      const table = document.createElement("table");
      table.style.margin = "0px";

      tooltipEl.appendChild(table);
      chart.canvas.parentNode.appendChild(tooltipEl);
    }

    return tooltipEl;
  };

  const externalTooltipHandler = (context) => {
    // Tooltip Element
    const { chart, tooltip } = context;
    const tooltipEl = getOrCreateTooltip(chart);

    // Hide if no tooltip
    if (tooltip.opacity === 0) {
      tooltipEl.style.opacity = 0;
      return;
    }

    // Set Text
    if (tooltip.body) {
      const titleLines = tooltip.title || [];
      const bodyLines = tooltip.body.map((b) => b.lines);

      const tableHead = document.createElement("thead");

      titleLines.forEach((title) => {
        const tr = document.createElement("tr");
        tr.style.borderWidth = 0;

        const th = document.createElement("th");
        th.style.borderWidth = 0;
        const text = document.createTextNode(title);

        th.appendChild(text);
        tr.appendChild(th);
        tableHead.appendChild(tr);
      });

      const tableBody = document.createElement("tbody");

      bodyLines.forEach((body, i) => {
        const colors = tooltip.labelColors[i];
        const tr = document.createElement("tr");
        tr.style.backgroundColor = "inherit";
        tr.style.borderWidth = 0;
        if (colors.backgroundColor == "#E1E8EFCC") {
          tr.style.color = "white";
        } else {
          tr.style.color = colors.backgroundColor;
        }
        const td = document.createElement("td");
        td.style.borderWidth = 0;
        td.style.display = "flex";
        td.style.flexDirection = "column";
        body[0] = toPersianNumber(body[0]);
        body[0] += "٪";
        const text = document.createTextNode(body);
        const sentiment = document.createElement("span");
        if (colors.backgroundColor == "#E0526A") {
          sentiment.innerText = "منفی";
        }
        if (colors.backgroundColor == "#1CB0A5") {
          sentiment.innerText = "مثبت";
        }
        if (colors.backgroundColor == "#E1E8EFCC") {
          sentiment.innerText = "خنثی";
        }
        sentiment.className = "font-overline-small";
        td.className = "font-overline-medium";
        sentiment.style.color = "white";
        td.appendChild(text);
        td.appendChild(sentiment);
        tr.appendChild(td);
        tableBody.appendChild(tr);
      });

      const tableRoot = tooltipEl.querySelector("table");

      // Remove old children
      while (tableRoot.firstChild) {
        tableRoot.firstChild.remove();
      }

      // Add new children
      tableRoot.appendChild(tableHead);
      tableRoot.appendChild(tableBody);
    }

    const { offsetLeft: positionX, offsetTop: positionY } = chart.canvas;
    // Display, position, and set styles for font
    tooltipEl.style.opacity = 1;
    tooltipEl.style.left = positionX - 120 + "px";
    tooltipEl.style.top = positionY + 45 + "px";
    tooltipEl.style.font = tooltip.options.bodyFont.string;
    tooltipEl.style.padding =
      tooltip.options.padding + "px " + tooltip.options.padding + "px";
  };

  const options = {
    plugins: {
      tooltip: {
        enabled: false,
        position: "nearest",
        external: externalTooltipHandler,
      },
    },
  };

  return (
    <div className="size-[120px]">
      <Doughnut data={data} options={options} />
    </div>
  );
}
