import {
  Alarm,
  Binoculars,
  Exclude,
  Graph,
  ListMagnifyingGlass,
  Newspaper,
  NewspaperClipping,
  Sliders,
  Target,
  TrendUp,
} from "@phosphor-icons/react";

const reportboxInfo = [
  {
    title: "جست‌وجوی پیشرفته",
    count: 100,
    icon: ListMagnifyingGlass,
    color: "#6F5CD1",
    variant: "#6F5CD126",
  },
  {
    title: "هشدار",
    count: 100,
    icon: Alarm,
    color: "#0A55E1",
    variant: "#0A55E126",
  },
  {
    title: "فیلترها",
    count: 100,
    icon: Sliders,
    color: "#ED5C74",
    variant: "#ED5C7426",
  },
  {
    title: "بولتن",
    count: 100,
    icon: NewspaperClipping,
    color: "#DB9100",
    variant: "#DB910026",
  },
  {
    title: "مقایسه",
    count: 100,
    icon: Exclude,
    color: "#D12AF6",
    variant: "#D12AF626",
  },
  {
    title: "گزارش ۳۶۰",
    count: 100,
    icon: Target,
    color: "#17968C",
    variant: "#17968C26",
  },
  {
    title: "موج شناسی",
    count: 100,
    icon: TrendUp,
    color: "#E1CF0A",
    variant: "#E1CF0A26",
  },
  {
    title: "افکارسنجی",
    count: 100,
    icon: Binoculars,
    color: "#5CD2ED",
    variant: "#5CD2ED26",
  },
  {
    title: "شناسایی روابط",
    count: 100,
    icon: Graph,
    color: "#DB009D",
    variant: "#DB009D26",
  },
  {
    title: "پیشخوان",
    count: 100,
    icon: Newspaper,
    color: "#2A9AF6",
    variant: "#2A9AF626",
  },
];
export default reportboxInfo;
