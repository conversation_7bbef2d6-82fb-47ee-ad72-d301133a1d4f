import { Bell, Megaphone } from "@phosphor-icons/react";
import { useEffect, useState } from "react";
import { toPersianNumber } from "utils/helper";
import notification from "service/api/notification";
import { useLocation } from "react-router-dom";

const Menu = ({ handleChange }) => {
  const location = useLocation();
  const getTabName = (pathname) => {
    if (pathname.includes("Notifications")) {
      return "Notifications";
    }
    return "Announcement";
  };

  const tab = getTabName(location.pathname);

  const [activeTab, setActiveTab] = useState(tab);
  const [totals, setTotals] = useState({});
  useEffect(() => {
    notification
      .statistical()
      .then((res) =>
        setTotals({
          notification: res.data.data.total_notification,
          announcement: res.data.data.total_announcement,
        }),
      )
      .catch((err) => console.log(err));
  }, []);

  useEffect(() => {
    setActiveTab(tab);
  }, [tab]);

  return (
    <div className="w-full">
      <div className="w-full bg-white rounded-lg p-6 flex flex-col gap-1">
        <div
          className={`flex gap-2 h-14 items-center p-4 rounded-lg cursor-pointer ${
            activeTab === "Notifications"
              ? "bg-light-neutral-surface-highlight"
              : "bg-white"
          }`}
          onClick={() => {
            setActiveTab("Notifications");
            handleChange("Notifications");
          }}
          style={{
            color: activeTab === "Notifications" ? "#000000" : "#00000080",
          }}
        >
          <Bell size={24} />
          <div className="font-body-medium flex gap-1">
            <span>اطلاعیه‌ها</span>
            <span>{`(${toPersianNumber(totals.notification)})`}</span>
          </div>
        </div>

        <div
          className={`flex gap-2 h-14 items-center p-4 rounded-lg cursor-pointer ${
            activeTab === "Announcement"
              ? "bg-light-neutral-surface-highlight"
              : "bg-white"
          }`}
          onClick={() => {
            setActiveTab("Announcement");
            handleChange("Announcement");
          }}
          style={{
            color: activeTab === "Announcement" ? "#000000" : "#00000080",
          }}
        >
          <Megaphone size={24} />
          <div className="font-body-medium flex gap-1">
            <span>اعلانات</span>
            <span>{`(${toPersianNumber(totals.announcement)})`}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Menu;
