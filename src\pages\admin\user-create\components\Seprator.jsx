import PropTypes from "prop-types";

const Separator = ({ state, step }) => {
  return (
    <div className="w-6 flex justify-center">
      <div
        className="min-h-6 max-h-full border-r border-light-neutral-border-medium-rest"
        style={{
          backgroundColor: state > step ? "#19A399" : "transparent",
        }}
      ></div>
    </div>
  );
};

Separator.propTypes = {
  step: PropTypes.number,
  state: PropTypes.number,
};

export default Separator;
