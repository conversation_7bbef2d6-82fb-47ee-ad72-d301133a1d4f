import { toPersianN<PERSON>ber } from "utils/helper";
import NetworkGraphChart from "./NetworkGraphChart";

const WaveContentShare = ({ fetchedData, selectedNode, from, to }) => {
  return (
    <>
      <div
        className="bg-light-neutral-surface-card rounded-lg p-3"
        style={{
          boxShadow: "0px 2px 20px 0px #0000000D",
        }}
      >
        <div className="flex items-center gap-1">
          <p className="font-subtitle-large">شبکه انتشار محتوا در موج</p>
        </div>
        <NetworkGraphChart
          fetchedData={fetchedData}
          selectedNode={selectedNode}
          from={from}
          to={to}
        />
      </div>

      <div className="flex flex-wrap gap-4 pt-4">
        <div
          className="bg-light-neutral-surface-card rounded-lg p-2 flex items-center justify-center flex-1 min-w-[200px]"
          style={{
            boxShadow: "0px 2px 20px 0px #0000000D",
            height: "68px",
          }}
        >
          <div className="flex items-center justify-between text-center font-body-medium h-full w-full">
            <p className="font-body-medium text-light-neutral-text-low">
              تعداد کل گره‌ها در شبکه
            </p>
            <span className="font-headline-medium">
              {toPersianNumber(fetchedData?.nodes_count?.toLocaleString())}
            </span>
          </div>
        </div>
        <div
          className="bg-light-neutral-surface-card rounded-lg p-2 flex items-center justify-center flex-1 min-w-[200px]"
          style={{
            boxShadow: "0px 2px 20px 0px #0000000D",
            height: "68px",
          }}
        >
          <div className="flex items-center justify-between text-center font-body-medium h-full w-full">
            <p className="font-body-medium text-light-neutral-text-low">
              تعداد کل یال‌ها در شبکه
            </p>
            <span className="font-headline-medium">
              {toPersianNumber(fetchedData?.links_count?.toLocaleString())}
            </span>
          </div>
        </div>
        <div
          className="bg-light-neutral-surface-card rounded-lg p-2 flex items-center justify-center flex-1 min-w-[200px]"
          style={{
            boxShadow: "0px 2px 20px 0px #0000000D",
            height: "68px",
          }}
        >
          <div className="flex items-center justify-between text-center font-body-medium h-full w-full">
            <p className="font-body-medium text-light-neutral-text-low">
              تعداد گروه‌ها شبکه
            </p>
            <span className="font-headline-medium">
              {toPersianNumber(fetchedData?.groups_count?.toLocaleString())}
            </span>
          </div>
        </div>
      </div>
    </>
  );
};

export default WaveContentShare;
