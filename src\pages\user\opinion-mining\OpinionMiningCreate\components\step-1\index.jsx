// import { CInput } from "components/ui/CInput";
// import { CButton } from "components/ui/CButton";
// import { Link } from "react-router-dom";

// const StepOne = ({ setStep, opinionMiningData, setOpinionMiningData }) => {
//   const changeHandler = (e) => {
//     setOpinionMiningData({
//       ...opinionMiningData,
//       [e?.target?.name]: e?.target?.value,
//     });
//   };

//   return (
//     <div className="rounded-md bg-light-neutral-surface-card p-4">
//       <p className="font-subtitle-large mb-6">عنوان گزارش</p>
//       <CInput
//         name="title"
//         value={opinionMiningData?.title}
//         onChange={changeHandler}
//         inputProps={{ name: "title" }}
//         placeholder="عنوان گزارش افکارسنجی مورد نظر را وارد کنید."
//       />
//       <label className="font-overline-large pb-3">توضیحات</label>
//       <textarea
//         name="description"
//         value={opinionMiningData?.description}
//         onChange={changeHandler}
//         className="w-full h-40 rounded-md p-1.5 font-body-large outline-none border border-light-neutral-border-medium-rest mt-1"
//         placeholder="برای مثال: این گزارش جهت بازخوردگیری افکار و رفتار کاربران بر اساس طیف‌های سیاسی مختلف ایجاد شده است."
//       />

//       <div className="flex items-center gap-4 mt-4">
//         <Link className="w-full" to={"/app/opinion-mining/list"}>
//           <CButton role="neutral">لغو</CButton>
//         </Link>
//         <div className="w-full">
//           <CButton
//             role="neutral"
//             mode="outline"
//             onClick={() => setStep(2)}
//             readOnly={!opinionMiningData?.title.trim()}
//           >
//             مرحله بعد
//           </CButton>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default StepOne;

import { CInput } from "components/ui/CInput";
import { CButton } from "components/ui/CButton";
import { Link } from "react-router-dom";

const StepOne = ({ setStep, opinionMiningData, updateOpinionMiningData }) => {
  const changeHandler = (e) => {
    updateOpinionMiningData({
      [e?.target?.name]: e?.target?.value,
    });
  };

  return (
    <div className="rounded-md bg-light-neutral-surface-card p-4">
      <p className="font-subtitle-large mb-6">عنوان گزارش</p>
      <CInput
        name="title"
        value={opinionMiningData?.title}
        onChange={changeHandler}
        inputProps={{ name: "title" }}
        placeholder="عنوان گزارش افکارسنجی مورد نظر را وارد کنید."
      />
      <label className="font-overline-large pb-3">توضیحات</label>
      <textarea
        name="description"
        value={opinionMiningData?.description}
        onChange={changeHandler}
        className="w-full h-40 rounded-md p-1.5 font-body-large outline-none border border-light-neutral-border-medium-rest mt-1"
        placeholder="برای مثال: این گزارش جهت بازخوردگیری افکار و رفتار کاربران بر اساس طیف‌های سیاسی مختلف ایجاد شده است."
      />

      <div className="flex items-center gap-4 mt-4">
        <Link className="w-full" to={"/app/opinion-mining/list"}>
          <CButton role="neutral">لغو</CButton>
        </Link>
        <div className="w-full">
          <CButton
            role="neutral"
            mode="outline"
            onClick={() => setStep(2)}
            readOnly={!opinionMiningData?.title.trim()}
          >
            مرحله بعد
          </CButton>
        </div>
      </div>
    </div>
  );
};

export default StepOne;
