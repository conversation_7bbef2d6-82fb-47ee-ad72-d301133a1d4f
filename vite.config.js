import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
const fileNames = [
  "src",
  "assets",
  "constants",
  "hooks",
  "context",
  "components",
  "pages",
  "service",
  "utils",
  "themes",
  "services",
  "styles",
  "features",
  "routes",
  "public",
  "store",
  "layout",
];

const pathes = fileNames.reduce(
  (acc, cur) => ({
    ...acc,
    [cur]: `/${cur === "src" ? cur : "src/" + cur}`,
  }),
  "",
);
// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    host: "0.0.0.0",
    port: "80",
  },
  resolve: {
    alias: {
      ...pathes,
    },
  },
});
