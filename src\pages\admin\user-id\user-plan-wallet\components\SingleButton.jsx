const SingleButton = ({ Icon, title }) => {
  return (
    <div>
      <div
        // to={path}
        className="flex flex-col items-center cursor-pointer transition-all hover:bg-light-neutral-background-medium px-3 py-1 rounded-md w-fit"
        // onClick={onClick}
      >
        <div className="flex items-center justify-center w-10 h-10 bg-light-primary-background-highlight rounded-full">
          <Icon size={20} />
        </div>
        <p className="font-body-large">{title}</p>
      </div>
    </div>
  );
};

export default SingleButton;
