import { Plus } from "@phosphor-icons/react";
import { toPersianNumber } from "utils/helper";
import GroupsState from "./GroupsState";

const UserInviteInfo = ({ data, onAddToSecondTable }) => {
  const ticketStates = ["Inactive", "Deleted", "Pending", "Active"];
  const randomState =
    ticketStates[Math.floor(Math.random() * ticketStates.length)];

  const ticketStateTitles = {
    Inactive: "غیرفعال",
    Deleted: "حذف شده",
    Pending: "در انتظار",
    Active: "فعال",
  };

  const handleDragStart = (e) => {
    e.dataTransfer.setData("text/plain", JSON.stringify(data));
  };

  return (
    <div
      className="grid grid-cols-4 hover:bg-light-neutral-surface-highlight py-2 cursor-pointer"
      draggable
      onDragStart={handleDragStart}
    >
      <div className="flex items-center gap-3 font-body-medium">
        <div
          className="border rounded-md flex justify-center items-center border-[#6f5cd1] cursor-pointer"
          onClick={() => onAddToSecondTable(data)}
        >
          <Plus className="p-1" size={30} color="#6f5cd1" />
        </div>
        <p>{data?.first_name || "نام گروه"}</p>
      </div>
      <div className="flex font-body-medium">
        {toPersianNumber(data?.username)}
      </div>
      <div className="flex font-body-medium">
        {toPersianNumber(data?.phone_number)}
      </div>
      <div className="font-body-medium">
        <GroupsState
          title={ticketStateTitles[randomState] || ""}
          state={randomState}
        />
      </div>
    </div>
  );
};

export default UserInviteInfo;
