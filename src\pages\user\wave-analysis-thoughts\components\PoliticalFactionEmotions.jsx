import { memo } from "react";
import { toPersianNumber } from "utils/helper";
import PercentageColumnChart from "./charts/PercentageColumnChart";
import emotionsMap from "./PoliticalParties/constants/emotionsMap";
import categories from "./PoliticalParties/constants/categories";
import factionMap from "./PoliticalParties/constants/factionMap";

const transformEmotionsData = (emotionsData) => {
  const data = emotionsMap?.map((emotion) => ({
    name: emotion?.name,
    data: new Array(categories.length).fill(0),
    color: emotion?.color,
  }));

  emotionsData?.emotion?.forEach((factionObj) => {
    const factionKey = Object.keys(factionObj)[0];
    const factionName = factionMap?.[factionKey];
    const categoryIndex = categories.indexOf(factionName);
    if (categoryIndex === -1) return;
    const emotions = factionObj[factionKey];
    emotions?.forEach(({ key, count }) => {
      let emotionIndex = emotionsMap.findIndex((e) => e.key === key);
      if (emotionIndex === -1) {
        emotionIndex = emotionsMap.findIndex((e) => e.name === "سایر");
      }
      if (emotionIndex !== -1) {
        data[emotionIndex].data[categoryIndex] = count;
      }
    });
  });
  return { data, categories };
};

const PoliticalFactionEmotions = ({ emotionsData }) => {
  const { data, categories } = transformEmotionsData(emotionsData);

  console.log("emotionsData");
  console.log(emotionsData);
  console.log(data);

  if (!data.length) {
    return (
      <div className="bg-light-neutral-surface-card rounded-[8px] p-6 h-[490px]">
        <p className="font-subtitle-large pb-4">تحلیل هیجانات طیف‌های سیاسی </p>

        <div className="h-full flex items-center justify-between mx-auto">
          <p className="py-1.5 text-center font-body-bold-large text-light-neutral-text-medium flex items-center justify-between mx-auto">
            داده ای یافت نشد
          </p>
        </div>
      </div>
    );
  }
  return (
    <div className="bg-light-neutral-surface-card rounded-[8px] p-6">
      <p className="font-subtitle-large pb-4">تحلیل هیجانات طیف‌های سیاسی </p>
      <PercentageColumnChart
        data={data}
        categories={categories}
        tooltipFormatter={function () {
          return `<div style="display: flex; flex-direction: column; gap: 8px; text-align: center; font-family: iranyekan;">
                            <div>${
                              this.color === "#47DBB6"
                                ? "<div style='display: flex; align-items: center; gap:2px;'>" +
                                  "<b>شاد</b>" +
                                  "</div>"
                                : this.color === "#004080"
                                ? "<div style='display: flex; align-items: center; gap:2px;'>" +
                                  "<b>غمگین</b>" +
                                  "</div>"
                                : this.color === "#FF3300"
                                ? "<div style='display: flex; align-items: center; gap:2px;'>" +
                                  "<b>خشم</b>" +
                                  "</div>"
                                : this.color === "#8B4513"
                                ? "<div style='display: flex; align-items: center; gap:2px;'>" +
                                  "<b>تنفر</b>" +
                                  "</div>"
                                : this.color === "#660066"
                                ? "<div style='display: flex; align-items: center; gap:2px;'>" +
                                  "<b>ترس</b>" +
                                  "</div>"
                                : this.color === "#d6ba2d"
                                ? "<div style='display: flex; align-items: center; gap:2px;'>" +
                                  "<b>ناامیدی</b>" +
                                  "</div>"
                                : this.color === "#04c421"
                                ? "<div style='display: flex; align-items: center; gap:2px;'>" +
                                  "<b>خوش‌بینی</b>" +
                                  "</div>"
                                : this.color === "#4fdbf7"
                                ? "<div style='display: flex; align-items: center; gap:2px;'>" +
                                  "<b>اعتماد</b>" +
                                  "</div>"
                                : this.color === "#cccccc"
                                ? "<div style='display: flex; align-items: center; gap:2px;'>" +
                                  "<b>خنثی</b>" +
                                  "</div>"
                                : this.color === "#FFA500"
                                ? "<div style='display: flex; align-items: center; gap:2px;'>" +
                                  "<b>تعجب</b>" +
                                  "</div>"
                                : "<div style='display: flex; align-items: center; gap:2px;'>" +
                                  "<b>سایر</b>" +
                                  "</div>"
                            }</div>
                            <p>${toPersianNumber(this?.y)}</p>
                          </div>`;
        }}
      />
    </div>
  );
};

export default memo(PoliticalFactionEmotions);
