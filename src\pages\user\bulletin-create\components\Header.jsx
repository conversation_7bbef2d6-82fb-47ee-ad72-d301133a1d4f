import { CaretLeft, CaretRight, CheckCircle } from "@phosphor-icons/react";
import Steps from "components/ui/Steps";
import Alert from "components/ui/Alert";
import Divider from "components/ui/Divider";
import { CButton } from "components/ui/CButton";
import { useBulletinStore } from "store/bulletinStore";
import Bulletin from "service/api/bulletin.js";
import { notification } from "utils/helper.js";
import { useState } from "react";

const Header = ({
  showAlert = false,
  alertText = "",
  isTemplateSelected,
  children,
  stepsTexts = [],
  footer,
  setShowPopup,
  validation,
}) => {
  const { bulletin } = useBulletinStore();
  const { step, type } = useBulletinStore((state) => state.bulletin);
  const setBulletin = useBulletinStore((state) => state.setBulletin);

  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    if (loading) return;
    setLoading(true);
    try {
      let data = JSON.parse(JSON.stringify(bulletin));
      if (type === "auto") {
        if (step === 2) {
          const { metadata, chart, type, contentManual, export_type, ...rest } =
            data;
          data = rest;
        }
        if (step === 3) {
          const { metadata, type, contentManual, export_type, ...rest } = data;
          data = rest;
        }
        if (step === 4) {
          const { type, contentManual, export_type, ...rest } = data;
          data = rest;
        }
        const response = await Bulletin.updatePeriodic(bulletin.id, data);
        if (response.data.code === 200 || response.data.code === 201) {
          setBulletin({ step: step + 1 });
        } else {
          notification.error(
            "خطا در ایجاد بولتن",
            <CheckCircle className="text-light-error-text-rest" size={26} />
          );
        }
      } else {
        if (step === 2) {
          const { metadata, chart, type, content, export_type, ...rest } = data;
          data = rest;
        }
        if (step === 3) {
          const { metadata, type, content, export_type, ...rest } = data;
          data = rest;
        }
        if (step === 4) {
          const { type, content, export_type, ...rest } = data;
          data = rest;
        }
        data.content = data.contentManual;
        data.contentManual = null;

        const response = await Bulletin.updateManual(bulletin.id, data);
        if (response.data.code === 200 || response.data.code === 201) {
          setBulletin({ step: step + 1 });
        } else {
          notification.error(
            "خطا در ایجاد بولتن",
            <CheckCircle className="text-light-error-text-rest" size={26} />
          );
        }
      }

      setLoading(false);
      return true;
    } catch (e) {
      console.error(e);
    }
  };

  return (
    <div className="p-4 bg-white rounded-lg flex flex-col gap-4">
      <Steps step={step} texts={stepsTexts} />
      {showAlert && <Alert>{alertText}</Alert>}
      {children}
      <Divider />
      <div className="flex justify-between items-center flex-row-reverse">
        <div className="flex gap-4">
          <CButton
            role="neutral"
            rightIcon={<CaretRight />}
            className="min-w-max [direction:ltr]"
            size="lg"
            onClick={() => setBulletin({ step: step - 1 })}
            readOnly={loading}
          >
            مرحله قبل
          </CButton>
          <CButton
            leftIcon={<CaretLeft />}
            className="min-w-max [direction:ltr]"
            readOnly={!validation}
            size="lg"
            onClick={() => {
              step === 4 && !isTemplateSelected
                ? setShowPopup(true)
                : handleSubmit();
            }}
            disabled={loading}
          >
            مرحله بعد
          </CButton>
        </div>
        {footer}
      </div>
    </div>
  );
};

export default Header;
