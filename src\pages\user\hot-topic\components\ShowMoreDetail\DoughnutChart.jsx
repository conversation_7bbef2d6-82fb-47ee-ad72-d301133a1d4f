import { Chart as ChartJS, ArcElement, Toolt<PERSON>, Legend } from "chart.js";
import { Doughnut } from "react-chartjs-2";
import { toPersianNumber } from "utils/helper";

ChartJS.register(ArcElement, Tooltip, Legend);

export function DoughnutChart({ categories }) {
  const selectCategory = {
    FarhangHonarVaResane: "فرهنگ و هنر و رسانه",
    <PERSON><PERSON>: "سیاسی",
    <PERSON><PERSON>zmare: "روزمره",
    Ejtemaee: "اجتماعی",
    Varzeshi: "ورزشی",
    ElmiVaDaneshghai: "علمی و دانشگاهی",
    Beinolmelal: "بین الملل",
    Eghtesadi: "اقتصادی",
  };
  const data = {
    datasets: [
      {
        label: "",
        data: [
          ...Object.values({ ...categories })
            .map((i) => i * 100)
            .sort((x, y) => y - x),
        ],
        backgroundColor: [
          "#432FA7",
          "#792FA7",
          "#A72F85",
          "#E03838",
          "#E0B138",
          "F4538A",
          "8E7AB5",
          "F1FADA",
        ],
        borderColor: ["white", "white", "white"],
        borderWidth: 4,
        borderRadius: 16,
        hoverBorderColor: "white",
      },
    ],
  };

  const getOrCreateTooltip = (chart) => {
    let tooltipEl = chart.canvas.parentNode.querySelector("div");

    if (!tooltipEl) {
      tooltipEl = document.createElement("div");
      tooltipEl.style.background = "rgba(0, 0, 0, 0.7)";
      tooltipEl.style.borderRadius = "3px";
      tooltipEl.style.opacity = 1;
      tooltipEl.style.pointerEvents = "none";
      tooltipEl.style.position = "absolute";
      tooltipEl.style.transform = "translate(50%,0 )";
      tooltipEl.style.transition = "all .1s ease";

      const table = document.createElement("table");
      table.style.margin = "0px";

      tooltipEl.appendChild(table);
      chart.canvas.parentNode.appendChild(tooltipEl);
    }

    return tooltipEl;
  };

  const externalTooltipHandler = (context) => {
    // Tooltip Element
    const reverseCategories = {};
    for (const key in categories) {
      reverseCategories[categories[key] * 100] = selectCategory[key];
    }

    const { chart, tooltip } = context;
    const selectCat = chart.tooltip.dataPoints[0].parsed;

    const tooltipEl = getOrCreateTooltip(chart);
    // Hide if no tooltip
    if (tooltip.opacity === 0) {
      tooltipEl.style.opacity = 0;
      return;
    }

    // Set Text
    if (tooltip.body) {
      const titleLines = tooltip.title || [];
      const bodyLines = tooltip.body.map((b) => b.lines);

      const tableHead = document.createElement("thead");

      titleLines.forEach((title) => {
        const tr = document.createElement("tr");
        tr.style.borderWidth = 0;

        const th = document.createElement("th");
        th.style.borderWidth = 0;
        const text = document.createTextNode(title);

        th.appendChild(text);
        tr.appendChild(th);
        tableHead.appendChild(tr);
      });

      const tableBody = document.createElement("tbody");

      bodyLines.forEach((body, i) => {
        const tr = document.createElement("tr");
        tr.style.backgroundColor = "inherit";
        tr.style.borderWidth = 0;
        tr.style.color = "white";
        const td = document.createElement("td");
        td.style.borderWidth = 0;
        td.style.display = "flex";
        td.style.flexDirection = "column";
        body[0] = toPersianNumber(body[0]);
        body[0] += "٪";
        const text = document.createTextNode(body);
        const category = document.createElement("span");
        category.innerText = reverseCategories[selectCat];
        category.style.color = "white";
        category.className = "font-overline-small";
        td.className = "font-overline-medium";
        td.appendChild(text);
        td.appendChild(category);
        tr.appendChild(td);
        tableBody.appendChild(tr);
      });

      const tableRoot = tooltipEl.querySelector("table");

      // Remove old children
      while (tableRoot.firstChild) {
        tableRoot.firstChild.remove();
      }

      // Add new children
      tableRoot.appendChild(tableHead);
      tableRoot.appendChild(tableBody);
    }

    const { offsetLeft: positionX, offsetTop: positionY } = chart.canvas;
    // Display, position, and set styles for font
    tooltipEl.style.opacity = 1;
    tooltipEl.style.left = positionX + 35 + "px";
    tooltipEl.style.top = positionY - 70 + "px";
    tooltipEl.style.font = tooltip.options.bodyFont.string;
    tooltipEl.style.padding =
      tooltip.options.padding + "px " + tooltip.options.padding + "px";
  };

  const options = {
    plugins: {
      tooltip: {
        enabled: false,
        position: "nearest",
        external: externalTooltipHandler,
      },
    },
  };

  return (
    <div className="size-[180px]">
      <Doughnut data={data} options={options} />
    </div>
  );
}
