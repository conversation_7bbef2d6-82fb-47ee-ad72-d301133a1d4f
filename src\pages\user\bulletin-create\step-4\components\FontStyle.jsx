import { useEffect, useState } from "react";
import {
  PencilCircle,
  HighlighterCircle,
  TextItalic,
  TextB,
  TextUnderline,
} from "@phosphor-icons/react";
import clsx from "clsx";
import ToolTip from "components/ui/ToolTip";
import TextColors from "./TextColors.jsx";
import HighlightColors from "./HighlightColors.jsx";

const FontStyle = ({ handleChange, initialValue }) => {
  const [fontStyle, setFontStyle] = useState({
    bold: initialValue?.bold || false,
    italic: initialValue?.italic || false,
    underlined: initialValue?.underlined || false,
    text_color: initialValue?.text_color || "#000000",
    text_highlight: initialValue?.text_highlight || "auto",
  });
  useEffect(() => {
    handleChange(fontStyle);
  }, [fontStyle]);
  const selectedStyle = "bg-light-neutral-background-high rounded-lg";

  return (
    <div className="flex items-center gap-1">
      <div
        className={clsx("p-[7px]", fontStyle.bold && selectedStyle)}
        onClick={() =>
          setFontStyle((l) => {
            return { ...l, bold: !l.bold };
          })
        }
      >
        <TextB size={18} />
      </div>
      <div
        className={clsx("p-[7px]", fontStyle.italic && selectedStyle)}
        onClick={() =>
          setFontStyle((l) => {
            return { ...l, italic: !l.italic };
          })
        }
      >
        <TextItalic size={18} />
      </div>
      <div
        className={clsx("p-[7px]", fontStyle.underlined && selectedStyle)}
        onClick={() =>
          setFontStyle((l) => {
            return { ...l, underlined: !l.underlined };
          })
        }
      >
        <TextUnderline size={18} />
      </div>
      <ToolTip
        position="bottom-start"
        comp={
          <TextColors
            handleColor={(color) =>
              setFontStyle((l) => {
                return { ...l, text_color: color };
              })
            }
          />
        }
      >
        <div
          className={clsx(
            "p-[7px]",
            fontStyle.text_color !== "#000000" && selectedStyle,
          )}
        >
          <PencilCircle size={18} color={fontStyle.text_color} />
        </div>
      </ToolTip>
      <ToolTip
        position="bottom-start"
        comp={
          <HighlightColors
            handleColor={(color) =>
              setFontStyle((l) => {
                return { ...l, text_highlight: color };
              })
            }
          />
        }
      >
        <div
          className={clsx(
            "p-[7px]",
            fontStyle.text_highlight !== "auto" && selectedStyle,
          )}
        >
          <HighlighterCircle
            size={18}
            color={
              fontStyle.text_highlight == "auto"
                ? "#000000"
                : fontStyle.text_highlight
            }
          />
        </div>
      </ToolTip>
    </div>
  );
};

export default FontStyle;
