import WaveCardDetails from "./components/WaveCardDetails";
import { Eye, Heart, Repeat, Upload, User } from "@phosphor-icons/react";
import WaveContentShare from "./components/WaveContentShare";
import PioneerUsers from "./components/PioneerUsers";
import WaveHashtagDistribute from "./components/WaveHashtagDistribute";
import WaveKeywordDistribute from "./components/WaveKeywordDistribute";
import WaveTimeline from "./components/WaveTimeline";
import ParticipantCredits from "./components/ParticipantCredits";
import TopUsers from "./components/TopUsers";
import ContentShareFlow from "./components/ContentShareFlow";
import TopicGrouping from "./components/TopicGrouping";
import ContentDistributionByAccountAge from "./components/ContentDistributionByAccountAge";
import { useEffect, useState } from "react";
import waveAnalytics from "service/api/waveAnalytics";
import { useLocation } from "react-router-dom";
import WaveFakeDoughnut from "./components/WaveFakeDoughnut";

const WaveInfo = ({ from, to, id }) => {
  const [data, setData] = useState([]);
  const [fetchPioneer, setFetchedPioneer] = useState([]);
  const [fetchedContentAge, setFetchedContentAge] = useState([]);
  const [topSources, setTopSources] = useState([]);
  const [sort, setSort] = useState({ fa: "محتوا", en: "date" });
  const [fetchedParticipantCredits, setFetchedParticipantCredits] = useState(
    []
  );
  const [topGroupingData, setTopGroupingData] = useState([]);
  const [contentShareData, setContentShareData] = useState([]);
  const [selectedNode, setSelectedNode] = useState(null);
  const location = useLocation();
  const { elements, query_end_time, query_start_time } = location.state || {};
  const [topSourceLoading, setTopSourceLoading] = useState(false);
  const [topUsersLoading, setTopUsersLoading] = useState(false);

  const transformApiData = (apiData) => {
    if (!apiData || !Array.isArray(apiData)) return [];

    const dataMap = apiData.reduce((acc, item) => {
      acc[item.key] = item.count;
      return acc;
    }, {});

    const formatNumber = (num) => {
      if (!num) return "0";
      if (num >= 1000000)
        return `${
          (num / 1000000).toFixed(1).split(".")[1].startsWith("0")
            ? (num / 1000000).toFixed(0)
            : (num / 1000000).toFixed(1)
        }M`;
      if (num >= 1000)
        return `${
          (num / 1000).toFixed(1).split(".")[1].startsWith("0")
            ? (num / 1000).toFixed(0)
            : (num / 1000).toFixed(1)
        }K`;
      return num.toString();
    };

    return [
      {
        id: 0,
        title: "مجموع محتوا",
        count: formatNumber(dataMap["post"]),
        icon: Upload,
        variant: "#6F5CD126",
        color: "#6F5CD1",
      },
      {
        id: 1,
        title: "مجموع کاربران",
        count: formatNumber(dataMap["source"]),
        icon: User,
        variant: "#0A55E126",
        color: "#0A55E1",
        ratio: dataMap["source_post_ratio"]?.toFixed(1),
        subtitle: "میانگین تعداد محتوا",
      },
      {
        id: 2,
        title: "مجموع لایک",
        count: formatNumber(dataMap["like"]),
        icon: Heart,
        variant: "#ED5C7426",
        color: "#ED5C74",
        ratio: dataMap["like_post_ratio"]?.toFixed(1),
        subtitle: "متوسط تعداد لایک‌ها",
      },
      {
        id: 3,
        title: "مجموع بازنشر",
        count: formatNumber(dataMap["retweet"]),
        icon: Repeat,
        variant: "#DB910026",
        color: "#DB9100",
        ratio: dataMap["retweet_post_ratio"]?.toFixed(1),
        subtitle: "متوسط تعداد بازنشر",
      },
      {
        id: 4,
        title: "مجموع بازدید",
        count: formatNumber(dataMap["view"]),
        icon: Eye,
        variant: "#1CB0A526",
        color: "#1CB0A5",
        ratio: dataMap["view_post_ratio"]?.toFixed(1),
        subtitle: "متوسط تعداد بازدید",
      },
    ];
  };
  const fullAnalysisHandler = async () => {
    try {
      const response = await waveAnalytics.sendWaveData({
        report_type: "trends_statistical",
        platform: "twitter",
        start_date: query_start_time,
        end_date: query_end_time,
        hashtags: elements?.map((item) => item?.content) || [],
      });
      setData(response?.data?.data?.twitter || []);
    } catch (error) {
      console.log("Error fetching wave data:", error);
      setData([]);
    }
  };
  const fetchPioneerUsers = async () => {
    try {
      const response = await waveAnalytics.sendWaveData({
        report_type: "trends_graph",
        platform: "twitter",
        start_date: query_start_time,
        end_date: query_end_time,
        hashtags: elements?.map((item) => item?.content) || [],
        rows: 7,
      });
      setTopUsersLoading(true);
      setFetchedPioneer(response?.data?.data);
      setTopUsersLoading(false);
    } catch (error) {
      console.log(error);
      setTopUsersLoading(false);
    }
  };
  const fetchTopSources = async () => {
    try {
      const response = await waveAnalytics.sendWaveData({
        report_type: "top_sources",
        platform: "twitter",
        start_date: query_start_time,
        end_date: query_end_time,
        hashtags: elements?.map((item) => item?.content) || [],
        rows: 7,
      });
      setTopSources(response?.data?.data);
    } catch (error) {
      console.log(error);
    }
  };
  const fetchContentDistributionByAccountAge = async () => {
    try {
      const response = await waveAnalytics.sendWaveData({
        report_type: "trends_content_age",
        platform: "twitter",
        start_date: query_start_time,
        end_date: query_end_time,
        hashtags: elements?.map((item) => item?.content) || [],
      });
      setFetchedContentAge(response?.data?.data);
    } catch (error) {
      console.log(error);
    }
  };
  const fetchParticipantCredits = async () => {
    try {
      const response = await waveAnalytics.sendWaveData({
        report_type: "trends_account_credential",
        platform: "twitter",
        start_date: query_start_time,
        end_date: query_end_time,
        hashtags: elements?.map((item) => item?.content) || [],
      });
      setFetchedParticipantCredits(response?.data?.data);
    } catch (error) {
      console.log(error);
    }
  };
  const fetchTopGrouping = async () => {
    try {
      const { data } = await waveAnalytics.sendWaveData({
        report_type: "categories",
        platform: "twitter",
        start_date: query_start_time,
        end_date: query_end_time,
        hashtags: elements?.map((item) => item?.content) || [],
      });
      setTopGroupingData(data?.data);
    } catch (error) {
      console.log(error);
    }
  };
  function formatDateWithOffset(offsetMinutes) {
    const date = new Date();
    // Get the local time + offset time
    const offsetMs = offsetMinutes * 60 * 1000;
    const localTime = new Date(date.getTime() + offsetMs);
    // Format YYYY-MM-DD
    const yyyy = localTime.getUTCFullYear();
    const mm = String(localTime.getUTCMonth() + 1).padStart(2, "0");
    const dd = String(localTime.getUTCDate()).padStart(2, "0");
    // Format HH:MM:SS
    const hh = String(localTime.getUTCHours()).padStart(2, "0");
    const mi = String(localTime.getUTCMinutes()).padStart(2, "0");
    const ss = String(localTime.getUTCSeconds()).padStart(2, "0");
    return `${yyyy}-${mm}-${dd}T${hh}:${mi}:${ss}`;
  }
  const formattedTime = formatDateWithOffset(210);
  const fetchContentShareFlow = async () => {
    try {
      let newQuery = {};
      newQuery.process_range = "hourly";
      const { data } = await waveAnalytics.sendWaveData(
        {
          report_type: "process",
          platform: "twitter",
          start_date: `${query_start_time}+03:30`,
          end_date: `${formattedTime}+03:30`,
          hashtags: elements?.map((item) => item?.content) || [],
        },
        newQuery
      );
      setContentShareData(data?.data);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    fullAnalysisHandler();
    fetchParticipantCredits();
    fetchContentDistributionByAccountAge();
    fetchContentShareFlow();
    fetchTopGrouping();
    fetchPioneerUsers();
    fetchTopSources();
  }, []);

  const cardDetails = transformApiData(data);

  return (
    <>
      <div className="flex items-center justify-between gap-3 px-6 mt-4">
        {cardDetails.map((item) => (
          <div className="w-full" key={item.id}>
            <WaveCardDetails
              title={item.title}
              count={item.count}
              variant={item.variant}
              color={item.color}
              icon={item.icon}
              subtitle={item.subtitle}
              ratio={item.ratio}
            />
          </div>
        ))}
      </div>
      <div className="grid grid-cols-12 gap-4 px-6 mt-4">
        <div className="col-span-9">
          <WaveContentShare
            from={from}
            to={to}
            elements={elements}
            fetchedData={fetchPioneer}
            selectedNode={selectedNode}
          />
        </div>
        <div className="col-span-3">
          <PioneerUsers
            data={fetchPioneer}
            loading={topSourceLoading}
            setSelectedNode={setSelectedNode}
            selectedNode={selectedNode}
          />
        </div>

        <div className="col-span-6">
          <WaveHashtagDistribute elements={elements} />
        </div>
        <div className="col-span-6">
          <TopUsers
            elements={elements}
            fetchTopSources={topSources}
            setSort={setSort}
            sort={sort}
            loading={topUsersLoading}
          />
        </div>
        {/* <div className="col-span-6">
          <WaveKeywordDistribute />
        </div> */}

        <div className="col-span-12">
          <WaveTimeline id={id} />
        </div>
        <div className="col-span-12">
          <ParticipantCredits
            fetchedParticipantCredits={fetchedParticipantCredits}
          />
        </div>
        <div className="col-span-3">
          <WaveFakeDoughnut
            elements={elements}
            fetchedContentAge={fetchedContentAge}
          />
        </div>
        {/* <div className="col-span-4">
          <ContentDistributionByAccountAgeBarchart
            fetchedParticipantCredits={fetchedParticipantCredits}
          />
        </div> */}
        <div className="col-span-9">
          <ContentDistributionByAccountAge
            fetchedContentAge={fetchedContentAge}
          />
        </div>

        {/* <div className="col-span-12">
          <ContentShareFlow data={contentShareData} />
        </div> */}
        {/* <div className="col-span-6">
          <TopUsers
            elements={elements}
            fetchTopSources={topSources}
            setSort={setSort}
            sort={sort}
            loading={topUsersLoading}
          />
        </div> */}
        <div className="col-span-12">
          <TopicGrouping topGroupingData={topGroupingData} />
        </div>
      </div>
    </>
  );
};

export default WaveInfo;
