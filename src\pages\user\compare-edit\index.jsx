import { useEffect, useState } from "react";
import { useCompareStore } from "store/compareStore.js";
import compare from "service/api/compare.js";
import { useParams } from "react-router-dom";
import Loading from "components/ui/Loading.jsx";
import CompareCreate from "pages/user/compare-create/index.jsx";

const CompareEdit = () => {
  const { id } = useParams();
  const setCompare = useCompareStore((state) => state.setCompare);

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchCompare = async () => {
      if (!id) return;
      if (loading) return;
      setLoading(true);
      try {
        const response = await compare.getById(id);
        const {
          date_range,
          comparison_platform,
          comparison_type,
          params,
          title,
        } = response.data.data;
        setCompare({
          id: id,
          step: 2,
          type: comparison_type,
          platform: comparison_platform,
          date: { from: date_range.from_date, to: date_range.to_date },
          fields: params,
          title: title,
        });
      } catch (e) {
        console.error(e);
      } finally {
        setLoading(false);
      }
    };

    fetchCompare();
  }, []);

  return loading ? <Loading /> : <CompareCreate isEdit={true} />;
};

export default CompareEdit;
