import ListTile from "../list_title";
import SummaryCard from "components/SummaryCard";
import TextSlicer from "components/TextSlicer";
import Divider from "components/ui/Divider";
import PropTypes from "prop-types";

const ListCol = ({ media, data, color = null, active = true }) => {
  return (
    <div
      className={`relative [direction:rtl] flex flex-col bg-white rounded-lg shadow-[0px_2px_20px_0px_#0000000D] ${!!color ? "border-t-[12px]" : ""}`}
      style={{
        borderColor: !!color ? color : "none",
        filter: !active ? "blur(2px)" : "none",
      }}
    >
      <div className="p-4">
        <ListTile data={data} />
      </div>
      <Divider />
      <div className="flex flex-col">
        {data?.data.slice(0, 3).map((item, index) => (
          <div key={item.id}>
            <div>
              <SummaryCard media={media} data={item} showHeaderMenu={true}>
                <TextSlicer media={media} length={100}>
                  {item.text}
                </TextSlicer>
              </SummaryCard>
            </div>

            {index !== 2 && (
              <div className="my-4 px-4">
                <Divider />
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

ListCol.propTypes = {
  media: PropTypes.string,
  data: PropTypes.any,
  color: PropTypes.string.isRequired,
  active: PropTypes.bool.isRequired,
};

export default ListCol;
