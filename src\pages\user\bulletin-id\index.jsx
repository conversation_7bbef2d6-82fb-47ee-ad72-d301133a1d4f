import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import Loading from "components/ui/Loading";
import CreateReport from "./components/CreateReport";
import BulletinDetailsInfo from "./components/BulletinDetailsInfo";
import BulletinDetailsContent from "./components/BulletinDetailsContent";
import bulletin from "service/api/bulletin";
import { ToastContainer } from "react-toastify";
import { useLayoutContext } from "context/layout-context";

const BulletinId = () => {
  const [rerender, setRerender] = useState(false);
  const { id } = useParams();
  const [bulletinData, setBulletinData] = useState([]);
  const [loading, setLoading] = useState(false);
  const { setBreadcrumb } = useLayoutContext();

  useEffect(() => {
    setBreadcrumb([
      { title: "بولتن", link: "/app/bulletin" },
      { title: bulletinData?.title },
    ]);
  }, [bulletinData]);

  const getBulletin = async (id) => {
    setLoading(true);
    try {
      const { data } = await bulletin.getById(id, true);
      setBulletinData(data?.data);
    } catch (error) {
      console.log(error.response.data.message);
    }
    setLoading(false);
  };
  useEffect(() => {
    getBulletin(id);
  }, []);
  useEffect(() => {
    getBulletin(id);
  }, [rerender]);
  if (loading) {
    return <Loading height={0} />;
  }
  return (
    <>
      <div className="p-6 min-h-screen pb-52">
        <div className="grid grid-cols-12 justify-between gap-4">
          <div className="bg-white col-span-9 p-6 rounded-lg shadow-[0px_2px_20px_0px_#0000000D]">
            <BulletinDetailsContent bulletinData={bulletinData} />
          </div>

          <div className="col-span-3 flex flex-col gap-3">
            <BulletinDetailsInfo
              setRerender={setRerender}
              bulletinData={bulletinData}
            />
            <CreateReport bulletinData={bulletinData} />
          </div>
        </div>
      </div>
      {loading && <Loading />}
      <ToastContainer />
    </>
  );
};

export default BulletinId;
