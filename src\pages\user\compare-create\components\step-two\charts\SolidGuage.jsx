import { useEffect, useState } from "react";
import SolidgaugeChart from "components/Charts/SolidgaugeChart.jsx";
import { useCompareStore } from "store/compareStore.js";
import advanceSearch from "service/api/advanceSearch.js";
import { generateStatsQuery } from "utils/stats.js";
import Title from "./Title.jsx";
import { Card } from "components/ui/Card.jsx";
import { SpinnerGap } from "@phosphor-icons/react";

const SolidGuage = () => {
  const { platform, date, fields } = useCompareStore((state) => state.compare);
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const { from, to } = date;

  const percent = (value, total) => {
    const percentage = total ? (value / total) * 100 : 0;
    return percentage.toFixed(2);
  };

  const getData = async (fieldsCleared) => {
    setLoading(true);
    try {
      const reqsQueries = fieldsCleared.map((field) => {
        const query = generateStatsQuery(
          platform,
          "age",
          {
            from: parseInt(new Date(from).getTime() / 1000),
            to: parseInt(new Date(to).getTime() / 1000),
            q: field.q,
            fields: ["adult", "old", "young", "teen"],
            count: 10,
          },
          field.filters
        );
        return advanceSearch.search({ q: query });
      });

      const response = await Promise.allSettled(reqsQueries);

      const aggregatedData = [];

      response.forEach((res, index) => {
        if (res.status === "fulfilled") {
          const data = JSON.parse(res.value.data.data);
          const result = data.result.data || {};

          const total = result.teen + result.young + result.adult + result.old;
          aggregatedData.push({
            teen: percent(result.teen, total),
            young: percent(result.young, total),
            adult: percent(result.adult, total),
            old: percent(result.old, total),
            name: fieldsCleared[index]?.q,
          });
        }
      });

      setData(aggregatedData);
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  useEffect(() => {
    const fieldsCleared = fields.filter((value) => value.q);
    getData(fieldsCleared);
  }, [platform, fields, date]);

  if (!fields.filter((user) => user.id || user.q).length) {
    return null;
  }

  return (
    <div className="flex">
      <Card className="px-0">
        <div className="w-full flex flex-col gap-6">
          <Title title="دسته‌بندی سن"></Title>
          {loading ? (
            <div className="w-full h-[400px] flex justify-center items-center">
              <SpinnerGap size={40} className="animate-spin" />
            </div>
          ) : (
            <div className="flex items-center justify-center gap-0">
              <SolidgaugeChart
                age="teen"
                names={data.map((item) => item.name)}
                firstPercentage={Number(data[0]?.teen)}
                secondPercentage={Number(data[1]?.teen)}
                thirdPercentage={Number(data[2]?.teen)}
                fourthPercentage={Number(data[3]?.teen)}
                minWidth={"10rem"}
              />
              <SolidgaugeChart
                age="young"
                names={data.map((item) => item.name)}
                firstPercentage={Number(data[0]?.young)}
                secondPercentage={Number(data[1]?.young)}
                thirdPercentage={Number(data[2]?.young)}
                fourthPercentage={Number(data[3]?.young)}
                minWidth={"10rem"}
              />
              <SolidgaugeChart
                age="adult"
                names={data.map((item) => item.name)}
                firstPercentage={Number(data[0]?.adult)}
                secondPercentage={Number(data[1]?.adult)}
                thirdPercentage={Number(data[2]?.adult)}
                fourthPercentage={Number(data[3]?.adult)}
                minWidth={"10rem"}
              />
              <SolidgaugeChart
                age="old"
                names={data.map((item) => item.name)}
                firstPercentage={Number(data[0]?.old)}
                secondPercentage={Number(data[1]?.old)}
                thirdPercentage={Number(data[2]?.old)}
                fourthPercentage={Number(data[3]?.old)}
                minWidth={"10rem"}
              />
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default SolidGuage;
