// src/components/Category.jsx
import { memo, useEffect, useState } from "react";
import MultipleBar from "components/Charts/MultipleBar.jsx";
import { useCompareStore } from "store/compareStore.js";
import { Card } from "components/ui/Card.jsx";
import advanceSearch from "service/api/advanceSearch.js";
import { SpinnerGap } from "@phosphor-icons/react";
import PropTypes from "prop-types";
import Title from "./Title.jsx";
import { buildRequestData } from "utils/requestData.js";
import { SUBJECT_CATEGORIES } from "constants/subject-category.js";
import ExportMenu from "components/ExportMenu/index.jsx";

const Category = ({ compareType }) => {
  const [categories, setCategories] = useState([]);
  const [data, setData] = useState([]);
  const [total, setTotal] = useState([]);
  const [loading, setLoading] = useState(false);
  const { fields, platform, date } = useCompareStore((state) => state.compare);
  const [fieldsCleared, setFieldsCleared] = useState([]);

  // Update fieldsCleared only when relevant field properties change
  useEffect(() => {
    const newFieldsCleared = fields.filter((value) =>
      compareType === "profile" ? value?.id || value?.user_id : value?.q
    );
    const hasChanged =
      newFieldsCleared.length !== fieldsCleared.length ||
      newFieldsCleared.some((field, index) => {
        const prevField = fieldsCleared[index] || {};
        return compareType === "profile"
          ? field.id !== prevField.id || field.user_id !== prevField.user_id
          : field.q !== prevField.q;
      });

    if (hasChanged) {
      console.log("fieldsCleared updated:", newFieldsCleared);
      setFieldsCleared(newFieldsCleared);
    }
  }, [fields, compareType]);

  const getData = async (fieldsCleared, abortController) => {
    if (!fieldsCleared.length) return;

    setLoading(true);
    try {
      const reqsQueries = fieldsCleared.map((field) => {
        const filters = {
          date,
          platform,
        };
        if (compareType === "topic") filters["q"] = field?.q;
        else
          filters["sources"] = field.id
            ? [field.id.toString()]
            : field.user_id
            ? [field.user_id.toString()]
            : [];

        const request = buildRequestData(filters, "categories");
        return advanceSearch.search(request, abortController);
      });

      const response = await Promise.allSettled(reqsQueries);

      const mappedData = response
        .filter((res) => res.status === "fulfilled")
        .map((res) => {
          const stinasData = res?.value?.data?.data?.[platform] || [];

          return {
            total: res?.value?.data?.data?.total || 0,
            categories: stinasData.map(
              (item) =>
                SUBJECT_CATEGORIES.find(
                  (category) => category.value === item.key
                )?.label
            ),
            data: stinasData,
          };
        });

      setTotal(mappedData.map((item) => item.total));
      setCategories(mappedData.flatMap((item) => item.categories));
      setData(mappedData.map((item) => item.data));
    } catch (error) {
      if (!abortController.signal.aborted) {
        console.error("Error fetching data:", error);
      }
    } finally {
      if (!abortController.signal.aborted) {
        setLoading(false);
      }
    }
  };

  // Fetch data when fieldsCleared or date changes
  useEffect(() => {
    const abortController = new AbortController();
    console.log("Fetch effect triggered with:", {
      fieldsCleared,
      date,
      platform,
    });
    if (fieldsCleared.length) {
      getData(fieldsCleared, abortController);
    }
    return () => {
      abortController.abort();
    };
  }, [fieldsCleared, date, platform]);

  // Prepare series and time for ExportMenu
  const series = fieldsCleared.map((field, index) => ({
    name: field.q || field.name || field.user_name || `Category ${index + 1}`,
    data: data[index]?.map((item) => item.count) || [],
    time:
      data[index]?.map(
        (item) =>
          SUBJECT_CATEGORIES.find((category) => category.value === item.key)
            ?.label
      ) || [],
  }));

  const time = [...new Set(categories)];

  if (!fields.filter((user) => user.id || user.user_id || user.q).length) {
    return null;
  }

  return (
    <div className="flex compare-category-container">
      <Card className="!p-6">
        <div className="flex flex-col gap-6 w-full">
          <div className="flex items-center justify-between">
            <Title title="دسته‌بندی موضوعی محتوای منتشر شده" />
            <ExportMenu
              chartSelector=".compare-category-container"
              fileName="category"
              series={series}
              time={time}
              excelHeaders={["Category", ...series.map((s) => s.name)]}
              onError={(error) => console.error("Export error:", error)}
              menuItems={["PNG", "JPEG", "Excel"]}
            />
          </div>
          {loading ? (
            <div className="w-full h-[400px] flex justify-center items-center">
              <SpinnerGap size={40} className="animate-spin" />
            </div>
          ) : (
            <>
              {data?.map((item) => item).length ? (
                <MultipleBar
                  categories={categories}
                  data={data}
                  total={total}
                  compareType={compareType}
                  platform={platform}
                />
              ) : (
                <div className="h-[430px] flex items-center justify-center font-subtitle-medium">
                  داده‌ای برای نمایش وجود ندارد
                </div>
              )}
            </>
          )}
        </div>
      </Card>
    </div>
  );
};

Category.propTypes = {
  type: PropTypes.string,
  compareType: PropTypes.string.isRequired,
};

export default memo(Category);
