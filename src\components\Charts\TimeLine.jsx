import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { toPersianNumber } from "utils/helper";
import { useEffect, useState } from "react";
import PropTypes from "prop-types";

const TimeLine = ({ data = [] }) => {
  const [chartData, setChartData] = useState([]);

  useEffect(() => {
    if (data && data.length > 0) {
      try {
        // Transform the API data to chart format
        const transformedData = data
          .map((item) => {
            const date = new Date(item.datetime);
            // Check if date is valid
            if (isNaN(date.getTime())) {
              console.warn("Invalid date format:", item.datetime);
              return null;
            }
            return {
              x: date.getTime(),
              y: item.count || 0,
              description: item.description || null,
              datetime: item.datetime,
            };
          })
          .filter(Boolean); // Remove null entries

        console.log("Transformed data:", transformedData);
        setChartData(transformedData);
      } catch (error) {
        console.error("Error transforming timeline data:", error);
        setChartData([]);
      }
    } else {
      setChartData([]);
    }
  }, [data]);

  const options = {
    chart: {
      type: "spline",
      height: 400,
      scrollablePlotArea: {
        minWidth: 600,
        scrollPositionX: 1,
      },
    },
    title: {
      text: null,
      enabled: false,
    },
    exporting: {
      buttons: {
        contextButton: {
          menuItems: ["downloadPNG"],
          x: 0,
          y: -10,
        },
      },
    },
    accessibility: {
      enabled: false,
    },
    subtitle: {
      text: null,
    },
    xAxis: {
      type: "datetime",
      visible: false,
    },
    legend: {
      enabled: false,
    },
    yAxis: {
      title: {
        text: null,
      },
      labels: {
        formatter: function () {
          return toPersianNumber(this.value);
        },
      },
      minorGridLineWidth: 0,
      gridLineWidth: 1,
    },
    tooltip: {
      headerFormat: "",
      backgroundColor: "#efefef",
      textAlign: "center",
      useHTML: true,
      shadow: false,
      crosshairs: true,
      shared: false,
      formatter: function () {
        const point = this.point;
        const date = new Date(point.x);
        const formattedDate = date
          .toLocaleDateString("fa-IR", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
            hour: "2-digit",
            minute: "2-digit",
          })
          .replace(/[\u200E]/g, "");

        let tooltipHTML = `<div style="font-family:'iranyekan',serif;font-size: 12px; direction: rtl; padding: 10px;">`;

        tooltipHTML += `<div style="margin-bottom: 8px;">
            <span style="font-weight: bold;">تعداد: </span>
            <span style="color: #333;">${toPersianNumber(point.y)}</span>
          </div>`;

        tooltipHTML += `<div style="margin-bottom: 8px; color: #555;">
            ${formattedDate}
          </div>`;

        if (point.description) {
          tooltipHTML += `<div style="margin-top: 10px; padding: 8px; background: #f5f5f5; border-radius: 4px; border-right: 3px solid #432FA7;">
              <div style="font-weight: bold; margin-bottom: 5px; color: #432FA7;">توضیحات:</div>
              <div style="line-height: 1.4; color: #333;">${point.description}</div>
            </div>`;
        }

        tooltipHTML += "</div>";
        return tooltipHTML;
      },
    },
    credits: {
      enabled: false,
    },
    plotOptions: {
      spline: {
        lineWidth: 3,
        states: {
          hover: {
            lineWidth: 4,
          },
        },
        marker: {
          enabled: true,
          radius: 4,
          symbol: "circle",
          fillColor: "#432FA7",
          lineColor: "#432FA7",
          lineWidth: 2,
        },
        color: "#432FA7",
      },
    },
    series: [
      {
        name: "تعداد",
        data: chartData.map((point) => ({
          x: point.x,
          y: point.y,
          description: point.description,
          datetime: point.datetime,
          marker: {
            enabled: true,
            radius: point.description ? 8 : 4,
            fillColor: point.description ? "#FF6B6B" : "#432FA7",
            lineColor: point.description ? "#FF6B6B" : "#432FA7",
            lineWidth: 2,
            symbol: "circle",
          },
        })),
        color: "#432FA7",
      },
    ],
  };

  // Show empty state if no data
  if (!data || data.length === 0) {
    return (
      <div
        style={{ width: "100%", height: "400px" }}
        className="flex items-center justify-center"
      >
        <div className="text-center text-gray-500">
          <p>داده‌ای برای نمایش وجود ندارد</p>
        </div>
      </div>
    );
  }

  return (
    <div style={{ width: "100%", height: "100%" }}>
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};

TimeLine.propTypes = {
  data: PropTypes.array,
};

export default TimeLine;
