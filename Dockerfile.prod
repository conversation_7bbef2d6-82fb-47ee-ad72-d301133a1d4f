FROM docker.arvancloud.ir/node:20-alpine AS builder
# ENV NODE_ENV production

# Add a work directory
WORKDIR /app

# Cache and Install dependencies
COPY package.json .
COPY yarn.lock .

RUN yarn install

# Copy app files
COPY . .
#COPY .env.production .env.production
ENV VITE_API_URL https://api.synappse.ir
ENV VITE_AI_SERVICE_URL https://api.synappse.ir

# Install Vite globally
RUN yarn global add vite

# Build the app
RUN yarn build

# Bundle static assets with nginx
FROM docker.arvancloud.ir/nginx:1.21.0-alpine as production
ENV NODE_ENV production

# Copy built assets from builder
COPY --from=builder /app/dist /app/build

# Add your nginx.conf
COPY nginx.prod.conf /etc/nginx/conf.d/default.conf

# Expose port
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
