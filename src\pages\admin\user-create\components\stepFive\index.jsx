import { CaretDown } from "@phosphor-icons/react";
import { Eye } from "@phosphor-icons/react/dist/ssr";
import { CButton } from "components/ui/CButton";
import Divider from "components/ui/Divider";

const StepFive = ({ setStep }) => {
  return (
    <div>
      <div className="grid gap-4 bg-light-neutral-surface-card rounded-[8px] p-6">
        <p className="font-subtitle-large">بررسی نهایی</p>

        <div className={`font-body-medium leading-9`}>
          <div className="flex items-center justify-between">
            <p className="text-light-neutral-text-medium">نام کاربری</p>
            <p>username.username</p>
          </div>

          <div className="flex items-center justify-between">
            <p className="text-light-neutral-text-medium">شماره موبایل</p>
            <p>09123456789</p>
          </div>

          <div className="flex items-center justify-between">
            <p className="text-light-neutral-text-medium">نام و نام‌خانوادگی</p>
            <p>نام کاربر</p>
          </div>

          <div className="flex items-center justify-between">
            <p className="text-light-neutral-text-medium">ایمیل </p>
            <p>نام خانوادگی کاربر</p>
          </div>

          <div className="flex items-start justify-between gap-4">
            <p className="text-light-neutral-text-medium">توضیحات</p>
            <div className="flex justify-end">
              <p className="w-96 leading-6">
                وی در خانواده‌ای مذهبی چشم به جهان گشود و پس از گذران زندگی در
                زندان‌های گوانتاناموا به سیاره مریخ فرار کرد
              </p>
            </div>
          </div>
          <Divider />

          <div className="flex items-center justify-between my-3">
            <p className="text-light-neutral-text-medium">پلن کاربر</p>
            <p className="font-body-large">پیشرفته</p>
          </div>
          <div className="flex items-center justify-between my-3">
            <p className="text-light-neutral-text-medium">قیمت پلن</p>
            <div className="flex items-center gap-2">
              <p className="font-headline-medium">۳٬۰۰۰٬۰۰۰</p>
              <p className="font-body-medium text-light-neutral-text-medium">
                تومان
              </p>
            </div>
          </div>
          <div className="flex justify-end mb-3 select-none">
            <div className="w-fit font-body-medium text-light-primary-background-rest rounded-md transition hover:bg-light-primary-background-highlight cursor-pointer flex items-center justify-center p-2">
              اطلاعات کامل پلن
              <CaretDown />
            </div>
          </div>

          <Divider />
          <div className="flex items-center justify-between my-3">
            <p className="text-light-neutral-text-medium">نقش کاربر</p>
            <p className="font-body-large">کاربر عادی</p>
          </div>
          <Divider />
          <div className="flex items-center justify-between my-3">
            <p className="text-light-neutral-text-medium">رمزعبور</p>
            <div className="flex items-center gap-1">
              <p className="font-body-large">*************</p>
              <Eye
                size={20}
                className="text-light-neutral-text-low cursor-pointer"
              />
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-full" onClick={() => setStep(4)}>
            <CButton role="neutral">مرحله قبل</CButton>
          </div>
          <div className="w-full" onClick={() => window.alert("افزودن کاربر")}>
            <CButton>افزودن کاربر</CButton>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StepFive;
