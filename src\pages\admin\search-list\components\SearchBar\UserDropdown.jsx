import { CaretLeft, Check, MagnifyingGlass, X } from "@phosphor-icons/react";
import PropTypes from "prop-types";
import { useEffect, useRef, useState } from "react";
import { CInput } from "components/ui/CInput.jsx";
import UserService from "service/api/userService.js";

const UserDropdown = ({ onChange }) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [selectedItems, setSelectedItems] = useState([]);
  const dropdownRef = useRef(null);
  const [searchValue, setSearchValue] = useState("");

  const [availableItems, setAvailableItems] = useState([]);

  const fetchUsers = async () => {
    try {
      const data = { q: searchValue };
      const response = await UserService.getUserList(data, {});
      console.log(response);
      if (response?.data?.data?.users) {
        setAvailableItems(
          response.data.data.users.map((item) => ({
            value: item.username,
            label: item.username,
          })),
        );
      } else {
        setAvailableItems([]);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    }
  };

  useEffect(() => {
    if (showDropdown && searchValue.length >= 2) {
      fetchUsers();
    } else {
      setAvailableItems([]);
    }
  }, [showDropdown, searchValue]);

  const getSelectedItemsLabel = () => {
    if (
      selectedItems &&
      selectedItems?.length > 0 &&
      selectedItems?.length !== availableItems.length
    ) {
      return selectedItems?.join(", ");
    }
    return "همه";
  };

  const handleChange = (item) => {
    setSelectedItems((prevItems) => {
      // Toggle the item: remove if present, add if not
      const newItems = prevItems.includes(item.value)
        ? prevItems.filter((val) => val !== item.value)
        : [...prevItems, item.value];

      onChange(newItems);
      return newItems;
    });
  };

  const clearList = () => {
    setSelectedItems([]);
    onChange([]);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowDropdown(false);
      }
    };

    if (showDropdown) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showDropdown]);

  return (
    <div>
      <div className="flex items-center gap-3 font-button-medium">
        <span
          className="flex gap-2 items-center cursor-pointer hover:bg-gray-200 transition duration-200 rounded-lg p-2"
          onClick={() => setShowDropdown(!showDropdown)}
        >
          انتخاب کاربر:
          <span className="text-[#7f7f7f]">{getSelectedItemsLabel()}</span>
          <CaretLeft />
        </span>
      </div>

      {showDropdown && (
        <div
          className="bg-white h-auto font-body-small w-[20rem] shadow-md absolute z-[10] p-4 rounded"
          ref={dropdownRef}
        >
          <div className={"flex flex-row gap-2 items-center justify-between"}>
            <CInput
              id={"q"}
              name={"q"}
              inset={true}
              headingIcon={<MagnifyingGlass />}
              size={"sm"}
              validation={"none"}
              direction={"rtl"}
              placeholder={"نام کاربر را جست‌و‌جو کنید"}
              onChange={(e) => setSearchValue(e.target.value)}
              className={"flex-1 !mb-0"}
            />

            {selectedItems.length > 0 && (
              <X
                size={16}
                className={"inline-block top-2 right-2 cursor-pointer"}
                onClick={clearList}
              />
            )}
          </div>
          <div className={"overflow-y-scroll max-h-[15rem] scrollbar-thin"}>
            {availableItems.map((item) => (
              <label
                key={item.value}
                onClick={() => handleChange(item)}
                className="flex justify-between items-center gap-2 py-2 px-2 rounded hover:bg-light-neutral-surface-highlight"
              >
                {item.label}
                {selectedItems.includes(item.value) && (
                  <Check size={16} className={"inline-flex"} />
                )}
              </label>
            ))}
            {searchValue.length < 2 ? (
              <p className={"font-body-small text-center p-4"}>
                برای جستجو حداقل ۲ کاراکتر را وارد کنید.
              </p>
            ) : (
              availableItems.length === 0 && (
                <p className={"font-body-small text-center p-4"}>
                  کاربری یافت نشد!
                </p>
              )
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default UserDropdown;

UserDropdown.propTypes = {
  onChange: PropTypes.func,
};
