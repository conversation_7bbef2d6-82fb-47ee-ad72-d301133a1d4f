import TextWithBullet from "components/TextWithBullet";

const StepTwoGuide = () => {
  return (
    <div className="font-body-medium">
      <h3 className="mb-4">راهنما</h3>
      <div className="list-disc list-inside flex flex-col gap-4">
        <TextWithBullet>
          برای تنظیم دقیق فیلتر خود، ابتدا بستر (های) مورد نظر خود را انتخاب
          کنید.
        </TextWithBullet>
        <TextWithBullet>
          سپس کلمه کلیدی، عبارت یا ترکیبی منطقی از کلمات کلیدی را در باکس "کلمات
          کلیدی" وارد نمایید. توجه داشته باشید که این فیلتر بر اساس کلمات تعیین
          شده در این مرحله فیلتر خواهد شد.
        </TextWithBullet>
        <TextWithBullet>
          شما می‌توانید با استفاده از عملوندهای منطقی + و - و * یک ترکیب از
          کلمات کلیدی را جهت فیلتر نتایج ایجاد کنید.عملوند + به معنای وجود قطعی
          ترکیب کلمات، عملوند - به معنای عدم وجود قطعی کلمات و عملوند * برای شرط
          وجود حداقلی یک کلمه در ترکیب کلمات به کار می‌رود.
        </TextWithBullet>
        <TextWithBullet>
          توجه داشته باشید که حداقل نیازمندی برای ایجاد یک فیلتر جدید، اعمال
          فیلتری دیگر علاوه بر تعیین بستر می‌باشد در غیر اینصورت فیلتر ساخته
          نمی‌شود.
        </TextWithBullet>
      </div>
    </div>
  );
};

export default StepTwoGuide;
