@import "textStyle.css";
@import "colors.css";

@font-face {
  font-family: iranyekan;
  font-style: normal;
  font-weight: bold;
  src: url(../assets/fonts/IranYekanX/IRANYekanX-Bold.woff2) format("woff2");
}

@font-face {
  font-family: iranyekan;
  font-style: normal;
  font-weight: 100;
  src: url(../assets/fonts/IranYekanX/IRANYekanX-Thin.woff2) format("woff2");
}

@font-face {
  font-family: iranyekan;
  font-style: normal;
  font-weight: 300;
  src: url(../assets/fonts/IranYekanX/IRANYekanX-Light.woff2) format("woff2");
}

@font-face {
  font-family: iranyekan;
  font-style: normal;
  font-weight: normal;
  src: url(../assets/fonts/IranYekanX/IRANYekanX-Regular.woff2) format("woff2");
}

@font-face {
  font-family: iranyekan;
  font-style: normal;
  font-weight: 400;
  src: url(../assets/fonts/IranYekanX/IRANYekanX-Regular.woff2) format("woff2");
}

@font-face {
  font-family: iranyekan;
  font-style: normal;
  font-weight: 500;
  src: url(../assets/fonts/IranYekanX/IRANYekanX-Regular.woff2) format("woff2");
}

@font-face {
  font-family: iranyekan;
  font-style: normal;
  font-weight: 700;
  src: url(../assets/fonts/IranYekanX/IRANYekanX-Bold.woff2) format("woff2");
}

@font-face {
  font-family: iranyekan;
  font-style: normal;
  font-weight: 800;
  src: url(../assets/fonts/IranYekanX/IRANYekanX-ExtraBold.woff2) format("woff2");
}

@font-face {
  font-family: iranyekan;
  font-style: normal;
  font-weight: 850;
  src: url(../assets/fonts/IranYekanX/IRANYekanX-Black.woff2) format("woff2");
}

@font-face {
  font-family: iranyekan;
  font-style: normal;
  font-weight: 900;
  src: url(../assets/fonts/IranYekanX/IRANYekanX-ExtraBlack.woff2) format("woff2");
}
