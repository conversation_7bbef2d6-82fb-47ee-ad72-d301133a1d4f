import { Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import NodeCardSelect from "./NodeCardSelect";

const PlaceSelect = ({ placeNodesData, data, setData }) => {
  const handleSelect = (item) => {
    const exists = data?.some((node) => node.id === item.id);
    setData(
      exists ? data?.filter((node) => node.id !== item.id) : [...data, item]
    );
  };
  const transformedData = Object.keys(placeNodesData).map((name, index) => ({
    id: "location-" + name,
    title: name,
    desc: "هویت فضای مجازی",
    twitter: placeNodesData[name].twitter?.toString() || "0",
    telegram: placeNodesData[name].telegram?.toString() || "0",
    instagram: placeNodesData[name].instagram?.toString() || "0",
    news: placeNodesData[name].news?.toString() || "0",
    isSelected: false,
  }));
  return (
    <>
      <p className="mb-2">شخص را جست‌وجو کنید</p>
      <div className="opinion-mining-swiper-container mb-6">
        <Swiper
          spaceBetween={10}
          slidesPerView={3}
          navigation
          pagination={{ clickable: true }}
          modules={[Navigation]}
          loop={true}
          breakpoints={{
            0: { slidesPerView: 3 }, // mobile fallback
            1024: { slidesPerView: 3 }, // medium screens
            2560: { slidesPerView: 3 }, // large screens and up
          }}
          className="swiper-container w-full px-6 kiosk-swiper"
        >
          {transformedData?.map((item) => (
            <SwiperSlide key={item.id}>
              <NodeCardSelect
                title={item?.title}
                twitter={item?.twitter}
                telegram={item?.telegram}
                instagram={item?.instagram}
                news={item?.news}
                handleSelect={() => handleSelect(item)}
                isSelected={data?.some((x) => x.id == item?.id)}
              />
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </>
  );
};

export default PlaceSelect;
