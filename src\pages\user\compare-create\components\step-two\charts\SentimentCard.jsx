import { useEffect, useRef, useState } from "react";
import SemiCircleDoughnut from "components/Charts/SemiCircleDoughnut.jsx";
import { fixPercentToShow, shortener } from "utils/helper.js";
import { sentiment } from "utils/selectIcon.js";
import { useCompareStore } from "store/compareStore.js";

const SentimentCard = ({ data, name }) => {
  const { fields } = useCompareStore((state) => state.compare);
  const fieldsCleared = fields.filter(
    (field) => field.q || field.username || field?.user_name,
  );
  const colors = ["#1DCEA3", "#6D72E5", "#DB6DE5", "#F7A912"];

  const selectSentiment = {
    positive: `<div style="display:flex;flex-direction:column;gap:4px"><img src=${sentiment["مثبت"]} /><span>مثبت</span></div>`,
    neutral: `<div style="display:flex;flex-direction:column;gap:4px"><img src=${sentiment["خنثی"]} /><span>خنثی</span></div>`,
    negative: `<div style="display:flex;flex-direction:column;gap:4px"><img src=${sentiment["منفی"]} /><span>منفی</span></div>`,
  };

  const chartRef = useRef(null);
  const [legendWidth, setLegendWidth] = useState("auto");

  useEffect(() => {
    if (chartRef.current) {
      // Set the legend width equal to the chart's width
      setLegendWidth(`${chartRef.current.offsetWidth - 30}`);
    }
  }, [data]);

  return (
    <div ref={chartRef} className="w-full flex justify-center">
      <SemiCircleDoughnut
        name={name}
        minWidth={"15rem"}
        colors={colors}
        titleFormatter={selectSentiment[name]}
        titleOffset={-2 * 4}
        legendFormatter={function () {
          const index = this.index;
          return `<div
      dir="rtl"
      style="display:grid;grid-template-columns:1fr 1fr;font-family:iranyekan;width:${legendWidth}px;padding: 2px ${
        legendWidth * 0.2
      }px;"
    >
      <div style="display:grid;grid-template-columns:1fr 1fr;gap:0px">
        <span style="font-size:16px">${fixPercentToShow(this.y / 100)}</span>
       
      </div>
      <div style="justify-self:right">
        <div style="display:flex;align-items:center;gap:4px">
          <span style="font-size:14px;direction: rtl;">${shortener(
            fieldsCleared[index]?.user_name ||
              fieldsCleared[index]?.q ||
              fieldsCleared[index]?.username,
            14,
          )}</span>
          <div style="width:12px;height:12px;background-color:${
            this.color
          };border-radius:100%;"></div>
        </div>
      </div>
    </div>`;
        }}
        tooltipFormatter={function () {
          return `<div style="display:flex;flex-direction:column;gap:8px;text-align:center;font-family:iranyekan,serif;direction: rtl;"><div>${shortener(
            fieldsCleared[this.colorIndex]?.q ||
              fieldsCleared[this.colorIndex]?.username ||
              fieldsCleared[this.colorIndex]?.user_name,
            14,
          )}</div><div>${fixPercentToShow(this.y / 100)}</div></div>`;
        }}
        data={data}
      />
    </div>
  );
};

export default SentimentCard;
