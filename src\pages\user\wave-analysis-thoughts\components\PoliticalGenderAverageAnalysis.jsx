import { toPersian<PERSON><PERSON>ber } from "utils/helper";
import PopulationChart from "./charts/BasicBarChart";
import { memo } from "react";

const transformGenderData = (gendersData) => {
  const { gender_groups } = gendersData;
  const genderMap = [
    { key: "male", name: "مرد", color: "#1C60B0" },
    { key: "female", name: "زن", color: "#E052B8" },
    { key: "unknown", name: "نامشخص", color: "#7F7F7F" },
  ];
  const categories = [
    "مستقل",
    "حامیان انقلاب اسلامی",
    "فعالین مدنی غرب‌گرا",
    "منافقین",
    "احمدی‌نژادی",
    "سلطنت‌طلب",
    "برانداز",
  ];
  const factionMap = {
    unknown: "مستقل",
    osoolgera: "حامیان انقلاب اسلامی",
    eslahtalab: "فعالین مدنی غرب‌گرا",
    monafegh: "منافقین",
    ahmadinezhad: "احمدی‌نژادی",
    saltanat: "سلطنت‌طلب",
    barandaz: "برانداز",
  };
  const data = genderMap.map((gender) => ({
    name: gender.name,
    data: new Array(categories.length).fill(0),
    color: gender.color,
  }));

  const categoryTotals = new Array(categories.length).fill(0);

  gender_groups?.forEach((factionObj) => {
    const factionKey = Object.keys(factionObj)[0];
    const factionName = factionMap[factionKey];
    const categoryIndex = categories.indexOf(factionName);
    if (categoryIndex === -1) return;

    const genders = factionObj[factionKey];
    const total = genders.reduce((sum, { count }) => sum + count, 0);
    categoryTotals[categoryIndex] = total;

    genders.forEach(({ key, count }) => {
      const genderIndex = genderMap.findIndex((g) => g.key === key);
      if (genderIndex !== -1) {
        data[genderIndex].data[categoryIndex] = count;
      }
    });
  });

  // Convert counts to percentages
  data.forEach((gender) => {
    gender.data = gender.data.map((count, index) => {
      const total = categoryTotals[index];
      return total > 0 ? (count / total) * 100 : 0;
    });
  });

  return { data, categories };
};

const PoliticalGenderAverageAnalysis = ({ gendersData }) => {
  const { data, categories } = transformGenderData(gendersData);
  if (!data.length) {
    return (
      <div className="bg-light-neutral-surface-card rounded-[8px] p-6 h-[490px]">
        <p className="font-subtitle-large pb-4">تحلیل جنسیت طیف‌های سیاسی</p>
        <div className="h-full flex items-center justify-between mx-auto">
          <p className="py-1.5 text-center font-body-bold-large text-light-neutral-text-medium flex items-center justify-between mx-auto">
            داده ای یافت نشد
          </p>
        </div>
      </div>
    );
  }
  return (
    <div className="bg-light-neutral-surface-card rounded-[8px] p-6">
      <p className="font-subtitle-large pb-4">تحلیل جنسیت طیف‌های سیاسی</p>
      <PopulationChart
        data={data}
        categories={categories}
        type="bar"
        YgridLineWidth={0}
        XgridLineWidth={1}
        tooltipFormatter={function () {
          return `<div style="display: flex; flex-direction: column; gap: 8px; text-align: center; font-family: iranyekan;">
                      <div>${
                        this.color === "#1C60B0"
                          ? "<div style='display: flex; align-items: center; gap:2px;'>" +
                            "<b>مرد</b>" +
                            '<svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">' +
                            '<path d="M15.3542 2.25H11.9792C11.83 2.25 11.687 2.30926 11.5815 2.41475C11.476 2.52024 11.4167 2.66332 11.4167 2.8125C11.4167 2.96168 11.476 3.10476 11.5815 3.21025C11.687 3.31574 11.83 3.375 11.9792 3.375H13.9965L11.0385 6.33305C9.92112 5.41974 8.49551 4.97079 7.05647 5.07907C5.61743 5.18735 4.27505 5.84456 3.30697 6.91478C2.33888 7.985 1.81916 9.38636 1.85528 10.829C1.8914 12.2717 2.48061 13.6453 3.50104 14.6657C4.52147 15.6861 5.89506 16.2753 7.33772 16.3115C8.78037 16.3476 10.1817 15.8278 11.252 14.8598C12.3222 13.8917 12.9794 12.5493 13.0877 11.1103C13.1959 9.67122 12.747 8.24561 11.8337 7.12828L14.7917 4.17094V6.1875C14.7917 6.33668 14.851 6.47976 14.9565 6.58525C15.062 6.69074 15.205 6.75 15.3542 6.75C15.5034 6.75 15.6465 6.69074 15.752 6.58525C15.8575 6.47976 15.9167 6.33668 15.9167 6.1875V2.8125C15.9167 2.66332 15.8575 2.52024 15.752 2.41475C15.6465 2.30926 15.5034 2.25 15.3542 2.25ZM10.6602 13.872C10.0308 14.5011 9.22893 14.9295 8.35607 15.1029C7.48321 15.2764 6.57851 15.1872 5.75636 14.8465C4.93421 14.5059 4.23153 13.9291 3.73715 13.1891C3.24277 12.4491 2.9789 11.5792 2.9789 10.6893C2.9789 9.79933 3.24277 8.92938 3.73715 8.1894C4.23153 7.44942 4.93421 6.87265 5.75636 6.53199C6.57851 6.19134 7.48321 6.10211 8.35607 6.27558C9.22893 6.44905 10.0308 6.87743 10.6602 7.50656C11.5028 8.35145 11.976 9.496 11.976 10.6893C11.976 11.8825 11.5028 13.0271 10.6602 13.872Z" fill="#1C60B0" /> ' +
                            "</svg>" +
                            "</div>"
                          : this.color === "#E052B8"
                          ? "<div style='display: flex; align-items: center; gap:2px;'>" +
                            "<b>زن</b>" +
                            '<svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">' +
                            '<path d="M14.792 6.74993C14.792 5.6614 14.4761 4.59625 13.8827 3.68367C13.2893 2.77109 12.4439 2.05029 11.449 1.60869C10.4541 1.16708 9.35236 1.02365 8.27752 1.19577C7.20268 1.3679 6.20088 1.8482 5.3936 2.57842C4.58633 3.30863 4.00827 4.25739 3.72953 5.30964C3.4508 6.36188 3.48336 7.47239 3.82327 8.5065C4.16318 9.5406 4.79584 10.4539 5.64451 11.1355C6.49319 11.8172 7.52141 12.238 8.60449 12.3468V14.0624H6.35449C6.20531 14.0624 6.06223 14.1217 5.95674 14.2272C5.85126 14.3327 5.79199 14.4757 5.79199 14.6249C5.79199 14.7741 5.85126 14.9172 5.95674 15.0227C6.06223 15.1282 6.20531 15.1874 6.35449 15.1874H8.60449V16.8749C8.60449 17.0241 8.66376 17.1672 8.76924 17.2727C8.87473 17.3782 9.01781 17.4374 9.16699 17.4374C9.31618 17.4374 9.45925 17.3782 9.56474 17.2727C9.67023 17.1672 9.72949 17.0241 9.72949 16.8749V15.1874H11.9795C12.1287 15.1874 12.2718 15.1282 12.3772 15.0227C12.4827 14.9172 12.542 14.7741 12.542 14.6249C12.542 14.4757 12.4827 14.3327 12.3772 14.2272C12.2718 14.1217 12.1287 14.0624 11.9795 14.0624H9.72949V12.3468C11.1163 12.2057 12.4015 11.5553 13.3366 10.5215C14.2717 9.48773 14.7903 8.14391 14.792 6.74993ZM4.66699 6.74993C4.66699 5.85992 4.93091 4.98989 5.42538 4.24987C5.91985 3.50985 6.62265 2.93307 7.44492 2.59247C8.26718 2.25188 9.17198 2.16277 10.0449 2.3364C10.9178 2.51003 11.7196 2.93862 12.349 3.56795C12.9783 4.19729 13.4069 4.99911 13.5805 5.87203C13.7542 6.74494 13.665 7.64974 13.3245 8.47201C12.9839 9.29428 12.4071 9.99708 11.6671 10.4915C10.927 10.986 10.057 11.2499 9.16699 11.2499C7.97392 11.2486 6.83008 10.7741 5.98645 9.93047C5.14282 9.08684 4.6683 7.94301 4.66699 6.74993Z" fill="#E052B8"/> ' +
                            "</svg>" +
                            "</div>"
                          : "<div style='display: flex; align-items: center; gap:2px;'>" +
                            "<b>نامشخص</b>" +
                            '<svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">' +
                            '<path d="M14.792 7.31243C14.792 6.2239 14.4761 5.15875 13.8827 4.24617C13.2893 3.33359 12.4439 2.61279 11.449 2.17119C10.4541 1.72958 9.35236 1.58615 8.27752 1.75827C7.20268 1.9304 6.20088 2.4107 5.3936 3.14092C4.58633 3.87113 4.00827 4.81989 3.72953 5.87214C3.4508 6.92438 3.48336 8.03489 3.82327 9.06899C4.16318 10.1031 4.79584 11.0164 5.64451 11.698C6.49319 12.3797 7.52141 12.8005 8.60449 12.9093V16.3124C8.60449 16.4616 8.66376 16.6047 8.76924 16.7102C8.87473 16.8157 9.01781 16.8749 9.16699 16.8749C9.31618 16.8749 9.45925 16.8157 9.56474 16.7102C9.67023 16.6047 9.72949 16.4616 9.72949 16.3124V12.9093C11.1163 12.7682 12.4015 12.1178 13.3366 11.084C14.2717 10.0502 14.7903 8.70641 14.792 7.31243ZM9.16699 11.8124C8.27698 11.8124 7.40695 11.5485 6.66693 11.054C5.92691 10.5596 5.35013 9.85678 5.00953 9.03451C4.66894 8.21224 4.57983 7.30744 4.75346 6.43453C4.92709 5.56161 5.35568 4.75979 5.98501 4.13045C6.61435 3.50112 7.41617 3.07253 8.28909 2.8989C9.162 2.72527 10.0668 2.81438 10.8891 3.15497C11.7113 3.49557 12.4141 4.07235 12.9086 4.81237C13.4031 5.55239 13.667 6.42242 13.667 7.31243C13.6657 8.50551 13.1912 9.64934 12.3475 10.493C11.5039 11.3366 10.3601 11.8111 9.16699 11.8124Z" fill="black" fill-opacity="0.5"/> ' +
                            "</svg>" +
                            "</div>"
                      }</div>
                      <p>${toPersianNumber(this.y.toFixed(0))}%</p>
                    </div>`;
        }}
      />
    </div>
  );
};

export default memo(PoliticalGenderAverageAnalysis);
