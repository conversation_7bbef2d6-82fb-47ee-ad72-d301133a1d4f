import { TrashSimple, User, Users, UsersFour } from "@phosphor-icons/react";
import { CButton } from "components/ui/CButton";
import AnnouncementDropDown from "../announcementDropdown";
import { toPersianNumber } from "utils/helper";
import { useState } from "react";

const Step2 = ({ setStep, step, selectedCard, handleCardClick }) => {
  const [selectedGroups, setSelectedGroups] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState([]);

  const handleUserSelect = (selectedOptions) => {
    if (!selectedOptions || selectedOptions.length === 0) return;

    const newOptions = Array.isArray(selectedOptions)
      ? selectedOptions
      : [selectedOptions];

    const newUsers = newOptions.filter(
      (option) => !selectedUsers.some((user) => user.value === option.value)
    );

    if (newUsers.length > 0) {
      setSelectedUsers((prev) => [...prev, ...newUsers]);
    }
  };

  const handleRemoveUser = (value) => {
    setSelectedUsers(selectedUsers.filter((user) => user.value !== value));
  };

  const handleGroupSelect = (selectedOptions) => {
    if (!selectedOptions || selectedOptions.length === 0) return;

    const newOptions = Array.isArray(selectedOptions)
      ? selectedOptions
      : [selectedOptions];

    const newGroups = newOptions.filter(
      (option) => !selectedGroups.some((group) => group.value === option.value)
    );

    if (newGroups.length > 0) {
      setSelectedGroups((prev) => [...prev, ...newGroups]);
    }
  };

  const handleRemoveGroup = (value) => {
    setSelectedGroups(selectedGroups.filter((group) => group.value !== value));
  };

  return (
    <div>
      <label className="font-overline-large" htmlFor="">
        ارسال به:
      </label>
      <div className="flex gap-2 justify-between pt-2 w-full">
        <div
          className={`border bg-[#f7f9fb] rounded-lg w-[190px] h-[120px] flex justify-center items-center hover:transition-colors cursor-pointer ${
            selectedCard === 0 ? "!bg-[#B4ABE34D]" : ""
          }`}
          onClick={() => handleCardClick(0)}
          tabIndex={0}
        >
          <UsersFour weight="duotone" color="#562272" size={60} />
        </div>
        <div
          className={`border bg-[#f7f9fb] rounded-lg w-[190px] h-[120px] flex justify-center items-center hover:transition-colors cursor-pointer ${
            selectedCard === 1 ? "!bg-[#B4ABE34D]" : ""
          }`}
          onClick={() => handleCardClick(1)}
          tabIndex={0}
        >
          <Users weight="duotone" color="#562272" size={60} />
        </div>
        <div
          className={`border bg-[#f7f9fb] rounded-lg w-[190px] h-[120px] flex justify-center items-center hover:transition-colors cursor-pointer ${
            selectedCard === 2 ? "!bg-[#B4ABE34D]" : ""
          }`}
          onClick={() => handleCardClick(2)}
          tabIndex={0}
        >
          <User weight="duotone" color="#562272" size={60} />
        </div>
      </div>
      {selectedCard === 1 && (
        <div className="pt-5">
          <AnnouncementDropDown
            subsets={[
              {
                text: "گروه شماره ۱",
                value: "support",
                memberCount: toPersianNumber("232"),
              },
              {
                text: "گروه شماره ۲",
                value: "technical",
                memberCount: toPersianNumber("102"),
              },
              {
                text: "گروه شماره ۳",
                value: "finances",
                memberCount: toPersianNumber("68"),
              },
              {
                text: "گروه شماره ۴",
                value: "sfasfaf",
                memberCount: toPersianNumber("25"),
              },
            ]}
            title="انتخاب گروه"
            handleChange={handleGroupSelect}
            value={selectedGroups}
            disabled={false}
            placeholder="گروه‌ها مورد نظر خود را انتخاب کنید"
          />
        </div>
      )}
      {selectedCard === 2 && (
        <div className="pt-5">
          <AnnouncementDropDown
            subsets={[
              {
                text: "کاربر شماره ۱",
                value: "user1",
                number: "۰۹۱۲ ۳۴۵ ۶۷۸۹",
              },
              {
                text: "کاربر شماره ۲",
                value: "user2",
                number: "۰۹۱۲ ۳۴۵ ۶۷۸۹",
              },
              {
                text: "کاربر شماره ۳",
                value: "user3",
                number: "۰۹۱۲ ۳۴۵ ۶۷۸۹",
              },
              {
                text: "کاربر شماره ۴",
                value: "user4",
                number: "۰۹۱۲ ۳۴۵ ۶۷۸۹",
              },
            ]}
            title="انتخاب کاربر"
            handleChange={handleUserSelect}
            value={selectedUsers}
            disabled={false}
            placeholder="کاربران مورد نظر خود را انتخاب کنید"
          />
        </div>
      )}
      {selectedCard !== 0 && (
        <div className="mt-4">
          {selectedGroups.length > 0 && (
            <div className="mb-4">
              <div className="flex flex-wrap gap-4 w-full">
                {selectedGroups.map((group) => (
                  <div
                    key={group.value}
                    className="w-[31%] bg-[#f7f9fb] border p-1 rounded-lg px-2 flex justify-between items-center"
                  >
                    <div className="flex flex-col justify-start items-start gap-2">
                      <span className="text-right font-body-medium">
                        {group.text}
                      </span>
                      <span className="text-right font-overline-small text-[#b1b3b4]">
                        {group.memberCount} نفر
                      </span>
                    </div>
                    <div>
                      <TrashSimple
                        size={20}
                        color="#BE223C"
                        className="cursor-pointer hover:opacity-80"
                        onClick={() => handleRemoveGroup(group.value)}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {selectedGroups.length > 0 && selectedUsers.length > 0 && (
            <hr className="my-4 border-t border-gray-300" />
          )}

          {selectedUsers.length > 0 && (
            <div className="flex flex-wrap gap-4 w-full">
              {selectedUsers.map((user) => (
                <div
                  key={user.value}
                  className="w-[31%] bg-[#f7f9fb] border p-1 rounded-lg px-2 flex justify-between items-center"
                >
                  <div className="flex flex-col justify-start items-start gap-2">
                    <span className="text-right font-body-medium">
                      {user.text}
                    </span>
                    <span className="text-right font-overline-small text-[#b1b3b4]">
                      username | {user.number}
                    </span>
                  </div>
                  <div>
                    <TrashSimple
                      size={20}
                      color="#BE223C"
                      className="cursor-pointer hover:opacity-80"
                      onClick={() => handleRemoveUser(user.value)}
                    />
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
      <div className="flex gap-6 w-full font-button-medium pt-6">
        <div
          className="bg-light-neutral-background-medium cursor-pointer rounded-lg w-full h-10 flex items-center justify-center"
          onClick={() => setStep(step - 1)}
        >
          گام قبل
        </div>
        <CButton type={"submit"} onClick={() => setStep(step + 1)}>
          ادامه
        </CButton>
      </div>
    </div>
  );
};

export default Step2;
