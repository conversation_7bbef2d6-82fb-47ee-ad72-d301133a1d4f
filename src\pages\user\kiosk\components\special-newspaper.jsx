import { useEffect, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import newsPaper from "service/api/newsPaper";
import {
  ArrowSquareOut,
  Copy,
  FileArrowDown,
  MagnifyingGlassPlus,
} from "@phosphor-icons/react";
import { parseTimeToPersianSummary, toPersianNumber } from "utils/helper";
import Divider from "components/ui/Divider";
import Paginate from "components/ui/Paginate";
import Popup from "./Popup";
import "../style.css";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";
import { useLayoutContext } from "context/layout-context";

const SpecialNewspaper = () => {
  const navigate = useNavigate();
  const { agency, id } = useParams();
  const [page, setPage] = useState(1);
  const [data, setData] = useState([]);
  const [loaded, setLoaded] = useState(false);
  const [dataById, setDataById] = useState([]);
  const [copiedText, setCopiedText] = useState("");
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState("");
  const [selectedImageUrl, setSelectedImageUrl] = useState("");
  const { setBreadcrumb } = useLayoutContext();
  const location = useLocation();
  const { id: newsPaperId } = location.state || {};

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await newsPaper.info(id);
        setDataById(response.data.data);
      } catch (err) {
        console.error(err);
      }
    };
    fetchData();
  }, [id]);

  useEffect(() => {
    const fetchData = async () => {
      const params = {
        agency: id,
        categories: [],
        rows: 9,
        page,
      };

      try {
        const response = await newsPaper.post(params);
        setData(response.data.data);
      } catch (err) {
        console.error(err);
      }
    };
    fetchData();
  }, [agency, page]);

  const categoryTranslations = {
    political: "سیاسی",
    economic: "اقتصادی",
    social: "اجتماعی",
    culture: "فرهنگی",
    sport: "ورزشی",
    other: "سایر",
    environment: "محیط زیست",
  };

  const handleImageClick = (image, url) => {
    setSelectedImage(image);
    setSelectedImageUrl(url);
    setIsPopupOpen(true);
  };

  useEffect(() => {
    setLoaded(true);
  }, []);

  const handleCopy = (text) => {
    const textarea = document.createElement("textarea");
    textarea.value = text;
    document.body.appendChild(textarea);
    textarea.select();
    document.execCommand("copy");
    document.body.removeChild(textarea);
    setCopiedText(text);
    setTimeout(() => {
      setCopiedText("");
    }, 2000);
  };

  const newsPaperData = data?.newspaper || [];
  const breadcrumbInfo = [
    { title: "پیشخوان", link: "/app/newspaper" },
    {
      title: dataById?.agency || "",
      link: `/app/newspaper/list/${newsPaperId}`,
    },
    { title: "آرشیو" },
  ];
  useBreadcrumb(breadcrumbInfo);
  useEffect(() => {
    setBreadcrumb(breadcrumbInfo);
  }, [dataById]);
  return (
    <div style={{ fontFamily: "iranyekan" }}>
      <div className="!bg-white w-[98%] duration-300 shadow-md p-4 mx-auto rounded-md">
        <div className="flex gap-5 items-center">
          <div className="h-[4.5rem] w-[4.5rem] bg-[#ffffff] flex justify-center items-center rounded-[130px] shadow-xl transition-shadow duration-300 hover:shadow-xl">
            <img
              className="rounded-[130px]"
              src={`${dataById?.logo_image}`}
              alt={`Avatar for ${data?.agency}`}
            />
          </div>
          <div className="flex gap-2 flex-col">
            <span className="font-headline-medium">{dataById?.agency}</span>
            <div>
              <span className="font-body-medium text-[#7f7f7f] pl-1">
                دسته‌بندی:
              </span>
              {dataById?.categories && (
                <span className="font-body-large">
                  {dataById.categories.length === 1
                    ? categoryTranslations[dataById.categories[0]]
                    : dataById.categories
                        .map((category) => categoryTranslations[category])
                        .join(", ")}
                </span>
              )}
            </div>
          </div>
        </div>
        <p className="pt-6 font-body-medium text-[#7f7f7f]">
          {dataById?.description}
        </p>
      </div>
      <div className="flex w-[98%] gap-4 mt-4 mx-auto">
        <div className="w-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {newsPaperData?.map((data, index) => (
            <div
              key={index}
              className={`my-2 swiper-card duration-300 shadow-md rounded-md my-[2px] overflow-hidden h-[16rem] !w-full mx-auto relative card-animation ${
                loaded ? `card-delay-1` : ""
              } cursor-pointer`}
            >
              <img
                src={`${data?.cover_image}`}
                alt={`Cover for ${data?.agency}`}
                className="h-full w-full object-cover object-top"
                onClick={() => navigate(`/app/newspaper/list/${data?.id}`)}
              />

              {/* <div className="absolute bottom-0 left-0 right-0 h-[30%] bg-gradient-to-t from-white via-white/90 to-transparent backdrop-blur-sm" /> */}
              <div className="absolute bottom-0 left-0 right-0 h-[35%] bg-gradient-to-t from-[#000000]/70 via-[#000000]/60 to-transparent" />

              <div className="absolute bottom-0 left-0 right-0 flex justify-between items-center p-4 bg-transparent">
                <div className="flex flex-col text-white">
                  <span className="font-bold text-[14px] text-white">
                    {data?.agency}
                  </span>
                  <span className="text-[12px] text-[#ffffff] pt-1">
                    {parseTimeToPersianSummary(data?.published_at)}
                  </span>
                </div>

                <div className="flex gap-3 items-center">
                  <div
                    onClick={() =>
                      handleImageClick(`${data?.cover_image}`, data?.file)
                    }
                    className="cursor-pointer hover:bg-[#e9e6f7] hover:opacity-70 hover:text-[#6F5CD1] transition-all duration-300 p-2 text-white rounded-md"
                  >
                    <MagnifyingGlassPlus size={22} />
                  </div>
                  <div
                    onClick={() => {
                      const fileUrl = `${data?.file}`;
                      window.open(fileUrl);
                    }}
                    className="bg-[#E9E6F7] p-2 rounded-md cursor-pointer"
                  >
                    <FileArrowDown color="#6F5CD1" size={21} />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        <div className="!bg-white w-[32rem] h-fit duration-300 rounded-md shadow-md p-4">
          <h6 className="font-bold">شناسنامه</h6>
          <div className="flex justify-between font-body-medium">
            <div className="flex flex-col gap-4 py-4 text-[#7f7f7f]">
              <span> امتیاز</span>
              <span>مدیر مسئول</span>
              <span>سردبیر</span>
            </div>
            <div className="flex flex-col gap-4 py-4 text-left">
              <span>{dataById?.concessioner ?? "-"}</span>
              <span>{dataById?.manager ?? "-"}</span>
              <span>{dataById?.lead_editor ?? "-"}</span>
            </div>
          </div>
          <Divider />
          <div>
            <div className="flex justify-between font-body-medium">
              <div className="flex flex-col gap-12 py-4 text-[#7f7f7f]">
                <span>تلفن</span>
              </div>
              <div className="flex flex-col gap-5 py-4 text-left">
                <div className="relative flex justify-between gap-2">
                  <span>{toPersianNumber(dataById?.phone) ?? "-"}</span>
                  <Copy
                    size={15}
                    className="cursor-pointer hover:text-[#6F5CD1]"
                    onClick={() => handleCopy(dataById?.phone)}
                  />
                  {copiedText === dataById?.phone && (
                    <span className="absolute -top-8 right-[60%] text-xs bg-gray-800 transition-all duration-300 text-white px-2 py-1 rounded whitespace-nowrap">
                      کپی شد
                    </span>
                  )}
                </div>
              </div>
            </div>
            <Divider />
            <div>
              <div className="flex justify-between font-body-medium">
                <div className="flex flex-col gap-4 py-4 text-[#7f7f7f]">
                  <span>وب‌سایت</span>
                  <span>ایمیل</span>
                </div>
                <div className="flex flex-col gap-4 py-4 text-left">
                  <a
                    href={dataById?.website}
                    target="_blank"
                    className="hover:underline hover:text-blue-500 transition-all duration-300 flex justify-between gap-2"
                    rel="noopener noreferrer"
                  >
                    {dataById?.website ?? "-"}
                    <ArrowSquareOut
                      className="cursor-pointer hover:text-[#6F5CD1]"
                      size={15}
                    />
                  </a>
                  <div className="flex justify-between gap-2">
                    <span className="flex-1">{dataById?.email ?? "-"}</span>
                    {dataById?.email && (
                      <div className="relative">
                        <Copy
                          size={15}
                          className="cursor-pointer hover:text-[#6F5CD1]"
                          onClick={() => handleCopy(dataById?.email)}
                        />
                        {copiedText === dataById?.email && (
                          <span className="absolute -top-8 left-0 text-xs bg-gray-800 transition-all duration-300 text-white px-2 py-1 rounded whitespace-nowrap">
                            کپی شد
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <Divider />
            </div>
            <div className="font-body-medium">
              <span className="block py-3 text-[#7f7f7f]">نشانی</span>
              <div className="relative flex justify-between gap-2">
                <p>{dataById?.address ?? "-"}</p>
                <Copy
                  size={15}
                  className="cursor-pointer hover:text-[#6F5CD1]"
                  onClick={() => handleCopy(dataById?.address)}
                />
                {copiedText === dataById?.address && (
                  <span className="absolute -top-8 right-[90%] text-xs bg-gray-800 transition-all duration-300 text-white px-2 py-1 rounded whitespace-nowrap">
                    کپی شد
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      <Popup
        isOpen={isPopupOpen}
        onClose={() => setIsPopupOpen(false)}
        imageSrc={selectedImage}
        url={selectedImageUrl}
      />
      <div className="flex justify-center">
        <Paginate
          page={page}
          setPage={setPage}
          dataCount={data?.total}
          per_page={9}
        />
      </div>
    </div>
  );
};

export default SpecialNewspaper;
