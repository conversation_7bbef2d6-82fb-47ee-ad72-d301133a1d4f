import React, { useContext, useEffect, useState } from "react";
import AlertAndFilterDetails from "components/alertAndFilterDetails";
import { useNavigate, useParams } from "react-router-dom";
import {
  PencilSimpleLine,
  TrashSimple,
  UserCircleGear,
  WarningCircle,
} from "@phosphor-icons/react";
import Divider from "components/ui/Divider";
import { User } from "@phosphor-icons/react/dist/ssr";
import filter from "service/api/filter";
import Loading from "components/ui/Loading";
import { notification, parseTimeToPersianSummary } from "utils/helper";
import Title from "components/alertAndFilterDetails/Title";
import { parseTimeToPersian } from "utils/helper";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";
import { useLayoutContext } from "context/layout-context";
import AuthContext from "context/auth-context.jsx";
import DeletePopUp from "components/ui/DeletePopUp.jsx";
import { ToastContainer } from "react-toastify";

const FilterId = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [filterData, setFilterData] = useState({});
  const [loading, setLoading] = useState(false);
  const [rerender, setRerender] = useState(false);
  const [showRemove, setShowRemove] = useState(false);
  const { setBreadcrumb } = useLayoutContext();
  const { profile } = useContext(AuthContext);

  const [deleteAccess, setDeleteAccess] = useState(true);
  const [editAccess, setEditAccess] = useState(true);
  const [appendAccess, setAppendAccess] = useState(true);

  const breadcrumbList = [
    { title: "فیلتر", link: "/app/filter/list" },
    { title: filterData?.title },
  ];
  useBreadcrumb(breadcrumbList);
  useEffect(() => {
    setBreadcrumb(breadcrumbList);
  }, [filterData]);

  const getFilter = async (id) => {
    setLoading(true);
    try {
      const {
        data: { data },
      } = await filter.getById(id);
      const help = JSON.parse(JSON.stringify(data));
      help.platform = help.params.platform;
      help.q = help.params.q;
      delete help.params;
      setFilterData(help);

      if (help.owner !== profile.username) {
        const userList = help.group?.contact || [];
        const permissions =
          userList.filter((item) => item.user === profile.username)?.[0]
            ?.permission || [];

        setDeleteAccess(false);
        if (!permissions.includes("edit")) setEditAccess(false);
        if (!permissions.includes("append")) setAppendAccess(false);
      }
    } catch (error) {
      console.log(error.response.data.message);
    }
    setLoading(false);
  };

  const deleteFilter = async () => {
    setLoading(true);
    try {
      const { data } = await filter.remove(filterData.id);
      notification.success(
        data.message,
        <WarningCircle className="text-light-inform-text-rest" />,
      );
      setShowRemove(false);
      if (setRerender) {
        setRerender((l) => !l);
      }
      setTimeout(() => {
        navigate("/app/filter/list");
      }, 2000);
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  useEffect(() => {
    getFilter(id);
  }, []);

  useEffect(() => {}, [filterData]);

  return (
    <>
      <div className="p-6 h-full">
        <div className="grid grid-cols-12 gap-6">
          <div className="bg-white w-full p-6 rounded-lg col-span-7 shadow-[0px_2px_20px_0px_#0000000D]">
            <AlertAndFilterDetails
              data={filterData}
              showReceivingPlatform={false}
            />
          </div>

          <div className="col-span-5 flex flex-col gap-6 w-full">
            <div className="bg-light-neutral-surface-card box-shadow[0px_2px_20px_0px_#0000000D] w-[500px] rounded-lg p-6 font-body-medium">
              <div className="flex flex-col gap-4">
                <div className="flex justify-between">
                  <span className="text-light-neutral-text-medium">
                    تاریخ ایجاد
                  </span>
                  <span>
                    {filterData?.created_at
                      ? parseTimeToPersian(filterData?.created_at)
                      : ""}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-light-neutral-text-medium">
                    تاریخ به‌روزرسانی
                  </span>
                  <span>
                    {filterData?.updated_at
                      ? parseTimeToPersian(filterData?.updated_at)
                      : ""}
                  </span>
                </div>

                {/* <div className="flex justify-between">
                  <span className="text-light-neutral-text-medium">
                    آخرین فعالیت
                  </span>
                  <span>۱۴۰۲/۱۱/۰۱ - ۱۲:۳۴:۵۴</span>
                </div> */}

                {/* <div>
                <span className="text-light-neutral-text-medium">
                  هشدارهای وابسته
                </span>
                <div></div>
              </div> */}
                {/* <div className="flex justify-between">
                <span className="text-light-neutral-text-medium">
                  دفعات استفاده در جست‌و‌جو
                </span>
                <span>۲۳ بار</span>
              </div> */}
              </div>

              {(deleteAccess || editAccess) && (
                <div className="my-6">
                  <Divider />
                </div>
              )}

              <div className="flex justify-between gap-4">
                {deleteAccess && (
                  <button
                    className="bg-light-neutral-background-medium rounded-lg h-8 w-full flex items-center justify-center gap-2"
                    onClick={() => setShowRemove(true)}
                  >
                    <TrashSimple />
                    <span>حذف</span>
                  </button>
                )}
                {editAccess && (
                  <button
                    className="bg-light-neutral-background-medium rounded-lg h-8 w-full flex items-center justify-center gap-2"
                    onClick={() =>
                      navigate("/app/filter/edit", {
                        state: {
                          ...filterData,
                        },
                      })
                    }
                  >
                    <PencilSimpleLine />
                    <span>ویرایش</span>
                  </button>
                )}
              </div>
            </div>

            <div className="col-span-5 flex flex-col gap-6 w-full">
              <div className="bg-light-neutral-surface-card box-shadow[0px_2px_20px_0px_#0000000D] w-[500px] rounded-lg p-6 font-body-medium">
                <div className="flex flex-col gap-3">
                  <div>
                    <Title>
                      <p className="font-body-medium w-36">دسترسی کاربران</p>
                    </Title>
                  </div>
                  <div
                    className={`flex justify-between ${
                      !filterData?.group ? "pb-4" : null
                    }`}
                  >
                    <span className="text-light-neutral-text-medium">
                      وضعیت دسترسی
                    </span>
                    <span className="flex items-center gap-1">
                      <User />
                      {!filterData?.group ? "شخصی" : "اشتراک‌گذاری شده"}
                    </span>
                  </div>
                </div>
                <>
                  <div className="mt-6 mb-3">
                    <Title>
                      <p className="font-body-medium w-46">مشترکین</p>
                    </Title>
                  </div>

                  <div className={"grid grid-cols-3 gap-2 pb-4"}>
                    <span className="text-light-neutral-text-medium text-right">
                      نام کاربر
                    </span>
                    <span className="text-light-neutral-text-medium text-center">
                      تاریخ دسترسی
                    </span>
                    <span className="text-light-neutral-text-medium text-left">
                      مجوزها
                    </span>
                  </div>

                  <div className={"grid grid-cols-3 gap-2 pb-5"}>
                    <span className="text-right">
                      {filterData?.group?.creator}
                    </span>
                    <span className="text-center">
                      {filterData?.created_at
                        ? parseTimeToPersianSummary(filterData?.created_at)
                        : ""}
                    </span>
                    <span className={"font-body-small text-left"}>
                      ایجاد کننده (مدیر اصلی)
                    </span>
                    {filterData?.group?.contact?.map(
                      (contact, contactIndex) => (
                        <>
                          <span className="text-right">{contact?.user}</span>
                          <span className="text-center">
                            {contact?.created_at
                              ? parseTimeToPersianSummary(contact?.created_at)
                              : ""}
                          </span>
                          <div className="font-body-small text-left">
                            {contact.permission.length === 0 && (
                              <div className="inline">
                                <span className="w-full">بازدید کننده</span>
                              </div>
                            )}
                            {contact?.permission.map((per, perIndex) => {
                              let translatedPermission;
                              switch (per) {
                                case "edit":
                                  translatedPermission = "ویرایش";
                                  break;
                                case "append":
                                  translatedPermission = "افزودن مشترک";
                                  break;
                                case "remove":
                                  translatedPermission = "حذف";
                                  break;
                                default:
                                  translatedPermission = per;
                              }

                              return (
                                <div key={perIndex} className="inline">
                                  <span className="w-full">
                                    {translatedPermission}
                                  </span>
                                  {perIndex < contact.permission.length - 1 &&
                                    " - "}
                                </div>
                              );
                            })}
                          </div>
                        </>
                      ),
                    )}
                  </div>
                </>

                {appendAccess && (
                  <div className="flex justify-between gap-4">
                    <button
                      className="bg-light-neutral-background-medium rounded-lg h-8 w-full flex items-center justify-center gap-2"
                      onClick={() => navigate(`/app/filter/access/list/${id}`)}
                    >
                      <span className="flex items-center gap-1">
                        <UserCircleGear />
                        ویرایش دسترسی
                      </span>
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* <div className="bg-light-neutral-surface-card box-shadow[0px_2px_20px_0px_#0000000D] w-[500px] rounded-lg p-6 font-body-medium">
            <div className="flex flex-col gap-6">
              <div className="flex flex-col gap-2">
                <div className="flex gap-2 items-center">
                  <span className="font-body-medium w-36">دسترسی کاربران</span>
                  <Divider />
                </div>
                <div className="flex justify-between">
                  <span className="text-light-neutral-text-medium">
                    وضعیت دسترسی
                  </span>
                  <div className="flex gap-2 items-center">
                    <User />
                    <span>اشتراک‌گذاری شده</span>
                  </div>
                </div>
              </div>

              <div className="flex flex-col gap-2 mb-6">
                <div className="flex gap-2 items-center">
                  <span className="font-body-medium text-light-neutral-text-medium ">
                    مشترکین
                  </span>
                  <Divider />
                </div>

                <div className="flex justify-between text-light-neutral-text-medium">
                  <div className="w-32">نام کاربر</div>
                  <div className="w-24">تاریخ دسترسی</div>
                  <div className="w-48 [direction:ltr]">مجوزها</div>
                </div>

                <div className="flex justify-between">
                  <div className="w-32">حامد فکور</div>
                  <div className="w-24">۱۴۰۲/۱۱/۰۱</div>
                  <div className="w-48 [direction:ltr]">
                    ایجاد کننده (مدیر اصلی)
                  </div>
                </div>

                <div className="flex justify-between">
                  <div className="w-32">احمد محسن</div>
                  <div className="w-24">۱۴۰۲/۱۱/۰۱</div>
                  <div className="w-48 [direction:ltr]">
                    افزودن مشترک - حذف - ویرایش
                  </div>
                </div>

                <div className="flex justify-between">
                  <div className="w-32">ناصر عبدالهی</div>
                  <div className="w-24">۱۴۰۲/۱۱/۰۱</div>
                  <div className="w-48 [direction:ltr]">حذف - ویرایش</div>
                </div>

                <div className="flex justify-between">
                  <div className="w-32">جاسم عبدالکشمیری</div>
                  <div className="w-24">۱۴۰۲/۱۱/۰۱</div>
                  <div className="w-48 [direction:ltr]">مشترک</div>
                </div>
              </div>

              <button
                className="flex gap-2 w-full h-8 justify-center items-center bg-light-neutral-background-medium rounded-lg"
                onClick={() => navigate("")}
              >
                <UserCircleGear />
                <span className="font-button-medium">ویرایش دسترسی</span>
              </button>
            </div>
          </div> */}
          </div>
        </div>
      </div>

      <DeletePopUp
        onClose={() => setShowRemove(false)}
        isOpen={showRemove}
        submitHandler={deleteFilter}
        title="آیا مطمئن هستید؟"
        description="در صورت حذف این فیلتر، امکان بازیابی آن وجود ندارد"
      />

      {loading && <Loading />}
      <ToastContainer />
    </>
  );
};

export default FilterId;
