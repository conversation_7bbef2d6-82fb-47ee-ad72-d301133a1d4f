import { memo, useCallback, useEffect, useState } from "react";
import {
  Calendar,
  CheckCircle,
  FilePdf,
  FloppyDisk,
  Spinner,
  WarningDiamond,
} from "@phosphor-icons/react";
import MediaBadge from "components/ui/MediaBadge";
import { useCompareStore } from "store/compareStore";
import RangeDatePicker from "components/ui/RangeDatePicker";
import { CButton } from "components/ui/CButton";
import { CInput } from "components/ui/CInput";
import PopUp from "components/ui/PopUp";
import compare from "service/api/compare";
import { notification } from "utils/helper";
import { ToastContainer } from "react-toastify";
import PropTypes from "prop-types";

const Header = ({ handlePrint, isEdit = false }) => {
  const { id, platform, fields, title, type, date } = useCompareStore(
    (state) => state.compare,
  );
  const setCompare = useCompareStore((state) => state.setCompare);
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const setCompareCallback = useCallback((data) => {
    setCompare(data);
  }, []);

  const handleDate = (rangeDate) => {
    setCompareCallback({ date: rangeDate });
  };

  const handleTitle = (e) => {
    setCompareCallback({ title: e.target.value });
  };

  const handleKeyDown = async (e) => {
    if (e.key === "Enter") {
      await createCompare();
    }
  };

  const createCompare = async () => {
    try {
      if (loading) return;
      setLoading(true);
      const data = {
        title,
        params:
          type === "profile"
            ? fields.filter((user) => user.id)
            : fields.filter((item) => item.q),
        column_num:
          type === "profile"
            ? fields.filter((user) => user.id).length
            : fields.filter((item) => item.q).length,
        date_range: {
          from_date: date.from,
          to_date: date.to,
        },
        comparison_type: type,
        comparison_platform: platform,
      };
      const response = await compare.create(data);
      setIsOpen(false);
      notification.success(
        response.data.message,
        <CheckCircle className="text-light-success-text-rest" />,
      );
    } catch (error) {
      setIsOpen(false);
      notification.error(
        error.response.data.message,
        <WarningDiamond size={32} className="text-light-error-text-rest" />,
      );
    } finally {
      setLoading(false);
    }
  };

  const updateCompare = async () => {
    try {
      if (loading) return;
      setLoading(true);
      const data = {
        params:
          type === "profile"
            ? fields.filter((user) => user.id)
            : fields.filter((item) => item.q),
        column_num:
          type === "profile"
            ? fields.filter((user) => user.id).length
            : fields.filter((item) => item.q).length,
        date_range: {
          from_date: date.from,
          to_date: date.to,
        },
        comparison_type: type,
        comparison_platform: platform,
      };
      const response = await compare.edit(id, data);
      notification.success(
        response.data.message,
        <CheckCircle className="text-light-success-text-rest" />,
      );
    } catch (error) {
      setIsOpen(false);
      notification.error(
        error.response.data.message,
        <WarningDiamond size={32} className="text-light-error-text-rest" />,
      );
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = async () => {
    await updateCompare();
  };

  useEffect(() => {
    if (!isEdit) setCompareCallback({ title: "" });
  }, [isOpen]);

  return (
    <>
      <div className="compareHeader flex w-full font-body-medium bg-light-neutral-surface-card rounded-[8px] p-4 shadow-[0_2px_20px_0_rgba(0,0,0,0.05)]">
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-4 h-full">
            <span className="font-button-medium">بازه زمانی بررسی:</span>
            <div className="flex items-center w-[370px] rounded-md outline-1 outline outline-light-neutral-border-low-rest bg-light-neutral-background-low px-4 py-2">
              <RangeDatePicker
                onChange={handleDate}
                from={date.from}
                to={date.to}
              />
              <span className="action-icon text-left">
                <Calendar size={18} color="#00000059" />
              </span>
            </div>
            <div className="border-l-[1px] border-[#D1D4DD4D] h-full"></div>
            <span className="font-button-medium">بستر مقایسه:</span>
            <MediaBadge media={platform} showMediaName />
          </div>
          <div className="flex items-center gap-2">
            <CButton
              onClick={handlePrint}
              className="[direction:ltr] !w-fit"
              leftIcon={<FilePdf size={22} />}
              readOnly={
                type === "profile"
                  ? !(fields.filter((user) => user.id).length >= 2)
                  : !(fields.filter((item) => item.q).length >= 2)
              }
            >
              خروجی{" "}
            </CButton>
            <CButton
              mode="outline"
              rightIcon={<FloppyDisk size={16} />}
              className="gap-2 [direction:ltr] !w-fit"
              onClick={() => {
                if (isEdit) handleEdit();
                else setIsOpen(true);
              }}
              readOnly={
                type === "profile"
                  ? !(fields.filter((user) => user.id).length >= 2)
                  : !(fields.filter((item) => item.q).length >= 2)
              }
            >
              {loading ? (
                <Spinner size={16} className="animate-spin" />
              ) : (
                <>{isEdit ? "به‌روزرسانی" : "ذخیره مقایسه"}</>
              )}
            </CButton>
          </div>
        </div>
      </div>
      <PopUp
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        title={isEdit ? "ویرایش مقایسه" : "ذخیره مقایسه"}
        readOnly={!(title.length > 1)}
        submitHandler={createCompare}
      >
        <div className="flex flex-col gap-6">
          <div className="font-overline-large">
            این مقایسه را با نتایج و فیلترهای اعمال شده ذخیره کنید تا در آینده
            بتوانید به آن رجوع کنید.
          </div>
          <div>
            <CInput
              title="عنوان مقایسه"
              type="text"
              onChange={handleTitle}
              value={title}
              placeholder="برای مقایسه یک عنوان بنویسید"
              inputProps={{
                onKeyDown: handleKeyDown,
              }}
            />
          </div>
        </div>
      </PopUp>
      <ToastContainer />
    </>
  );
};

Header.propTypes = {
  handlePrint: PropTypes.func,
  isEdit: PropTypes.bool,
};

export default memo(Header);
