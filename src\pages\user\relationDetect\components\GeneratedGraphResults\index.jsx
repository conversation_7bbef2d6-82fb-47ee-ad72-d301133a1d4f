// import GraphPreview from "./GraphPreview";

// const GeneratedGraphResults = () => {
//   return (
//     <div>
//       <GraphPreview />
//     </div>
//   );
// };

// export default GeneratedGraphResults;


import { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import GraphPreview from "./GraphPreview";

const GeneratedGraphResults = () => {
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showDialog, setShowDialog] = useState(false);
  const [nextPath, setNextPath] = useState(null);
  const navigate = useNavigate();
  const location = useLocation();

  // Example: Assume changes are made when some condition is met
  // Replace with your actual logic to track changes
  const handleChange = () => {
    setHasUnsavedChanges(true);
  };

  // Listen for route changes
  useEffect(() => {
    const unlisten = () => {
      if (hasUnsavedChanges && showDialog === false) {
        setShowDialog(true);
        return false; // Prevent navigation
      }
      return true; // Allow navigation
    };

    // Custom history listener for BrowserRouter
    const handlePopState = (event) => {
      if (hasUnsavedChanges) {
        event.preventDefault();
        setShowDialog(true);
        setNextPath(window.location.pathname);
        // Push current location back to prevent navigation
        window.history.pushState(null, null, location.pathname);
      }
    };

    window.addEventListener("popstate", handlePopState);

    return () => {
      window.removeEventListener("popstate", handlePopState);
    };
  }, [hasUnsavedChanges, location.pathname, showDialog]);

  // Handle dialog confirm (proceed with navigation)
  const handleConfirm = () => {
    setShowDialog(false);
    setHasUnsavedChanges(false); // Reset changes
    if (nextPath) {
      navigate(nextPath);
    }
  };

  // Handle dialog cancel (stay on page)
  const handleCancel = () => {
    setShowDialog(false);
  };

  return (
    <div className="relative">
      <GraphPreview onChange={handleChange} />

      {/* Custom Dialog for Unsaved Changes */}
      {showDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-sm w-full">
            <h2 className="text-lg font-semibold mb-4">Unsaved Changes</h2>
            <p className="mb-6">You have unsaved changes. Are you sure you want to leave?</p>
            <div className="flex justify-end space-x-4">
              <button
                onClick={handleCancel}
                className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirm}
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                Leave
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GeneratedGraphResults;