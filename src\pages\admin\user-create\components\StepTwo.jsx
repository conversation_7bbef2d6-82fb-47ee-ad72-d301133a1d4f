import { CButton } from "components/ui/CButton";
import DropDown from "pages/admin/user-id/components/DropDown";
import { useState } from "react";

const StepTwo = ({ setStep }) => {
  const [role, setRole] = useState("");
  return (
    <>
      <div className="grid gap-4 bg-light-neutral-surface-card rounded-[8px] p-6">
        <p className="font-subtitle-large">نقش کاربر</p>
        <p className="font-body-medium text-light-neutral-text-medium">
          در اینجا شما می‌توانید نوع کاربر و دسترسی‌های آن را تغییر دهید
        </p>

        <DropDown
          subsets={[
            { text: "کاربر عادی", value: "normalUser" },
            {
              text: "مدیر سازمان",
              value: "org_admin",
            },
            { text: "کارشناس سازمان", value: "org_expert" },
            { text: "مدیر سینپاس", value: "stinas_admin" },
            { text: "پشتیبان", value: "support" },
          ]}
          title="نوع کاربر"
          handleChange={(e) => setRole(e?.value)}
          disabled={false}
        />
        <div className="flex items-center gap-2">
          <div className="w-full" onClick={() => setStep(1)}>
            <CButton role="neutral">مرحله قبل</CButton>
          </div>
          <div className="w-full" onClick={() => setStep(3)}>
            <CButton>مرحله بعد</CButton>
          </div>
        </div>
      </div>
    </>
  );
};

export default StepTwo;
