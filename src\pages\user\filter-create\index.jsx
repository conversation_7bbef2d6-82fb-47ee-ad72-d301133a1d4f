import { useState } from "react";
import Indicator from "./components/Indicator";
import StepOne from "./components/StepOne";
import StepTwo from "./components/StepTwo";
import StepOneGuide from "./components/StepOneGuide";
import StepTwoGuide from "./components/StepTwoGuide";
import StepThreeGuide from "./components/StepThreeGuide";
import { useLocation } from "react-router-dom";
import StepFour from "./components/StepFour";
import StepThree from "./components/StepThree";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";

const FilterCreate = () => {
  const { state, pathname } = useLocation();
  const breadcrumbList = [
    { title: "فیلتر", link: "/app/filter/list" },
    {
      title: pathname.includes("edit") ? "ویرایش فیلتر" : "فیلتر جدید",
    },
  ];
  useBreadcrumb(breadcrumbList);
  const [status, setStatus] = useState(1);

  const [filterData, setFilterData] = useState(
    state || {
      q: "",
      title: "",
      description: "",
      messenger: "",
      channel_id: "",
      platform: {},
    },
  );

  const guide = {
    1: <StepOneGuide />,
    2: <StepTwoGuide />,
    3: <StepThreeGuide />,
  };

  return (
    <div className="p-6 h-full">
      <Indicator status={status} step={1} title="عنوان فیلتر">
        <div className="col-span-7">
          <div className="bg-white rounded-lg shadow-[0px_2px_20px_0px_#0000000D] flex-1">
            <StepOne
              setStatus={setStatus}
              setFilterData={setFilterData}
              filterData={filterData}
              state={state}
            />
          </div>
        </div>
        <div className="col-span-5">{guide[status]}</div>
      </Indicator>
      <Indicator status={status} step={2} title="تنظیمات فیلتر">
        <div className="col-span-7">
          <div className="bg-white rounded-lg shadow-[0px_2px_20px_0px_#0000000D] flex-1">
            <StepTwo
              setStatus={setStatus}
              setFilterData={setFilterData}
              filterData={filterData}
            />
          </div>
        </div>
        <div className="col-span-5">{guide[status]}</div>
      </Indicator>
      <Indicator status={status} step={3} title="تنظیم فیلتر ها">
        <div className="col-span-7">
          <div className="rounded-lg flex-1">
            <StepThree
              setStatus={setStatus}
              setFilterData={setFilterData}
              filterData={filterData}
            />
          </div>
        </div>
        <div className="col-span-5">{guide[status]}</div>
      </Indicator>
      <Indicator status={status} step={4} title="بررسی نهایی">
        <div className="col-span-7">
          <div className="bg-white rounded-lg shadow-[0px_2px_20px_0px_#0000000D] flex-1">
            <StepFour
              setStatus={setStatus}
              setFilterData={setFilterData}
              filterData={filterData}
              state={state}
            />
          </div>
        </div>
        <div className="col-span-5">{guide[status]}</div>
      </Indicator>
    </div>
  );
};

export default FilterCreate;
