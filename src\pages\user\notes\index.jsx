import React, { useEffect, useState } from "react";
import noteLists from "service/api/noteLists.js";
import NotesSummaryCard from "./components/NotesSummaryCard.jsx";
import Popup from "components/ui/PopUp.jsx";
import NotesPopUp from "components/NotesPopUp.jsx";
import DeletePopUp from "components/ui/DeletePopUp";
import { CheckCircle, Plus, WarningDiamond } from "@phosphor-icons/react";
import { notification } from "utils/helper";
import { ToastContainer } from "react-toastify";
import note from "service/api/note";
import EmptyList from "components/ui/EmptyList";
import Paginate from "components/ui/Paginate";
import Loading from "components/ui/Loading";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";
import { CButton } from "components/ui/CButton.jsx";
import ChipsCategory from "./components/ChipsCategory.jsx";
import ToolTip from "components/ui/ToolTip.jsx";
import { CInput } from "components/ui/CInput.jsx";

const Notes = () => {
  const breadcrumbList = [{ title: "یادداشت‌ها" }];
  useBreadcrumb(breadcrumbList);
  const [myNotesLists, setMyNotesLists] = useState([]);
  const [noteValue, setNoteValue] = useState("");
  const [noteToModify, setNoteToModify] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [updater, setUpdater] = useState(false);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [isDeletePopUpOpen, setIsDeletePopUpOpen] = useState(false);
  const [isDeletePopUpOpen2, setIsDeletePopUpOpen2] = useState(false);
  const [page, setPage] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [noteData, setNoteData] = useState([]);
  const [createCategoryPopup, setCreateCategoryPopup] = useState(false);
  const [collectionId, setCollectionId] = useState(null);
  const [newCategoryValue, setNewCategoryValue] = useState({
    title: "",
    description: "",
    collection_type: "note",
  });
  const [newCollectionTitle, setNewCollectionTitle] = useState("default");
  const [activeCollection, setActiveCollection] = useState("default");
  const [collectionList, setCollectionList] = useState([]);

  const loadNotes = async (page) => {
    setIsLoading(true);
    try {
      const res = await noteLists.getNotes(page, activeCollection);
      const result = res?.data?.data;
      setMyNotesLists(result?.notes);
      setTotalItems(result?.total_notes);
    } catch (error) {
      if (res.data.code !== 403) {
        notification.error(
          "نام مجموعه باید یکتا باشد!",
          <WarningDiamond size={32} className="text-light-error-text-rest" />
        );
      }
    }
    setIsLoading(false);
  };
  const getCollections = async () => {
    try {
      const { data } = await noteLists.getCollections({
        page: 1,
        count: 10,
        collection_type: "note",
      });
      setCollectionList(data?.data);
    } catch (error) {
      console.log(error);
    }
  };

  const fetchNoteData = async (noteId) => {
    setIsLoading(true);
    try {
      const res = await noteLists.getNote(noteId);
      setNoteValue(res?.data?.data?.note?.text);
      setNoteData(res?.data?.data);
    } catch (error) {
      console.log(error);
    }
    setIsLoading(false);
  };
  const handleEditClick = (content) => {
    setNoteToModify(content);
    fetchNoteData(content.content_id);
    setIsPopupOpen(true);
  };
  const deleteNoteHandler = async () => {
    if (isLoading) return;
    setIsLoading(true);
    try {
      const res = await note.toggle(noteToModify);
      if (res?.data?.code === 200) {
        notification.success(
          "یادداشت حذف شد",
          <CheckCircle className="text-light-success-text-rest" size={26} />
        );
        loadNotes();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsDeletePopUpOpen(false);
      setIsLoading(false);
    }
  };
  const updateNoteHandler = async () => {
    setIsLoading(true);
    try {
      const res = await note.toggle({
        ...noteToModify,
        note: noteValue,
        collection_title: newCollectionTitle,
      });
      loadNotes();
      notification.success(
        "یادداشت با موفقیت ثبت شد",
        <CheckCircle className="text-light-success-text-rest" size={26} />
      );
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
      getCollections();
      setIsPopupOpen(false);
    }
  };
  const addCategoryHandler = (e) => {
    setNewCategoryValue((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };
  const handleSubmitCategory = async () => {
    try {
      if (newCategoryValue.title === "") {
        notification.error(
          "عنوان پوشه را وارد نمایید!",
          <WarningDiamond size={32} className="text-light-error-text-rest" />
        );
        return;
      }
      if (newCategoryValue.description === "")
        newCategoryValue.description = newCategoryValue.title;
      const res = await noteLists.createCollection(newCategoryValue);
      setActiveCollection(newCategoryValue.title);
      setCollectionId(res?.data?.data?.id);
      notification.success(
        "پوشه جدید با موفقیت ثبت شد",
        <CheckCircle className="text-light-success-text-rest" size={26} />
      );
      getCollections();
    } catch (e) {
      notification.error(
        e?.response?.data?.message || "عنوان نمیتواند خالی باشد!",
        <WarningDiamond size={32} className="text-light-error-text-rest" />
      );
    }
    setCreateCategoryPopup(false);
    setNewCategoryValue({
      title: "",
      description: "",
      collection_type: "note",
    });
  };
  const deleteCollectionHandler = async () => {
    try {
      await noteLists.deleteCollection(collectionId);
      notification.success(
        "پوشه با موفقیت حذف شد",
        <CheckCircle className="text-light-success-text-rest" size={26} />
      );
      getCollections();
      setActiveCollection("default");
    } catch (error) {
      console.log(error);
    } finally {
      setIsDeletePopUpOpen2(false);
      setIsDeletePopUpOpen(false);
    }
  };
  useEffect(() => {
    loadNotes(page);
    getCollections();
  }, [page, updater, activeCollection]);

  return (
    <>
      <div className="h-full flex flex-col">
        <div className="flex items-center justify-between px-5">
          <div className="flex items-center flex-wrap gap-2">
            {collectionList?.map((item) => (
              <React.Fragment key={item?.id}>
                {item?.description ? (
                  <ToolTip comp={item?.description}>
                    <ChipsCategory
                      title={item?.title === "default" ? "پیشفرض" : item?.title}
                      onClick={() => {
                        setActiveCollection(item?.title);
                        setCollectionId(item?.id);
                      }}
                      isActive={activeCollection === item?.title}
                    />
                  </ToolTip>
                ) : (
                  <ChipsCategory
                    title={item?.title === "default" ? "پیشفرض" : item?.title}
                    onClick={() => {
                      setActiveCollection(item?.title);
                      setCollectionId(item?.id);
                    }}
                    isActive={activeCollection === item?.title}
                  />
                )}
              </React.Fragment>
            ))}
          </div>
          <div className="flex items-center gap-2">
            <div className="w-fit" onClick={() => setIsDeletePopUpOpen2(true)}>
              {activeCollection !== "default" && (
                <CButton className="flex items-center gap-1" mode="outline">
                  <p>حذف پوشه</p>
                </CButton>
              )}
            </div>
            <div
              className="w-fit"
              onClick={() => setCreateCategoryPopup(!createCategoryPopup)}
            >
              <CButton className="flex items-center gap-1">
                <p>افزودن پوشه</p>
                <Plus />
              </CButton>
            </div>
          </div>
        </div>
        {!myNotesLists.length && (
          <EmptyList
            title={"هیچ یادداشتی ندارید"}
            description={
              "شما می‌توانید برای هر محتوا به دلخواه یک یادداشت بنویسید"
            }
          />
        )}
        <div className="container px-5 mx-auto grid grid-cols-12 gap-3 mt-5">
          {myNotesLists?.map((item) => (
            <div
              key={item?.id}
              className="md:col-span-6 col-span-12 bg-light-neutral-surface-card rounded-lg"
            >
              <NotesSummaryCard
                media={item?.platform}
                showHeaderMenu={false}
                data={item}
                myNotesLists={myNotesLists}
                loadNotes={loadNotes}
                setUpdater={setUpdater}
                setNoteToModify={setNoteToModify}
                setIsDeletePopUpOpen={setIsDeletePopUpOpen}
                handleEditClick={handleEditClick}
                isBookmarked={!!item.bookmark}
              />
            </div>
          ))}
          <Popup
            isOpen={isPopupOpen}
            onClose={() => setIsPopupOpen(!isPopupOpen)}
            submitHandler={updateNoteHandler}
            disabled={isLoading}
            title="یادداشت"
          >
            <NotesPopUp
              loadNotes={loadNotes}
              activeCollection={activeCollection}
              noteValue={noteValue}
              setNoteValue={setNoteValue}
              noteData={noteData}
              setNewCollectionTitle={setNewCollectionTitle}
            />
          </Popup>
          <DeletePopUp
            onClose={() => setIsDeletePopUpOpen(false)}
            isOpen={isDeletePopUpOpen}
            submitHandler={deleteNoteHandler}
            title="آیا می‌خواهید یادداشت را حذف کنید؟"
            description="در صورت حذف این یادداشت، محتوای نوشته شده قابل بازیابی نخواهد بود."
          />
        </div>
        <div>
          <Paginate
            page={page}
            setPage={setPage}
            dataCount={totalItems}
            per_page={10}
          />
        </div>
        {isLoading && <Loading />}
      </div>
      <Popup
        isOpen={createCategoryPopup}
        title="افزودن پوشه"
        hasButton={false}
        onClose={() => setCreateCategoryPopup(!createCategoryPopup)}
      >
        <CInput
          value={newCategoryValue?.title}
          placeholder="نام دسته‌بندی را وارد کنید..."
          onChange={addCategoryHandler}
          inputProps={{ name: "title" }}
        ></CInput>
        <textarea
          className="w-full h-32 rounded-md py-2 px-6 font-body-large outline-none border border-light-neutral-border-medium-rest mt-1"
          rows={4}
          value={newCategoryValue?.description}
          onChange={addCategoryHandler}
          name="description"
          placeholder="توضیحات دسته بندی را وارد کنید..."
        />
        <div className="flex items-center justify-between gap-4 mt-4 font-button-large">
          <div className="w-full">
            <CButton
              className="bg-light-neutral-background-medium rounded-lg p-3 w-[100px]"
              role="neutral"
              onClick={() => setCreateCategoryPopup(!createCategoryPopup)}
            >
              انصراف
            </CButton>
          </div>
          <div className="w-full" onClick={handleSubmitCategory}>
            <CButton readOnly={!newCategoryValue.title}>ثبت</CButton>
          </div>
        </div>
      </Popup>
      <DeletePopUp
        onClose={() => setIsDeletePopUpOpen2(false)}
        isOpen={isDeletePopUpOpen2}
        submitHandler={() => deleteCollectionHandler()}
        title="آیا می‌خواهید این مجموعه را حذف کنید؟"
      />
      <ToastContainer />
    </>
  );
};

export default Notes;
