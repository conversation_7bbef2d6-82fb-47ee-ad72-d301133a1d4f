import { useEffect, useMemo, useState } from "react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import newsPaper from "service/api/newsPaper";
import {
  FileArrowDown,
  MagnifyingGlass,
  MagnifyingGlassPlus,
} from "@phosphor-icons/react";
import { parseTimeToPersianSummary } from "utils/helper";
import { CButton } from "components/ui/CButton";
import { CInput } from "components/ui/CInput";
import DateCategoryFilters from "./dateCategoryFilters.jsx";
import Paginate from "components/ui/Paginate";
import CardLoading from "components/ui/CardLoading";
import Popup from "./Popup.jsx";
import newspaper from "../../../../assets/images/kiosk/newspaper.jpg";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";
import ToolTip from "components/ui/ToolTip.jsx";
import debounce from "lodash.debounce";

const fallbackImage = newspaper;

const SearchResults = () => {
  const breadcrumbList = [
    { title: "پیشخوان", link: "/app/newspaper/list" },
    { title: "جست‌وجو" },
  ];
  useBreadcrumb(breadcrumbList);
  const location = useLocation();
  const { searchValue, selectedCategories, fromDate, toDate, exact_match } =
    location.state || {};
  const { category } = useParams();
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState("");
  const [loading, setLoading] = useState(false);
  const [selectedImageUrl, setSelectedImageUrl] = useState("");
  const [isFallback, setIsFallback] = useState(false);
  const [searchParams, setSearchParams] = useState({
    searchValue: searchValue,
    selectedCategories: [],
    fromDate: null,
    toDate: null,
    exact_match: false,
  });
  const [selectedCategory, setSelectedCategory] = useState(
    selectedCategories || []
  );
  const [selectedDateRange, setSelectedDateRange] = useState({
    from: fromDate,
    to: toDate,
  });
  const [isExactMatch, setIsExactMatch] = useState(exact_match);
  const [loaded, setLoaded] = useState(false);
  const [page, setPage] = useState(1);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState([]);
  const [dateModal, setDateModal] = useState(false);
  const [categoryModal, setCategoryModal] = useState(false);
  const [data, setData] = useState([]);
  const navigate = useNavigate();

  const categoryMap = {
    ورزشی: "sport",
    اقتصادی: "economic",
    بین‌الملل: "international",
    سایر: "other",
  };

  const categories = Object.values(categoryMap);
  const fetchData = async (resetPage = false) => {
    if (resetPage) {
      setPage(1);
    }
    setLoading(true);
    setTimeout(async () => {
      const buildParams = () => {
        const agency = searchParams.searchValue?.trim();
        const categoriesToSend =
          selectedCategory?.length > 0
            ? selectedCategory
            : selectedCategories?.length > 0
            ? selectedCategories
            : category
            ? [category]
            : [];
        return {
          ...(agency ? { agency } : {}),
          categories: categoriesToSend,
          ...(selectedDateRange.from || fromDate
            ? { start_date: selectedDateRange.from || fromDate }
            : {}),
          ...(selectedDateRange.to || toDate
            ? { end_date: selectedDateRange.to || toDate }
            : {}),
          page: resetPage ? 1 : page,
          rows: 16,
          exact_match: isExactMatch,
        };
      };

      const params = buildParams();

      try {
        const response = await newsPaper.post(params);
        setData(response.data.data);
      } catch (err) {
        console.error(err);
      } finally {
        setLoading(false);
      }
    }, 1000);
  };

  useEffect(() => {
    if (
      searchValue ||
      category ||
      selectedCategory ||
      isExactMatch ||
      (fromDate && toDate)
    ) {
      fetchData();
    }
  }, [searchValue, selectedCategories, category, fromDate, toDate, page]);

  useEffect(() => {
    if (category) {
      setSelectedCategory((prev) => [category]);
    }
  }, [category]);

  const handleCategoryChange = (category) => {
    setSelectedCategory((prev) => {
      const isSelected = prev?.includes(category);
      const newSelected = isSelected
        ? prev.filter((c) => c !== category)
        : [...prev, category];

      return newSelected;
    });
  };

  const handleImageClick = (image, url) => {
    setSelectedImage(image);
    setSelectedImageUrl(url);
    setIsPopupOpen(true);
  };

  useEffect(() => {
    const englishValues = selectedCategory?.map((cat) => categoryMap[cat]);

    setSearchParams((prev) => ({
      ...prev,
      selectedCategories: englishValues,
    }));
  }, [selectedCategory]);

  const handleDateChange = (dates) => {
    const { from, to } = dates;
    setSelectedDateRange({ from, to });

    setSearchParams((prev) => ({
      ...prev,
      fromDate: from,
      toDate: to,
    }));
  };

  useEffect(() => {
    setSearchParams((prev) => ({
      ...prev,
      fromDate: selectedDateRange.from,
      toDate: selectedDateRange.to,
      selectedCategories: selectedCategory,
      exact_match: isExactMatch,
    }));
  }, [selectedDateRange, selectedCategory]);

  const handleSearch = (event) => {
    if (event && event.key === "Enter") {
      setPage(1);
      const trimmedSearchValue = searchParams.searchValue?.trim();
      navigate(`/app/newspaper/newspaper-results`, {
        state: {
          ...searchParams,
          searchValue: trimmedSearchValue || null,
        },
      });
    }
  };

  const fetchSuggestions = useMemo(
    () =>
      debounce(async (newValue) => {
        if (newValue.length > 2) {
          try {
            const params = { q: newValue };
            const suggest = await newsPaper.searchSuggest(params);
            console.log(
              "suggest?.data?.data?.suggest",
              suggest?.data?.data?.suggest
            );

            setSuggestions(suggest?.data?.data?.suggest);
            setShowSuggestions(true);
          } catch (error) {
            console.error("Error fetching suggestions:", error);
            setShowSuggestions(false);
          }
        } else {
          setShowSuggestions(false);
        }
      }, 1000),
    []
  );

  useEffect(() => {
    return () => fetchSuggestions.cancel();
  }, [fetchSuggestions]);

  const handleInputChange = (e) => {
    const newValue = e.target.value;
    setSearchParams((prev) => ({
      ...prev,
      searchValue: newValue || "",
    }));

    if (newValue.length === 0) {
      setShowSuggestions(false);
      return;
    }

    fetchSuggestions(newValue);
  };

  const handleSuggestionClick = (suggest) => {
    setSearchParams((prev) => ({
      ...prev,
      searchValue: suggest,
    }));

    setShowSuggestions(false);
  };

  useEffect(() => {
    setLoaded(true);
  }, []);

  const newsPaperData = data?.newspaper || [];

  return (
    <>
      <div className="font-body-medium mx-5 h-full bg-white p-5 overflow-hidden rounded-lg shadow-md z-[-1]">
        <div className="flex flex-row gap-[16px]">
          <CInput
            id={"q"}
            name={"q"}
            inset={true}
            headingIcon={<MagnifyingGlass />}
            size={"lg"}
            validation={"none"}
            value={searchParams.searchValue ?? searchValue ?? ""}
            direction={"rtl"}
            placeholder={"نام روزنامه را جست‌و‌جو کنید"}
            onChange={handleInputChange}
            inputProps={{ onKeyDown: handleSearch }}
            className={"flex-1 !mb-0"}
            onDateChange={handleDateChange}
          ></CInput>
          <CButton
            type={"submit"}
            onClick={() => fetchData(true)}
            size={"lg"}
            className={"[direction:rtl] [width:150px!important]"}
          >
            جست‌و‌جو
          </CButton>
        </div>
        <div
          className={`absolute left-0 mx-5 right-0 z-10 p-4 bg-gray-100 rounded-lg shadow-md origin-top transition-transform duration-700 ${
            showSuggestions ? "scale-y-100" : "scale-y-0"
          }`}
          style={{ transformOrigin: "top" }}
        >
          <ul>
            {suggestions?.map((suggest, index) => (
              <li
                key={index}
                className="p-2 hover:bg-gray-200 cursor-pointer"
                onClick={() => handleSuggestionClick(suggest)}
              >
                {suggest}
              </li>
            ))}
          </ul>
        </div>
        <div className="pt-1">
          <DateCategoryFilters
            dateModal={dateModal}
            setDateModal={setDateModal}
            handleDateChange={handleDateChange}
            categoryModal={categoryModal}
            setCategoryModal={setCategoryModal}
            categories={categories}
            selectedCategory={selectedCategory}
            selectedCategories={selectedCategories}
            handleCategoryChange={handleCategoryChange}
            selectedDateRange={selectedDateRange}
            isExactMatch={isExactMatch}
            setIsExactMatch={setIsExactMatch}
          />
        </div>
      </div>
      <div
        className="flex w-full gap-4 mt-12 px-5"
        style={{ fontFamily: "iranyekan" }}
      >
        {loading ? (
          <div className="min-h-screen flex !w-full items-center !justify-center">
            <CardLoading />
          </div>
        ) : newsPaperData.length === 0 ? (
          <div className="h-[430px] w-full flex items-center justify-center font-subtitle-medium">
            روزنامه ای برای نمایش وجود ندارد
          </div>
        ) : (
          <div className="w-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {newsPaperData?.map((data, index) => (
              <div
                key={index}
                className="swiper-card duration-300 shadow-md rounded-md my-[2px] overflow-hidden h-[16rem] !w-[96.5%] mx-auto relative cursor-pointer"
              >
                <img
                  src={`${data?.cover_image}`}
                  alt={`Cover for ${data?.agency}`}
                  onError={(e) => {
                    e.target.src = fallbackImage;
                    setIsFallback((prevState) => ({
                      ...prevState,
                      [index]: true,
                    }));
                  }}
                  className={`h-full w-full transition-all duration-300 ease-in-out group-hover:blur-sm object-cover object-top ${
                    isFallback[index] ? "pb-2" : ""
                  }`}
                  onClick={() => navigate(`/app/newspaper/list/${data?.id}`)}
                />
                <div className="absolute bottom-0 left-0 right-0 h-[35%] bg-gradient-to-t from-[#000000]/70 via-[#000000]/60 to-transparent" />
                <div className="absolute bottom-0 left-0 right-0 flex justify-between items-center p-4 bg-transparent">
                  <div className="flex flex-col text-white">
                    <span className="font-bold text-[14px] text-white">
                      {data?.agency}
                    </span>
                    <span className="text-[12px] text-[#ffffff] pt-1">
                      {parseTimeToPersianSummary(data?.published_at)}
                    </span>
                  </div>
                  <div className="flex gap-3 items-center">
                    <div
                      onClick={() =>
                        handleImageClick(`${data?.cover_image}`, data?.file)
                      }
                      className="cursor-pointer hover:bg-[#e9e6f7] hover:opacity-70 hover:text-[#6F5CD1] transition-all duration-300 p-2 text-white rounded-md"
                    >
                      <MagnifyingGlassPlus size={22} className="" />
                    </div>
                    {data?.file === null ? (
                      <ToolTip
                        comp={"امکان دانلود روزنامه وجود ندارد."}
                        position="right"
                        className="!z-10"
                      >
                        <div
                          onClick={() => {
                            if (data?.file !== null) {
                              const fileUrl = `${data?.file}`;
                              window.open(fileUrl);
                            }
                          }}
                          className={`${
                            data?.file === null ? "bg-gray-500" : "bg-[#E9E6F7]"
                          } p-2 rounded-md cursor-pointer opacity-80 hover:opacity-100 transition-all duration-200 ${
                            data?.file === null
                              ? "cursor-not-allowed opacity-50"
                              : ""
                          }`}
                        >
                          <FileArrowDown color="#B3B3B3" size={22} />
                        </div>
                      </ToolTip>
                    ) : (
                      <div
                        onClick={() => {
                          const fileUrl = `${data?.file}`;
                          window.open(fileUrl);
                        }}
                        className="bg-[#E9E6F7] p-2 rounded-md cursor-pointer"
                      >
                        <FileArrowDown color="#6F5CD1" size={21} />
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <Popup
        isOpen={isPopupOpen}
        onClose={() => setIsPopupOpen(false)}
        imageSrc={selectedImage}
        url={selectedImageUrl}
      />

      <div className="flex justify-center">
        <Paginate
          page={page}
          setPage={setPage}
          dataCount={data?.total}
          per_page={16}
        />
      </div>
    </>
  );
};

export default SearchResults;
