import { useEffect, useState } from "react";
import {
  DotsThreeOutlineVertical,
  Link,
  Eye,
  PushPin,
  PushPinSimple,
} from "@phosphor-icons/react";
import Drawer from "components/Drawer";
import ShowMoreDetail from "../ShowMoreDetail";
import DropDown from "./DropDown";
import clsx from "clsx";
import { useLocation } from "react-router-dom";
import note from "service/api/note";
import PropTypes from "prop-types";
import bookmark from "service/api/bookmark";
import ToolTip from "../../../../../components/ui/ToolTip";
import useSearchStore from "../../../../../store/searchStore";
import { useReport360Store } from "store/report360Store";
const SummaryCardHeaderMenu = ({
  media,
  data,
  is_Pin,
  hasNotes,
  isShowMore = false,
  platform: plat,
  isBookmarked = false,
  myLists,
  setUpdater,
  isDropdownOpen,
  fallbackImg,
  is360 = false,
}) => {
  const [showMore, setShowMore] = useState(false);
  const [showAvatar, setShowAvatar] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isPinned, setIsPinned] = useState(is_Pin);
  const { platform } = useSearchStore((state) => state.filters);
  const { profile } = useReport360Store((state) => state.report);
  const { platform: platform360 } = profile || {};

  const handleClickOnAvatar = () => {
    setShowAvatar(true);
  };
  const pinNotesPostHandler = async (id) => {
    setIsLoading(true);
    try {
      const res = await note.pinPost({
        content_id: id,
        is_pin: !isPinned,
      });
      if (res?.data?.code === 200) {
        setIsPinned(!isPinned);
        setUpdater((prev) => !prev);
      }
    } catch (error) {
      console.log(error);
    }
    setIsLoading(false);
  };
  const pinBookmarksPostHandler = async (id) => {
    setIsLoading(true);
    try {
      const res = await bookmark.pinPost({
        content_id: id,
        is_pin: !isPinned,
      });
      if (res?.data?.code === 200) {
        setIsPinned(!isPinned);
        setUpdater((prev) => !prev);
      }
    } catch (error) {
      console.log(error);
    }
    setIsLoading(false);
  };
  const pinPostStyles = clsx(
    "flex items-center gap-2 cursor-pointer rounded-[6px] p-1",
    {
      "bg-light-neutral-background-medium ": !isPinned,
      "bg-light-primary-background-tag": isPinned,
    },
  );
  const { pathname } = useLocation();
  useEffect(() => {
    setShowAvatar(false);
  }, [showMore]);
  return (
    <div className={`${isDropdownOpen ? "!z-[-1]" : ""}`}>
      <div className="flex justify-end flex-1">
        <div className="flex items-center gap-2">
          {pathname === "/app/bookmarks" || pathname === "/app/notes" ? (
            <div
              className={pinPostStyles}
              onClick={() => {
                pathname === "/app/notes"
                  ? pinNotesPostHandler(data?.id)
                  : pathname === "/app/bookmarks"
                    ? pinBookmarksPostHandler(data?.id)
                    : null;
              }}
            >
              {isPinned ? (
                <PushPinSimple weight="fill" color="#6F5CD1" size={14} />
              ) : (
                <PushPin className="size-[14px]" />
              )}
            </div>
          ) : null}
          <a
            href={data.url || data?.post_url}
            target="_blank"
            className="flex items-center gap-2  bg-light-neutral-background-medium p-1 rounded-[6px]"
            rel="noreferrer"
          >
            <ToolTip
              comp={`مشاهده در ${
                is360
                  ? (plat || platform360) === "twitter"
                    ? "ایکس"
                    : (plat || platform360) === "telegram"
                      ? "تلگرام"
                      : (plat || platform360) === "instagram"
                        ? "اینستاگرام"
                        : (plat || platform360) === "news"
                          ? "اخبار"
                          : ""
                  : platform === "twitter"
                    ? "ایکس"
                    : platform === "telegram"
                      ? "تلگرام"
                      : platform === "instagram"
                        ? "اینستاگرام"
                        : platform === "news"
                          ? "اخبار"
                          : ""
              }`}
              position="top"
            >
              <Link className="size-[14px]" />
            </ToolTip>
          </a>
          <div
            className="flex items-center gap-2 cursor-pointer bg-light-neutral-background-medium p-1 rounded-[6px]"
            onClick={() => setShowMore(true)}
          >
            <ToolTip comp={"جزئیات"} position="top">
              <Eye className="size-[14px]" />
            </ToolTip>
          </div>
          <DropDown
            data={data}
            lowWidth={false}
            media={media}
            hasNotes={hasNotes}
            isBookmarked={isBookmarked}
            myLists={myLists}
          >
            <div className="flex items-center gap-2 bg-light-neutral-background-medium p-[2px] rounded-sm">
              <DotsThreeOutlineVertical className="size-[14px]" />
            </div>
          </DropDown>
        </div>
      </div>
      {showMore && (
        <Drawer setShowMore={setShowMore}>
          <div style={{ ...(showAvatar ? { filter: "blur(4px)" } : {}) }}>
            <ShowMoreDetail
              media={media}
              isShowMore={isShowMore}
              platform={plat}
              data={data}
              handleClickOnAvatar={handleClickOnAvatar}
            />
          </div>
          {showAvatar && (
            <div className="h-screen w-full flex justify-center items-center absolute top-0 left-0 z-10">
              <div
                className="absolute z-10 w-full h-full top-0 left-0"
                onClick={() => setShowAvatar(false)}
              ></div>
              <div
                className="size-80 rounded-full bg-contain cursor-default ring-4 ring-[#432FA7] ring-offset-4 relative z-20 bg-center bg-no-repeat"
                style={{
                  backgroundImage: `url(${data?.avatar || fallbackImg})`,
                }}
              >
                <img
                  src={data?.avatar || fallbackImg}
                  alt="avatar"
                  className="w-full h-full object-cover rounded-full"
                  onError={(e) => {
                    e.target.onerror = null;
                    e.target.src = fallbackImg;
                  }}
                />
              </div>
            </div>
          )}
        </Drawer>
      )}
    </div>
  );
};

SummaryCardHeaderMenu.propTypes = {
  media: PropTypes.string.isRequired,
  data: PropTypes.shape({
    avatar: PropTypes.string,
    "relation.avatar": PropTypes.string,
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    url: PropTypes.string,
    post_url: PropTypes.string,
  }).isRequired,
  is_Pin: PropTypes.bool.isRequired,
  hasNotes: PropTypes.bool.isRequired,
  platform: PropTypes.string.isRequired,
  isBookmarked: PropTypes.bool,
  myLists: PropTypes.array,
  setUpdater: PropTypes.func.isRequired,
  fallbackImg: PropTypes.string,
  isDropdownOpen: PropTypes.bool.isRequired,
  is360: PropTypes.bool,
  isShowMore: PropTypes.bool,
};

export default SummaryCardHeaderMenu;
