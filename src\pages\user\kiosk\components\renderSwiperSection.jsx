import { useEffect, useState } from "react";
import {
  CaretLeft,
  FileArrowDown,
  MagnifyingGlassPlus,
} from "@phosphor-icons/react";
import { parseTimeToPersianSummary } from "utils/helper";
import { Swiper, SwiperSlide } from "swiper/react";
import { useNavigate } from "react-router-dom";
import { Navigation } from "swiper/modules";
import PropTypes from "prop-types";
import Popup from "./Popup";
import "../style.css";
import newspaper from "../../../../assets/images/kiosk/newspaper.jpg";
import { Card } from "components/ui/Card";
import ToolTip from "components/ui/ToolTip";

const fallbackImage = newspaper;

const RenderSwiperSection = ({
  cards,
  title,
  showMore = true,
  data = false,
  category = false,
  agencyId = false,
}) => {
  const navigate = useNavigate();
  const [loaded, setLoaded] = useState(false);
  const [isFallback, setIsFallback] = useState(false);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState("");
  const [selectedImageUrl, setSelectedImageUrl] = useState("");

  useEffect(() => {
    setLoaded(true);
  }, []);

  if (!Array.isArray(cards)) {
    return null;
  }

  const handleImageClick = (image, url) => {
    setSelectedImage(image);
    setSelectedImageUrl(url);
    setIsPopupOpen(true);
  };

  return (
    <>
      <Card className="flex flex-col my-5 !w-[96.5%] mx-auto">
        <div className="flex justify-between items-center px-2 !w-[98%] mb-3 mx-auto border-r-8 py-3 border-[#6F5CD1] h-[10px]">
          <div className="flex items-center">
            <h2 className="font-bold rounded">{title}</h2>
          </div>
          {showMore && (
            <span
              className="flex items-center gap-3 text-[14px] cursor-pointer text-[#6F5CD1]"
              onClick={() =>
                data
                  ? navigate(
                      `/app/newspaper/special-newspaper/${
                        agencyId ? data?.agency_id : data?.id
                      }/${data?.agency}`
                    )
                  : category
                  ? navigate(`/app/newspaper/newspaper-results/${category}`)
                  : null
              }
            >
              نمایش بیشتر
              <CaretLeft />
            </span>
          )}
        </div>
        <Swiper
          spaceBetween={1}
          navigation={true}
          modules={[Navigation]}
          slidesPerView={4}
          loop={false}
          className="swiper-container w-full px-6 kiosk-swiper"
        >
          {cards?.map((data, index) => (
            <SwiperSlide
              key={index}
              className={`swiper-slide2 card-animation flex gap-10 ${
                loaded ? `card-delay-${(index % 4) + 1}` : ""
              }`}
              style={{ "--index": index }}
            >
              <div className="swiper-card duration-300 shadow-md rounded-md my-[2px] overflow-hidden h-[16rem] relative cursor-pointer">
                <img
                  src={`${data?.cover_image}`}
                  alt={`Cover for ${data?.agency}`}
                  onError={(e) => {
                    e.target.src = fallbackImage;
                    setIsFallback((prevState) => ({
                      ...prevState,
                      [index]: true,
                    }));
                  }}
                  className={`h-full w-full transition-all duration-300 ease-in-out object-cover object-top ${
                    isFallback[index] ? "" : ""
                  }`}
                  onClick={() => navigate(`/app/newspaper/list/${data?.id}`)}
                />

                <div className="absolute bottom-0 left-0 right-0 h-[35%] bg-gradient-to-t from-[#000000]/70 via-[#000000]/60 to-transparent" />

                <div className="absolute bottom-0 left-0 right-0 flex justify-between items-center p-4 bg-transparent">
                  <div className="flex flex-col text-white">
                    <span className="font-bold text-[14px] text-white">
                      {data?.agency}
                    </span>
                    <span className="text-[12px] text-[#ffffff] pt-1">
                      {parseTimeToPersianSummary(data?.published_at)}
                    </span>
                  </div>

                  <div className="flex gap-3 items-center">
                    <div
                      onClick={() =>
                        handleImageClick(`${data?.cover_image}`, data?.file)
                      }
                      className="cursor-pointer hover:bg-[#e9e6f7] hover:opacity-80 hover:text-[#6F5CD1] transition-all duration-300 p-2 text-white rounded-md"
                    >
                      <MagnifyingGlassPlus size={22} />
                    </div>
                    {data?.file === null ? (
                      <ToolTip
                        comp={"امکان دانلود روزنامه وجود ندارد."}
                        position="right"
                        className="!z-10"
                      >
                        <div
                          onClick={() => {
                            if (data?.file !== null) {
                              const fileUrl = `${data?.file}`;
                              window.open(fileUrl);
                            }
                          }}
                          className={`${
                            data?.file === null ? "bg-gray-500" : "bg-[#E9E6F7]"
                          } p-2 rounded-md cursor-pointer opacity-80 hover:opacity-100 transition-all duration-200 ${
                            data?.file === null
                              ? "cursor-not-allowed opacity-50"
                              : ""
                          }`}
                        >
                          <FileArrowDown color="#B3B3B3" size={22} />
                        </div>
                      </ToolTip>
                    ) : (
                      <div
                        onClick={() => {
                          if (data?.file !== null) {
                            const fileUrl = `${data?.file}`;
                            window.open(fileUrl);
                          }
                        }}
                        className={`${
                          data?.file === null ? "bg-gray-500" : "bg-[#E9E6F7]"
                        } p-2 rounded-md cursor-pointer opacity-80 hover:opacity-100 transition-all duration-200 ${
                          data?.file === null
                            ? "cursor-not-allowed opacity-50"
                            : ""
                        }`}
                      >
                        <FileArrowDown color="#6f5cd1" size={22} />
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </Card>

      <Popup
        isOpen={isPopupOpen}
        onClose={() => setIsPopupOpen(false)}
        imageSrc={selectedImage}
        url={selectedImageUrl}
      />
    </>
  );
};

RenderSwiperSection.propTypes = {
  cards: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
      agency: PropTypes.string,
      cover_image: PropTypes.string,
      published_at: PropTypes.string,
      file: PropTypes.string,
    })
  ).isRequired,
  title: PropTypes.string.isRequired,
  category: PropTypes.string,
  showMore: PropTypes.bool,
  agencyId: PropTypes.bool,
  data: PropTypes.shape({
    agency: PropTypes.string.isRequired,
  }),
};

export default RenderSwiperSection;
