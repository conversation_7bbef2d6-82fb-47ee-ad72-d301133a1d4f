import React, { useState } from "react";
import { Card } from "components/ui/Card.jsx";
import { CInput } from "components/ui/CInput.jsx";
import { MagnifyingGlass, Sliders } from "@phosphor-icons/react";
import CustomSearchDrawer from "components/CustomSearchDrawer/index.jsx";

const BulletinSearchBox = ({ query, setQuery, disabled, noFilter = false }) => {
  const [showAdvancedSearchDrawer, setShowAdvancedSearchDrawer] =
    useState(false);

  const handleCustomSearchSubmit = async (newQuery) => {
    setQuery(newQuery);
    setShowAdvancedSearchDrawer(false);
  };

  return (
    <Card
      title={<p className={"font-body-large"}>کلمه کلیدی برای ایجاد بولتن</p>}
      className={"flex-col mb-4"}
    >
      <div className={"flex flex-col w-full"}>
        <CInput
          id={"q"}
          name={"q"}
          // inset={true}
          headingIcon={<MagnifyingGlass />}
          size={"lg"}
          validation={"none"}
          direction={"rtl"}
          placeholder={"کلمه مورد نظر را بنویسید"}
          className={"flex-1 !mb-0"}
          value={query}
          disabled={disabled}
          onChange={(e) => setQuery(e?.target?.value)}
          customAction={() => setShowAdvancedSearchDrawer(true)}
          customActionText={!noFilter ? <Sliders size={22} /> : <></>}
        ></CInput>
        <div className={!showAdvancedSearchDrawer ? "hidden" : "block"}>
          <CustomSearchDrawer
            setShowMore={setShowAdvancedSearchDrawer}
            query={query}
            setQuery={setQuery}
            onSubmit={handleCustomSearchSubmit}
          />
        </div>
      </div>
    </Card>
  );
};

export default BulletinSearchBox;
