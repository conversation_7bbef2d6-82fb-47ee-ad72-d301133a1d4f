import { toPersianNumber } from "utils/helper";
import Doughnut from "components/Charts/Doughnut";
import PropTypes from "prop-types";

const SentimentAnalysis = ({ data }) => {
  return (
    <div className="flex flex-col gap-4">
      <p className="font-subtitle-medium self-start">تحلیل احساسات:</p>
      <div className="flex items-center gap-28">
        <Doughnut
          name="sentiment"
          height={200}
          data={[
            {
              name: "مثبت",
              y: +data.sentiment?.prob?.positive || 0,
            },
            { name: "خنثی", y: +data.sentiment?.prob?.neutral || 0 },
            { name: "منفی", y: +data.sentiment?.prob?.negative || 0 },
          ]}
          legendFormatter={function () {
            return `<div dir="rtl" style="display:grid;grid-template-columns:1fr 1fr;font-family:iranyekan,serif;width:100px;padding:2px;">
                <span style="font-size:16px">
                  ${toPersianNumber((this.y * 100).toFixed(0))}٪
                </span>
                <div
                  style="justify-self:right"
                >
                  <span style="color:${this.color};font-size:14px">${
                    this.name
                  }</span>
                </div>
              </div>`;
          }}
          tooltipFormatter={function () {
            return `<div style="display:flex;flex-direction:column;gap:8px;text-align:center;font-family:iranyekan,serif"><div>${
              this.key
            }</div><div>${toPersianNumber((this.y * 100).toFixed(0))}٪</div></div>`;
          }}
          colors={["#1CB0A5", "#00000080", "#E0526A"]}
        />
      </div>
    </div>
  );
};

SentimentAnalysis.propTypes = {
  data: PropTypes.object,
};

export default SentimentAnalysis;
