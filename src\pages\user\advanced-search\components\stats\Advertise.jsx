import { useEffect, useState } from "react";
import Divider from "components/ui/Divider";
import Doughnut from "components/Charts/Doughnut";
import { fixPercentToShow } from "utils/helper";
import advanceSearch from "service/api/advanceSearch";
import { SpinnerGap } from "@phosphor-icons/react";
import { useMeasure } from "react-use";
import PropTypes from "prop-types";
import PLATFORMS from "constants/platforms.js";
import useSearchStore from "store/searchStore.js";
import { buildRequestData } from "utils/requestData";

const Advertise = ({ showDataLabels }) => {
  const { filters, query } = useSearchStore();

  const [data, setData] = useState({});
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [ref, { width }] = useMeasure();

  const getData = async () => {
    setLoading(true);
    try {
      const requestData = buildRequestData(
        {
          ...filters,
          q: query,
        },
        "advertise",
      );
      const res = await advanceSearch.search(requestData);
      let platformData;
      switch (filters.platform) {
        case "twitter":
          platformData = res.data.data.twitter;
          break;
        case "instagram":
          platformData = res.data.data.instagram;
          break;
        case "telegram":
          platformData = res.data.data.telegram;
          break;
        case "eitaa":
          platformData = res.data.data.eitaa;
          break;
        default:
          platformData = res.data.data.news;
      }
      const converted = {};
      platformData.forEach((item) => {
        converted[item.key] = item.count / res?.data?.data?.total;
      });
      setData(converted);
      setTotal(res?.data?.data?.total);
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };
  useEffect(() => {
    if (filters.platform === PLATFORMS.NEWS) return;
    getData();
  }, [query, filters]);

  if (filters.platform === PLATFORMS.NEWS) return <></>;

  return (
    <div className="flex flex-col gap-4" ref={ref}>
      <p className="font-subtitle-large text-right">تحلیل محتوای تبلیغاتی</p>
      <Divider />
      {loading ? (
        <div className="w-full h-[365px] flex justify-center items-center">
          <SpinnerGap size={40} className="animate-spin" />
        </div>
      ) : Object.keys(data).length === 0 ? (
        <div className="h-[270px] flex items-center justify-center font-subtitle-medium">
          داده ای برای نمایش وجود ندارد
        </div>
      ) : (
        <Doughnut
          showDataLabels={showDataLabels}
          colLayout
          name="gender"
          minWidth="100px"
          height={365}
          data={[
            {
              name: "غیر تبلیغاتی",
              y: data?.["non-adv"],
            },
            {
              name: "تبلیغاتی",
              y: data?.adv,
            },
          ]}
          legendFormatter={function () {
            return `<div dir="rtl" style="display:grid;grid-template-columns:1fr 1fr;font-family:iranyekan,serif;width:${width - 50}px;padding:2px;">
<span style="font-size:16px">
${fixPercentToShow(this.y) || ((data[0]?.count / total) * 100).toFixed(2)}
</span>
<div
style="justify-self:right"
>
<div style="display:flex;gap:6px;align-items:center;">
<span style="color:${this.color};font-size:14px">${this.name}</span> 
<span style="width: 12px; height: 12px; border-radius: 50%; background-color: ${this.color};"></span>
</div>
</div>
</div>`;
          }}
          tooltipFormatter={function () {
            return `<div style="display:flex;flex-direction:column;gap:8px;text-align:center;font-family:iranyekan,serif"><div>${
              this.key
            }</div><div>${fixPercentToShow(this.y)}</div></div>`;
          }}
          colors={["#1CB0A5", "#E0526A"]}
        />
      )}
    </div>
  );
};

Advertise.propTypes = {
  platform: PropTypes.string,
  req_data: PropTypes.object,
  query: PropTypes.string,
  showDataLabels: PropTypes.bool,
};

export default Advertise;
