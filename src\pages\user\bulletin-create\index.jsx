import { useEffect, useState } from "react";
import { useBulletinStore } from "store/bulletinStore";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";
import useSearchStore from "store/searchStore";
import MasterpageLayout from "layout/masterpage-layout.jsx";
import Header from "./components/Header.jsx";
import Step1 from "./step-1";
import Step2Auto from "./step-2-auto";
import Step2Manual from "./step-2-manual";
import Step3 from "./step-3";
import Step4 from "./step-4";
import Step5 from "./step-5";
import Step6 from "./step-6";
import Step7 from "./step-7";
import { Sliders } from "@phosphor-icons/react";
import FooterInHeader from "./step-3/FooterInHeader.jsx";
import ManualStep2Footer from "./step-2-manual/ManualStep2Footer.jsx";
import AutoStep2Footer from "./step-2-auto/AutoStep2Footer.jsx";
import Alert from "components/ui/Alert.jsx";
import { useLayoutContext } from "context/layout-context";
import { useLocation } from "react-router-dom";

const BulletinCreate = ({ isUpdate = false }) => {
  const { setBreadcrumb } = useLayoutContext();
  const { step, type, content, contentManual } = useBulletinStore(
    (state) => state.bulletin
  );
  const [templateSelected, setTemplateSelected] = useState(false);
  const [showPopup, setShowPopup] = useState(false);
  const [isTemplateSelected, setIsTemplateSelected] = useState(false);
  const { bulletin } = useBulletinStore();
  const { clearFilter, clearQuery } = useSearchStore();
  const { pathname } = useLocation();

  useEffect(() => {
    clearFilter();
    clearQuery();
  }, []);

  useEffect(() => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, [step]);

  useEffect(() => {
    setBreadcrumb([
      { title: "بولتن", link: "/app/bulletin/list" },
      {
        title:
          pathname.split("/")[3] === "edit" ? "ویرایش بولتن" : "بولتن جدید",
      },
    ]);

    console.log("bulletin updated:");
    console.log(bulletin);
  }, [bulletin]);

  const handleTemplateSelected = (isSelected) => {
    setTemplateSelected(isSelected);
  };

  const alertTextPerStep = {
    2: "شما در این صفحه می‌توانید محتوا را بر اساس فیلترها و روش‌های جست‌وجوی مختلف بررسی کرده و آن‌ها را برای حضور در بولتن انتخاب کنید.",
    3: (
      <div className="flex flex-col gap-[2px]">
        <p>
          شما در این صفحه می‌توانید تنها یک عبارت جست‌وجو کنید و بر اساس آن
          نمودارهای مختلف را براساس بسترهای مختلف انتخاب کنید.
        </p>
        <p className="flex gap-1 items-center">
          <span>برای نوشتن عبارت بهتر روی دکمه</span>
          <Sliders size={18} />
          <span>کلیک کرده تا بتوانید عبارت دقیق‌تری را بنویسید.</span>
        </p>
        <p>
          در صورتی که عبارت یا فیلتر را تغییر دهید، نمودارهای انتخاب شده، به
          حالت اولیه بازگردانده می‌شود.
        </p>
      </div>
    ),
  };

  // console.log(content?.platform?.map((item) => item.length));
  const validationNextStep = {
    // 2: !!Object.keys(content).filter((x) => x != "filter").length,
    2:
      type === "auto"
        ? !!content.q &&
          !!content.filter &&
          (content.platform?.instagram?.length > 0 ||
            content.platform?.telegram?.length > 0 ||
            content.platform?.twitter?.length > 0 ||
            content.platform?.news?.length > 0)
        : contentManual.twitter?.length > 0 ||
          contentManual.telegram?.length > 0 ||
          contentManual.instagram?.length > 0 ||
          contentManual.news?.length > 0,
    3: true,
    4: true,
  };

  const footer = {
    2: type === "manual" ? <ManualStep2Footer /> : <AutoStep2Footer />,
    3: <FooterInHeader />,
    4: <Alert showCloseButton={false}>این تنظیمات اختیاری هستند</Alert>,
  };

  return (
    <MasterpageLayout
      overrideFilterSettings={true}
      topSearchBarComponent={
        [2, 3, 4].includes(step) ? (
          <div className={"p-6 mb-6 pb-0 [direction:rtl]"}>
            <Header
              showAlert={step !== 4}
              alertText={alertTextPerStep[step]}
              validation={validationNextStep[step]}
              footer={footer[step]}
              setShowPopup={setShowPopup}
              isTemplateSelected={isTemplateSelected}
              stepsTexts={[
                "اطلاعات بولتن",
                "انتخاب محتوا",
                "افزودن نمودار",
                "قالب بولتن",
              ]}
            />
          </div>
        ) : null
      }
    >
      <div className="flex-1">
        {step === 1 ? (
          <Step1 />
        ) : step === 2 && type === "auto" ? (
          <Step2Auto />
        ) : step === 2 && type === "manual" ? (
          <Step2Manual />
        ) : step === 3 ? (
          <Step3 />
        ) : step === 4 ? (
          <Step4
            onTemplateSelected={handleTemplateSelected}
            isTemplateSelected={isTemplateSelected}
            showPopup={showPopup}
            setShowPopup={setShowPopup}
            setIsTemplateSelected={setIsTemplateSelected}
          />
        ) : step === 5 ? (
          <Step5 />
        ) : step === 6 ? (
          <Step6 />
        ) : step === 7 ? (
          <Step7 />
        ) : null}
      </div>
    </MasterpageLayout>
  );
};

export default BulletinCreate;
