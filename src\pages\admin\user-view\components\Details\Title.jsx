import Divider from "components/ui/Divider.jsx";
import PropTypes from "prop-types";
import { PencilSimpleLine } from "@phosphor-icons/react";
import { useNavigate } from "react-router-dom";
import ToolTip from "components/ui/ToolTip.jsx";

const Title = ({ children, onAction, tooltip = "" }) => {
  return (
    <div className="flex items-center gap-[10px]">
      {children}
      <Divider />
      {onAction && (
        <ToolTip comp={tooltip}>
          <button
            className="font-button-medium bg-light-neutral-background-medium rounded-lg w-[35px] h-8 flex justify-center items-center gap-2"
            onClick={onAction}
          >
            <PencilSimpleLine />
          </button>
        </ToolTip>
      )}
    </div>
  );
};

Title.propTypes = {
  children: PropTypes.node,
  onAction: PropTypes.func,
  tooltip: PropTypes.string,
};

export default Title;
