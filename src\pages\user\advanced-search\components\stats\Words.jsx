import { useEffect, useState } from "react";
import WordCloud from "./WordCloud";
import FrequentWord from "./FrequentWord";
import { useNavigate } from "react-router-dom";
import { generateStatsQuery } from "utils/stats";
import advanceSearch from "service/api/advanceSearch";
import { preprocessWord } from "utils/helper";
import PropTypes from "prop-types";
import useSearchStore from "store/searchStore.js";
import { buildRequestData } from "utils/requestData";
import PLATFORMS from "constants/platforms.js";
import ExportMenu from "components/ExportMenu/index.jsx";

const Words = () => {
  const navigate = useNavigate();
  const [words, setWords] = useState([]);
  const [word, setWord] = useState("");
  const [loading, setLoading] = useState(false);
  const [type, setType] = useState("hashtags");
  const { filters, query, setQuery } = useSearchStore();

  const getData = async () => {
    setLoading(true);
    try {
      const requestData = buildRequestData(
        {
          ...filters,
          q: query,
        },
        "cloud",
        30
      );
      const res = await advanceSearch.search(requestData, null, {
        cloud_type: filters.platform === PLATFORMS.NEWS ? "hashtags" : type,
      });

      const platformData = res?.data?.data?.[filters.platform] || [];
      setWords(
        platformData?.map(({ key, count }) => ({
          text: preprocessWord(key),
          value: count,
        }))
      );
    } catch (error) {
      console.error(error);
      setWords([]);
    }
    setLoading(false);
  };

  // Prepare series and time for ExportMenu
  const series = [
    {
      name: type === "hashtags" ? "Hashtags" : "Keywords",
      data: words.map((item) => item.value),
      time: words.map((item) => item.text),
    },
  ];

  const time = words.map((item) => item.text);

  useEffect(() => {
    if (word.length > 2) {
      setQuery(`( ${[word].join(" + ")} )`);
      navigate("/app/advanced-search");
    }
  }, [word]);

  useEffect(() => {
    getData();
  }, [filters, query, type]);

  useEffect(() => {
    setType(filters.platform === PLATFORMS.NEWS ? "keywords" : "hashtags");
  }, [filters.platform]);

  return (
    <div className="words-container grid grid-cols-12 gap-4">
      <div className="col-span-8">
        <WordCloud
          words={words}
          loading={loading}
          type={type}
          setType={setType}
          time={time}
          series={series}
          platform={filters.platform}
        />
      </div>
      <div className="col-span-4 bg-white rounded-lg p-6 shadow-[0px_2px_20px_0px_#0000000D]">
        <FrequentWord words={words} loading={loading} type={type} />
      </div>
    </div>
  );
};

Words.propTypes = {
  platform: PropTypes.string,
  req_data: PropTypes.object,
  query: PropTypes.string,
};

export default Words;
