import { CaretLeft } from "@phosphor-icons/react";
import { useEffect, useRef, useState } from "react";

const StatusDropdown = ({ status = [], onChange }) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [selectedRoles, setSelectedRoles] = useState(status || []);
  const dropdownRef = useRef(null);

  const roleMap = {
    all: "همه",
    open: "در‌حال‌برسی",
    closed: "پایان‌یافته",
    resolved: "منتظر‌‌پاسخ",
  };

  const availableItems = [
    {
      value: "all",
      label: "همه",
    },
    {
      value: "open",
      label: "در‌حال‌برسی",
    },
    {
      value: "closed",
      label: "پایان‌یافته",
    },
    {
      value: "resolved",
      label: "منتظر‌‌پاسخ",
    },
  ];

  const getSelectedRolesLabel = () => {
    if (
      selectedRoles &&
      selectedRoles?.length > 0 &&
      selectedRoles?.length !== availableItems.length
    ) {
      return selectedRoles?.map((role) => roleMap[role] || role).join(", ");
    }
    return "همه";
  };

  const handleChange = (item) => {
    setSelectedRoles((prevSelectedRoles) => {
      let updatedRoles = [item.value];

      // let updatedRoles;
      // if (prevSelectedRoles.includes(item.value)) {
      //   updatedRoles = prevSelectedRoles.filter((role) => role !== item.value);
      // } else {
      //   updatedRoles = [...prevSelectedRoles, item.value];
      // }
      if (onChange) {
        onChange(updatedRoles);
      }
      return updatedRoles;
    });
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowDropdown(false);
      }
    };

    if (showDropdown) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showDropdown]);

  return (
    <div>
      <span
        className="flex gap-2 items-center cursor-pointer hover:bg-gray-200 transition duration-200 rounded-lg p-2"
        onClick={() => setShowDropdown(!showDropdown)}
      >
        وضعیت تیکت:
        <span className="text-[#7f7f7f]">{getSelectedRolesLabel()}</span>
        <CaretLeft />
      </span>
      {showDropdown && (
        <div
          className="bg-white h-auto w-[12rem] shadow-md absolute z-[10] p-4 rounded"
          ref={dropdownRef}
        >
          {availableItems.map((item) => (
            <label key={item.value} className="flex items-center gap-2 py-1">
              <input
                type="radio"
                checked={selectedRoles.includes(item.value)}
                onChange={() => handleChange(item)}
              />
              {item.label}
            </label>
          ))}
        </div>
      )}
    </div>
  );
};

export default StatusDropdown;
