import {
  PencilSimpleLine,
  TrashSimple,
  WarningCircle,
} from "@phosphor-icons/react";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import alert from "service/api/alert";
import Loading from "components/ui/Loading";
import Status from "components/ui/Status";
import Divider from "components/ui/Divider";
import AlertAndFilterDetails from "components/alertAndFilterDetails";
import { notification, parseTimeToPersian } from "utils/helper";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";
import { useLayoutContext } from "context/layout-context";
import DeletePopUp from "components/ui/DeletePopUp.jsx";
import { ToastContainer } from "react-toastify";

const AlertId = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [alertData, setAlertData] = useState({});
  const [loading, setLoading] = useState(false);
  const [rerender, setRerender] = useState(false);
  const [showRemove, setShowRemove] = useState(false);
  const { setBreadcrumb } = useLayoutContext();

  const changeStatus = async () => {
    try {
      const response = await alert.status(id);
      setRerender((l) => !l);
    } catch (error) {
      console.log(error);
    }
  };

  const getAlerts = async (id) => {
    setLoading(true);
    try {
      const {
        data: { data },
      } = await alert.getById(id);
      setAlertData({ ...data, ...data.query.params });
    } catch (error) {
      console.log(error.response.data.message);
    }
    setLoading(false);
  };

  const deleteAlert = async () => {
    setLoading(true);
    try {
      const { data } = await alert.remove(id);
      notification.error(
        data.message,
        <WarningCircle className="text-light-inform-text-rest" />,
      );
      setShowRemove(false);
      if (setRerender) {
        setRerender((l) => !l);
      }
      navigate("/app/alert/list");
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  useEffect(() => {
    getAlerts(id);
  }, [rerender]);

  const breadcrumbList = [
    { title: "هشدار", link: "/app/alert/list" },
    { title: alertData?.title },
  ];
  useBreadcrumb(breadcrumbList);
  useEffect(() => {
    setBreadcrumb(breadcrumbList);
  }, [alertData]);
  return (
    <>
      <div className="p-6 h-full">
        <div className="grid grid-cols-12 justify-between gap-6">
          <div></div>
          <div className="bg-white w-full p-6 rounded-lg col-span-6 shadow-[0px_2px_20px_0px_#0000000D]">
            <AlertAndFilterDetails data={alertData} isFilter={false} />
          </div>

          <div className="w-full h-64 flex flex-col gap-4 rounded-lg bg-white p-6 col-span-3 shadow-[0px_2px_20px_0px_#0000000D] *:font-body-medium *:text-light-neutral-text-medium">
            {alertData.created_at && (
              <div className="flex justify-between">
                <span>تاریخ ایجاد</span>
                <span>{parseTimeToPersian(alertData.created_at)}</span>
              </div>
            )}

            {alertData.last_execute && (
              <div className="flex justify-between">
                <span>آخرین فعالیت</span>
                <span>{parseTimeToPersian(alertData.last_execute)}</span>
              </div>
            )}

            <div className="flex justify-between">
              <span>وضعیت</span>
              <Status status={alertData.is_active} />
            </div>

            <div className="py-6">
              <Divider />
            </div>

            <div className="flex gap-4">
              <button
                className="font-button-medium bg-light-neutral-background-medium rounded-lg w-full h-8"
                onClick={() => changeStatus()}
              >
                {alertData.is_active ? "غیرفعال سازی" : "فعال سازی"}
              </button>
              <button
                className="bg-light-neutral-background-medium rounded-lg w-full h-8 flex justify-center items-center gap-2"
                onClick={() => setShowRemove(true)}
              >
                <TrashSimple />
                <span className="font-button-medium">حذف</span>
              </button>
              <button
                className="font-button-medium bg-light-neutral-background-medium rounded-lg w-full h-8 flex justify-center items-center gap-2"
                onClick={() =>
                  navigate("/app/alert/edit", {
                    state: {
                      ...alertData,
                    },
                  })
                }
              >
                <PencilSimpleLine />
                <span className="font-button-medium">ویرایش</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <DeletePopUp
        onClose={() => setShowRemove(false)}
        isOpen={showRemove}
        submitHandler={deleteAlert}
        title="آیا مطمئن هستید؟"
        description="در صورت حذف این هشدار، امکان بازیابی آن وجود ندارد"
      />
      {loading && <Loading />}
      <ToastContainer />
    </>
  );
};

export default AlertId;

// {
//   "id": 11,
//   "title": "قیمت دلار",
//   "is_active": true,
//   "description": "",
//   "messenger": "telegram",
//   "channel_id": "5963528741",
//   "created_at": "2024-04-24T16:47:52.951020+03:30",
//   "last_execute": "2024-04-24T16:47:56.039085+03:30",
//   "query": {
//       "id": 18,
//       "title": "قیمت دلار",
//       "description": "",
//       "params": {
//           "platform": {
//               "telegram": {
//                   "languages": [
//                       "fa"
//                   ],
//                   "contain_copy": "all",
//                   "impact": 0,
//                   "keywords": [],
//                   "hashtags": [],
//                   "categories": [],
//                   "sources": []
//               },
//               "twitter": {
//                   "sentiment": "all",
//                   "languages": [
//                       "fa"
//                   ],
//                   "gender": "all",
//                   "contain_copy": "all",
//                   "impact": 0,
//                   "keywords": [],
//                   "hashtags": [],
//                   "categories": [],
//                   "sources": []
//               },
//               "instagram": {
//                   "sentiment": "all",
//                   "languages": [
//                       "fa"
//                   ],
//                   "gender": "all",
//                   "contain_copy": "all",
//                   "impact": 0,
//                   "keywords": [],
//                   "hashtags": [],
//                   "categories": [],
//                   "sources": []
//               },
//               "news": {
//                   "languages": [
//                       "fa"
//                   ],
//                   "impact": 0,
//                   "keywords": [],
//                   "categories": [],
//                   "sources": []
//               }
//           },
//           "q": "کاهش قیمت دلار"
//       }
//   },
//   "platform": {
//       "telegram": {
//           "languages": [
//               "fa"
//           ],
//           "contain_copy": "all",
//           "impact": 0,
//           "keywords": [],
//           "hashtags": [],
//           "categories": [],
//           "sources": []
//       },
//       "twitter": {
//           "sentiment": "all",
//           "languages": [
//               "fa"
//           ],
//           "gender": "all",
//           "contain_copy": "all",
//           "impact": 0,
//           "keywords": [],
//           "hashtags": [],
//           "categories": [],
//           "sources": []
//       },
//       "instagram": {
//           "sentiment": "all",
//           "languages": [
//               "fa"
//           ],
//           "gender": "all",
//           "contain_copy": "all",
//           "impact": 0,
//           "keywords": [],
//           "hashtags": [],
//           "categories": [],
//           "sources": []
//       },
//       "news": {
//           "languages": [
//               "fa"
//           ],
//           "impact": 0,
//           "keywords": [],
//           "categories": [],
//           "sources": []
//       }
//   },
//   "q": "کاهش قیمت دلار"
// }
