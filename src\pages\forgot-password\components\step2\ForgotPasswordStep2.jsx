import { useContext, useState } from "react";
import { Field, Form, Formik } from "formik";
import { CButton } from "components/ui/CButton.jsx";
import { CaretLeft, CaretRight } from "@phosphor-icons/react";
import { FormErrorWrapper } from "components/ui/FormErrorArea.jsx";
import { forgetPasswordStep2Schema } from "utils/validationSchemas.js";

import logoImg from "assets/images/logo_main.png";
import PropTypes from "prop-types";
import { COtpInput } from "components/ui/COtpInput.jsx";
import AuthService from "service/api/authService.js";

const ForgotPasswordStep2 = ({ setStep, setOtp, mobile }) => {
  const [formErrorTitle, setFormErrorTitle] = useState(null);
  const [formErrorText, setFormErrorText] = useState(null);

  const handleSubmit = async (values, { setSubmitting, setFieldError }) => {
    setFormErrorTitle(null);
    setSubmitting(true);
    try {
      const response = await AuthService.confirm({
        parameter: values.mobile,
        token: values.otp,
      });
      if (!(response?.data?.code === 200)) {
        setFormErrorTitle("خطا در فراموشی رمز عبور");
        setSubmitting(false);
        return false;
      }

      setOtp(values.otp);
      setStep(3);
    } catch (error) {
      if (error.response) {
        setFieldError("general", error.response.data.message);
        setFieldError("otp", " ");
        setFormErrorTitle(error.response.data.message);
      } else if (error.request) {
        // The request was made but no response was received
        console.error("No response received:", error.request);
        setFormErrorTitle("پاسخی از سرور دریافت نشد");
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error("Error", error.message);
        setFormErrorTitle("خطا در فرآیند!");
      }
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <>
      <Formik
        initialValues={{ mobile: mobile, otp: "" }}
        validationSchema={forgetPasswordStep2Schema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting, handleChange, handleBlur, isValid, dirty }) => (
          <Form className="font-body-medium flex-1 bg-white mx-auto w-full h-full max-w-lg overflow-hidden">
            {/* Title */}
            <img
              className={"top-0 w-auto m-auto h-[96px]"}
              src={logoImg}
              alt="logo"
            />

            <h2 className="font-headline-medium text-gray-800 mt-8 mb-5 text-right">
              فراموشی رمز عبور
            </h2>
            <p className={"font-paragraph-medium mb-8"}>
              کد تغییر رمز عبور برای شماره {mobile === "" ? "شما" : mobile}{" "}
              ارسال شده. لطفا به صندوق پیامکی خود رفته و رمز ۶ رقمی دریافت شده
              را وارد کنید. پس از تایید شما میتوانید برای خود رمز جدید تعیین
              کنید.
            </p>
            <Field
              id={"otp"}
              name={"otp"}
              component={COtpInput}
              size={"lg"}
              validation={"none"}
              direction={"ltr"}
              title={"کد تایید"}
              onChange={handleChange("otp")}
            ></Field>

            <FormErrorWrapper
              show={!!formErrorTitle}
              title={formErrorTitle}
              text={formErrorText}
            />

            {/* Submit button */}
            <div className="flex justify-end">
              <CButton
                rightIcon={<CaretLeft />}
                type={"submit"}
                size={"lg"}
                disabled={isSubmitting}
              >
                ثبت
              </CButton>
            </div>
          </Form>
        )}
      </Formik>
    </>
  );
};

ForgotPasswordStep2.propTypes = {
  setStep: PropTypes.func.isRequired,
  mobile: PropTypes.string.isRequired,
  setOtp: PropTypes.func.isRequired,
};

export default ForgotPasswordStep2;
