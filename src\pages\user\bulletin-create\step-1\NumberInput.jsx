import { useEffect, useRef, useState } from "react";
import { CaretDown, CaretUp } from "@phosphor-icons/react";
import PropTypes from "prop-types";
import { convertToEinglihsNumbers, toPersianNumber } from "utils/helper.js";
import useClickAway from "hooks/useClickAway.jsx";

const NumberInput = ({ max, handleChange, initilaValue = "00" }) => {
  const [number, setNumber] = useState(initilaValue);
  const [isSelect, setIsSelect] = useState(false);

  const handleValue = (value) => {
    if (isNaN(value)) return setNumber((l) => l);
    if (value.length == 1 && value >= 1) return setNumber("0" + value);
    if (Number(value) < 1) {
      return setNumber("00");
    }
    if (Number(value) >= max) {
      return setNumber((l) => l);
    }
    if (value.length > 2 && value.includes("0"))
      return setNumber(value.slice(1));
    if (value.length > 2) return setNumber((l) => l);
    return setNumber(value);
  };

  useEffect(() => {
    handleChange(number);
  }, [number]);
  const ref = useRef();
  useClickAway(ref, () => setIsSelect(false));
  return (
    <div
      ref={ref}
      className="flex justify-center items-center gap-[10px] bg-light-neutral-background-medium w-full py-3 rounded-lg"
      style={{ outline: isSelect ? "1px solid #4D36BF" : "" }}
      onClick={() => setIsSelect(true)}
    >
      <div>
        <CaretUp onClick={() => handleValue(String(+number + 1))} />
        <CaretDown onClick={() => handleValue(String(+number - 1))} />
      </div>
      <input
        type="text"
        className="w-6 border-none outline-none bg-transparent font-headline-small"
        dir="ltr"
        onChange={(e) => handleValue(convertToEinglihsNumbers(e.target.value))}
        value={toPersianNumber(number)}
        style={{ color: isSelect ? "#6F5CD1" : "black" }}
      />
    </div>
  );
};

export default NumberInput;

NumberInput.propTypes = {
  max: PropTypes.number,
  handleChange: PropTypes.func,
};
