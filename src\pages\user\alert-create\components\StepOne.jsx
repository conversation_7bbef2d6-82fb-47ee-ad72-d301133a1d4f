import { CInput } from "components/ui/CInput";
import { Field, Form, Formik } from "formik";
import { alertStepOneSchema } from "utils/validationSchemas";
import { CButton } from "components/ui/CButton.jsx";

const StepOne = ({ setStatus, setAlertData, alertData, state }) => {
  const handleSubmit = (data) => {
    const copy = { ...data };
    copy.title = data.title.trim();
    setAlertData((l) => {
      return { ...l, ...copy };
    });
    setStatus(2);
  };
  return (
    <div className="p-6 w-full">
      <Formik
        initialValues={{
          title: alertData.title,
          description: alertData.description,
          receiver: alertData.receiver,
        }}
        validationSchema={alertStepOneSchema}
        onSubmit={handleSubmit}
      >
        {({
          handleChange,
          handleBlur,
          isValid,
          dirty,
          initialValues,
          values,
        }) => (
          <Form className="flex-1 bg-white mx-auto w-full h-full overflow-hidden">
            <Field
              id={"title"}
              name={"title"}
              component={CInput}
              size={"lg"}
              validation={"none"}
              direction={"rtl"}
              title={"عنوان هشدار"}
              placeholder="برای مثال هشدار رشد قیمت"
              onChange={handleChange("title")}
              onBlur={handleBlur("title")}
              className="w-full font-overline-large"
              value={initialValues.title}
              inputProps={{ readOnly: state }}
            />
            <Field
              id={"receiver"}
              name={"receiver"}
              component={CInput}
              size={"lg"}
              validation={"none"}
              direction={"rtl"}
              title={"نام دریافت کننده"}
              placeholder="نام دریافت کننده را وارد کنید"
              onChange={handleChange("receiver")}
              onBlur={handleBlur("receiver")}
              className="w-full font-overline-large"
              value={initialValues.receiver}
            />
            <div className="mb-12">
              <label
                htmlFor="description"
                className="font-overline-large mb-2 inline-block"
              >
                {" "}
                توضیحات
              </label>
              <Field
                id={"description"}
                name={"description"}
                as="textarea"
                placeholder="برای مثال این هشدار برای رشد قیمت ارز خاص در نظر گرفته شده است"
                onChange={handleChange("description")}
                onBlur={handleBlur("description")}
                className="w-full h-40 rounded-md p-4 font-body-large outline-none border border-light-neutral-border-medium-rest"
                // value={initialValues.description}
              />
            </div>
            <div className="flex flex-row-reverse">
              <CButton
                type={"submit"}
                className={`!w-[200px]`}
                readOnly={!((isValid && dirty) || values.title)}
              >
                ادامه
              </CButton>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default StepOne;
