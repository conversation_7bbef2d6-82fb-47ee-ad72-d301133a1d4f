import { Info, PencilSimpleLine } from "@phosphor-icons/react";
import { CButton } from "components/ui/CButton";
import { CInput } from "components/ui/CInput";
import { useState } from "react";
import { convertToToman, toPersianNumber } from "utils/helper";

const PlanOptionsCard = ({ selectedPlan }) => {
  const [isPriceEditing, setIsPriceEditing] = useState(false);
  const [priceVal, setPriceVal] = useState("");
  return (
    <>
      {selectedPlan === "پیشرفته" && (
        <div className="flex items-start justify-start gap-2 p-4 rounded-lg bg-light-inform-background-highlight mb-4">
          <Info className="text-light-inform-background-rest" size={20} />
          <p className="font-body-medium">
            با توجه به پلن مدیر سازمان٬ برای کارشناسان سازمان هم همین طرح اعمال
            می‌شود
          </p>
        </div>
      )}

      <div className="bg-light-neutral-surface-highlight rounded-md p-4">
        <p className="font-subtitle-large mb-4">ویژگی پلن </p>

        <div className={`font-body-medium leading-10`}>
          <div className="flex items-center justify-between">
            <p className="text-light-neutral-text-medium">تعداد کاربران</p>
            <div className="flex items-center gap-2">
              <p>{toPersianNumber("5")}</p>
              <p className="text-light-neutral-text-medium">کاربر</p>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <p className="text-light-neutral-text-medium">
              فیلتر زمانی دسترسی به داده‌ها
            </p>
            <div className="flex items-center gap-2">
              <p>{toPersianNumber("5")}</p>
              <p className="text-light-neutral-text-medium">سال گذشته</p>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <p className="text-light-neutral-text-medium">
              تعداد رکوردهای قابل دانلود
            </p>
            <div className="flex items-center gap-2">
              <p>{toPersianNumber("5")}</p>
              <p className="text-light-neutral-text-medium">میلیون</p>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <p className="text-light-neutral-text-medium">تعداد بولتن </p>
            <div className="flex items-center gap-2">
              <p>{toPersianNumber("30")}</p>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <p className="text-light-neutral-text-medium">تعداد هشدار</p>
            <div className="flex items-center gap-2">
              <p>{toPersianNumber("30")}</p>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <p className="text-light-neutral-text-medium">
              تعداد گزارش مقایسه‌ای
            </p>
            <div className="flex items-center gap-2">
              <p>{toPersianNumber("30")}</p>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <p className="text-light-neutral-text-medium">تعداد گزارش ۳۶۰</p>
            <p>{toPersianNumber("30")} </p>
          </div>
          <div className="flex items-center justify-between">
            <p className="text-light-neutral-text-medium">
              دسترسی به روزنامه‌های پیشخوان
            </p>
            <p>کامل </p>
          </div>
          <div className="flex items-center justify-between">
            <p className="text-light-neutral-text-medium">
              تعداد محتوای نشان شده
            </p>
            <p>{toPersianNumber("30")} </p>
          </div>
          <div className="flex items-center justify-between">
            <p className="text-light-neutral-text-medium">
              تعداد محتوای یاداداشت گذاری شده
            </p>
            <p>{toPersianNumber("30")} </p>
          </div>
        </div>
      </div>

      {isPriceEditing ? (
        <div
          className={`bg-light-neutral-surface-highlight rounded-md p-4 flex items-center gap-3 ${
            selectedPlan === "پیشرفته" ? "hidden" : ""
          }`}
        >
          <div className="w-full">
            <CInput
              type="number"
              title="مبلغ"
              placeholder="مبلغ را وارد کنید"
              value={priceVal}
              onChange={(e) => setPriceVal(e?.target?.value)}
              caption={toPersianNumber(convertToToman(priceVal))}
              inputProps={{ min: 2, max: 2, type: "number" }}
            />
          </div>

          <div className="flex items-center gap-2 mb-5">
            <div className="w-24">
              <CButton>ثبت قیمت</CButton>
            </div>
            <div className="w-24" onClick={() => setIsPriceEditing(false)}>
              <CButton role="error" mode="outline">
                انصراف
              </CButton>
            </div>
          </div>
        </div>
      ) : (
        <div
          className={`bg-light-neutral-surface-highlight rounded-md p-4 flex items-center justify-between ${
            selectedPlan === "پیشرفته" ? "hidden" : ""
          }`}
        >
          <p className="font-body-medium text-light-neutral-text-medium">
            ویژگی پلن
          </p>

          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <p className="font-headline-medium">۳٬۰۰۰٬۰۰۰</p>
              <p className="font-body-medium text-light-neutral-text-medium">
                تومان
              </p>
            </div>
            <PencilSimpleLine
              onClick={() => setIsPriceEditing(true)}
              className="w-6 h-6 text-light-primary-text-rest rounded-md transition hover:bg-light-primary-background-highlight cursor-pointer flex items-center justify-center"
            />
          </div>
        </div>
      )}
    </>
  );
};

export default PlanOptionsCard;
