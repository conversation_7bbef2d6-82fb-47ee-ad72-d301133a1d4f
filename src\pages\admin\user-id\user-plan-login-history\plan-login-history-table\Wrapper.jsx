import { CaretDown } from "@phosphor-icons/react";

const Wrapper = ({ children }) => {
  return (
    <div className="py-4 flex flex-col gap-4">
      <ul className="font-overline-medium text-light-neutral-text-medium grid grid-cols-5 select-none">
        <li className="">شناسه مرورگر</li>
        <li className="text-center">مکان</li>
        <li className="text-center">آدرس IP</li>
        <li
          className="cursor-pointer flex items-center justify-center gap-1"
          // onClick={() => {
          //   setFilter((prev) => ({
          //     ...prev,
          //     sort_rules: [
          //       {
          //         field: "created_at",
          //         direction:
          //           prev.sort_rules?.[0]?.direction === "asc" ? "desc" : "asc",
          //       },
          //     ],
          //   }));
          //   setIsDown((prev) => !prev);
          // }}
        >
          <p>تاریخ و زمان</p>
          <CaretDown
            size={12}
            // className={`${
            //   isDown ? "rotate-180 transition-all" : "transition-all"
            // }`}
          />
        </li>
        <li className="text-center">وضعیت</li>
      </ul>
      {children}
    </div>
  );
};

export default Wrapper;
