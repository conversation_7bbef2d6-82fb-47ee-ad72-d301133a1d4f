import { useState } from "react";
import Indicator from "./components/Indicator";
import StepOne from "./components/StepOne";
import StepOneGuide from "./components/StepOneGuide";
import StepTwoGuide from "./components/StepTwoGuide";
import StepThreeGuide from "./components/StepThreeGuide";
import StepFourGuide from "./components/StepFourGuide";
import StepTwo from "./components/StepTwo";
import StepThree from "./components/StepThree";
import NewFilterGuide from "./components/NewFilterGuide";
import StoreFilterGuide from "./components/StoreFilterGuide";
import StepFour from "./components/StepFour";
import { useLocation } from "react-router-dom";
import StepFive from "./components/StepFive";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";

const AlertCreate = () => {
  const { state } = useLocation();
  const breadcrumbList = [
    { title: "هشدار", link: "/app/alert/list" },
    { title: state ? "ویرایش هشدار" : "هشدار جدید" },
  ];
  useBreadcrumb(breadcrumbList);

  const [status, setStatus] = useState(1);

  const [alertData, setAlertData] = useState(
    state || {
      q: "",
      title: "",
      description: "",
      receiver: "",
      messenger: "",
      channel_id: "",
      platform: {},
    }
  );

  const [selectFilter, setSelectFiter] = useState("");
  const guide = {
    1: <StepOneGuide />,
    2: <StepTwoGuide />,
    3: <StepThreeGuide />,
    4: <StepFourGuide />,
  };

  return (
    <div className="p-6 h-full">
      <Indicator status={status} step={1} title="عنوان هشدار">
        <div className="col-span-7">
          <div className="bg-white rounded-lg shadow-[0px_2px_20px_0px_#0000000D] flex-1">
            <StepOne
              setStatus={setStatus}
              setAlertData={setAlertData}
              alertData={alertData}
              state={state}
            />
          </div>
        </div>
        <div className="col-span-5">{guide[status]}</div>
      </Indicator>

      <Indicator status={status} step={2} title="انتخاب بستر دریافت مطالب">
        <div className="col-span-7">
          <div className="bg-white rounded-lg shadow-[0px_2px_20px_0px_#0000000D] flex-1">
            <StepTwo
              setStatus={setStatus}
              setAlertData={setAlertData}
              alertData={alertData}
            />
          </div>
        </div>
        <div className="col-span-5">{guide[status]}</div>
      </Indicator>

      <Indicator status={status} step={3} title="تنظیمات هشدار">
        <div className="col-span-7">
          <div className="bg-white  rounded-lg shadow-[0px_2px_20px_0px_#0000000D] flex-1">
            <StepThree
              setStatus={setStatus}
              selectFilter={selectFilter}
              setSelectFiter={setSelectFiter}
              alertData={alertData}
              setAlertData={setAlertData}
            />
          </div>
        </div>
        <div className="col-span-5">
          {selectFilter === "store-filter" ? (
            <NewFilterGuide />
          ) : selectFilter === "new-filter" ? (
            <StoreFilterGuide />
          ) : (
            guide[status]
          )}
        </div>
      </Indicator>

      <Indicator status={status} step={4} title="تنظیم فیلترها">
        <div className="col-span-7">
          <div className="rounded-lg flex-1">
            <StepFour
              setStatus={setStatus}
              alertData={alertData}
              setAlertData={setAlertData}
              selectFilter={state ? "old" : selectFilter}
              state={state}
            />
          </div>
        </div>
        <div className="col-span-5">{guide[status]}</div>
      </Indicator>

      <Indicator status={status} step={5} title="بررسی نهایی">
        <div className="col-span-7">
          <div className="bg-white  rounded-lg shadow-[0px_2px_20px_0px_#0000000D] flex-1">
            <StepFive
              setStatus={setStatus}
              alertData={alertData}
              selectFilter={state ? "old" : selectFilter}
              state={state}
            />
          </div>
        </div>
        <div className="col-span-5">{guide[status]}</div>
      </Indicator>
    </div>
  );
};

export default AlertCreate;
