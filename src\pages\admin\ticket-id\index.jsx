import { useParams } from "react-router-dom";
import { ToastContainer } from "react-toastify";
import Loading from "components/ui/Loading";
import { useEffect, useState } from "react";
import TicketInformation from "./components/TicketInformation";
import TicketingChatRoom from "./components/TicketingChatRoom";
import ticket from "service/api/ticket";
import { useLayoutContext } from "context/layout-context";

const AdminTicketDetails = () => {
  const { setBreadcrumb } = useLayoutContext();
  const { id } = useParams();
  const [isLoading, setIsLoading] = useState(false);
  const [ticketData, setTicketData] = useState([]);
  const getTicket = async () => {
    setIsLoading(true);
    try {
      const {
        data: { data },
      } = await ticket.getAdminTicket(id);
      setTicketData(data);
    } catch (error) {
      console.log(error.response.data.message);
    }
    setIsLoading(false);
  };
  useEffect(() => {
    getTicket();
  }, []);

  useEffect(() => {
    setBreadcrumb([
      { title: "تیکت‌", link: "/admin/ticket/list" },
      { title: ticketData?.ticket?.title },
    ]);
  }, [ticketData]);

  return (
    <>
      <div className="grid grid-cols-12 items-start gap-4 px-4">
        <div className="col-span-9 bg-light-neutral-surface-card p-4 rounded-[8px]">
          <TicketingChatRoom
            id={id}
            messageId={ticketData?.messages?.length - 1}
            ticketData={ticketData?.ticket}
            closed_date={ticketData?.ticket?.updated_at}
            isClosed={ticketData?.messages?.at(-1)?.is_responder}
          />
        </div>
        <div className="col-span-3 bg-light-neutral-surface-card p-4 rounded-[8px]">
          <TicketInformation
            ticketData={ticketData?.ticket}
            closed_date={ticketData?.ticket?.updated_at}
            isClosed={ticketData?.messages?.at(-1)?.is_responder}
          />
        </div>
      </div>
      {isLoading && <Loading />}
      <ToastContainer />
    </>
  );
};

export default AdminTicketDetails;
