// import { useRef, useEffect, useState } from "react";
// import ForceGraph2D from "react-force-graph-2d";
// import { polygonHull } from "d3-polygon";
// import MyData from "./graph_output.json";
// const MyGraph = () => {
//   const [dimensions, setDimensions] = useState({ width: 300, height: 300 });
//   const containerRef = useRef();

//   const fgRef = useRef();
//   useEffect(() => {
//     const handleResize = () => {
//       if (containerRef.current) {
//         const { width, height } = containerRef.current.getBoundingClientRect();
//         setDimensions({ width, height });
//       }
//     };
//     handleResize();
//     window.addEventListener("resize", handleResize);
//     return () => window.removeEventListener("resize", handleResize);
//   }, []);

//   return (
//     <div ref={containerRef} style={{ width: "100%", height: "500px" }}>
//       <ForceGraph2D
//         ref={fgRef}
//         graphData={MyData}
//         nodeId="id"
//         nodeAutoColorBy="group"
//         width={dimensions.width}
//         height={dimensions.height}
//         maxZoom={10}
//         minZoom={0.1}
//         nodeLabel="id"
//         // nodeLabel={(X) => console.log('X')}
//         // linkColor={() => "rgba(0,0,0,0.2)"}
//         linkColor={(node) => {
//           return `rgba(${node?.source?.color?.[0] || 0},${
//             node?.source?.color?.[1] || 0
//           },${node?.source?.color?.[2] || 0} ,0.2)`;
//         }}
//         nodeColor={(node) => {
//           return `rgba(${node?.color?.[0] || 0},${node?.color?.[1] || 0},${
//             node?.color?.[2] || 0
//           } ,1)`;
//         }}
//         onRenderFramePost={(ctx, globalScale) => {
//           // 1) retrieve the final node positions from the graph
//           const { nodes } = MyData;
//           // 2) group nodes by 'group'
//           const groups = {};
//           nodes.forEach((node) => {
//             const g = node.group || "Ungrouped";
//             if (!groups[g]) groups[g] = [];
//             // each node’s x,y is in the current canvas coordinate space
//             groups[g].push([node.x, node.y, node.color]);
//           });
//           // 3) draw a "bubble" (convex hull) around each group
//           Object.entries(groups).forEach(([groupId, coords]) => {
//             if (coords.length < 3) return; // need >= 3 points for a hull
//             const hull = polygonHull(coords);
//             if (!hull) return;

//             // draw the hull shape
//             ctx.beginPath();
//             ctx.moveTo(hull[0][0], hull[0][1]);
//             for (let i = 1; i < hull.length; i++) {
//               ctx.lineTo(hull[i][0], hull[i][1]);
//             }
//             ctx.closePath();
//             // fill + stroke
//             ctx.fillStyle = `rgba(${coords[2][2][0]}, ${coords[2][2][1]}, ${coords[2][2][2]}, 0.08)`; // translucent fill
//             ctx.strokeStyle = `rgba(${coords[2][2][0]}, ${coords[2][2][1]}, ${coords[2][2][2]}, 0.2)`; // slightly darker outline
//             ctx.lineWidth = 1;
//             ctx.fill();
//             ctx.stroke();
//           });
//         }}
//       />
//     </div>
//   );
// };

// export default MyGraph;
// import { useRef, useEffect, useState } from "react";
// import ForceGraph2D from "react-force-graph-2d";
// import { polygonHull } from "d3-polygon";
// import MyData from "./graph_output.json";
// import { toPersianNumber } from "utils/helper";
// const MyGraph = () => {
//   const [dimensions, setDimensions] = useState({ width: 300, height: 300 });
//   const [filteredNodes, setFilteredNodes] = useState(
//     MyData?.nodes?.length / 2 || 0
//   );
//   const containerRef = useRef();

//   const fgRef = useRef();
//   useEffect(() => {
//     const handleResize = () => {
//       if (containerRef.current) {
//         const { width, height } = containerRef.current.getBoundingClientRect();
//         setDimensions({ width, height });
//       }
//     };
//     handleResize();
//     window.addEventListener("resize", handleResize);
//     return () => window.removeEventListener("resize", handleResize);
//   }, []);
//   console.log();
//   return (
//     <div>
//       <div className="grid mb-5">
//         <label htmlFor="slider" className="font-body-medium">
//           تعداد گره‌ها: {toPersianNumber(filteredNodes)}
//         </label>
//         <input
//           className=""
//           id="slider"
//           type="range"
//           min="0"
//           max={MyData?.nodes?.length}
//           value={filteredNodes}
//           onChange={(e) => setFilteredNodes(e?.target?.value)}
//         />
//       </div>

//       <div ref={containerRef} style={{ width: "100%", height: "500px" }}>
//         <ForceGraph2D
//           ref={fgRef}
//           graphData={MyData}
//           nodeId="id"
//           nodeAutoColorBy="group"
//           width={dimensions.width}
//           height={dimensions.height}
//           maxZoom={10}
//           minZoom={0.1}
//           nodeLabel="id"
//           // nodeLabel={(X) => console.log('X')}
//           // linkColor={() => "rgba(0,0,0,0.2)"}
//           linkColor={(node) => {
//             return `rgba(${node?.source?.color?.[0] || 0},${
//               node?.source?.color?.[1] || 0
//             },${node?.source?.color?.[2] || 0} ,0.2)`;
//           }}
//           nodeColor={(node) => {
//             return `rgba(${node?.color?.[0] || 0},${node?.color?.[1] || 0},${
//               node?.color?.[2] || 0
//             } ,1)`;
//           }}
//           onRenderFramePost={(ctx, globalScale) => {
//             // 1) retrieve the final node positions from the graph
//             const { nodes } = MyData;
//             // 2) group nodes by 'group'
//             const groups = {};
//             nodes.forEach((node) => {
//               const g = node.group || "Ungrouped";
//               if (!groups[g]) groups[g] = [];
//               // each node’s x,y is in the current canvas coordinate space
//               groups[g].push([node.x, node.y, node.color]);
//             });
//             // 3) draw a "bubble" (convex hull) around each group
//             Object.entries(groups).forEach(([groupId, coords]) => {
//               if (coords.length < 3) return; // need >= 3 points for a hull
//               const hull = polygonHull(coords);
//               if (!hull) return;

//               // draw the hull shape
//               ctx.beginPath();
//               ctx.moveTo(hull[0][0], hull[0][1]);
//               for (let i = 1; i < hull.length; i++) {
//                 ctx.lineTo(hull[i][0], hull[i][1]);
//               }
//               ctx.closePath();
//               // fill + stroke
//               ctx.fillStyle = `rgba(${coords[2][2][0]}, ${coords[2][2][1]}, ${coords[2][2][2]}, 0.08)`; // translucent fill
//               ctx.strokeStyle = `rgba(${coords[2][2][0]}, ${coords[2][2][1]}, ${coords[2][2][2]}, 0.2)`; // slightly darker outline
//               ctx.lineWidth = 1;
//               ctx.fill();
//               ctx.stroke();
//             });
//           }}
//         />
//       </div>
//     </div>
//   );
// };

// export default MyGraph;

// import { useRef, useEffect, useState } from "react";
// import ForceGraph2D from "react-force-graph-2d";
// import { polygonHull } from "d3-polygon";
// import MyData from "./graph_output.json";
// import { toPersianNumber } from "utils/helper";

// const MyGraph = () => {
//   const [dimensions, setDimensions] = useState({ width: 300, height: 300 });
//   const [filteredNodesCount, setFilteredNodesCount] = useState(
//     Math.floor(MyData?.nodes?.length / 2) || 0
//   );
//   const containerRef = useRef();
//   const fgRef = useRef();

//   useEffect(() => {
//     const handleResize = () => {
//       if (containerRef.current) {
//         const { width, height } = containerRef.current.getBoundingClientRect();
//         setDimensions({ width, height });
//       }
//     };
//     handleResize();
//     window.addEventListener("resize", handleResize);
//     return () => window.removeEventListener("resize", handleResize);
//   }, []);

//   // Filter nodes and links based on slider value
//   const filteredNodes = MyData.nodes.slice(0, filteredNodesCount);
//   const filteredNodeIds = new Set(filteredNodes.map((node) => node.id));

//   const filteredLinks = MyData.links.filter(
//     (link) =>
//       filteredNodeIds.has(
//         typeof link.source === "object" ? link.source.id : link.source
//       ) &&
//       filteredNodeIds.has(
//         typeof link.target === "object" ? link.target.id : link.target
//       )
//   );

//   return (
//     <div>
//       <div className="grid mb-5">
//         <label htmlFor="slider" className="font-body-medium">
//           تعداد گره‌ها: {toPersianNumber(filteredNodesCount)}
//         </label>
//         <input
//           id="slider"
//           type="range"
//           min="0"
//           max={MyData?.nodes?.length}
//           value={filteredNodesCount}
//           onChange={(e) => setFilteredNodesCount(Number(e.target.value))}
//         />
//       </div>

//       <div ref={containerRef} style={{ width: "100%", height: "500px" }}>
//         <ForceGraph2D
//           ref={fgRef}
//           graphData={{ nodes: filteredNodes, links: filteredLinks }}
//           nodeId="id"
//           nodeAutoColorBy="group"
//           width={dimensions.width}
//           height={dimensions.height}
//           maxZoom={10}
//           minZoom={0.1}
//           nodeLabel="id"
//           linkColor={(node) => {
//             return `rgba(${node?.source?.color?.[0] || 0},${
//               node?.source?.color?.[1] || 0
//             },${node?.source?.color?.[2] || 0},0.2)`;
//           }}
//           nodeColor={(node) => {
//             return `rgba(${node?.color?.[0] || 0},${node?.color?.[1] || 0},${
//               node?.color?.[2] || 0
//             },1)`;
//           }}
//           onRenderFramePost={(ctx, globalScale) => {
//             // 1) retrieve the final node positions from the graph
//             const { nodes } = MyData;
//             // 2) group nodes by 'group'
//             const groups = {};
//             nodes.forEach((node) => {
//               const g = node.group || "Ungrouped";
//               if (!groups[g]) groups[g] = [];
//               // each node’s x,y is in the current canvas coordinate space
//               groups[g].push([node.x, node.y, node.color]);
//             });
//             // 3) draw a "bubble" (convex hull) around each group
//             Object.entries(groups).forEach(([groupId, coords]) => {
//               if (coords.length < 3) return; // need >= 3 points for a hull
//               const hull = polygonHull(coords);
//               if (!hull) return;

//               // draw the hull shape
//               ctx.beginPath();
//               ctx.moveTo(hull[0][0], hull[0][1]);
//               for (let i = 1; i < hull.length; i++) {
//                 ctx.lineTo(hull[i][0], hull[i][1]);
//               }
//               ctx.closePath();
//               // fill + stroke
//               ctx.fillStyle = `rgba(${coords[2][2][0]}, ${coords[2][2][1]}, ${coords[2][2][2]}, 0.08)`; // translucent fill
//               ctx.strokeStyle = `rgba(${coords[2][2][0]}, ${coords[2][2][1]}, ${coords[2][2][2]}, 0.2)`; // slightly darker outline
//               ctx.lineWidth = 1;
//               ctx.fill();
//               ctx.stroke();
//             });
//           }}
//         />
//       </div>
//     </div>
//   );
// };

// export default MyGraph;

import { useRef, useEffect, useState } from "react";
import ForceGraph2D from "react-force-graph-2d";
import { polygonHull } from "d3-polygon";
import MyData from "./graph_output.json";
import { toPersianNumber } from "utils/helper";

const MyGraph = () => {
  const [dimensions, setDimensions] = useState({ width: 300, height: 300 });
  const [filteredNodesCount, setFilteredNodesCount] = useState(
    Math.floor(MyData?.nodes?.length / 2) || 0
  );
  const containerRef = useRef();
  const fgRef = useRef();

  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current) {
        const { width, height } = containerRef.current.getBoundingClientRect();
        setDimensions({ width, height });
      }
    };
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Filter nodes and links based on slider value
  const filteredNodes = MyData.nodes.slice(0, filteredNodesCount);
  const filteredNodeIds = new Set(filteredNodes.map((node) => node.id));

  const filteredLinks = MyData.links.filter(
    (link) =>
      filteredNodeIds.has(
        typeof link.source === "object" ? link.source.id : link.source
      ) &&
      filteredNodeIds.has(
        typeof link.target === "object" ? link.target.id : link.target
      )
  );

  return (
    <div>
      <div className="grid mb-5">
        <label htmlFor="slider" className="font-body-medium">
          تعداد گره‌ها: {toPersianNumber(filteredNodesCount)}
        </label>
        <input
          id="slider"
          type="range"
          min="0"
          max={MyData?.nodes?.length}
          value={filteredNodesCount}
          onChange={(e) => setFilteredNodesCount(Number(e.target.value))}
        />
      </div>

      <div ref={containerRef} style={{ width: "100%", height: "500px" }}>
        <ForceGraph2D
          ref={fgRef}
          graphData={{ nodes: filteredNodes, links: filteredLinks }}
          nodeId="id"
          dagLevelDistance={10}
          nodeAutoColorBy="group"
          nodeRelSize={10}
          width={dimensions.width}
          height={dimensions.height}
          maxZoom={10}
          minZoom={0.1}
          nodeLabel="id"
          linkColor={(node) => {
            return `rgba(${node?.source?.color?.[0] || 0},${
              node?.source?.color?.[1] || 0
            },${node?.source?.color?.[2] || 0},0.2)`;
          }}
          nodeColor={(node) => {
            return `rgba(${node?.color?.[0] || 0},${node?.color?.[1] || 0},${
              node?.color?.[2] || 0
            },1)`;
          }}
          // onRenderFramePost={(ctx, globalScale) => {
          //   // 1) retrieve the final node positions from the graph
          //   // const { nodes } = { nodes: filteredNodes, links: filteredLinks };

          //   if (!filteredNodes || filteredNodes.length === 0) {
          //     ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
          //     return;
          //   }

          //   // 2) group nodes by 'group'
          //   const groups = {};
          //   filteredNodes.forEach((node) => {
          //     const g = node.group || "Ungrouped";
          //     if (!groups[g]) groups[g] = [];
          //     // each node’s x,y is in the current canvas coordinate space
          //     groups[g].push([node.x, node.y, node.color]);
          //   });
          //   // 3) draw a "bubble" (convex hull) around each group
          //   Object.entries(groups).forEach(([groupId, coords]) => {
          //     if (coords.length < 3) return; // need >= 3 points for a hull
          //     const hull = polygonHull(coords);
          //     if (!hull) return;

          //     // draw the hull shape
          //     ctx.beginPath();
          //     ctx.moveTo(hull[0][0], hull[0][1]);
          //     for (let i = 1; i < hull.length; i++) {
          //       ctx.lineTo(hull[i][0], hull[i][1]);
          //     }
          //     ctx.closePath();
          //     // fill + stroke
          //     ctx.fillStyle = `rgba(${coords[2][2][0]}, ${coords[2][2][1]}, ${coords[2][2][2]}, 0.08)`; // translucent fill
          //     ctx.strokeStyle = `rgba(${coords[2][2][0]}, ${coords[2][2][1]}, ${coords[2][2][2]}, 0.2)`; // slightly darker outline
          //     ctx.lineWidth = 1;
          //     ctx.fill();
          //     ctx.stroke();
          //   });
          // }}
        />
      </div>
    </div>
  );
};

export default MyGraph;
