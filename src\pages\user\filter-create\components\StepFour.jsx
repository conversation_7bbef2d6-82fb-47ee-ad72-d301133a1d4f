import { useState } from "react";
import AlertAndFilterDetails from "components/alertAndFilterDetails";
import { useNavigate } from "react-router-dom";
import { ToastContainer } from "react-toastify";
import { notification } from "utils/helper";
import { WarningDiamond } from "@phosphor-icons/react";
import filter from "service/api/filter";
import Loading from "components/ui/Loading";
import { CButton } from "components/ui/CButton.jsx";
import PropTypes from "prop-types";

const StepFour = ({ filterData, setStatus, state }) => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const createFilter = async () => {
    setLoading(true);
    try {
      if (filterData?.group?.length && typeof filterData.group === "object") {
        filterData.group = filterData?.group?.map((item) => item.title);
      }

      const { data } = state
        ? await filter.update(filterData.id, filterData)
        : await filter.new(filterData);
      navigate("/app/filter/list", {
        state: { success: true, message: data.message },
      });
    } catch (error) {
      console.log(error?.response?.data?.message);
      notification.error(
        error?.response?.data?.message,
        <WarningDiamond size={32} className="text-light-error-text-rest" />,
      );
    }
    setLoading(false);
  };
  return (
    <>
      {loading && <Loading />}
      <div className="p-6 flex flex-col gap-12">
        <AlertAndFilterDetails
          data={filterData}
          showReceivingPlatform={false}
        />
        <div className="flex flex-row-reverse">
          <CButton
            type={"submit"}
            className={`!w-[200px]`}
            onClick={() => createFilter()}
          >
            ثبت و ذخیره
          </CButton>
          <button
            className="w-[100px] h-10 flex justify-center items-center text-light-primary-text-rest font-button-medium"
            onClick={() => {
              setStatus(1);
            }}
          >
            ویرایش
          </button>
        </div>
      </div>
      <ToastContainer />
    </>
  );
};

StepFour.propTypes = {
  filterData: PropTypes.object.isRequired,
  setStatus: PropTypes.func.isRequired,
  state: PropTypes.bool,
};

export default StepFour;
