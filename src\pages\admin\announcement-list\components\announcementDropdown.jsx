import { useState, useRef, useEffect } from "react";
import { CaretDown } from "@phosphor-icons/react";

const AnnouncementDropDown = ({
  subsets = [],
  handleChange,
  value = [], // changed from initialValue
  title,
  placeholder = "Select items...",
}) => {
  const [open, setOpen] = useState(false);
  const [selectedItems, setSelectedItems] = useState(value);
  const dropdownRef = useRef();

  // Sync with external value updates
  useEffect(() => {
    setSelectedItems(value);
  }, [value]);

  const handleItemClick = (item) => {
    const isSelected = selectedItems.some(
      (selected) => selected.value === item.value
    );
    const newSelectedItems = isSelected
      ? selectedItems.filter((selected) => selected.value !== item.value)
      : [...selectedItems, item];

    setSelectedItems(newSelectedItems);
    handleChange(newSelectedItems);
  };

  useEffect(() => {
    const handleClickOutside = (e) => {
      if (dropdownRef.current && !dropdownRef.current.contains(e.target)) {
        setOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <div ref={dropdownRef} className="relative w-full">
      {title && <p className="font-overline-large pb-1">{title}</p>}
      <div
        onClick={() => setOpen((prev) => !prev)}
        className={`flex gap-4 items-center justify-between p-2 rounded-lg bg-white border ${
          open ? "border-[#9198AD]" : "border-light-neutral-border-medium-rest"
        } w-full cursor-pointer`}
      >
        <span className="font-body-medium">{placeholder}</span>
        <CaretDown
          style={{
            transform: open ? "rotate(180deg)" : "rotate(0deg)",
            transition: "all 0.5s",
          }}
        />
      </div>

      {open && (
        <div className="absolute top-12 left-0 bg-white rounded-lg shadow-[0px_4px_20px_0px_#0000001A] p-4 w-full z-50">
          <div className="font-body-small flex flex-col gap-1">
            {subsets?.map((item) => (
              <div
                key={item.value}
                className="flex items-center gap-4 p-2 rounded-lg hover:bg-light-neutral-surface-highlight cursor-pointer"
                onClick={() => handleItemClick(item)}
              >
                <input
                  type="checkbox"
                  checked={selectedItems.some(
                    (selected) => selected.value === item.value
                  )}
                  readOnly
                  className="w-4 h-4"
                />
                <span>{item.text}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default AnnouncementDropDown;
