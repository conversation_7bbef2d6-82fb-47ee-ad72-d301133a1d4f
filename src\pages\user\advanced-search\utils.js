export const findPlatform = (data) => {
  if (data?.platform === "eitaa_channel_post") return "eitaa";
  if (data?.url?.includes("t.me")) return "telegram";
  if (
    data?.url?.includes("twitter") ||
    data?.url?.includes("x.com") ||
    data?.avatar?.split("/").includes("twitter") ||
    data.in_reply_to_post_id
  )
    return "twitter";
  if (data?.url?.includes("instagram")) return "instagram";
  return "news";
};
