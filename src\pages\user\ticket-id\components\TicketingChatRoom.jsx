import { useEffect, useRef, useState } from "react";
import Chat<PERSON>ontent from "./ChatContent";
import { Info, Paperclip, PaperPlaneTilt } from "@phosphor-icons/react";
import { CButton } from "components/ui/CButton";
import ticket from "service/api/ticket";
import { parseTimeToPersianSummary, shortener } from "utils/helper";
import { notification } from "utils/helper.js";
import {
  File,
  XCircle,
  FileZip,
  FilePdf,
  FileTxt,
  FileDoc,
  MicrosoftExcelLogo,
} from "@phosphor-icons/react";
const TicketingChatRoom = ({ id, ticketData, closed_date, isClosed }) => {
  const [ticketLists, setTicketLists] = useState([]);
  const [attachments, setAttachments] = useState([]);
  const [ticketContent, setTicketContent] = useState("");
  const [selectedImage, setSelectedImage] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const chatContainerRef = useRef(null);
  const fileInputRef = useRef(null);
  const loadTicket = async () => {
    try {
      const {
        data: { data },
      } = await ticket.get(id);
      setTicketLists(data?.messages);
    } catch (error) {
      console.log(error);
    }
  };

  const sendTicketHandler = async () => {
    setIsLoading(true);
    try {
      if (attachments.length > 0 && ticketContent.trim()) {
        const res = await ticket.sendTicket(id, {
          ticket_id: id,
          description: ticketContent,
          has_attachment: attachments.length > 0,
        });
        const formData = new FormData();
        attachments.forEach((file) => {
          formData.append("files", file);
        });
        await ticket.updateAttachment(id, res?.data?.data?.id, formData);
      } else if (attachments.length > 0) {
        const formData = new FormData();
        attachments.forEach((file) => {
          formData.append("files", file);
        });
        await ticket.updateAttachment(id, 0, formData);
      } else if (ticketContent.trim()) {
        await ticket.sendTicket(id, {
          ticket_id: id,
          description: ticketContent,
        });
      }
      setTicketContent("");
      setAttachments([]);
      fileInputRef.current.value = null;
    } catch (error) {
      notification.error("فایل آپلود شده پشتیبانی نمیشود!");
    }
    await loadTicket();
    setIsLoading(false);
  };

  const handlePaperclipClick = () => {
    fileInputRef.current.click();
  };

  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTo({
        top: chatContainerRef.current.scrollHeight,
        behavior: "smooth",
      });
    }
  }, [ticketLists]);

  useEffect(() => {
    loadTicket();
  }, []);

  const handleImageClick = (file) => {
    if (file.type.startsWith("image/")) {
      setSelectedImage(URL.createObjectURL(file));
      setIsModalOpen(true);
    }
  };
  const renderFilePreview = (file) => {
    if (file.type.startsWith("image/")) {
      return (
        <img
          src={URL.createObjectURL(file)}
          alt={file.name}
          className="w-16 h-16 rounded-md object-cover"
        />
      );
    }

    if (file.type === "application/pdf") {
      return (
        <div className="w-16 h-16 flex items-center justify-center bg-red-100 rounded-md">
          <FilePdf size={32} className="text-red-500" />
        </div>
      );
    }

    if (
      file.type === "application/zip" ||
      file.type === "application/x-zip-compressed"
    ) {
      return (
        <div className="w-16 h-16 flex items-center justify-center bg-yellow-100 rounded-md">
          <FileZip size={32} className="text-yellow-500" />
        </div>
      );
    }

    if (
      file.type ===
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
      file.type === "application/vnd.ms-excel"
    ) {
      return (
        <div className="w-16 h-16 flex items-center justify-center bg-green-100 rounded-md">
          <MicrosoftExcelLogo size={32} className="text-green-500" />
        </div>
      );
    }

    if (file.type === "text/plain") {
      return (
        <div className="w-16 h-16 flex items-center justify-center bg-gray-100 rounded-md">
          <FileTxt size={32} className="text-gray-500" />
        </div>
      );
    }

    if (
      file.type ===
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
      file.type === "application/msword"
    ) {
      return (
        <div className="w-16 h-16 flex items-center justify-center bg-blue-100 rounded-md">
          <FileDoc size={32} className="text-blue-500" />
        </div>
      );
    }

    return (
      <div className="w-16 h-16 flex items-center justify-center bg-light-neutral-background-secondary rounded-md">
        <File size={32} />
      </div>
    );
  };
  const handleDeleteFile = (index) => {
    setAttachments((prevAttachments) =>
      prevAttachments.filter((_, i) => i !== index),
    );
  };

  return (
    <>
      <div className={`h-[68vh] overflow-auto`} ref={chatContainerRef}>
        {ticketLists?.map((item) => (
          <div
            key={item?.id}
            className={`flex items-start ${
              item?.is_responder ? "justify-end" : "justify-start"
            }`}
          >
            <ChatContent data={item} />
          </div>
        ))}
      </div>

      <div className="flex flex-col mt-5">
        <div className="flex items-center gap-x-3">
          <div className="w-full relative">
            {ticketData?.ticket?.ticket_state === "closed" ? (
              <div className="flex items-center gap-2 bg-light-inform-background-highlight rounded-md p-4">
                <Info size={20} className="text-light-inform-text-rest" />
                <p className="font-body-medium">
                  این تیکت توسط {isClosed ? "کارشناس" : "کاربر"} در تاریخ{" "}
                  {parseTimeToPersianSummary(closed_date)} بسته شد
                </p>
              </div>
            ) : (
              <>
                <textarea
                  className="border border-light-neutral-border-medium-rest rounded-md w-full p-2 mt-1 p-y-[13px] font-body-large focus:outline-none"
                  placeholder="پیام خود را اینجا بنویسید"
                  value={ticketContent}
                  onChange={(e) => setTicketContent(e?.target?.value)}
                  title={"متن پیام"}
                  rows={4}
                  type="textarea"
                />

                <div className="absolute bottom-3 left-5 flex items-center gap-2">
                  <div onClick={handlePaperclipClick}>
                    {/* <input
                      type="file"
                      ref={fileInputRef}
                      accept=".jpeg,.jpg,.png,.docx,.pdf,.doc,.csv,.xls,.txt,.zip"
                      className="hidden"
                      onChange={(e) => {
                        const newFiles = Array.from(e.target.files);
                        const totalSize =
                          attachments.reduce(
                            (acc, file) => acc + file.size,
                            0
                          ) +
                          newFiles.reduce((acc, file) => acc + file.size, 0);
                        if (totalSize > 10 * 1024 * 1024) {
                          notification.error(
                            "حجم کل فایل‌ها نباید بیشتر از ۱۰ مگابایت باشد!"
                          );
                          return;
                        }
                        const totalFiles = attachments.length + newFiles.length;
                        if (totalFiles > 8) {
                          notification.error(
                            "تعداد فایل‌های آپلود شده باید حداکثر ۸ عدد باشد!"
                          );
                          return;
                        }
                        const filteredFiles = newFiles.filter(
                          (newFile) =>
                            !attachments.some(
                              (existingFile) =>
                                existingFile.name === newFile.name
                            )
                        );
                        setAttachments((prev) => [...prev, ...filteredFiles]);
                      }}
                      // onChange={(e) => {
                      //   const newFiles = Array.from(e.target.files);
                      //   const totalFiles = attachments.length + newFiles.length;
                      //   if (totalFiles > 8) {
                      //     notification.error(
                      //       "تعداد فایل‌های آپلود شده باید حداکثر ۸ عدد باشد!"
                      //     );
                      //     return;
                      //   }
                      //   const filteredFiles = newFiles.filter(
                      //     (newFile) =>
                      //       !attachments.some(
                      //         (existingFile) =>
                      //           existingFile.name === newFile.name
                      //       )
                      //   );
                      //   setAttachments((prev) => [...prev, ...filteredFiles]);
                      // }}
                      multiple
                    /> */}
                    <input
                      type="file"
                      ref={fileInputRef}
                      accept=".jpeg,.jpg,.png,.docx,.pdf,.doc,.csv,.xls,.txt,.zip"
                      className="hidden"
                      onChange={(e) => {
                        const validExtensions = [
                          ".jpeg",
                          ".jpg",
                          ".png",
                          ".docx",
                          ".pdf",
                          ".doc",
                          ".csv",
                          ".xls",
                          ".txt",
                          ".zip",
                        ];
                        const newFiles = Array.from(e.target.files);
                        const invalidFiles = newFiles.filter((file) => {
                          const fileExtension = file.name
                            .slice(file.name.lastIndexOf("."))
                            .toLowerCase();
                          return !validExtensions.includes(fileExtension);
                        });
                        if (invalidFiles.length > 0) {
                          notification.error(
                            `فرمت فایل‌های زیر معتبر نیست: ${invalidFiles
                              .map((file) => file.name)
                              .join(", ")}`,
                          );
                          return;
                        }
                        const totalSize =
                          attachments.reduce(
                            (acc, file) => acc + file.size,
                            0,
                          ) +
                          newFiles.reduce((acc, file) => acc + file.size, 0);
                        if (totalSize > 10 * 1024 * 1024) {
                          notification.error(
                            "حجم کل فایل‌ها نباید بیشتر از ۱۰ مگابایت باشد!",
                          );
                          return;
                        }
                        const totalFiles = attachments.length + newFiles.length;
                        if (totalFiles > 8) {
                          notification.error(
                            "تعداد فایل‌های آپلود شده باید حداکثر ۸ عدد باشد!",
                          );
                          return;
                        }
                        const filteredFiles = newFiles.filter(
                          (newFile) =>
                            !attachments.some(
                              (existingFile) =>
                                existingFile.name === newFile.name,
                            ),
                        );
                        setAttachments((prev) => [...prev, ...filteredFiles]);
                      }}
                      multiple
                    />
                    <div className="w-20">
                      <CButton
                        role="neutral"
                        size="sm"
                        rightIcon={<Paperclip size={17} />}
                      >
                        پیوست
                      </CButton>
                    </div>
                  </div>

                  <div className="w-20">
                    <CButton
                      size="sm"
                      onClick={sendTicketHandler}
                      readOnly={
                        !(ticketContent.trim() || attachments?.length > 0)
                      }
                      rightIcon={<PaperPlaneTilt size={17} />}
                      disabled={isLoading}
                    >
                      ارسال
                    </CButton>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
        <div className="flex gap-4 mt-2 flex-wrap">
          {attachments.map((file, index) => (
            <div className="relative flex flex-col" key={index}>
              <button
                onClick={() => handleDeleteFile(index)}
                className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full"
              >
                <XCircle size={20} />
              </button>
              <div
                className="flex-none"
                onClick={() => {
                  if (file.type.startsWith("image/")) {
                    handleImageClick(file);
                  }
                }}
              >
                {renderFilePreview(file)}
              </div>
              <p className="font-body-small">{shortener(file?.name, 7)}</p>
            </div>
          ))}
        </div>
      </div>

      {isModalOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          onClick={() => {
            setIsModalOpen(false);
            setSelectedImage(null);
          }}
        >
          <div
            className="relative p-4 rounded"
            onClick={(e) => e.stopPropagation()}
          >
            <img
              src={selectedImage}
              alt="Preview"
              className="max-w-96 max-h-max-w-96"
            />
          </div>
        </div>
      )}
    </>
  );
};

export default TicketingChatRoom;
