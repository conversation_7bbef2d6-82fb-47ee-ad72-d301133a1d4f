import React, { useEffect, useState } from "react";
import DatePicker from "react-multi-date-picker";
import persian from "react-date-object/calendars/persian";
import persian_fa from "react-date-object/locales/persian_fa";
import { CaretLeft, CaretRight } from "@phosphor-icons/react";
import PropTypes from "prop-types";

const SingleDatePicker = ({ value, onChange = () => {} }) => {
  const initialValue = value || new Date();
  const [selectedDate, setSelectedDate] = useState(initialValue);

  const handleDateChange = (date) => {
    const newDate = new Date(date).setHours(0, 0, 0, 0);
    setSelectedDate(new Date(newDate));
    onChange(new Date(newDate));
  };

  useEffect(() => {
    onChange(initialValue);
  }, [initialValue, onChange]);

  return (
    <DatePicker
      value={selectedDate}
      calendar={persian}
      locale={persian_fa}
      calendarPosition="bottom-right"
      containerClassName={"datepicker-container"}
      onChange={handleDateChange}
      renderButton={(direction, handleClick) => (
        <button onClick={handleClick}>
          {direction === "right" ? (
            <CaretLeft size={20} />
          ) : (
            <CaretRight size={20} />
          )}
        </button>
      )}
    />
  );
};

SingleDatePicker.propTypes = {
  value: PropTypes.instanceOf(Date),
  onChange: PropTypes.func,
};

export default SingleDatePicker;
