import { useContext, useEffect, useState, useRef } from "react";
import AuthContext from "context/auth-context";
import { notification, parseTimeToPersian } from "utils/helper";
import Divider from "components/ui/Divider";
import { Field, Formik, Form } from "formik";
import { updateProfileSchema } from "utils/validationSchemas";
import { CInput } from "components/ui/CInput";
import { Link, NavLink, useNavigate } from "react-router-dom";
import {
  CaretRight,
  CheckCircle,
  User,
  WarningDiamond,
  Image,
} from "@phosphor-icons/react";
import { ToastContainer } from "react-toastify";
import Loading from "components/ui/Loading";
import userService from "service/api/userService.js";
import FetchImage from "./components/FetchImage.jsx";
import Popup from "components/ui/PopUp.jsx";
import SubmitEmailContent from "./components/SubmitEmailContent.jsx";
import authService from "service/api/authService";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";

const ShowProfile = () => {
  const { profile, loadProfile } = useContext(AuthContext);
  const [loading, setLoading] = useState(false);
  const [loadingAvatar, setLoadingAvatar] = useState(false);
  const [noteValue, setNoteValue] = useState("");
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [emailValue, setEmailValue] = useState("");
  const navigate = useNavigate();
  const breadcrumbList = [{ title: "پروفایل کاربری" }];
  useBreadcrumb(breadcrumbList);
  const [initialValues, setInitialValues] = useState({
    first_name: profile.first_name || "",
    last_name: profile.last_name || "",
    username: profile.username || "",
    email: profile.email || "",
    phone_number: profile.phone_number || "",
    description: profile.description || "",
  });
  useEffect(() => {
    loadProfile();
  }, []);

  const fileInputRef = useRef(null);
  const [backgroundImage, setBackgroundImage] = useState(profile.avatar);
  const handleAvatarClick = () => fileInputRef.current.click();
  const handleFileChange = async (event) => {
    setLoadingAvatar(true);
    const file = event.target.files[0];
    if (file) {
      const maxSizeInBytes = 1 * 1024 * 1024; // 1 MB in bytes

      if (file.size > maxSizeInBytes) {
        notification.error(
          "حجم فایل انتخاب شده نباید بیشتر از 1 مگابایت باشد.",
          <WarningDiamond size={32} className="text-light-error-text-rest" />,
        );
        setLoadingAvatar(false);
        return;
      }
      if (file.type === "image/png" || file.type === "image/jpeg") {
        const imageUrl = URL.createObjectURL(file);
        setBackgroundImage(imageUrl);
        const formData = new FormData();
        formData.append("avatar", file);
        try {
          const response = await userService.updateProfilePicture(formData);
          if (response.data.code !== 200) {
            notification.error(
              "خطا در بارگذاری عکس پروفایل!",
              <WarningDiamond
                size={32}
                className="text-light-error-text-rest"
              />,
            );
          } else {
            notification.success(
              response.data.message,
              <CheckCircle
                size={20}
                className="text-light-success-text-rest"
              />,
            );
          }
        } catch (error) {
          console.error("Error uploading file:", error);
          setBackgroundImage(undefined);
          notification.error(
            "خطا در بارگذاری عکس پروفایل!",
            <WarningDiamond size={32} className="text-light-error-text-rest" />,
          );
        }
      } else {
        notification.error(
          "فرمت فایل انتخاب شده صحیح نیست.",
          <WarningDiamond size={32} className="text-light-error-text-rest" />,
        );
      }
    }
    setLoadingAvatar(false);
  };

  const handleSubmit = async (data) => {
    setLoading(true);
    try {
      const help = Object.fromEntries(
        Object.entries(data).filter((x) => x[1]?.length),
      );
      const response = await userService.updateProfile(help);
      notification.success(
        response.data.message,
        <CheckCircle size={20} className="text-light-success-text-rest" />,
      );
    } catch (error) {
      if (error.response.status === 400) {
        notification.error(
          "اطلاعات وارد شده صحیح نیست",
          <WarningDiamond size={32} className="text-light-error-text-rest" />,
        );
      }
    }
    setLoading(false);
  };
  const handleChangePhoneAction = async () => {
    setIsLoading(true);
    try {
      const res = await authService.changePhoneNo({
        phone: profile?.phone_number,
      });
    } catch (error) {
      console.log(error);
    }
    setIsLoading(false);
    navigate("/app/user/profile/phoneNo-change");
  };
  const handleChangeEmailAction = async () => {
    setIsLoading(true);
    try {
      const res = await authService.changeEmail({
        email: profile?.email,
      });
    } catch (error) {
      console.log(error);
    }
    setIsLoading(false);
    navigate("/app/user/profile/email-change");
    // submit email
    // try {
    //   const res = await authService.changeEmail({
    //     email: emailValue,
    //   });
    // } catch (error) {
    //   console.log(error);
    // }
    // profile?.email ? setIsPopupOpen(true) : console.log(3);
  };
  return (
    <>
      <div className="flex flex-col justify-center items-center gap-4 py-6">
        <div className="bg-white w-[600px] rounded-lg shadow-[0px_2px_20px_0px_#0000000D] p-6">
          <div className="flex gap-8 font-body-medium text-light-neutral-text-medium relative mb-6">
            <NavLink
              to="/app/user/profile"
              className="pb-[6px]"
              style={({ isActive }) => {
                return {
                  color: isActive ? "#6F5CD1" : "",
                  borderBottom: isActive ? "1px solid #6F5CD1" : "",
                };
              }}
            >
              اطلاعات کاربری
            </NavLink>
            <NavLink
              to="/app/user/security"
              className="pb-[6px]"
              style={({ isActive }) => {
                return {
                  color: isActive ? "#6F5CD1" : "",
                  borderBottom: isActive ? "1px solid #6F5CD1" : "",
                };
              }}
            >
              امنیت
            </NavLink>
            <div className="w-full absolute bottom-0">
              <Divider />
            </div>
          </div>
          <>
            {loading && <Loading />}
            <Formik
              initialValues={initialValues}
              validationSchema={updateProfileSchema}
              onSubmit={handleSubmit}
              enableReinitialize={true}
            >
              {({ handleChange, handleBlur, initialValues }) => (
                <Form className="flex flex-col gap-4">
                  <div
                    className={
                      "flex flex-row w-full justify-end gap-1 font-body-medium text-light-neutral-text-high"
                    }
                  >
                    <div className={"flex ml-4"}>
                      <FetchImage
                        imageUrl={profile.avatar}
                        onFetchSuccess={setBackgroundImage}
                      />
                      <div
                        className={
                          "flex relative cursor-pointer w-[120px] h-[120px] bg-light-neutral-background-medium" +
                          " items-center align-middle text-center group bg-center bg-no-repeat bg-contain"
                        }
                        style={{
                          clipPath: "circle(50%)",
                          backgroundImage: backgroundImage
                            ? `url(${backgroundImage})`
                            : "none",
                        }}
                        onClick={handleAvatarClick}
                      >
                        {!loadingAvatar ? (
                          !backgroundImage && (
                            <User
                              className={"w-full text-light-primary-text-rest"}
                              size={40}
                            />
                          )
                        ) : (
                          <Loading className={"relative w-full h-full"} />
                        )}
                        <div
                          className={
                            "flex w-full absolute bottom-0 bg-light-primary-background-highlight text-light-neutral-text-low h-[22px] text-center opacity-0 transition-opacity duration-300 ease-in-out group-hover:opacity-100"
                          }
                        >
                          <Image size={20} style={{ margin: "0 auto" }} />
                        </div>
                        <input
                          type="file"
                          ref={fileInputRef}
                          className="hidden"
                          max={"1m"}
                          onChange={handleFileChange}
                        />
                      </div>
                    </div>
                    <div className={"flex flex-auto p-2"}>
                      <div>
                        <p className={"font-headline-medium pt-2"}>
                          {initialValues.username}
                        </p>
                        <div className={"font-body-medium pt-2 mt-1"}>
                          <span
                            className={"text-light-neutral-text-medium ml-6"}
                          >
                            تاریخ عضویت
                          </span>
                          <span className={"text-light-neutral-text-high"}>
                            {parseTimeToPersian(profile.date_joined)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className={"flex flex-row w-full gap-6 mt-7"}>
                    <Field
                      id={"first_name"}
                      name={"first_name"}
                      component={CInput}
                      size={"lg"}
                      validation={"none"}
                      direction={"rtl"}
                      title={"نام"}
                      placeholder="برای مثال احمد"
                      onChange={handleChange("first_name")}
                      onBlur={handleBlur("first_name")}
                      className="w-1/2 font-overline-large"
                      value={initialValues.first_name}
                    />
                    <Field
                      id={"last_name"}
                      name={"last_name"}
                      component={CInput}
                      size={"lg"}
                      validation={"none"}
                      direction={"rtl"}
                      title={"نام خانوادگی"}
                      placeholder="برای مثال احمدیان"
                      onChange={handleChange("last_name")}
                      onBlur={handleBlur("last_name")}
                      className="w-1/2 font-overline-large"
                      value={initialValues.last_name}
                    />
                  </div>
                  <div className={"flex flex-row w-full gap-6"}>
                    <Field
                      id={"email"}
                      name={"email"}
                      component={CInput}
                      size={"lg"}
                      validation={"none"}
                      direction={"rtl"}
                      title={"ایمیل"}
                      placeholder="<EMAIL>"
                      // onChange={handleChange("email")}
                      onChange={(e) => setEmailValue(e.target.value)}
                      onBlur={handleBlur("email")}
                      customAction={handleChangeEmailAction}
                      customActionText={"تغییر"}
                      className="w-1/2 font-overline-large"
                      value={initialValues.email}
                      // caption="نام کاربری یک عبارت یکتا و به زبان انگلیسی است."
                    />
                    <Field
                      id={"phone_number"}
                      name={"phone_number"}
                      component={CInput}
                      size={"lg"}
                      validation={"none"}
                      direction={"rtl"}
                      title={"شماره موبایل"}
                      placeholder="برای مثال ۰۹۱۲۳۴۵۶۷۸۹"
                      onChange={handleChange("phone_number")}
                      onBlur={handleBlur("phone_number")}
                      customAction={handleChangePhoneAction}
                      customActionText={"تغییر"}
                      className="w-1/2 font-overline-large"
                      value={initialValues.phone_number}
                      // caption="نام کاربری یک عبارت یکتا و به زبان انگلیسی است."
                    />
                  </div>

                  <div className="flex flex-col w-full">
                    <label
                      htmlFor="description"
                      className="font-overline-large mb-2 inline-block"
                    >
                      توضیحات
                    </label>
                    <Field
                      id={"description"}
                      name={"description"}
                      as="textarea"
                      placeholder="هرگونه توضیحات در مورد خودتان"
                      onChange={handleChange("description")}
                      onBlur={handleBlur("description")}
                      className="w-full h-40 rounded-md p-4 font-body-large outline-none border border-light-neutral-border-medium-rest"
                      // value={initialValues.description}
                    />
                  </div>
                  <div className="font-button-large flex gap-4 [direction:ltr]">
                    <button
                      type="submit"
                      className="w-[200px] h-12 bg-light-primary-background-rest text-light-neutral-text-white rounded-lg"
                    >
                      ذخیره
                    </button>
                    <button
                      className="rounded-lg h-12 w-[100px] bg-light-neutral-background-medium"
                      onClick={() => navigate("/app/dashboard")}
                    >
                      انصراف
                    </button>
                  </div>
                </Form>
              )}
            </Formik>
            <ToastContainer />
          </>
        </div>
      </div>
      <Popup
        isOpen={isPopupOpen}
        onClose={() => setIsPopupOpen(!isPopupOpen)}
        hasButton={false}
        // submitHandler={updateNoteHandler}
        disabled={isLoading}
      >
        <SubmitEmailContent noteValue={noteValue} setNoteValue={setNoteValue} />
      </Popup>
    </>
  );
};
export default ShowProfile;
