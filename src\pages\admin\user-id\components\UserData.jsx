import { PencilSimpleLine, User } from "@phosphor-icons/react";
import Divider from "components/ui/Divider";
import { Link, useLocation } from "react-router-dom";

const UserData = () => {
  const { pathname } = useLocation();
  return (
    <div className="p-6">
      <div className="flex items-center justify-between">
        <p className="font-subtitle-large">اطلاعات کاربر</p>
        <Link
          to={`/admin/user/list/${pathname.split("/").slice(-1)[0]}/settings`}
          className="w-7 h-7 rounded-md transition hover:bg-light-neutral-background-medium cursor-pointer flex items-center justify-center"
        >
          <PencilSimpleLine />
        </Link>
      </div>

      <div className="flex items-center gap-6 my-7">
        <div className="rounded-full w-[120px] h-[120px] flex items-center justify-center bg-light-neutral-background-medium">
          <User className="text-light-primary-text-rest" size={35} />
        </div>
        <div className="">
          <p className="font-headline-medium">username.username</p>
          <div className="flex items-center gap-4">
            <span className="font-body-small text-light-success-text-rest bg-light-success-background-highlight px-3 py-1 rounded-[3px]">
              فعال
            </span>
            <p className="font-body-medium">کاربر عادی</p>
          </div>
        </div>
      </div>

      <div className="font-body-medium  leading-10">
        <div className="flex items-center justify-between">
          <p className="text-light-neutral-text-medium">تاریخ عضویت</p>
          <p>۱۲:۳۴:۵۴ - ۱۴۰۲/۱۱/۰۱</p>
        </div>
        <div className="flex items-center justify-between">
          <p className="text-light-neutral-text-medium">نام و نام خانوادگی</p>
          <p>محمد کامیار پوراحمدیان پور اصل زاده</p>
        </div>
        <div className="flex items-center justify-between">
          <p className="text-light-neutral-text-medium">شماره موبایل</p>
          <p>۰۹۱۲۳۴۵۶۷۸۹</p>
        </div>
        <div className="flex items-center justify-between">
          <p className="text-light-neutral-text-medium">ایمیل</p>
          <p><EMAIL></p>
        </div>
        <Divider className={"my-4"} />
        <div className="justify-between">
          <p className="text-light-neutral-text-medium">توضیحات پروفایل</p>
          <p className="leading-5">
            اینجا توضیحاتی که کاربر در مورد خودش نوشته را می‌توانیم ببینیم. در
            صورتی که توضیحاتی وجود نداشته باشد حتما وجود نداشته است.
          </p>
        </div>
      </div>
    </div>
  );
};

export default UserData;
