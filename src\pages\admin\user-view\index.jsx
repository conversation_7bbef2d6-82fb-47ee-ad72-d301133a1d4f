import {
  CaretR<PERSON>,
  PencilSimpleLine,
  TrashSimple,
  Wallet,
} from "@phosphor-icons/react";
import { useEffect, useState } from "react";
import { Link, useNavigate, useParams } from "react-router-dom";
import Loading from "components/ui/Loading";
import Divider from "components/ui/Divider";
import RemoveUser from "../user-list/components/RemoveUser.jsx";
import { parseTimeToPersian } from "utils/helper";
import userService from "service/api/userService.js";
import RoleBadge from "../user-list/components/RoleBadge.jsx";
import Details from "./components/Details/index.jsx";

const AdminUserView = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [data, setData] = useState({});
  const [loading, setLoading] = useState(false);
  const [rerender, setRerender] = useState(false);
  const [showRemove, setShowRemove] = useState(false);
  // const changeStatus = async () => {
  //   try {
  //     const response = await alert.status(id);
  //     setRerender((l) => !l);
  //     // console.log(error.response.data.message);
  //   } catch (error) {
  //     console.log(error);
  //   }
  // };

  const getData = async (id) => {
    setLoading(true);
    try {
      const {
        data: { data },
      } = await userService.getProfile();
      // console.log({ ...data, ...data.query.params });
      setData({
        ...data,
        id: id,
        created_at: new Date(),
        last_seen: new Date(),
      });
    } catch (error) {
      console.log(error.response.data.message);
    }
    setLoading(false);
  };
  useEffect(() => {
    getData(id);
  }, [rerender]);

  // if (loading) {
  //   return <Loading height={0} />;
  // }

  return (
    <div>
      <div className="p-6 h-full">
        <Link className="flex items-center mb-4" to="/admin/user/list">
          <CaretRight className="size-6" />
          <span className="font-subtitle-large">بازگشت به لیست کاربران</span>
        </Link>

        <div className="grid grid-cols-12 justify-between gap-6">
          <div></div>
          <div className="bg-white w-full p-6 rounded-lg col-span-6 shadow-[0px_2px_20px_0px_#0000000D]">
            <Details data={data} isFilter={false} />
          </div>

          <div className="w-full h-64 flex flex-col gap-4 rounded-lg bg-white p-6 col-span-3 shadow-[0px_2px_20px_0px_#0000000D] *:font-body-medium *:text-light-neutral-text-medium">
            {data.created_at && (
              <div className="flex justify-between">
                <span>تاریخ ایجاد</span>
                <span>{parseTimeToPersian(data.created_at)}</span>
              </div>
            )}

            {data.last_seen && (
              <div className="flex justify-between">
                <span>آخرین ورود</span>
                <span>{parseTimeToPersian(data.last_seen)}</span>
              </div>
            )}

            <div className="flex justify-between">
              <span>نقش</span>
              <RoleBadge role={data.role} />
            </div>

            <div className="py-6">
              <Divider />
            </div>

            <div className="flex gap-4">
              <button
                className="font-button-medium bg-light-neutral-background-medium rounded-lg w-full h-8 flex justify-center items-center gap-2"
                // onClick={() => changeStatus()}
              >
                <Wallet />
                <span className="font-button-medium">کیف پول</span>
              </button>
              <button
                className="font-button-medium bg-light-neutral-background-medium rounded-lg w-full h-8 flex justify-center items-center gap-2"
                onClick={() => navigate(`/admin/user/edit/step1/${id}`)}
              >
                <PencilSimpleLine />
                <span className="font-button-medium">ویرایش</span>
              </button>
              <button
                className="bg-light-neutral-background-medium rounded-lg w-full h-8 flex justify-center items-center gap-2"
                onClick={() => setShowRemove(true)}
              >
                <TrashSimple />
                <span className="font-button-medium">حذف</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {showRemove && (
        <RemoveUser
          setShowRemove={setShowRemove}
          alert_id={id}
          alert_title={data.title}
          setLoading={setLoading}
        />
      )}
      {loading && <Loading />}
    </div>
  );
};

export default AdminUserView;
