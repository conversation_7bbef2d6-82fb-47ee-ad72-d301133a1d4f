import useSearchStore from "store/searchStore.js";
import { DownloadExcelButton } from "components/DownloadExcelButton/index.jsx";
import { toPersianNumber } from "utils/helper.js";
import DropDown from "components/ui/DropDown.jsx";
import SORT_TYPE from "constants/sort-type.js";
import { CTabs } from "components/ui/CTabs.jsx";
import { ChartPie, List } from "@phosphor-icons/react";
import { Card } from "components/ui/Card.jsx";
import PropTypes from "prop-types";
import { CompareButton } from "components/CompareButton/index.jsx";

const OptionMenu = ({
  activeTab,
  changeActiveTab,
  handleQuery,
  dataCount,
  sort,
  setSort,
}) => {
  const { filters, query, isFilterListOpen } = useSearchStore();

  return (
    <Card>
      <div className={"flex flex-row justify-between w-full"}>
        {activeTab === "content" && dataCount && dataCount !== 0 ? (
          <div className={"flex flex-row justify-end items-center gap-4"}>
            <div className={"flex gap-2"}>
              <DownloadExcelButton
                platform={filters.platform}
                minimize={isFilterListOpen}
                q={handleQuery()}
              >
                دانلود خروجی اکسل
              </DownloadExcelButton>
              <CompareButton
                platform={filters.platform}
                minimize={isFilterListOpen}
                filters={filters}
                query={query}
              >
                مقایسه
              </CompareButton>
            </div>

            <div
              className={
                "flex justify-end align-middle items-center font-body-medium text-light-neutral-text-high h-full [direction:rtl] gap-4"
              }
            >
              <p
                className={
                  "font-overline-medium text-light-neutral-text-medium"
                }
              >
                {toPersianNumber(dataCount?.toString())} محتوا
              </p>
              {filters.platform !== "all" && (
                <div className="flex gap-4 z-10">
                  <DropDown
                    title="نمایش بر اساس"
                    subsets={SORT_TYPE[filters.platform].map((item) => item.fa)}
                    selected={sort.fa}
                    setSelected={(value) => {
                      setSort(
                        SORT_TYPE[filters.platform].filter(
                          (item) => item.fa === value,
                        )[0],
                      );
                    }}
                  />
                  {/*<DropDown*/}
                  {/*  title="مرتب‌سازی بر اساس"*/}
                  {/*  subsets={["نزولی", "صعودی"]}*/}
                  {/*  sort_type={sort_type}*/}
                  {/*  setSort_type={setSort_type}*/}
                  {/*/>*/}
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className={"w-fit flex flex-start"}></div>
        )}

        <div className={"w-fit flex flex-start"}>
          <CTabs
            hasIcon={true}
            tabArray={[
              { id: "stats", title: "گزارش آماری", icon: ChartPie },
              { id: "content", title: "محتوا", icon: List },
            ]}
            config={{ buttonMinWidth: "124px" }}
            activeTab={activeTab}
            onChange={changeActiveTab}
          />
        </div>
      </div>
    </Card>
  );
};

OptionMenu.propTypes = {
  activeTab: PropTypes.string.isRequired,
  changeActiveTab: PropTypes.func.isRequired,
  handleQuery: PropTypes.func.isRequired,
  dataCount: PropTypes.number,
  sort: PropTypes.object.isRequired,
  setSort: PropTypes.func.isRequired,
};

export default OptionMenu;
