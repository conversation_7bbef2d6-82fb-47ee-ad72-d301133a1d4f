import { XLogo } from "@phosphor-icons/react";
import profile from "/logo_small.png";
import { shortener, toPersianNumber } from "utils/helper";

const PioneerUsersTable = ({ data, setSelectedNode, selectedNode }) => {
  return (
    <div className="overflow-x-auto">
      <table className="min-w-full">
        <thead>
          <tr>
            <th className="text-right font-body-medium text-light-neutral-text-medium">
              منبع
            </th>
            {data?.weight && (
              <th className="text-center font-body-medium text-light-neutral-text-medium">
                وزن
              </th>
            )}
            <th className="text-center font-body-medium text-light-neutral-text-medium">
              تعداد ارتباط
            </th>
          </tr>
        </thead>
        <tbody className="font-subtitle-medium">
          {data?.length > 0 ? (
            data.slice(0, 10)?.map((row) => (
              <tr
                key={row?.node_id || Math.random()} // Fallback key if node_id is missing
                onClick={() => setSelectedNode(row)}
                className={`hover:bg-gray-50 cursor-pointer ${
                  selectedNode?.node_id === row?.node_id
                    ? "bg-light-primary-background-highlight"
                    : ""
                }`}
              >
                <td className="flex items-center gap-1 py-2 font-body-medium text-light-neutral-text-medium">
                  <div className="relative">
                    <div
                      className={"w-9 h-9 rounded-full mr-2"}
                      style={{
                        backgroundImage: `url(${
                          row?.node_avatar || profile || row?.avatar
                        }), url(/logo_small.png)`,
                        backgroundRepeat: "no-repeat",
                        backgroundSize: "cover",
                        backgroundPosition: "center center",
                      }}
                    ></div>

                    <div className="bg-black text-white p-1 rounded-full absolute top-6 right-0">
                      <XLogo size={11} />
                    </div>
                  </div>
                  <div className="grid">
                    <p className="font-subtitle-medium text-black leading-4">
                      {toPersianNumber(
                        shortener(
                          row?.node_title || row?.title || "",
                          10,
                          "rtl"
                        )
                      ) || "نامشخص"}
                    </p>
                    <p className="font-overline-medium text-light-neutral-text-medium">
                      {toPersianNumber(
                        shortener(row?.node_name || row?.key || "", 10, "rtl")
                      ) || "نامشخص"}
                    </p>
                  </div>
                </td>
                {data?.weight && (
                  <td className="font-body-medium text-light-neutral-text-medium text-center">
                    {toPersianNumber(row?.node_weight ?? 0)}
                  </td>
                )}
                <td className="font-body-medium text-light-neutral-text-medium text-center">
                  {toPersianNumber(row?.node_link || row?.count)}
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td
                colSpan={3}
                className="py-4 text-center font-body-bold-large text-light-neutral-text-medium"
              >
                موردی یافت نشد!
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
};

export default PioneerUsersTable;
