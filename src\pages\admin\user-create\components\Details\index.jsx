import Title from "./Title";
import Subtitle from "./Subtitle";
import PropTypes from "prop-types";
import { toPersianNumber } from "utils/helper.js";
const Details = ({ data }) => {
  return (
    <div className="w-full">
      <div className="flex flex-col gap-4 mb-8">
        <Title
          url={`/admin/user/edit/step1/${data.id}`}
          tooltip={"ویرایش اطلاعات شخصی"}
        >
          {data.avatar && (
            <img
              src={`${data.avatar}`}
              alt="avatar"
              className={"h-16 rounded-full"}
            />
          )}
          <p className="font-body-medium w-36">اطلاعات شخصی</p>
        </Title>
        <Subtitle subtitle={"نام کاربری"}>
          <p className="font-body-large">{data.username || "---"}</p>
        </Subtitle>
        <Subtitle subtitle={"نام و نام خانوادگی"}>
          <p className="font-body-large">
            {`${data.first_name} ${data.last_name}` || "---"}
          </p>
        </Subtitle>
        <Subtitle subtitle={"آدرس ایمیل"}>
          <p className="font-body-large">{data.email || "---"}</p>
        </Subtitle>
        <Subtitle subtitle={"موبایل"}>
          <p className="font-body-large">
            {toPersianNumber(data.phone_number) || "---"}
          </p>
        </Subtitle>

        <Subtitle subtitle={"تاریخ تولد"}>
          <p className="font-body-large">{data.birthdate || "---"}</p>
        </Subtitle>

        <Subtitle subtitle={"توضیحات"}>
          <p className="font-body-large">{data.description || "---"}</p>
        </Subtitle>
      </div>

      <div className="flex flex-col gap-4 mb-8">
        <Title
          url={`/admin/user/edit/step2/${data.id}`}
          tooltip={"ویرایش محدودیت‌ها"}
        >
          <p className="font-body-medium w-36"> محدودیت ها </p>
        </Title>
        <Subtitle subtitle="تعداد هشدار‌های قابل ایجاد">
          <div
            className={
              "font-body-small rounded-[4px] px-1 text-light-success-text-rest bg-light-success-background-highlight"
            }
          >
            {data.limitations?.alert_access
              ? toPersianNumber(`${data.limitations?.alert_access} عدد`)
              : "بدون محدودیت"}
          </div>
        </Subtitle>
        <Subtitle subtitle="محدودیت در تعداد بولتن‌ها">
          <div
            className={
              "font-body-small rounded-[4px] px-1 text-light-success-text-rest bg-light-success-background-highlight"
            }
          >
            {data.limitations?.bulletin_access
              ? toPersianNumber(`${data.limitations?.bulletin_access} عدد`)
              : "بدون محدودیت"}
          </div>
        </Subtitle>
        <Subtitle subtitle="تعداد گزارشات مقایسه‌ای">
          <div
            className={
              "font-body-small rounded-[4px] px-1 text-light-success-text-rest bg-light-success-background-highlight"
            }
          >
            {data.limitations?.comparison_access
              ? toPersianNumber(`${data.limitations?.comparison_access} عدد`)
              : "بدون محدودیت"}
          </div>
        </Subtitle>
        <Subtitle subtitle="تعداد فیلتر‌های پیش‌ساخته">
          <div
            className={
              "font-body-small rounded-[4px] px-1 text-light-success-text-rest bg-light-success-background-highlight"
            }
          >
            {data.limitations?.filter_access
              ? toPersianNumber(`${data.limitations?.filter_access} عدد`)
              : "بدون محدودیت"}
          </div>
        </Subtitle>
        <Subtitle subtitle="تعداد پرونده‌های قابل ایجاد">
          <div
            className={
              "font-body-small rounded-[4px] px-1 text-light-success-text-rest bg-light-success-background-highlight"
            }
          >
            {data.limitations?.folder_access
              ? toPersianNumber(`${data.limitations?.folder_access} عدد`)
              : "بدون محدودیت"}
          </div>
        </Subtitle>
        <Subtitle subtitle="تعداد گزارش روابط گرافی">
          <div
            className={
              "font-body-small rounded-[4px] px-1 text-light-success-text-rest bg-light-success-background-highlight"
            }
          >
            {data.limitations?.graph_access
              ? toPersianNumber(`${data.limitations?.graph_access} عدد`)
              : "بدون محدودیت"}
          </div>
        </Subtitle>
        <Subtitle subtitle="تعداد گزارشات ۳۶۰">
          <div
            className={
              "font-body-small rounded-[4px] px-1 text-light-success-text-rest bg-light-success-background-highlight"
            }
          >
            {data.limitations?.report_360_access
              ? toPersianNumber(`${data.limitations?.report_360_access} عدد`)
              : "بدون محدودیت"}
          </div>
        </Subtitle>
        <Subtitle subtitle="تعداد گزارشات افکارسنجی">
          <div
            className={
              "font-body-small rounded-[4px] px-1 text-light-success-text-rest bg-light-success-background-highlight"
            }
          >
            {data.limitations?.opinion_access
              ? toPersianNumber(`${data.limitations?.opinion_access} عدد`)
              : "بدون محدودیت"}
          </div>
        </Subtitle>
        <Subtitle subtitle="محدودیت تعداد محتوای قابل مشاهده">
          <div
            className={
              "font-body-small rounded-[4px] px-1 text-light-success-text-rest bg-light-success-background-highlight"
            }
          >
            {data.limitations?.unlimited_content_access
              ? "بدون محدودیت"
              : "دارد"}
          </div>
        </Subtitle>
      </div>

      <div className="flex flex-col gap-4 mb-8">
        <Title
          url={`/admin/user/edit/step3/${data.id}`}
          tooltip={"ویرایش گروه کاربران مجاز"}
        >
          <p className="font-body-medium w-36"> گروه کاربران مجاز </p>
        </Title>
        <div className={"grid grid-cols-4 items-center justify-around gap-2"}>
          {data.limitations && data.limitations.user_access && (
            <>
              {data.limitations.user_access.map((user) => (
                <div
                  key={user}
                  className="h-7 rounded-lg flex items-center justify-center w-full font-body-small px-2 py-[6px] max-w-[200px] text-ellipsis [direction:ltr]"
                  style={{
                    backgroundColor: "#E1E8EF40",
                    outline: "1px solid #D1D6DB",
                  }}
                  // onClick={() => handleChange(value)}
                >
                  {`@${user}`}
                </div>
              ))}
            </>
          )}
        </div>
      </div>

      {/* <TopicFiltersDetails data={data} selectFilter={selectFilter} /> */}
    </div>
  );
};

Details.propTypes = {
  data: PropTypes.object.isRequired,
};

export default Details;
