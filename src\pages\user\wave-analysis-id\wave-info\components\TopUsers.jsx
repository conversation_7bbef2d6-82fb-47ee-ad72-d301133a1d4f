import { SpinnerGap, UserSwitch } from "@phosphor-icons/react";
import Divider from "components/ui/Divider";
import { shortener, toPersianNumber } from "utils/helper";
import RadialBar from "./charts/RadialBar";
import { useNavigate } from "react-router-dom";
import { useReport360Store } from "store/report360Store";
import { useState } from "react";
import PopUp from "components/ui/PopUp";

const TopUsers = ({ fetchTopSources, loading }) => {
  const [selectedItem, setSelectedItem] = useState(null);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const setReport = useReport360Store((state) => state.setReport);
  const navigate = useNavigate();

  const handleProfileClick = (profile) => {
    try {
      const selectedProfile = {
        id: profile?.id,
        user_title: profile?.title,
        user_name: profile?.key,
        platform: "twitter",
        avatar: profile?.avatar,
      };

      setReport({
        isFromTopSource: true,
        step: 3,
        type: "profile",
        profile: selectedProfile,
      });

      navigate("/app/report-360/create");
    } catch (e) {
      console.error("Profile click error:", e);
    }
  };

  const openConfirmPopup = (item) => {
    setSelectedItem(item);
    setIsPopupOpen(true);
  };

  const closeConfirmPopup = () => {
    setIsPopupOpen(false);
    setSelectedItem(null);
  };

  const submitHandler = () => {
    if (selectedItem) {
      handleProfileClick(selectedItem);
      setIsPopupOpen(false);
    }
  };
  return (
    <>
      <div
        className="bg-light-neutral-surface-card rounded-lg p-3 h-[400px]"
        style={{
          boxShadow: "0px 2px 20px 0px #0000000D",
        }}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            <p className="font-subtitle-large">برترین کاربران</p>
          </div>
        </div>

        <div className="py-4">
          <Divider />
        </div>

        <div className="relative">
          {loading && (
            <div className="w-full h-[365px] flex justify-center items-center absolute top-0 left-0 z-50">
              <SpinnerGap size={40} className="animate-spin" />
            </div>
          )}
          <div
            className={`w-full grid grid-cols-12 gap-6 ${
              loading ? "blur-sm" : ""
            }`}
          >
            {fetchTopSources?.twitter?.length === 0 ? (
              <>
                {!loading && (
                  <div className="h-[270px] flex items-center justify-center font-subtitle-medium col-span-12">
                    داده ای برای نمایش وجود ندارد
                  </div>
                )}
              </>
            ) : (
              <>
                <div className="col-span-8 w-full flex flex-col [direction:rtl]">
                  <div className="grid grid-cols-2 *:font-body-medium *:text-light-neutral-text-medium">
                    <p>منبع</p>
                    <p className="text-center">تعداد محتوا</p>
                  </div>
                  {fetchTopSources?.twitter?.slice(0, 5)?.map((item) => (
                    <div
                      key={item.id}
                      className="grid grid-cols-2 items-center p-1 cursor-pointer rounded-lg hover:bg-light-primary-background-highlight"
                      onClick={() => openConfirmPopup(item)}
                    >
                      <div className="flex gap-2 items-center">
                        <div
                          className={"w-10 h-10 rounded-full mr-2"}
                          style={{
                            backgroundImage: `url(${item?.avatar}), url(/logo_small.png)`,
                            backgroundRepeat: "no-repeat",
                            backgroundSize: "cover",
                            backgroundPosition: "center center",
                          }}
                        ></div>

                        <div>
                          <div
                            className="font-subtitle-medium overflow-hidden whitespace-nowrap text-ellipsis max-w-[10.5rem]"
                            title={item?.title || "--"}
                          >
                            {shortener(item?.title, 15) || "--"}
                          </div>
                          <div className="font-overline-medium text-light-neutral-text-low">
                            {item?.key || "--"}@
                          </div>
                        </div>
                      </div>
                      <div className="font-body-bold-medium text-center">
                        {toPersianNumber(item?.count)}
                      </div>
                    </div>
                  ))}
                </div>

                <div className="col-span-4">
                  <RadialBar
                    data={fetchTopSources?.twitter?.map((item) => ({
                      y: item?.count,
                      username: item?.title,
                      avatar: item?.avatar,
                    }))}
                  />
                </div>
              </>
            )}
          </div>
        </div>
      </div>
      <PopUp
        isOpen={isPopupOpen}
        onClose={closeConfirmPopup}
        submitHandler={submitHandler}
        title="آیا می‌خواهید گزارش های 360 این منبع نمایش داده شود؟"
        agreeButton="بله"
        cancleButton="خیر"
        icon={<UserSwitch size={45} />}
      >
        <p className="py-5 font-body-medium">
          توجه کنید که با کلیک برروی گزینه بله به صفحه گزارشات 360 این منبع
          منتقل خواهید شد.
        </p>
      </PopUp>
    </>
  );
};

export default TopUsers;
