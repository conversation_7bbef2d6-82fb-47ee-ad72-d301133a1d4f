import { TrashSimple } from "@phosphor-icons/react";
import PropTypes from "prop-types";

const RemoveUser = ({ setShowRemove, removeHandler = () => {} }) => {
  return (
    <div className="fixed top-0 left-0 h-screen w-full bg-light-neutral-surface-backdrop flex justify-center items-center z-50">
      <button
        className="w-[560px] h-80 bg-white rounded-lg shadow-[0px_2px_20px_0px_#00000012] flex flex-col items-center justify-between px-10 py-6 outline-none border-none"
        onBlur={() => setShowRemove(false)}
        autoFocus
      >
        <TrashSimple className="size-14" />
        <span className="font-headline-medium">آیا مطمئن هستید؟</span>
        <span className="font-body-medium">
          در صورت حذف این کاربر، امکان بازیابی آن وجود ندارد
        </span>
        <div className="flex gap-6 w-full font-button-medium">
          <div
            className="bg-light-neutral-background-medium rounded-lg w-full h-10 flex items-center justify-center"
            onClick={() => setShowRemove(false)}
          >
            انصراف
          </div>
          <div
            className="text-[#BE223C] border border-light-error-text-rest rounded-lg w-full h-10 flex items-center justify-center"
            onClick={removeHandler}
          >
            حذف
          </div>
        </div>
      </button>
    </div>
  );
};

RemoveUser.propTypes = {
  setShowRemove: PropTypes.func,
  removeHandler: PropTypes.func,
};

export default RemoveUser;
