import useFetch from "../index";

class NoteLists {
  getNotes(page = 1, collection_title) {
    return useFetch.get(
      `/api/v1/interaction/note/?page=${page}&collection_title=${collection_title}`
    );
  }
  getNote(id) {
    return useFetch.get(`/api/v1/interaction/content/${id}/`);
  }
  getCollections(query = {}) {
    return useFetch.get(
      `/api/v1/interaction/collections/?${new URLSearchParams(
        query
      ).toString()}`
    );
  }
  createCollection(data) {
    return useFetch.post("/api/v1/interaction/collections/", data);
  }
  deleteCollection(id) {
    return useFetch.delete(`/api/v1/interaction/collections/${id}/`);
  }
}
export default new NoteLists();
