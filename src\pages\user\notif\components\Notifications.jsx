import { useEffect, useState } from "react";
import NotificationsCard from "./NotificationsCard";
import notification from "service/api/notification";
import queryString from "query-string";
import Paginate from "components/ui/Paginate";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";

const Notifications = () => {
  const [notifs, setNotifs] = useState({});
  const [page, setPage] = useState(1);
  useEffect(() => {
    const query = queryString.stringify({
      page,
      count: 10,
      type: "notification",
      status: "all",
    });
    notification
      .get(query)
      .then((res) => setNotifs(res.data.data))
      .catch((e) => console.log(e));
  }, []);
  const breadcrumbList = [{ title: "اطلاعیه‌ها" }];
  useBreadcrumb(breadcrumbList);
  return (
    <div className="flex-1 bg-white shadow-[0px_2px_20px_0px_#0000000D] rounded-lg px-4 py-6 relative h-full flex flex-col justify-between">
      <div className="divide-y-2">
        {notifs?.notifications?.length > 0 ? (
          notifs.notifications.map(({ title, created_at, unread, id }) => (
            <NotificationsCard
              key={id}
              title={title}
              date={created_at}
              unread={unread}
              id={id}
            />
          ))
        ) : (
          <div className="h-full flex items-center justify-center font-subtitle-medium">
            داده ای برای نمایش وجود ندارد
          </div>
        )}
      </div>
      <div className="flex justify-center">
        <Paginate
          page={page}
          setPage={setPage}
          dataCount={notifs.total_notify}
          per_page={10}
        />
      </div>
    </div>
  );
};

export default Notifications;
