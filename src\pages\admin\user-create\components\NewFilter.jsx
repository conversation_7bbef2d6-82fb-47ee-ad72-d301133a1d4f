import { useState } from "react";
import Divider from "components/ui/Divider";
import SelectPlatform from "components/FilterSelector/SelectPlatform";
import { platformFilters } from "utils/platformFilters";
import { CInput } from "components/ui/CInput";
import { TagInput } from "components/ui/TagInput";
import Drawer from "components/Drawer";
import { Sliders } from "@phosphor-icons/react";

const NewFilter = ({ alertData, setAlertData, setStatus }) => {
  const [touchedQ, setTouchedQ] = useState(false);

  const [showAdvancedSearchDrawer, setShowAdvancedSearchDrawer] =
    useState(false);

  const [andWords, setAndWords] = useState([]);
  const [orWords, setOrWords] = useState([]);
  const [notWords, setNotWords] = useState([]);

  const generateQueryString = (andArr, orArr, notArr) => {
    let and = andArr.filter(Boolean).join(" * ");
    let or = orArr.filter(Boolean).join(" + ");
    or = !!or && orArr.length > 2 ? `( ${or} )` : or;
    let not = notArr
      .filter(Boolean)
      .map((item) => `!${item}`)
      .join(" * ");
    return [and, or, not].filter(Boolean).join(" * ");
  };

  const setPlatform = (media) => {
    setAlertData((l) => {
      const copy = JSON.parse(JSON.stringify(l));
      copy.platform = {};
      media.forEach((item) => {
        copy.platform[item] = Object.hasOwn(l.platform, item)
          ? l.platform[item]
          : platformFilters[item];
      });
      return copy;
    });
  };

  return (
    <div>
      <div className="mt-12 mb-6">
        <Divider />
      </div>

      <div className="flex flex-col gap-4">
        <span className="font-body-medium">بستر مورد بررسی:</span>
        <p className="font-body-small">
          می‌توانید هم‌زمان چند بستر گوناگون را انتخاب کنید و در صورتی که قصد
          دارید همه بسترها را انتخاب کنید٬ کافی است گزینه «همه» را انتخاب کنید{" "}
        </p>
        <SelectPlatform
          handleChange={setPlatform}
          initialValue={Object.keys(alertData?.platform)}
        />
      </div>

      <div className="my-6">
        <Divider />
      </div>

      <div className="flex flex-col gap-2">
        <label className="font-overline-large w-20" htmlFor="keywords">
          کلمات کلیدی
        </label>
        <CInput
          id={"q2"}
          name={"q2"}
          inset={true}
          readOnly={true}
          size={"md"}
          validation={"none"}
          direction={"rtl"}
          className={"flex-1 mt-2"}
          onFocus={() => setTouchedQ(false)}
          onBlur={() => setTouchedQ(true)}
          value={alertData.q}
          field={{}}
          onChange={(e) => {
            setAlertData((l) => {
              const copy = JSON.parse(JSON.stringify(l));
              copy.q = e.target.value;
              return copy;
            });
          }}
          form={{ errors: [], touched: [] }}
          customAction={() => setShowAdvancedSearchDrawer(true)}
          customActionText={<Sliders size={20} />}
        ></CInput>
        <span className="font-body-small text-light-neutral-text-medium">
          از عملوندهای منطقی AND و OR و NOT استفاده کنید.
        </span>
        {touchedQ && alertData.q.length < 2 && (
          <span className="font-body-small text-light-error-text-rest">
            طول کلمات کلیدی حداقل باید ۲ کاراکتر باشد
          </span>
        )}
      </div>

      <div className="flex flex-row-reverse mt-12">
        <button
          type="submit"
          className={`w-[200px] h-10 ${
            alertData.q?.length >= 2 && Object.values(alertData.platform).length
              ? "bg-light-primary-background-rest text-light-neutral-text-white"
              : "bg-light-neutral-background-disable text-light-neutral-text-disable"
          } rounded-lg`}
          onClick={() => {
            if (
              alertData.q?.length >= 2 &&
              Object.values(alertData.platform).length
            ) {
              setStatus(4);
            }
          }}
        >
          ادامه
        </button>
        <button
          className="w-[100px] h-10 flex justify-center items-center text-light-primary-text-rest font-button-medium"
          onClick={() => {
            setStatus(2);
          }}
        >
          مرحله قبل
        </button>
      </div>
      <div className={!showAdvancedSearchDrawer ? "hidden" : "block"}>
        <Drawer setShowMore={setShowAdvancedSearchDrawer}>
          <div className={"container flex flex-col"}>
            <div className={"flex flex-container flex-col w-full mt-3"}>
              <h6
                className={
                  "block w-full font-overline-large text-light-neutral-text-high"
                }
              >
                دقیقا شامل این کلمات باشد
              </h6>
              <TagInput
                id={"andWords"}
                name={"andWords"}
                inset={true}
                size={"lg"}
                validation={"none"}
                direction={"rtl"}
                placeholder={"کلمه مورد نظر را بنویسید"}
                caption={"بعد از نوشتن هر کلمه از Enter استفاده کنید"}
                className={"flex-1 mt-1"}
                onChange={(e) => {
                  setAndWords(e);
                  const newQuery = generateQueryString(e, orWords, notWords);
                  setAlertData((l) => {
                    const copy = JSON.parse(JSON.stringify(l));
                    copy.q = newQuery;
                    return copy;
                  });
                }}
                field={{}}
                form={{ errors: [], touched: [] }}
              />
            </div>
            <Divider className={"mt-3"} />
            <div className={"flex flex-container flex-col w-full mt-6"}>
              <h6
                className={
                  "block w-full font-overline-large text-light-neutral-text-high"
                }
              >
                می‌تواند حداقل یکی از این کلمات را داشته باشد
              </h6>
              <TagInput
                id={"orWords"}
                name={"orWords"}
                inset={true}
                size={"lg"}
                validation={"none"}
                direction={"rtl"}
                placeholder={"کلمه مورد نظر را بنویسید"}
                caption={"بعد از نوشتن هر کلمه از Enter استفاده کنید"}
                className={"flex-1 mt-1"}
                onChange={(e) => {
                  setOrWords(e);
                  const newQuery = generateQueryString(andWords, e, notWords);
                  setAlertData((l) => {
                    const copy = JSON.parse(JSON.stringify(l));
                    copy.q = newQuery;
                    return copy;
                  });
                }}
                field={{}}
                form={{ errors: [], touched: [] }}
              />
            </div>
            <Divider className={"mt-3"} />
            <div className={"flex flex-container flex-col w-full mt-6"}>
              <h6
                className={
                  "block w-full font-overline-large text-light-neutral-text-high"
                }
              >
                هیچ کدام از این کلمات نباشد
              </h6>
              <TagInput
                id={"notWords"}
                name={"notWords"}
                inset={true}
                size={"lg"}
                validation={"none"}
                direction={"rtl"}
                placeholder={"کلمه مورد نظر را بنویسید"}
                caption={"بعد از نوشتن هر کلمه از Enter استفاده کنید"}
                className={"flex-1 mt-1"}
                onChange={(e) => {
                  setNotWords(e);
                  const newQuery = generateQueryString(andWords, orWords, e);
                  setAlertData((l) => {
                    const copy = JSON.parse(JSON.stringify(l));
                    copy.q = newQuery;
                    return copy;
                  });
                }}
                field={{}}
                form={{ errors: [], touched: [] }}
              />
            </div>
            <Divider className={"mt-3"} />
            <div
              className={
                "flex flex-container flex-col w-full mt-6 rounded bg-light-primary-background-highlight p-4"
              }
            >
              <h6
                className={
                  "block w-full font-overline-large text-light-neutral-text-high"
                }
              >
                کوئری ایجاد شده
              </h6>
              <CInput
                id={"q2"}
                name={"q2"}
                inset={true}
                readOnly={true}
                size={"md"}
                validation={"none"}
                direction={"rtl"}
                className={"flex-1 mt-2"}
                value={alertData.q}
                field={{}}
                // onChange={handleChange("q")}
                form={{ errors: [], touched: [] }}
                customAction={() => {}}
                // customActionText={<PencilSimpleLine size={20} />}
              ></CInput>
            </div>
          </div>
        </Drawer>
      </div>
    </div>
  );
};

export default NewFilter;
