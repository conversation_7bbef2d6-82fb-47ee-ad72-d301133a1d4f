import { CaretLeft } from "@phosphor-icons/react";
import { Link } from "react-router-dom";
import { parseTimeToPersian } from "utils/helper";

const NotificationsCard = ({
  title = "هدیه ویژه به مناسبت سربازی وحید سرمدی",
  text = "به مناسبت سربازی وحید سرمدی تا ۷۰ درصد تخفیف روی همه پلن‌ها اعمال می‌شود. به مناسبت سربازی وحید سرمدی تا ۷۰ درصد تخفیف روی همه پلن‌ها اعمال می‌شود. این تخفیف تا ۲۳ اردیبهشت فعال است.",
  date = "",
  id,
  unread,
}) => {
  return (
    <div className="flex gap-2 py-4">
      <div
        className={`shrink-0 mt-[5px] size-3 rounded-full ${
          unread ? "bg-light-warning-background-rest" : "bg-white"
        } bg-light-warning-background-rest`}
      ></div>
      <div className="flex flex-col gap-2 flex-1">
        <div className="flex justify-between">
          <span className="font-subtitle-medium">{title}</span>
          <span className="font-overline-medium">
            {parseTimeToPersian(date)}
          </span>
        </div>
        {/* <div className="font-body-medium">{text}</div> */}
        <div className="flex flex-row-reverse">
          <Link
            to={`/app/notif/Notifications/${id}`}
            className="font-button-medium text-light-primary-text-rest flex gap-2"
          >
            <span>بیشتر</span>
            <CaretLeft />
          </Link>
        </div>
      </div>
    </div>
  );
};

export default NotificationsCard;
