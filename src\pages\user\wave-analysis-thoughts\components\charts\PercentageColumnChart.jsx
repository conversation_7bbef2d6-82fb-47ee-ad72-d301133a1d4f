import { useRef } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { toPersianNumber } from "utils/helper";

const PercentageColumnChart = ({
  data = [],
  categories = [],
  tooltipFormatter,
}) => {
  const chartRef = useRef(null);
  const options = {
    chart: {
      type: "column",
    },
    title: {
      text: null,
    },
    subtitle: {
      text: null,
    },
    xAxis: {
      categories,
    },
    yAxis: {
      min: 10,
      title: {
        text: null,
      },
      labels: {
        formatter: function () {
          return toPersianNumber(this.value);
        },
      },
      gridLineDashStyle: "dash",
    },
    credits: {
      enabled: false,
    },
    tooltip: {
      useHTML: true,
      formatter: tooltipFormatter,
    },
    // tooltip: {
    //   formatter: function () {
    //     return tooltipFormat
    //       ? tooltipFormat
    //       : `<b>${this.key}</b><br/>
    //           ${(this.y / this.total) * 100}%<br/>
    //         `;
    //   },
    // },

    plotOptions: {
      column: {
        borderRadius: 10,
        stacking: "percent",
        dataLabels: {
          enabled: true,
          format: "",
        },
      },
    },
    legend: {
      align: "right",
      rtl: true,
      itemStyle: {
        color: "#333333",
      },
    },
    series: data,
  };

  return (
    <HighchartsReact highcharts={Highcharts} options={options} ref={chartRef} />
  );
};

export default PercentageColumnChart;
