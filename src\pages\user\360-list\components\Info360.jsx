const Info360 = () => {
  return (
    <div>
      <h3 className="font-subtitle-medium py-4">گزارش ۳۶۰ چیست؟</h3>
      <p className="font-body-medium text-justify">
        در گزارش ۳۶۰، هدف، بررسی اطلاعات مرتبط با یک موجودیت خاص می‌باشد به
        گونه‌ای که یک موجودیت می‌تواند یک پروفایل کاربری در شبکه‌های اجتماعی و
        پیام‌رسان‌ها، نام شخص، نام مکان، نام سازمان یا گروه باشد.
      </p>

      <h3 className="font-subtitle-medium py-4">گزارش ۳۶۰ منبع</h3>
      <p className="font-body-medium text-justify">
        در این گزارش، یک اکانت از شبکه‌های اجتماعی یا پیام‌رسان‌ها را جست‌وجو
        کرده و گزارش‌های آماری و تحلیلی مختلف مربوط به حساب کاربری مورد نظر را
        مشاهده خواهید کرد.
      </p>

      <h3 className="font-subtitle-medium py-4">گزارش ۳۶۰ موجودیت</h3>
      <p className="font-body-medium text-justify">
        در این گزارش، نام یک شخص، سازمان، حزب، گروه، تیم ورزشی و ... را جست‌وجو
        کرده، پس از انتخاب موجودیت مورد نظر از لیست موجودیت‌های پیشنهاد شده،
        گزارش‌های آماری و تحلیلی مختلف مربوط به آن را مشاهده خواهید کرد.
      </p>

      <h3 className="font-subtitle-medium py-4">گزارش ۳۶۰ مکان‌محور</h3>
      <p className="font-body-medium text-justify">
        در این نوع گزارش، از نقشه نمایش داده شده نام یک کشور، استان، شهر یا
        شهرستان را انتخاب کرده و گزارش‌های آماری و تحلیلی مربوط به آن را مشاهده
        کنید.
      </p>
    </div>
  );
};

export default Info360;
