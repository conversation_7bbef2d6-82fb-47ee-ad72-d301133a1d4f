// import { useRef, useEffect, useState, memo } from "react";
// import ForceGraph2D from "react-force-graph-2d";
// import MyData from "./graph_output.json";
// import { toP<PERSON>ianNumber } from "utils/helper";

// const MyGraph = ({ graphData }) => {
//   console.log(graphData);

//   const [dimensions, setDimensions] = useState({ width: 300, height: 300 });
//   const [filteredNodesCount, setFilteredNodesCount] = useState(
//     Math.floor(MyData?.nodes?.length / 2) || 0
//   );
//   const containerRef = useRef();
//   const fgRef = useRef();

//   useEffect(() => {
//     const handleResize = () => {
//       if (containerRef.current) {
//         const { width, height } = containerRef.current.getBoundingClientRect();
//         setDimensions({ width, height });
//       }
//     };
//     handleResize();
//     window.addEventListener("resize", handleResize);
//     return () => window.removeEventListener("resize", handleResize);
//   }, []);

//   // Filter nodes and links based on slider value
//   const filteredNodes = MyData.nodes.slice(0, filteredNodesCount);
//   const filteredNodeIds = new Set(filteredNodes.map((node) => node.id));

//   const filteredLinks = MyData.links.filter(
//     (link) =>
//       filteredNodeIds.has(
//         typeof link.source === "object" ? link.source.id : link.source
//       ) &&
//       filteredNodeIds.has(
//         typeof link.target === "object" ? link.target.id : link.target
//       )
//   );

//   return (
//     <>
//       <div className="grid mb-5">
//         <label htmlFor="slider" className="font-body-medium">
//           تعداد گره‌ها: {toPersianNumber(filteredNodesCount)}
//         </label>
//         <input
//           id="slider"
//           type="range"
//           min="0"
//           max={MyData?.nodes?.length}
//           value={filteredNodesCount}
//           onChange={(e) => setFilteredNodesCount(Number(e.target.value))}
//         />
//       </div>

//       <div ref={containerRef} style={{ width: "100%", height: "500px" }}>
//         <ForceGraph2D
//           ref={fgRef}
//           graphData={{ nodes: filteredNodes, links: filteredLinks }}
//           nodeId="id"
//           dagLevelDistance={10}
//           nodeAutoColorBy="group"
//           nodeRelSize={10}
//           width={dimensions.width}
//           height={dimensions.height}
//           maxZoom={10}
//           minZoom={0.1}
//           nodeLabel="id"
//           linkColor={(node) => {
//             return `rgba(${node?.source?.color?.[0] || 0},${
//               node?.source?.color?.[1] || 0
//             },${node?.source?.color?.[2] || 0},0.2)`;
//           }}
//           nodeColor={(node) => {
//             return `rgba(${node?.color?.[0] || 0},${node?.color?.[1] || 0},${
//               node?.color?.[2] || 0
//             },1)`;
//           }}
//           // onRenderFramePost={(ctx, globalScale) => {
//           //   // 1) retrieve the final node positions from the graph
//           //   // const { nodes } = { nodes: filteredNodes, links: filteredLinks };

//           //   if (!filteredNodes || filteredNodes.length === 0) {
//           //     ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
//           //     return;
//           //   }

//           //   // 2) group nodes by 'group'
//           //   const groups = {};
//           //   filteredNodes.forEach((node) => {
//           //     const g = node.group || "Ungrouped";
//           //     if (!groups[g]) groups[g] = [];
//           //     // each node’s x,y is in the current canvas coordinate space
//           //     groups[g].push([node.x, node.y, node.color]);
//           //   });
//           //   // 3) draw a "bubble" (convex hull) around each group
//           //   Object.entries(groups).forEach(([groupId, coords]) => {
//           //     if (coords.length < 3) return; // need >= 3 points for a hull
//           //     const hull = polygonHull(coords);
//           //     if (!hull) return;

//           //     // draw the hull shape
//           //     ctx.beginPath();
//           //     ctx.moveTo(hull[0][0], hull[0][1]);
//           //     for (let i = 1; i < hull.length; i++) {
//           //       ctx.lineTo(hull[i][0], hull[i][1]);
//           //     }
//           //     ctx.closePath();
//           //     // fill + stroke
//           //     ctx.fillStyle = `rgba(${coords[2][2][0]}, ${coords[2][2][1]}, ${coords[2][2][2]}, 0.08)`; // translucent fill
//           //     ctx.strokeStyle = `rgba(${coords[2][2][0]}, ${coords[2][2][1]}, ${coords[2][2][2]}, 0.2)`; // slightly darker outline
//           //     ctx.lineWidth = 1;
//           //     ctx.fill();
//           //     ctx.stroke();
//           //   });
//           // }}
//         />
//       </div>
//     </>
//   );
// };

// export default memo(MyGraph);

// import { useRef, useEffect, useState, memo } from "react";
// import ForceGraph2D from "react-force-graph-2d";
// import { toPersianNumber } from "utils/helper";
// const legendConfig = [
//   {
//     id: 6,
//     title: "حامیان انقلاب اسلامی",
//     dataKey: "osoolgera",
//     color: "#47DBB6",
//   },
//   {
//     id: 2,
//     title: "فعالین مدنی غرب‌گرا",
//     dataKey: "eslahtalab",
//     color: "#6F5CD1",
//   },
//   {
//     id: 4,
//     title: "سلطنت‌طلب",
//     dataKey: "saltanat",
//     color: "#DEC402",
//   },
//   {
//     id: 3,
//     title: "برانداز",
//     dataKey: "barandaz",
//     color: "#E0526A",
//   },
//   {
//     id: 5,
//     title: "عدالت‌خواه",
//     dataKey: "edalatkhah",
//     color: "#4EA1FA",
//   },
//   {
//     id: 1,
//     title: "منافقین",
//     dataKey: "monafegh",
//     color: "#dc6414",
//   },
//   {
//     id: 0,
//     title: "احمدی‌نژادی",
//     dataKey: "ahmadinezhad",
//     color: "#918c8c",
//   },
//   {
//     id: 7,
//     title: "ری‌استارتی",
//     dataKey: "restart",
//     color: "#A45E2C",
//   },
//   {
//     id: 8,
//     title: "ناشناخته",
//     dataKey: "unknown",
//     color: "#000",
//   },
// ];

// function hexToRgb(hex) {
//   const cleanHex = hex.replace("#", "");
//   const r = parseInt(cleanHex.substring(0, 2), 16);
//   const g = parseInt(cleanHex.substring(2, 4), 16);
//   const b = parseInt(cleanHex.substring(4, 6), 16);
//   return [r, g, b];
// }

// const MyGraph = ({ graphData }) => {
//   const [dimensions, setDimensions] = useState({ width: 300, height: 300 });
//   const [filteredNodesCount, setFilteredNodesCount] = useState(
//     Math.floor(graphData?.nodes?.length / 2) || 0
//   );
//   const containerRef = useRef();
//   const fgRef = useRef();
//   useEffect(() => {
//     const handleResize = () => {
//       if (containerRef.current) {
//         const { width, height } = containerRef.current.getBoundingClientRect();
//         setDimensions({ width, height });
//       }
//     };
//     handleResize();
//     window.addEventListener("resize", handleResize);
//     return () => window.removeEventListener("resize", handleResize);
//   }, []);
//   // Transform nodes to match expected format (id, group, etc.)
//   const transformedNodes =
//     graphData?.nodes?.map((node) => ({
//       id: node.node_name, // Use node_name as the unique identifier
//       group: node.node_political_category || "unknown", // Use node_political_category for grouping, default to "unknown"
//       follower_count: node.node_follower_count || 0, // Optional: for potential sizing/scaling
//       color: getNodeColor(node.node_political_category), // Assign color based on category
//     })) || [];
//   // Transform links to reference node_name
//   const transformedLinks =
//     graphData?.links?.map((link) => ({
//       source: link.source,
//       target: link.target,
//     })) || [];
//   // Filter nodes and links based on slider value
//   const filteredNodes = transformedNodes.slice(0, filteredNodesCount);
//   const filteredNodeIds = new Set(filteredNodes.map((node) => node.id));
//   const filteredLinks = transformedLinks.filter(
//     (link) =>
//       filteredNodeIds.has(link.source) && filteredNodeIds.has(link.target)
//   );
//   // Helper function to assign colors based on node_political_category
//   function getNodeColor(category) {
//     const config = legendConfig.find(
//       (item) => item.dataKey === (category || "unknown")
//     );
//     return config ? hexToRgb(config.color) : hexToRgb("#000"); // Default to black RGB
//   }

//   return (
//     <>
//       <div className="grid mb-5">
//         <label htmlFor="slider" className="font-body-medium">
//           تعداد گره‌ها: {toPersianNumber(filteredNodesCount)}
//         </label>
//         <input
//           id="slider"
//           type="range"
//           min="0"
//           max={graphData?.nodes?.length || 0}
//           value={filteredNodesCount}
//           onChange={(e) => setFilteredNodesCount(Number(e.target.value))}
//         />
//       </div>

//       <div ref={containerRef} style={{ width: "100%", height: "500px" }}>
//         <ForceGraph2D
//           ref={fgRef}
//           graphData={{ nodes: filteredNodes, links: filteredLinks }}
//           nodeId="id"
//           dagLevelDistance={10}
//           nodeAutoColorBy="group"
//           nodeRelSize={10}
//           width={dimensions.width}
//           height={dimensions.height}
//           maxZoom={10}
//           minZoom={0.1}
//           nodeLabel="id"
//           linkColor={(link) => {
//             const sourceNode = filteredNodes.find((n) => n.id === link.source);
//             return sourceNode
//               ? `rgba(${sourceNode.color[0]},${sourceNode.color[1]},${sourceNode.color[2]},0.2)`
//               : "rgba(0,0,0,0.2)";
//           }}
//           nodeColor={(node) =>
//             `rgba(${node.color[0]},${node.color[1]},${node.color[2]},1)`
//           }
//         />
//       </div>
//     </>
//   );
// };

// export default memo(MyGraph);

import { useRef, useEffect, useState, memo, useMemo } from "react";
import ForceGraph2D from "react-force-graph-2d";
import { toPersianNumber } from "utils/helper";
import CardLoading from "components/ui/CardLoading";

const legendConfig = [
  {
    id: 6,
    title: "حامیان انقلاب اسلامی",
    dataKey: "osoolgera",
    color: "#47DBB6",
  },
  {
    id: 2,
    title: "فعالین مدنی غرب‌گرا",
    dataKey: "eslahtalab",
    color: "#6F5CD1",
  },
  {
    id: 4,
    title: "سلطنت‌طلب",
    dataKey: "saltanat",
    color: "#DEC402",
  },
  {
    id: 3,
    title: "برانداز",
    dataKey: "barandaz",
    color: "#E0526A",
  },
  {
    id: 5,
    title: "عدالت‌خواه",
    dataKey: "edalatkhah",
    color: "#4EA1FA",
  },
  {
    id: 1,
    title: "منافقین",
    dataKey: "monafegh",
    color: "#dc6414",
  },
  {
    id: 0,
    title: "احمدی‌نژادی",
    dataKey: "ahmadinezhad",
    color: "#918c8c",
  },
  {
    id: 7,
    title: "ری‌استارتی",
    dataKey: "restart",
    color: "#A45E2C",
  },
  {
    id: 8,
    title: "ناشناخته",
    dataKey: "unknown",
    color: "#000000",
  },
];

function hexToRgb(hex) {
  const cleanHex = hex.replace("#", "");
  const r = parseInt(cleanHex.substring(0, 2), 16);
  const g = parseInt(cleanHex.substring(2, 4), 16);
  const b = parseInt(cleanHex.substring(4, 6), 16);
  return [r, g, b];
}

const NetworkGraphCategoryChart = ({ graphData, graphDataLoading }) => {
  const [dimensions, setDimensions] = useState({ width: 300, height: 300 });
  const [filteredNodesCount, setFilteredNodesCount] = useState(0); // Initialize to 0, will update when graphData loads
  const containerRef = useRef();
  const fgRef = useRef();

  // Set initial slider value to half of nodes when graphData loads
  useEffect(() => {
    if (graphData?.nodes?.length) {
      setFilteredNodesCount(Math.floor(graphData.nodes.length));
    }
  }, [graphData]);

  // Handle container resizing
  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current) {
        const { width, height } = containerRef.current.getBoundingClientRect();
        setDimensions({ width, height });
      }
    };
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Transform nodes to match expected format (id, group, etc.)
  const transformedNodes = useMemo(() => {
    return graphData?.nodes
      ?.sort((a, b) => a.follower_count < b.follower_count)
      .map((node) => ({
        id: node.node_name, // Use node_name as the unique identifier
        group: node.node_political_category || "unknown", // Use node_political_category for grouping, default to "unknown"
        follower_count: node.node_follower_count || 0, // Optional: for potential sizing/scaling
        color: getNodeColor(node.node_political_category), // Assign color based on category
      }));
  }, [graphData]);

  // Transform links to reference node_name
  const transformedLinks = graphData?.links?.map((link) => ({
    source: link.source,
    target: link.target,
  }));

  // Show preloader if graphData is not loaded
  if (!graphData || !graphData.nodes || !graphData.nodes.length) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          fontSize: "1.5rem",
          color: "#333",
          width: "100%",
          height: "500px",
        }}
        ref={containerRef}
        className="font-body-bold-large"
      >
        {!graphDataLoading ? (
          <p className="text-center pt-6">داده ای یافت نشد!</p>
        ) : (
          <CardLoading />
        )}
      </div>
    );
  }

  // Filter nodes and links based on slider value
  const filteredNodes = transformedNodes.slice(0, filteredNodesCount);
  const filteredNodeIds = new Set(filteredNodes.map((node) => node.id));
  const filteredLinks = transformedLinks.filter(
    (link) =>
      filteredNodeIds.has(link.source) && filteredNodeIds.has(link.target)
  );

  // Helper function to assign colors based on node_political_category
  function getNodeColor(category) {
    const config = legendConfig.find(
      (item) => item.dataKey === (category || "unknown")
    );
    return config ? hexToRgb(config.color) : hexToRgb("#000"); // Default to black RGB
  }
  return (
    <>
      <div className="grid mb-5">
        <label htmlFor="slider" className="font-body-medium">
          تعداد گره‌ها: {toPersianNumber(filteredNodesCount)}
        </label>
        <input
          id="slider"
          type="range"
          min="0"
          max={graphData.nodes.length}
          value={filteredNodesCount}
          onChange={(e) => setFilteredNodesCount(Number(e.target.value))}
        />
      </div>

      <div ref={containerRef} style={{ width: "100%", height: "500px" }}>
        <ForceGraph2D
          ref={fgRef}
          graphData={{ nodes: filteredNodes, links: filteredLinks }}
          nodeId="id"
          dagLevelDistance={10}
          nodeAutoColorBy="group"
          nodeRelSize={10}
          width={dimensions.width}
          height={dimensions.height}
          maxZoom={10}
          minZoom={0.1}
          nodeLabel="id"
          linkColor={(link) => {
            const sourceNode = filteredNodes.find((n) => n.id === link.source);
            return sourceNode
              ? `rgba(${sourceNode.color[0]},${sourceNode.color[1]},${sourceNode.color[2]},0.2)`
              : "rgba(0,0,0,0.2)";
          }}
          nodeColor={(node) =>
            `rgba(${node.color[0]},${node.color[1]},${node.color[2]},1)`
          }
        />
      </div>
    </>
  );
};

export default memo(NetworkGraphCategoryChart);
