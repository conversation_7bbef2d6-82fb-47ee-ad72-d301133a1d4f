import React from "react";
import { useCompareStore } from "store/compareStore.js";
import CotentCard from "./ContentCard.jsx";
import { countAvg } from "../../../utils.js";

const ContentInstagram = () => {
  const { content } = useCompareStore((state) => state.compare);
  return (
    <>
      <CotentCard
        titleOffset={-12.5 * content.length}
        title="مجموع محتوا"
        data={countAvg(content, "post_count")}
      />
      <CotentCard
        titleOffset={-12.5 * content.length}
        title="تصویر"
        data={countAvg(content, "photo_count")}
      />
      <CotentCard
        titleOffset={-12.5 * content.length}
        title="ویدئو"
        data={countAvg(content, "video_count")}
      />
      <CotentCard
        titleOffset={-12.5 * content.length}
        title="اسلاید"
        data={countAvg(content, "slide_count")}
      />
    </>
  );
};

export default ContentInstagram;
