import Title from "./Title";
import Subtitle from "./Subtitle";
import PropTypes from "prop-types";
import { toPersianNumber } from "utils/helper.js";
import React, { useState } from "react";
import { CInput } from "components/ui/CInput.jsx";
import { CButton } from "components/ui/CButton.jsx";
import { Pencil } from "@phosphor-icons/react";

const Details = ({ data }) => {
  const [personalInfoEditMode, setPersonalInfoEditMode] = useState(false);
  const [limitationEditMode, setLimitationEditMode] = useState(false);
  const [userGroupEditMode, setUserGroupEditMode] = useState(false);

  const [unlimitedContentAccessChange, setUnlimitedContentAccessChange] =
    useState(false);

  return (
    <div className="w-full">
      <div className="flex flex-col gap-4 mb-8">
        <Title
          onAction={() => setPersonalInfoEditMode(true)}
          tooltip={"ویرایش اطلاعات شخصی"}
        >
          {data.avatar && (
            <img
              src={`${data.avatar}`}
              alt="avatar"
              className={"h-16 rounded-full"}
            />
          )}
          <p className="font-body-medium w-36">اطلاعات شخصی</p>
        </Title>
        <Subtitle subtitle={"نام کاربری"}>
          {personalInfoEditMode ? (
            <CInput
              disabled={true}
              inset={true}
              size={"sm"}
              value={data.username}
              className={"!m-0"}
              headingIcon={<Pencil size={14} />}
            />
          ) : (
            <p className="font-body-large">{data.username || "---"}</p>
          )}
        </Subtitle>
        <Subtitle subtitle={"نام و نام خانوادگی"}>
          {personalInfoEditMode ? (
            <CInput
              size={"sm"}
              headingIcon={<Pencil size={14} />}
              value={`${data.first_name} ${data.last_name}`}
              className={"!m-0"}
            />
          ) : (
            <p className="font-body-large">
              {`${data.first_name} ${data.last_name}` || "---"}
            </p>
          )}
        </Subtitle>
        <Subtitle subtitle={"آدرس ایمیل"}>
          {personalInfoEditMode ? (
            <CInput
              disabled={true}
              inset={true}
              size={"sm"}
              value={data.email}
              className={"!m-0"}
              headingIcon={<Pencil size={14} />}
            />
          ) : (
            <p className="font-body-large">{data.email || "---"}</p>
          )}
        </Subtitle>
        <Subtitle subtitle={"موبایل"}>
          {personalInfoEditMode ? (
            <CInput
              disabled={true}
              inset={true}
              size={"sm"}
              value={data.phone_number}
              className={"!m-0"}
              headingIcon={<Pencil size={14} />}
            />
          ) : (
            <p className="font-body-large">
              {toPersianNumber(data.phone_number) || "---"}
            </p>
          )}
        </Subtitle>

        <Subtitle subtitle={"تاریخ تولد"}>
          {personalInfoEditMode ? (
            <CInput
              size={"sm"}
              value={data.birthdate}
              className={"!m-0"}
              headingIcon={<Pencil size={14} />}
            />
          ) : (
            <p className="font-body-large">{data.birthdate || "---"}</p>
          )}
        </Subtitle>

        <Subtitle subtitle={"توضیحات"}>
          {personalInfoEditMode ? (
            <CInput
              size={"sm"}
              value={data.description}
              className={"!m-0"}
              headingIcon={<Pencil size={14} />}
            />
          ) : (
            <p className="font-body-large">{data.description || "---"}</p>
          )}
        </Subtitle>
        {personalInfoEditMode && (
          <div className={"flex justify-center items-center gap-2"}>
            <CButton
              size={"sm"}
              onClick={() => {
                setPersonalInfoEditMode(false);
              }}
            >
              ثبت تغییرات
            </CButton>
            <CButton
              size={"sm"}
              className={"!w-[200px]"}
              role={"neutral"}
              onClick={() => {
                setPersonalInfoEditMode(false);
              }}
            >
              انصراف
            </CButton>
          </div>
        )}
      </div>

      <div className="flex flex-col gap-4 mb-8">
        <Title
          onAction={() => setLimitationEditMode(true)}
          tooltip={"ویرایش محدودیت‌ها"}
        >
          <p className="font-body-medium w-36"> محدودیت ها </p>
        </Title>
        <Subtitle subtitle="تعداد هشدار‌های قابل ایجاد">
          {limitationEditMode ? (
            <CInput
              size={"sm"}
              value={data.limitations?.alert_access}
              className={"!m-0 !w-[100px]"}
            />
          ) : (
            <div
              className={
                "font-body-small rounded-[4px] px-1 text-light-success-text-rest bg-light-success-background-highlight"
              }
            >
              {data.limitations?.alert_access
                ? toPersianNumber(`${data.limitations?.alert_access} عدد`)
                : "بدون محدودیت"}
            </div>
          )}
        </Subtitle>
        <Subtitle subtitle="محدودیت در تعداد بولتن‌ها">
          {limitationEditMode ? (
            <CInput
              size={"sm"}
              value={data.limitations?.bulletin_access}
              className={"!m-0 !w-[100px]"}
            />
          ) : (
            <div
              className={
                "font-body-small rounded-[4px] px-1 text-light-success-text-rest bg-light-success-background-highlight"
              }
            >
              {data.limitations?.bulletin_access
                ? toPersianNumber(`${data.limitations?.bulletin_access} عدد`)
                : "بدون محدودیت"}
            </div>
          )}
        </Subtitle>
        <Subtitle subtitle="تعداد گزارشات مقایسه‌ای">
          {limitationEditMode ? (
            <CInput
              size={"sm"}
              value={data.limitations?.comparison_access}
              className={"!m-0 !w-[100px]"}
            />
          ) : (
            <div
              className={
                "font-body-small rounded-[4px] px-1 text-light-success-text-rest bg-light-success-background-highlight"
              }
            >
              {data.limitations?.comparison_access
                ? toPersianNumber(`${data.limitations?.comparison_access} عدد`)
                : "بدون محدودیت"}
            </div>
          )}
        </Subtitle>
        <Subtitle subtitle="تعداد فیلتر‌های پیش‌ساخته">
          {limitationEditMode ? (
            <CInput
              size={"sm"}
              value={data.limitations?.filter_access}
              className={"!m-0 !w-[100px]"}
            />
          ) : (
            <div
              className={
                "font-body-small rounded-[4px] px-1 text-light-success-text-rest bg-light-success-background-highlight"
              }
            >
              {data.limitations?.filter_access
                ? toPersianNumber(`${data.limitations?.filter_access} عدد`)
                : "بدون محدودیت"}
            </div>
          )}
        </Subtitle>
        <Subtitle subtitle="تعداد پرونده‌های قابل ایجاد">
          {limitationEditMode ? (
            <CInput
              size={"sm"}
              value={data.limitations?.folder_access}
              className={"!m-0 !w-[100px]"}
            />
          ) : (
            <div
              className={
                "font-body-small rounded-[4px] px-1 text-light-success-text-rest bg-light-success-background-highlight"
              }
            >
              {data.limitations?.folder_access
                ? toPersianNumber(`${data.limitations?.folder_access} عدد`)
                : "بدون محدودیت"}
            </div>
          )}
        </Subtitle>
        <Subtitle subtitle="تعداد گزارش روابط گرافی">
          {limitationEditMode ? (
            <CInput
              size={"sm"}
              value={data.limitations?.graph_access}
              className={"!m-0 !w-[100px]"}
            />
          ) : (
            <div
              className={
                "font-body-small rounded-[4px] px-1 text-light-success-text-rest bg-light-success-background-highlight"
              }
            >
              {data.limitations?.graph_access
                ? toPersianNumber(`${data.limitations?.graph_access} عدد`)
                : "بدون محدودیت"}
            </div>
          )}
        </Subtitle>
        <Subtitle subtitle="تعداد گزارشات ۳۶۰">
          {limitationEditMode ? (
            <CInput
              size={"sm"}
              value={data.limitations?.report_360_access}
              className={"!m-0 !w-[100px]"}
            />
          ) : (
            <div
              className={
                "font-body-small rounded-[4px] px-1 text-light-success-text-rest bg-light-success-background-highlight"
              }
            >
              {data.limitations?.report_360_access
                ? toPersianNumber(`${data.limitations?.report_360_access} عدد`)
                : "بدون محدودیت"}
            </div>
          )}
        </Subtitle>
        <Subtitle subtitle="تعداد گزارشات افکارسنجی">
          {limitationEditMode ? (
            <CInput
              size={"sm"}
              value={data.limitations?.opinion_access}
              className={"!m-0 !w-[100px]"}
            />
          ) : (
            <div
              className={
                "font-body-small rounded-[4px] px-1 text-light-success-text-rest bg-light-success-background-highlight"
              }
            >
              {data.limitations?.opinion_access
                ? toPersianNumber(`${data.limitations?.opinion_access} عدد`)
                : "بدون محدودیت"}
            </div>
          )}
        </Subtitle>
        <Subtitle subtitle="محدودیت تعداد محتوای قابل مشاهده">
          {limitationEditMode ? (
            <>
              <label className="flex items-center font-body-medium text-light-neutral-text-medium gap-1">
                <input
                  type={"radio"}
                  name={"unlimited_content_access"}
                  value={data.unlimited_content_access}
                  onChange={() => setUnlimitedContentAccessChange(true)}
                />
                با محدودیت
              </label>
              <label className="flex items-center font-body-medium text-light-neutral-text-medium gap-1">
                <input
                  type={"radio"}
                  name={"unlimited_content_access"}
                  value={data.unlimited_content_access}
                  onChange={() => setUnlimitedContentAccessChange(false)}
                  selected={data.unlimited_content_access}
                />
                بدون محدودیت
              </label>
            </>
          ) : (
            // <CInput
            //   size={"sm"}
            //   value={data.limitations?.unlimited_content_access}
            //   className={"!m-0 !w-[100px]"}
            //   onChange={() => unlimitedContentAccessChange(true)}
            // />
            <div
              className={
                "font-body-small rounded-[4px] px-1 text-light-success-text-rest bg-light-success-background-highlight"
              }
            >
              {data.limitations?.unlimited_content_access
                ? "بدون محدودیت"
                : "دارد"}
            </div>
          )}
        </Subtitle>
        {limitationEditMode && (
          <div className={"flex justify-center items-center gap-2"}>
            <CButton
              size={"sm"}
              onClick={() => {
                setLimitationEditMode(false);
              }}
            >
              ثبت تغییرات
            </CButton>
            <CButton
              size={"sm"}
              className={"!w-[200px]"}
              role={"neutral"}
              onClick={() => {
                setLimitationEditMode(false);
              }}
            >
              انصراف
            </CButton>
          </div>
        )}
      </div>

      <div className="flex flex-col gap-4 mb-8">
        <Title
          url={`/admin/user/edit/step3/${data.id}`}
          tooltip={"ویرایش گروه کاربران مجاز"}
        >
          <p className="font-body-medium w-36"> گروه کاربران مجاز </p>
        </Title>
        <div className={"grid grid-cols-4 items-center justify-around gap-2"}>
          {data.limitations && data.limitations.user_access && (
            <>
              {data.limitations.user_access.map((user) => (
                <div
                  key={user}
                  className="h-7 rounded-lg flex items-center justify-center w-full font-body-small px-2 py-[6px] max-w-[200px] text-ellipsis [direction:ltr]"
                  style={{
                    backgroundColor: "#E1E8EF40",
                    outline: "1px solid #D1D6DB",
                  }}
                  // onClick={() => handleChange(value)}
                >
                  {`@${user}`}
                </div>
              ))}
            </>
          )}
        </div>
      </div>

      {/* <TopicFiltersDetails data={data} selectFilter={selectFilter} /> */}
    </div>
  );
};

Details.propTypes = {
  data: PropTypes.object.isRequired,
};

export default Details;
