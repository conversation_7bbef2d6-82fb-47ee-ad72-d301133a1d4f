import { CaretDown, Check } from "@phosphor-icons/react";
import clsx from "clsx";
import { useState } from "react";

const DropDown = ({
  subsets = [],
  handleChange,
  title,
  value,
  disabled = false,
}) => {
  const [open, setOpen] = useState(false);
  const [selected, setSelected] = useState(value || subsets[0]);

  return (
    <button
      onBlur={() => setOpen(false)}
      className="relative w-full h-10"
      type="button"
      disabled={disabled}
    >
      <div
        className={clsx(
          "flex justify-between items-center px-3 h-10 rounded-lg border w-full",
          open
            ? "border-light-neutral-border-medium-rest"
            : "border-light-neutral-border-low-rest",
          disabled
            ? "bg-light-neutral-background-low border-light-neutral-border-disable"
            : "bg-light-neutral-background-medium",
        )}
        onClick={() => setOpen((l) => !l)}
      >
        {title && (
          <span className="font-overline-medium text-light-neutral-text-medium">
            {title}
          </span>
        )}
        <div className="flex gap-2 items-center">
          <span className="font-body-medium">{selected.text}</span>
          <CaretDown
            style={{
              transform: open ? "rotate(180deg)" : "rotate(0deg)",
              transition: "all 0.5s",
            }}
          />
        </div>
      </div>
      {open && (
        <div className="absolute z-20 top-12 left-0 bg-white rounded-lg shadow-[0px_4px_20px_0px_#0000001A] p-4">
          <div className="font-body-small flex flex-col gap-1">
            {subsets?.map((item) => (
              <div
                key={item.value}
                className="flex items-center gap-4 p-2 rounded-lg hover:bg-light-neutral-surface-highlight"
                onClick={() => {
                  setOpen(false);
                  setSelected(item);
                  handleChange(item);
                }}
              >
                <span>{item.text}</span>
                {selected.value === item.value && <Check />}
              </div>
            ))}
          </div>
        </div>
      )}
    </button>
  );
};

export default DropDown;
