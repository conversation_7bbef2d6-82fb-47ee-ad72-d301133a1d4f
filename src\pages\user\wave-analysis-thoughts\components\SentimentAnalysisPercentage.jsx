import { memo } from "react";
import Doughnut from "components/Charts/Doughnut";
import { toPersianNumber } from "utils/helper";

const transformSentimentData = (sentimentAnalysis) => {
  const { total, twitter } = sentimentAnalysis;
  if (!total || total === 0) {
    return [];
  }
  const sentimentMap = [
    { key: "positive", name: "مثبت", color: "#1CB0A5" },
    { key: "neutral", name: "خنثی", color: "#E1E8EFCC" },
    { key: "negative", name: "منفی", color: "#E0526A" },
  ];
  const data = sentimentMap.map((sentiment) => ({
    name: sentiment.name,
    y: 0,
    color: sentiment.color, // Include color for consistency
  }));
  twitter?.forEach(({ key, count }) => {
    const sentimentIndex = sentimentMap.findIndex((s) => s.key === key);
    if (sentimentIndex !== -1) {
      data[sentimentIndex].y = (count / total) * 100;
    }
  });
  // Sort data by percentage (y) in descending order
  return data.sort((a, b) => b.y - a.y);
};

const SentimentAnalysisPercentage = ({ sentimentAnalysis }) => {
  const data = transformSentimentData(sentimentAnalysis);
  if (!data?.length > 0) {
    return (
      <div className="bg-light-neutral-surface-card rounded-[8px] p-6 h-full">
        <p className="font-subtitle-large pb-4">تحلیل احساسات</p>

        <div className="h-[240px] flex items-center justify-between mx-auto">
          <p className="py-1.5 text-center font-body-bold-large text-light-neutral-text-medium flex items-center justify-between mx-auto">
            داده ای یافت نشد
          </p>
        </div>
      </div>
    );
  }
  return (
    <div className="bg-light-neutral-surface-card rounded-[8px] p-6">
      <p className="font-subtitle-large pb-4">تحلیل احساسات</p>
      <Doughnut
        colLayout={true}
        name="sentiment"
        size={"80%"}
        height={400}
        data={data}
        legendFormatter={function () {
          return `<div style="font-family: iranyekan, sans-serif; display: grid; grid-template-columns: 1fr auto; gap: 16px; width: 100%; padding: 4px; align-items: center;">
                    <span style="font-size: 16px; color: #333;">
                      ${toPersianNumber(Number(this.y).toFixed(1))}%
                    </span>
                    <div style="display: flex; gap: 8px; align-items: center;">
                        <span style="font-size: 14px; color: #555;">
                          ${this.name}
                        </span>
                        <div style="background-color: ${
                          this.color
                        }; border-radius: 50%; width: 12px; height: 12px;"></div>
                    </div>
                </div>`;
        }}
        tooltipFormatter={function () {
          return `<div style="display: flex; flex-direction: column; gap: 8px; text-align: center; font-family: iranyekan;">
                    <p>${this.key}</p>
                    <p> ${toPersianNumber(Number(this.y).toFixed(1))} %</p>
                  </div>`;
        }}
        colors={["#1CB0A5", "#E1E8EFCC", "#E0526A"]}
      />
    </div>
  );
};

export default memo(SentimentAnalysisPercentage);
