import { CInput } from "components/ui/CInput";
import { CButton } from "components/ui/CButton.jsx";
import { CirclesThreePlus, Info, Warning } from "@phosphor-icons/react";
import { useState } from "react";

const StepOne = ({ setStep }) => {
  const [collapseCard, setCollapseCard] = useState(false);

  return (
    <div className="grid gap-4 bg-light-neutral-surface-card rounded-[8px] p-6">
      <div className="flex items-start justify-start gap-2 p-4 rounded-lg bg-light-warning-background-highlight ">
        <Warning className="text-light-warning-background-rest" size={20} />
        <p className="font-body-medium">
          ثبت نام کاربری و شماره موبایل اجباری است.
        </p>
      </div>

      <div className="">
        <CInput
          placeholder="نام کاربری وارد کنید"
          title="نام کاربری"
          caption="نام کاربری یک عبارت یکتا به زبان انگلیسی است."
        />
      </div>
      <div className="">
        <CInput
          placeholder="برای مثال ۰۹۱۲۳۴۵۶۷۸۹"
          title="شماره موبایل"
          type="number"
        />
      </div>

      <div className="flex justify-end">
        <div
          onClick={() => setCollapseCard(!collapseCard)}
          className="font-button-medium text-light-primary-background-rest rounded-md transition hover:bg-light-primary-background-highlight cursor-pointer flex items-center p-2 w-fit  select-none"
        >
          <CirclesThreePlus size={20} />
          <p className="pr-2">اطلاعات {collapseCard ? "کمتر" : "بیشتر"}</p>
        </div>
      </div>
      {collapseCard && (
        <>
          <div className="flex items-start justify-start gap-2 p-4 rounded-lg bg-light-neutral-surface-highlight">
            <Info className="text-light-neutral-text-medium" size={20} />
            <p className="font-body-medium">ثبت سایر اطلاعات اختیاری است</p>
          </div>

          <div className="flex gap-6">
            <div className="w-full">
              <CInput placeholder="نام " title="نام کاربر" />
            </div>
            <div className="w-full">
              <CInput placeholder="نام خانوادگی کاربر" title="نام خانوادگی" />
            </div>
          </div>
          <div className="">
            <CInput placeholder="<EMAIL>" title="ایمیل" />
          </div>

          <div>
            <label htmlFor="" className="font-overline-large">
              توضیحات
            </label>
            <textarea
              className="w-full h-40 rounded-md p-1.5 font-body-large outline-none border border-light-neutral-border-medium-rest mt-1"
              placeholder="هرگونه توضیحات در مورد کاربر"
            ></textarea>
          </div>
        </>
      )}

      <CButton onClick={() => setStep(2)}>مرحله بعد</CButton>
    </div>
  );
};

// StepOne.propTypes = {
//   data: PropTypes.object,
//   setData: PropTypes.func,
//   setStep: PropTypes.func,
// };

export default StepOne;
