import { useState } from "react";
import { useBreadcrumb } from "hooks/useBreadcrumb";
import GraphPreview from "./components/GraphSettings/components/GraphPreview";
import EdgeSettings from "./components/GraphSettings/components/EdgeSettings";

const RelationDetect = () => {
  const breadcrumbList = [
    { title: "شناسایی روابط", link: "/app/relation-detect/list" },
    { title: "پیش نمایش" },
  ];
  useBreadcrumb(breadcrumbList);
  const [displayBadges, setDisplayBadges] = useState(false);

  return (
    <div className="grid grid-cols-12 gap-4 mx-auto px-5 max-h-screen ">
      <div className="col-span-6 overflow-y-scroll max-h-screen no-scrollbar -mx-1">
        <EdgeSettings setDisplayBadges={setDisplayBadges} />
      </div>
      <div className="col-span-6">
        <GraphPreview
          displayBadges={displayBadges}
          setDisplayBadges={setDisplayBadges}
        />
      </div>
    </div>
  );
};

export default RelationDetect;
