import { useBulletinStore } from "store/bulletinStore.js";
import PropTypes from "prop-types";
import ToggleInput from "components/ui/ToggleInput.jsx";

const Toggle = ({
  name = "",
  title = "",
  description = "",
  onChange = () => {},
}) => {
  const setBulletin = useBulletinStore((state) => state.setBulletin);
  const { metadata } = useBulletinStore((state) => state.bulletin);

  const handleChange = (value) => {
    const help = JSON.parse(JSON.stringify(metadata));
    help[name] = value || false;
    setBulletin({ metadata: { ...help } });
    onChange(value);
  };

  return (
    <ToggleInput
      title={title}
      description={description}
      onChange={handleChange}
      isOn={metadata?.[name]}
    />
  );
};

Toggle.propTypes = {
  name: PropTypes.string,
  title: PropTypes.string,
  description: PropTypes.string,
  onChange: PropTypes.func,
};

export default Toggle;
