import Steps from "components/ui/Steps";
import { useBreadcrumb } from "hooks/useBreadcrumb";
import StepOne from "./components/step-1";
import { useState } from "react";
import StepTwo from "./components/step-2";
import StepThree from "./components/step-3";
import poster from "/opinion-mining/poster.png";
import opinion from "service/api/opinion";
import { useLocation } from "react-router-dom";

const OpinionMiningEdit = () => {
  const { state } = useLocation();
  const [step, setStep] = useState(1);
  const breadcrumbList = [
    { title: "گزارش افکارسنجی", link: "/app/opinion-mining/list" },
    { title: "ویرایش گزارش" },
    { title: state?.title },
  ];
  const [opinionMiningData, setOpinionMiningData] = useState({
    type: "",
    title: "",
    description: "",
    opinion_platform: "twitter",
    opinion_type: "customized",
    hashtags: [],
    query: {
      platform: {
        twitter: {
          sentiment: ["positive", "negative", "neutral"],
          adv: ["adv", "non-adv"],
        },
      },
    },
  });
  useBreadcrumb(breadcrumbList);

  // const getReport = async (id) => {
  //   try {
  //     const res = await opinion.getOpinionReport();
  //     setReports(res?.data?.data?.user);
  //   } catch (error) {
  //     notification.error(
  //       "خطایی رخ داد",
  //       <WarningCircle size={25} className="text-light-error-text-rest" />
  //     );
  //   }
  // };
  // useEffect(() => {
  //   getReport();
  // }, []);
  const components = {
    1: (
      <StepOne
        opinionMiningData={state}
        setOpinionMiningData={setOpinionMiningData}
        setStep={setStep}
      />
    ),
    2: (
      <StepTwo
        opinionMiningData={state}
        setOpinionMiningData={setOpinionMiningData}
        setStep={setStep}
      />
    ),
    3: (
      <StepThree
        opinionMiningData={state}
        setOpinionMiningData={setOpinionMiningData}
        setStep={setStep}
      />
    ),
  };
  return (
    <div className="container mx-auto flex items-start justify-between px-5">
      <div className="w-[800px]">
        <div>
          <div className="rounded-md bg-light-neutral-surface-card p-4">
            <p className="font-body-large pb-3">فرآیند ایجاد گزارش</p>
            <Steps
              step={step}
              texts={["ثبت عنوان و توضیحات", "انتخاب فیلتر", "مشاهده نتایج"]}
              stepCounts={3}
            />
          </div>
        </div>
        <div className="mt-4">{components[step]}</div>
      </div>

      <img src={poster} className="w-[450px]" />
    </div>
  );
};

export default OpinionMiningEdit;
