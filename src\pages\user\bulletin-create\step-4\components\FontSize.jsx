import { useEffect, useState } from "react";
import { Minus, Plus } from "@phosphor-icons/react";
import { toPersianNumber } from "utils/helper";

const FontSize = ({ initialValue, handleChange }) => {
  const [size, setSize] = useState(initialValue.font_size);
  useEffect(() => handleChange({ font_size: size }), [size]);
  return (
    <div className="flex gap-2 items-center">
      <div className="p-1" onClick={() => setSize((l) => (l < 28 ? l + 1 : l))}>
        <Plus size={16} />
      </div>
      <div className="p-1">
        <span className="font-body-medium select-none">
          {toPersianNumber(size)}
        </span>
      </div>
      <div className="p-1" onClick={() => setSize((l) => (l > 9 ? l - 1 : l))}>
        <Minus size={16} />
      </div>
    </div>
  );
};

export default FontSize;
