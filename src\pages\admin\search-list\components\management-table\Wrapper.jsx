import { CaretDown, CaretUp, Info } from "@phosphor-icons/react";
import ToolTip from "components/ui/ToolTip";

const Wrapper = ({ children, setFilter, filter }) => {
  return (
    <div className="py-4 flex flex-col gap-4">
      <ul className="font-overline-medium text-light-neutral-text-medium grid grid-cols-6 select-none">
        <li>عبارات جستجو</li>
        <li>نام کاربر</li>
        <li>بستر</li>
        <li
          className="flex items-center gap-1 cursor-pointer"
          onClick={() => {
            setFilter({
              ...filter,
              sort_rules: [
                {
                  field: "login_at",
                  direction: filter.sort_rules?.[0]?.direction,
                },
                {
                  field: "register_at",
                  direction:
                    filter.sort_rules?.[1]?.direction === "asc"
                      ? "desc"
                      : "asc",
                },
              ],
            });
          }}
        >
          <p>تاریخ انجام</p>
          <div className="flex flex-col">
            <CaretUp
              weight={
                filter?.sort_rules[1]?.direction == "asc" ? "fill" : "regular"
              }
            />
            <CaretDown
              weight={
                filter?.sort_rules[1]?.direction == "desc" ? "fill" : "regular"
              }
            />
          </div>
        </li>
        <li>اقدامات</li>
      </ul>
      {children}
    </div>
  );
};

export default Wrapper;
