import { useEffect, useState } from "react";
import { generateStatsQuery } from "utils/stats";
import Divider from "components/ui/Divider";
import Doughnut from "components/Charts/Doughnut";
import { fixPercentToShow } from "utils/helper";
import advanceSearch from "service/api/advanceSearch";
import { gender } from "utils/selectIcon";
import { SpinnerGap } from "@phosphor-icons/react";
import { useMeasure } from "react-use";
import PropTypes from "prop-types";
import PLATFORMS from "constants/platforms.js";
import useSearchStore from "store/searchStore.js";

const Gender = ({ showDataLabels }) => {
  const { filters, query } = useSearchStore();

  const [data, setData] = useState({});
  const [loading, setLoading] = useState(false);
  const [ref, { width }] = useMeasure();
  const from = parseInt(new Date(filters?.date?.from).getTime() / 1000);
  const to = parseInt(new Date(filters?.date?.to).getTime() / 1000);
  const percent = (value, all) => {
    const sum = Object.values(all).reduce((prev, cur) => prev + cur, 0);
    return (value / sum) * 100;
  };
  const getData = async () => {
    setLoading(true);
    try {
      const res = await advanceSearch.search({
        q: generateStatsQuery(
          filters.platform,
          "gender",
          {
            from,
            to,
            q: query,
            fields: ["male", "female", "org"],
            count: 10,
          },
          filters,
        ),
      });
      setData(JSON.parse(res.data.data).result.data);
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  useEffect(() => {
    if (
      filters.platform === PLATFORMS.TELEGRAM ||
      filters.platform === PLATFORMS.NEWS
    )
      return;
    getData();
  }, [filters, query]);

  if (
    filters.platform === PLATFORMS.TELEGRAM ||
    filters.platform === PLATFORMS.NEWS
  )
    return <></>;

  return (
    <div className="flex flex-col gap-4" ref={ref}>
      <p className="font-subtitle-large text-right">دسته‌بندی جنسیت</p>
      <Divider />
      {loading ? (
        <div className="w-full h-[365px] flex justify-center items-center">
          <SpinnerGap size={40} className="animate-spin" />
        </div>
      ) : Object.values(data).filter(Boolean).length === 0 ? (
        <div className="h-full flex items-center justify-center font-subtitle-medium">
          داده ای برای نمایش وجود ندارد
        </div>
      ) : (
        <Doughnut
          showDataLabels={showDataLabels}
          colLayout
          name="gender"
          height={365}
          data={[
            {
              name: "زن",
              y: +percent(data.female, data).toFixed(2),
            },
            {
              name: "مرد",
              y: +percent(data.male, data).toFixed(2),
            },
            {
              name: "نامشخص",
              y: +percent(data.org, data).toFixed(2),
            },
          ]}
          legendFormatter={function () {
            return `<div dir="rtl" style="display:grid;grid-template-columns:1fr 1fr;font-family:iranyekan;width:${
              width - 50
            }px;padding:2px;">
<span style="font-size:16px">
${fixPercentToShow(this.y / 100)}
</span>
<div style="justify-self:right">
<div style="display:flex;gap:4px">
<span style="color:${this.color};font-size:14px">${this.name}</span>
<img src=${gender[this.name]} alt="icon"/>
</div>
</div>
</div>`;
          }}
          tooltipFormatter={function () {
            return `<div style="display:flex;flex-direction:column;gap:8px;text-align:center;font-family:iranyekan"><div>${
              this.key
            }</div><div>${fixPercentToShow(this.y / 100)}</div></div>`;
          }}
          colors={["#E052B8", "#1C60B0", "#00000080"]}
        />
      )}
    </div>
  );
};

Gender.propTypes = {
  platform: PropTypes.string,
  req_data: PropTypes.object,
  query: PropTypes.string,
  showDataLabels: PropTypes.bool,
};

export default Gender;
