import { HandCoins, PencilSimpleLine } from "@phosphor-icons/react";
import CardListItem from "./CardListItem";
import { Link, useLocation } from "react-router-dom";

const UserWallet = () => {
  const { pathname } = useLocation();
  return (
    <>
      <div className="p-6">
        <div className="flex items-center justify-between">
          <p className="font-subtitle-large">کیف پول کاربر</p>
          <Link
            to={`/admin/user/list/${
              pathname.split("/").slice(-1)[0]
            }/planWallet`}
            className="w-7 h-7 rounded-md transition hover:bg-light-neutral-background-medium cursor-pointer flex items-center justify-center"
          >
            <PencilSimpleLine />
          </Link>
        </div>

        <div className="flex items-center justify-between my-3">
          <p className="font-body-medium text-light-neutral-text-medium">
            اعتبار کیف پول کاربر:
          </p>
          <div className="flex items-center gap-2">
            <p className="font-headline-medium">۱۲٬۷۵۰٬۰۰۰</p>
            <span className="font-body-small text-light-neutral-text-medium">
              تومان
            </span>
          </div>
        </div>

        <CardListItem icon={HandCoins} title="تاریخچه تراکنش‌های مالی" />
      </div>
    </>
  );
};

export default UserWallet;
