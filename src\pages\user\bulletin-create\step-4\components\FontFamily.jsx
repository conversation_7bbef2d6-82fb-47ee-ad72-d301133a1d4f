import { useState } from "react";
import { CaretDown } from "@phosphor-icons/react";
import PropTypes from "prop-types";

const FontFamily = ({ initialValue, handleChange }) => {
  const options = [
    { name: "ایران یکان", value: "fa_yekan" },
    { name: "نازنین", value: "fa_nazanin" },
    { name: "زر", value: "fa_zar" },
  ];
  const [selectedFont, setSelectedFont] = useState(() => {
    const selectedOption = options.find(option => option.value === initialValue?.font);
    return selectedOption ? selectedOption.name : "ایران یکان";
  });
  
  const [isOpen, setIsOpen] = useState(false);
  
  

  const handleOptionClick = (font) => {
    setSelectedFont(font.name);
    handleChange({ font: font.value });
    setIsOpen(false);
  };

  return (
    <div className="relative pt-2">
      <div
        className="w-24 bg-transparent cursor-pointer flex items-center justify-between"
        onClick={() => setIsOpen(!isOpen)}
        style={{ fontFamily: "iranyekan" }}
      >
        <span className="mr-1">{selectedFont}</span>
        <CaretDown className="text-black-500" />
      </div>
      {isOpen && (
        <ul className="absolute z-10 w-32 bg-white border border-gray-300 rounded-lg mt-4 shadow-md">
          {options.map((font, i) => (
            <li
              key={i}
              className={`p-2 cursor-pointer hover:bg-[#ededf3] flex items-center justify-between ${
                selectedFont === font?.name ? "font-bold text-[#432FA7]" : ""
              }`}
              style={{ fontFamily: "iranyekan" }}
              onClick={() => handleOptionClick(font)}
            >
              {font?.name}
              {selectedFont === font?.name && (
                <CaretDown className="text-[#432FA7]" />
              )}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

FontFamily.propTypes = {
  initialValue: PropTypes.shape({
    font: PropTypes.string,
  }),
  handleChange: PropTypes.func.isRequired,
};

export default FontFamily;
