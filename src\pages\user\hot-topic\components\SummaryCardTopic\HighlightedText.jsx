import PropTypes from "prop-types";
import DOMPurify from "dompurify";

const HighlightedText = ({ description, query }) => {
  // const parseQueryString = (queryString) => {
  //   const andWords = [];
  //   const orWords = [];
  //   const notWords = [];
  //
  //   const parts = queryString.split("*").map((part) => part.trim());
  //
  //   parts.forEach((part) => {
  //     if (part.startsWith("(") && part.endsWith(")")) {
  //       orWords.push(
  //         ...part
  //           .slice(1, -1)
  //           .split("+")
  //           .map((word) => word.trim())
  //       );
  //     } else if (part.includes("+")) {
  //       orWords.push(...part.split("+").map((word) => word.trim()));
  //     } else if (part.startsWith("!")) {
  //       notWords.push(
  //         ...part
  //           .split("!")
  //           .filter(Boolean)
  //           .map((word) => word.trim())
  //       );
  //     } else {
  //       andWords.push(part.trim());
  //     }
  //   });
  //
  //   return {
  //     andWords,
  //     orWords,
  //     notWords,
  //   };
  // };

  const parseQueryString = (queryString) => {
    const andWords = [];
    const orWords = [];
    const notWords = [];

    // Quick guard for empty or undefined strings
    if (!queryString) {
      return { andWords, orWords, notWords };
    }

    // First, let's trim the entire query and normalize spacing a bit
    let normalized = queryString.trim();

    // ----------------------------
    // 1) Extract NOT words first
    //    We look for " NOT someWord" or "NOT someWord"
    //    and remove them from the main string
    // ----------------------------
    // This regex will match "NOT <something>" capturing <something> in group 1
    // We'll repeatedly apply it to remove all NOT words from the string.
    const notRegex = /\bNOT\s+(\S+)\b/g;
    let match;
    while ((match = notRegex.exec(normalized)) !== null) {
      const word = match[1].replace(/[()]/g, ""); // remove parentheses if accidentally captured
      notWords.push(word.trim());
    }
    // Remove all " NOT word" occurrences from normalized
    normalized = normalized.replace(notRegex, "").trim();

    // ----------------------------
    // 2) Split the remaining string by AND
    //    Each chunk is either:
    //      - a single pand word (e.g. "word1")
    //      - a parenthetical group with OR (e.g. "(word3 OR word4)")
    // ----------------------------
    const andChunks = normalized
      .split(/\bAND\b/)
      .map((c) => c.trim())
      .filter(Boolean);

    andChunks.forEach((chunk) => {
      // If it starts with "(" and ends with ")", treat it as an OR group
      if (chunk.startsWith("(") && chunk.endsWith(")")) {
        // Remove parentheses
        const insideParens = chunk.slice(1, -1).trim();
        // Split by OR
        orWords.push(...insideParens.split(/\bOR\b/).map((c) => c.trim()));
        console.log(orWords);
      }
      // If it contains an OR but no parentheses, e.g. "word3 OR word4"
      else if (/\bOR\b/.test(chunk)) {
        orWords.push(...chunk.split(/\bOR\b/).map((c) => c.trim()));
      }
      // Otherwise, treat it as a pand word
      else {
        // might have leftover parentheses, so remove them
        andWords.push(chunk.replace(/[()]/g, "").trim());
      }
    });

    return {
      andWords,
      orWords,
      notWords,
    };
  };

  if (!description || !query) return description;

  const { andWords, orWords, notWords } = parseQueryString(query);

  const escapeRegExp = (str) => str.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
  const allWords = [...andWords, ...orWords, ...notWords]
    .map(escapeRegExp)
    .join("|");

  const regex = new RegExp(`(${allWords})`, "gi");

  const sanitizedText = DOMPurify.sanitize(description, {
    USE_PROFILES: { html: false },
  });

  const maxLength = 220; 

  const text =
    sanitizedText.length > maxLength
      ? sanitizedText.slice(0, maxLength) + "..."
      : sanitizedText;

  const parts = text.split(regex);

  return (
    <p>
      {parts.map((part, index) => {
        const lowerPart = part.toLowerCase();

        if (andWords.some((word) => word.toLowerCase() === lowerPart)) {
          return (
            <span key={index} style={{ backgroundColor: "#f1c40f" }}>
              {part}
            </span>
          );
        } else if (orWords.some((word) => word.toLowerCase() === lowerPart)) {
          return (
            <span key={index} style={{ backgroundColor: "#f1c40f" }}>
              {part}
            </span>
          );
        } else if (notWords.some((word) => word.toLowerCase() === lowerPart)) {
          return (
            <span
              key={index}
              style={{
                backgroundColor: "lightcoral",
                textDecoration: "line-through",
              }}
            >
              {part}
            </span>
          );
        } else {
          return part;
        }
      })}
    </p>
  );
};

HighlightedText.propTypes = {
  description: PropTypes.string.isRequired,
  query: PropTypes.string.isRequired,
};

export default HighlightedText;
