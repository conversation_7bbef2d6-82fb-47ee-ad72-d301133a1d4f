import { useRef } from "react";
import Fields from "./components/Fields.jsx";
import Info from "../charts/Info.jsx";
import { useCompareStore } from "src/store/compareStore";
import Category from "../charts/Category.jsx";
import Content from "../charts/Content.jsx";
import RepeatWords from "../charts/RepeatWords.jsx";
import RepeatHashtags from "../charts/RepeatHashtag.jsx";
import ReleaseChartContainer from "../charts/ReleaseProcess.jsx";
import Sentiment from "../charts/Sentiment.jsx";
import Header from "pages/user/compare-create/components/step-two/Header.jsx";
import { useReactToPrint } from "react-to-print";
import PropTypes from "prop-types";
import Entities from "../charts/entities.jsx";
import StatisticalContent from "../charts/StatisticalContent.jsx";

const ProfileCompare = ({ isEdit = false }) => {
  const { platform } = useCompareStore((state) => state.compare);
  const contentRef = useRef();
  const pageStyle = `
  @page {
    size: A3 landscape;
    margin: 0mm 5mm;
   }
  body {
    direction: rtl !important;
  }
  -webkit-print-color-adjust: exact !important; 
  color-adjust: exact !important; 
  print-color-adjust: exact !important;
`;
  const reactToPrintFn = useReactToPrint({ contentRef, pageStyle });

  return (
    <div className={"p-6 pt-0 flex flex-col gap-2"} ref={contentRef}>
      <div id="topic-compare-header">
        <Header handlePrint={reactToPrintFn} isEdit={isEdit} />
      </div>
      <div className="flex flex-col gap-2">
        <Fields />
        {platform !== "news" && <Info />}
        {platform === "news" ? (
          <>
            <div className="flex gap-2 items-center w-full">
              <div className="w-2/3">
                <ReleaseChartContainer compareType={"profile"} />
              </div>
              <div className="w-1/3">
                <StatisticalContent
                  compareType={"profile"}
                  id={
                    platform === "news"
                      ? `content-chart-${platform}`
                      : `content-chart`
                  }
                />
              </div>
            </div>
          </>
        ) : (
          <>
            <ReleaseChartContainer compareType={"profile"} />
            <Entities compareType={"profile"} tabName="person" />
            <div>
              <Content compareType={"profile"} />
            </div>
          </>
        )}
        {platform === "news" && (
          <Entities compareType={"profile"} tabName="person" />
        )}
        {/* <RepeatWords type={"text"} compareType={"profile"} /> */}
        {platform !== "news" && (
          <div
            id={
              platform === "news"
                ? `hashtags-chart-${platform}`
                : `hashtags-chart`
            }
          >
            <RepeatHashtags type={"hashtag"} compareType={"profile"} />
          </div>
        )}
        {platform !== "news" && <Sentiment compareType={"profile"} />}

        <div
          id={platform === "news" ? "category-chart-news" : "category-chart"}
        >
          <Category compareType={"profile"} />
        </div>
        <Entities compareType={"profile"} tabName="organ" />
      </div>
    </div>
  );
};

ProfileCompare.propTypes = {
  isEdit: PropTypes.bool,
};

export default ProfileCompare;
