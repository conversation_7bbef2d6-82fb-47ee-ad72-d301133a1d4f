import { Calendar } from "@phosphor-icons/react";
import PropTypes from "prop-types";
import RangeDatePicker from "components/ui/RangeDatePicker.jsx";

const DateFilter = ({ handleDateChange, selectedDateRange }) => {
  return (
    <div className="flex gap-4 font-overline-large">
      <span className="flex gap-2 items-center cursor-pointer w-32">
        <span className="action-icon text-left">
          <Calendar size={18} color="#00000059" />
        </span>
        بازه زمانی جستجو
      </span>
      <div className="flex items-center w-80 rounded-md outline-1 outline-light-neutral-border-low-rest bg-light-neutral-background-low px-1 py-2 text-[#7f7f7f] border border-light-neutral-border-low-rest">
        <RangeDatePicker
          onChange={handleDateChange}
          from={selectedDateRange.from}
          to={selectedDateRange.to}
        />
        <span className="action-icon text-left">
          <Calendar size={18} color="#00000059" />
        </span>
      </div>
    </div>
  );
};

export default DateFilter;

DateFilter.propTypes = {
  handleDateChange: PropTypes.func.isRequired,
  selectedDateRange: PropTypes.shape({
    from: PropTypes.instanceOf(Date),
    to: PropTypes.instanceOf(Date),
  }).isRequired,
};
