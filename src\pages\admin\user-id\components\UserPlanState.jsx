import { Diamond, PencilSimpleLine } from "@phosphor-icons/react";
import { Link, useLocation } from "react-router-dom";

const UserPlanState = () => {
  const { pathname } = useLocation();
  return (
    <div className="p-6">
      <div className="flex items-center justify-between">
        <p className="font-subtitle-large">وضعیت پلن کاربر</p>
        <Link
          to={`/admin/user/list/${
            pathname.split("/").slice(-1)[0]
          }/planSettings`}
          className="w-7 h-7 rounded-md transition hover:bg-light-neutral-background-medium cursor-pointer flex items-center justify-center"
        >
          <PencilSimpleLine />
        </Link>
      </div>

      <div>
        <div className="flex items-center gap-4 my-5">
          <Diamond className="text-light-primary-background-rest" />
          <p className="font-headline-medium">الماسی</p>
        </div>

        <div className="flex items-center gap-4">
          <span className="font-body-medium text-light-neutral-text-medium">
            از
          </span>
          <p className="font-body-large">۱۲:۳۴:۵۴ - ۱۴۰۲/۱۱/۰۱</p>
          <span className="font-body-medium text-light-neutral-text-medium">
            تا
          </span>
          <p className="font-body-large">۱۲:۳۴:۵۴ - ۱۴۰۲/۱۱/۰۱</p>
        </div>
      </div>
    </div>
  );
};

export default UserPlanState;
