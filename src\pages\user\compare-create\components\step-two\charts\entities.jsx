// src/components/Entities.jsx
import { useState, useEffect, memo } from "react";
import PropTypes from "prop-types";
import ReactWordcloud from "react-wordcloud";
import { SpinnerGap } from "@phosphor-icons/react";
import { useCompareStore } from "store/compareStore";
import { buildRequestData } from "utils/requestData";
import CLUSTER_COLORS from "constants/colors";
import Title from "./Title.jsx";
import advanceSearch from "service/api/advanceSearch";
import { Card } from "components/ui/Card";
import { toPersianNumber } from "utils/helper.js";
import ExportMenu from "components/ExportMenu/index.jsx";

const Entities = ({ compareType, tabName }) => {
  const [loading, setLoading] = useState(false);
  const [wordCloudData, setWordCloudData] = useState([]);
  const { fields, platform, date } = useCompareStore((state) => state.compare);
  const [fieldsCleared, setFieldsCleared] = useState([]);

  // Update fieldsCleared only when relevant field properties change
  useEffect(() => {
    const newFieldsCleared = fields.filter((value) =>
      compareType === "profile" ? value?.id || value?.user_id : value?.q
    );
    const hasChanged =
      newFieldsCleared.length !== fieldsCleared.length ||
      newFieldsCleared.some((field, index) => {
        const prevField = fieldsCleared[index] || {};
        return compareType === "profile"
          ? field.id !== prevField.id || field.user_id !== prevField.user_id
          : field.q !== prevField.q;
      });

    if (hasChanged) {
      console.log("fieldsCleared updated:", newFieldsCleared);
      setFieldsCleared(newFieldsCleared);
    }
  }, [fields, compareType]);

  const tabTranslations = {
    person: "اشخاص",
    event: "رویداد",
    location: "مکان ها",
    organ: "سازمان ها",
  };

  const getData = async (id, index) => {
    setLoading(true);
    try {
      const requestData = buildRequestData(
        {
          date,
          platform,
          sources:
            compareType === "profile"
              ? fieldsCleared[index]?.id
                ? [fieldsCleared[index]?.id.toString()]
                : fieldsCleared[index]?.user_id
                ? [fieldsCleared[index]?.user_id.toString()]
                : []
              : undefined,
          q: compareType === "topic" ? fieldsCleared[index].q : undefined,
        },
        "cloud",
        30
      );

      const res = await advanceSearch.search(requestData, null, {
        cloud_type: tabName,
      });

      const platformData = res?.data?.data?.[platform] || [];

      const arabicRange = /[\u0600-\u06FF]/;
      const updatedWordClouds = platformData.map(({ key, count }) => {
        if (!arabicRange.test(key) && key?.startsWith("#")) {
          key = key.slice(1) + "#";
        }
        return { text: key, value: count };
      });

      setWordCloudData((prevData) => {
        const newData = [...prevData];
        newData[index] = updatedWordClouds;
        return newData;
      });
    } catch (error) {
      console.error(error);
      setWordCloudData((prevData) => {
        const newData = [...prevData];
        newData[index] = [];
        return newData;
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch data when fieldsCleared, date, tabName, or platform changes
  useEffect(() => {
    if (fieldsCleared.length > 0) {
      console.log("Fetch effect triggered with:", {
        fieldsCleared,
        date,
        platform,
        tabName,
      });
      setWordCloudData(new Array(fieldsCleared.length).fill([]));
      fieldsCleared.forEach((field, index) => getData(field.id, index));
    }
  }, [fieldsCleared, date, tabName, platform]);

  // Prepare series and time for ExportMenu
  const series = fieldsCleared.map((field, index) => ({
    name: field.q || field.name || field.user_name || `Entity ${index + 1}`,
    data: wordCloudData[index]?.map((word) => word.value) || [],
    time: wordCloudData[index]?.map((word) => word.text) || [],
  }));

  const time = wordCloudData.flatMap((words) => words.map((word) => word.text));

  if (loading) {
    return (
      <div className="flex">
        <Card className="flex flex-col gap-4 w-full">
          <div className="flex w-full h-80 justify-center items-center">
            <SpinnerGap size={40} className="animate-spin" />
          </div>
        </Card>
      </div>
    );
  }

  return (
    <>
      {fieldsCleared.length > 0 && (
        <div className="flex h-full compare-entities-container">
          <Card className="px-0 h-full h-[31rem] card-animation card-delay">
            <div className="w-full h-full flex flex-col gap-6">
              <div className="flex flex-row items-center gap-10 w-full justify-between px-6">
                <Title
                  title={
                    tabName === "organ"
                      ? "اسامی سازمان‌های پرتکرار"
                      : "اسامی اشخاص پرتکرار"
                  }
                />
                <ExportMenu
                  chartSelector=".compare-entities-container"
                  fileName={`wordcloud-${tabName}`}
                  series={series}
                  time={time}
                  excelHeaders={["Text", ...series.map((s) => s.name)]}
                  onError={(error) => console.error("Export error:", error)}
                  menuItems={["PNG", "JPEG", "Excel"]}
                />
              </div>
              <div className="flex divide-x-[1px] h-full divide-x-reverse">
                {fieldsCleared?.map((item, index) => (
                  <div
                    className="flex flex-1 justify-center h-full responsive-svg"
                    key={index}
                  >
                    {wordCloudData[index]?.length ? (
                      <ReactWordcloud
                        options={{
                          rotations: 1,
                          rotationAngles: [0],
                          enableTooltip: true,
                          deterministic: false,
                          fontFamily: "iranyekan",
                          fontSizes: [14, 54],
                          padding: 10,
                          colors: CLUSTER_COLORS,
                          tooltipOptions: { theme: "light", arrow: true },
                        }}
                        words={wordCloudData[index]}
                        callbacks={{
                          getWordTooltip: (word) => {
                            const countInPersian = toPersianNumber(word?.value);
                            return `${word.text} (${countInPersian})`;
                          },
                        }}
                      />
                    ) : (
                      <div className="h-72 flex items-center justify-center font-subtitle-medium">
                        داده ای برای نمایش وجود ندارد
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </Card>
        </div>
      )}
    </>
  );
};

Entities.propTypes = {
  compareType: PropTypes.string.isRequired,
  tabName: PropTypes.string.isRequired,
};

export default memo(Entities);
