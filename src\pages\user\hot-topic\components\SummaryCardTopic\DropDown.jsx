import {
  FilePdf,
  Planet,
  ShareNetwork,
  Note,
  CheckCircle,
  BookmarkSimple,
} from "@phosphor-icons/react";
import { useState } from "react";
import Popup from "components/ui/PopUp";
import NotesPopUp from "components/NotesPopUp";
import note from "service/api/note.js";
import bookmark from "service/api/bookmark.js";
import { notification } from "utils/helper.js";
import { useLocation } from "react-router-dom";
import DropdownWithAddOption from "components/DropdownWithAddOption";
const DropDown = ({
  children,
  data,
  lowWidth,
  media,
  hasNotes,
  isBookmarked,
}) => {
  const [open, setOpen] = useState(false);
  const [bookmarkPopupOpen, setBookmarkPopupOpen] = useState(false);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [isSharePopupOpen, setIsSharePopupOpen] = useState(false);
  const [noteValue, setNoteValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [currentId, setCurrentId] = useState("");
  const [shareUrl, setShareUrl] = useState("");
  const { pathname } = useLocation();
  const [bookmarkCandidate, setBookmarkCandidate] = useState(null);
  const [newCollectionTitle, setNewCollectionTitle] = useState("default");

  const displayBookmark = pathname === "/app/bookmarks" ? false : true;
  const displayNotes = pathname === "/app/notes" ? false : true;
  const submitHandler = async (e) => {
    setIsLoading(true);
    try {
      const res = await note.toggle({
        note: noteValue,
        content_id: currentId,
        content: data,
        platform: media,
        collection_title: newCollectionTitle,
      });
      notification.success(
        "یادداشت با موفقیت ثبت شد",
        <CheckCircle className="text-light-success-text-rest" size={26} />,
      );
    } catch (error) {
      console.log(error);
    }
    setIsLoading(false);
    setIsPopupOpen(false);
    setNoteValue("");
  };
  const shareContentHandler = (id) => {
    setIsSharePopupOpen(true);
    setCurrentId(id);
  };

  const bookmarkHandler = async (id) => {
    setIsLoading(true);
    try {
      const res = await bookmark.toggleBookmark({
        content_id: id,
        content: data,
        platform: media,
        collection_title: newCollectionTitle,
      });
      // console.log(res);
      notification.success(
        "محتوای مورد نظر به لیست نشان‌شده‌ها اضافه شد",
        <CheckCircle className="text-light-success-text-rest" size={26} />,
      );
    } catch (error) {
      console.log(error);
    }
    setIsLoading(false);
    setBookmarkPopupOpen(false);
  };

  const handleBookmarkPopupOpen = (id) => {
    setBookmarkCandidate(id);
    setBookmarkPopupOpen(true);
  };

  const handleBookmarkSubmit = () => {
    if (bookmarkCandidate) {
      bookmarkHandler(bookmarkCandidate);
      setBookmarkCandidate(null);
    }
  };
  return (
    <>
      <button onBlur={() => setOpen(false)} className="relative">
        <div
          className="flex justify-between items-center rounded-lg bg-light-neutral-background-medium border border-[#E1E8EF80]"
          onClick={() => {
            setOpen((l) => !l);
          }}
        >
          {children}
        </div>

        {open && (
          <div className="absolute top-6 left-0 w-44 bg-white rounded-lg shadow-[0px_4px_20px_0px_#0000001A] p-2 z-10">
            {lowWidth && (
              <>
                <div className="font-body-small flex flex-col gap-1">
                  <div
                    className="flex items-center justify-between p-2 rounded-lg hover:bg-light-neutral-surface-highlight"
                    onClick={() => {
                      window.open(data?.url, "_blank");
                      setOpen(false);
                    }}
                  >
                    <div className="flex items-center gap-2 w-full">
                      <Planet className="size-[14px]" />
                      <span className="font-overline-medium text-light-neutral-text-high">
                        مشاهده در بستر
                      </span>
                    </div>
                  </div>
                </div>

                <div className="font-body-small flex flex-col gap-1">
                  <div
                    className="flex items-center justify-between p-2 rounded-lg hover:bg-light-neutral-surface-highlight"
                    onClick={() => {
                      setOpen(false);
                    }}
                  >
                    <div className="flex items-center gap-2 w-full">
                      <FilePdf className="size-[14px]" />
                      <span className="font-overline-medium text-light-neutral-text-high">
                        افزودن به بولتن
                      </span>
                    </div>
                  </div>
                </div>
              </>
            )}

            {displayBookmark && (
              <div
                className="font-body-small flex flex-col gap-1"
                onClick={() => handleBookmarkPopupOpen(data?.id)}
              >
                <div
                  className="flex items-center justify-between p-2 rounded-lg hover:bg-light-neutral-surface-highlight"
                  onClick={() => {
                    setOpen(false);
                  }}
                >
                  <div className="flex items-center gap-2 w-full">
                    {isBookmarked ? (
                      <BookmarkSimple weight="fill" size={14} />
                    ) : (
                      <BookmarkSimple size={14} />
                    )}
                    <span className="font-overline-medium text-light-neutral-text-high">
                      نشان‌گذاری
                    </span>
                  </div>
                </div>
              </div>
            )}

            {displayNotes && (
              <div
                className="font-body-small flex flex-col gap-1"
                onClick={() => {
                  setIsPopupOpen(true);
                  setCurrentId(data?.id);
                }}
              >
                <div
                  className="flex items-center justify-between p-2 rounded-lg hover:bg-light-neutral-surface-highlight"
                  onClick={() => {
                    setOpen(false);
                  }}
                >
                  <div className="flex items-center gap-2 w-full">
                    <Note
                      weight={hasNotes ? "fill" : "duotone"}
                      className="size-[14px]"
                    />
                    <span className="font-overline-medium text-light-neutral-text-high">
                      {hasNotes ? "یادداشت (ثبت شده)" : "یادداشت"}
                    </span>
                  </div>
                </div>
              </div>
            )}

            <div
              className="font-body-small flex flex-col gap-1"
              onClick={() => {
                shareContentHandler(data?.id);
                setShareUrl(data?.url);
              }}
            >
              <div
                className="flex items-center justify-between p-2 rounded-lg hover:bg-light-neutral-surface-highlight"
                onClick={() => {
                  setOpen(false);
                }}
              >
                <div className="flex items-center gap-2 w-full">
                  <ShareNetwork className="size-[14px]" />
                  <span className="font-overline-medium text-light-neutral-text-high">
                    اشتراک گذاری
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}
      </button>
      <Popup
        isOpen={isPopupOpen}
        onClose={() => {
          setIsPopupOpen(!isPopupOpen);
          setNoteValue("");
        }}
        submitHandler={submitHandler}
        readOnly={!noteValue.length ? true : false}
        disabled={isLoading}
        title={"یادداشت"}
      >
        <NotesPopUp
          noteValue={noteValue}
          setNoteValue={setNoteValue}
          noteData={data}
          setNewCollectionTitle={setNewCollectionTitle}
        />
      </Popup>
      <Popup
        isOpen={isSharePopupOpen}
        onClose={() => setIsSharePopupOpen(!isSharePopupOpen)}
        title={"اشتراک‌گذاری"}
        isSharePopup={true}
        width="352px"
        currentId={currentId}
        urlToShare={shareUrl}
      />
      <Popup
        isOpen={bookmarkPopupOpen}
        onClose={() => setBookmarkPopupOpen(false)}
        submitHandler={handleBookmarkSubmit}
        readOnly={false}
        disabled={false}
        title={"ذخیره در فهرست نشان شده‌ها"}
      >
        <p className="font-overline-large">
          لطفا نام پوشه مورد نظر را جهت نشان کردن پست انتخاب شده، مشخص کنید.
        </p>
        <DropdownWithAddOption
          setNewCollectionTitle={setNewCollectionTitle}
          collection_type="bookmark"
        />
      </Popup>
    </>
  );
};

export default DropDown;
