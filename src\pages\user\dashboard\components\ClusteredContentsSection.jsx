import { useEffect, useState } from "react";
import CLUSTER_COLORS from "constants/colors.js";
import ListCol from "./list_col/index.jsx";
import CardsLayout from "layout/CardsLayout.jsx";
import useSearchStore from "store/searchStore.js";
import PropTypes from "prop-types";

export const ClusteredContentsSection = ({
  loading = false,
  clusterData = {},
  clusterListActiveTab = { name: "all", index: -1 },
}) => {
  const { filters, cluster, setCluster } = useSearchStore();
  const [posts, setPosts] = useState([]);

  const reloadPosts = (data) => {
    setPosts(data.clusters || []);
  };

  useEffect(() => {
    reloadPosts(clusterData);
    setCluster({
      baseUrl: clusterData.meta?.base_url || "",
    });
  }, [clusterData]);

  if (loading) return;

  return (
    <CardsLayout>
      {/*<div className="grid sm:grid-cols-1 lg:grid-cols-3 gap-4 mb-4 [direction:rtl]">*/}
      {posts.slice(0, 3).map((post, index) => (
        <ListCol
          key={index}
          data={post}
          media={filters.platform}
          base_url={cluster.baseUrl}
          color={CLUSTER_COLORS[index]}
          active={
            clusterListActiveTab.name === "all" ||
            clusterListActiveTab.index === index
          }
        />
      ))}
      {/*</div>*/}

      {posts.length > 3 && (
        // <div className="grid sm:grid-cols-1 lg:grid-cols-3 gap-4 mb-4 [direction:rtl]">
        <>
          {posts.slice(3, 6).map((post, index) => (
            <ListCol
              key={index}
              data={post}
              media={filters.platform}
              base_url={cluster.baseUrl}
              color={CLUSTER_COLORS[3 + index]}
              active={
                clusterListActiveTab.name === "all" ||
                clusterListActiveTab.index === 3 + index
              }
            />
          ))}
        </>

        // </div>
      )}

      {posts.length > 6 && (
        // <div className="grid sm:grid-cols-1 lg:grid-cols-3 gap-4 mb-4 [direction:rtl]">
        <>
          {posts.slice(6, 9).map((post, index) => (
            <ListCol
              key={index}
              data={post}
              media={filters.platform}
              base_url={cluster.baseUrl}
              color={CLUSTER_COLORS[6 + index]}
              active={
                clusterListActiveTab.name === "all" ||
                clusterListActiveTab.index === 6 + index
              }
            />
          ))}
        </>
        // </div>
      )}
    </CardsLayout>
  );
};

ClusteredContentsSection.propTypes = {
  loading: PropTypes.bool,
  clusterData: PropTypes.object,
  clusterListActiveTab: PropTypes.object,
};
