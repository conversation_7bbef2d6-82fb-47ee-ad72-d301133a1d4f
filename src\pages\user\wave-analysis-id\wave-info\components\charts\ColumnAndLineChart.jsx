// import Highcharts from "highcharts";
// import HighchartsReact from "highcharts-react-official";
// import { toPersianNumber } from "utils/helper";

// const ColumnAndLineChart = ({
//   fetchedContentAge,
//   selectedYear,
//   setSelectedYear,
// }) => {
//   if (!fetchedContentAge) {
//     return (
//       <div className="font-body-bold-large flex items-center justify-center h-screen">
//         داده‌ای برای نمایش وجود ندارد
//       </div>
//     );
//   }

//   const years = Object.keys(fetchedContentAge)
//     .filter((key) => key !== "total")
//     .sort((a, b) => {
//       if (a === "2007-2015") return -1;
//       if (b === "2007-2015") return 1;
//       return a - b;
//     });

//   const contentCountData = years.map(
//     (year) => fetchedContentAge[year]?.year?.content_count || 0
//   );
//   const repostData = years.map(
//     (year) => fetchedContentAge[year]?.year?.repost || 0
//   );
//   const replyData = years.map(
//     (year) => fetchedContentAge[year]?.year?.reply || 0
//   );
//   const quoteData = years.map(
//     (year) => fetchedContentAge[year]?.year?.quote || 0
//   );
//   const postData = years.map(
//     (year) => fetchedContentAge[year]?.year?.post || 0
//   );

//   const pieOptions = {
//     title: {
//       text: null,
//     },
//     credits: {
//       enabled: false,
//     },
//     xAxis: {
//       categories: years.map((year) =>
//         year === "2007-2015"
//           ? `${toPersianNumber(2007)}-${toPersianNumber(2015)}`
//           : toPersianNumber(year)
//       ),
//       lineWidth: 1,
//       minorGridLineWidth: 0,
//       lineColor: "#949EB7",
//       labels: {
//         enabled: true,
//         style: {
//           fontSize: "12px",
//         },
//       },
//       minorTickLength: 0,
//       tickLength: 0,
//       gridLineWidth: 0,
//       startOnTick: false,
//       endOnTick: false,
//       title: {
//         text: "تاریخ ایجاد حساب کاربری",
//       },
//     },
//     yAxis: {
//       lineWidth: 1,
//       minorGridLineWidth: 0,
//       lineColor: "#949EB7",
//       labels: {
//         enabled: true,
//         formatter: function () {
//           return toPersianNumber(this.value);
//         },
//       },
//       minorTickLength: 0,
//       tickLength: 0,
//       gridLineWidth: 0,
//       startOnTick: false,
//       endOnTick: false,
//       title: {
//         text: "تعداد محتوا",
//       },
//     },
//     tooltip: {
//       formatter: function () {
//         const seriesName = this.series.name;
//         const value = toPersianNumber(this.y);
//         const year = this.x;
//         return `<b>${year}</b><br/>${seriesName}: ${value}`;
//       },
//     },
//     plotOptions: {
//       series: {
//         borderRadius: "25%",
//       },
//     },
//     series: [
//       {
//         type: "column",
//         name: "توئیت",
//         data: postData,
//         color: "#6D72E5",
//       },
//       {
//         type: "column",
//         name: "ریتوئیت",
//         data: repostData,
//         color: "#1DCEA3",
//       },
//       {
//         type: "column",
//         name: "ریپلای",
//         data: replyData,
//         color: "#E0526A",
//       },
//       {
//         type: "column",
//         name: "کوت",
//         data: quoteData,
//         color: "#FFB946",
//       },
//       {
//         type: "line",
//         step: "center",
//         name: "میانگین",
//         data: contentCountData,
//         marker: {
//           lineWidth: 2,
//           lineColor: "red",
//           fillColor: "white",
//         },
//       },
//     ],
//   };

//   return <HighchartsReact highcharts={Highcharts} options={pieOptions} />;
// };

// export default ColumnAndLineChart;

// import Highcharts from "highcharts";
// import HighchartsReact from "highcharts-react-official";
// import { toPersianNumber } from "utils/helper";

// const ColumnAndLineChart = ({
//   fetchedContentAge,
//   selectedYear,
//   setSelectedYear,
// }) => {
//   if (!fetchedContentAge) {
//     return (
//       <div className="font-body-bold-large flex items-center justify-center h-screen">
//         داده‌ای برای نمایش وجود ندارد
//       </div>
//     );
//   }
// console.log(fetchedContentAge)
//   const years = Object.keys(fetchedContentAge)
//     .filter((key) => key !== "total")
//     .sort((a, b) => {
//       if (a === "2007-2015") return -1;
//       if (b === "2007-2015") return 1;
//       return a - b;
//     });

//   const contentCountData = years.map(
//     (year) => fetchedContentAge[year]?.year?.content_count || 0
//   );
//   const repostData = years.map(
//     (year) => fetchedContentAge[year]?.year?.repost || 0
//   );
//   const replyData = years.map(
//     (year) => fetchedContentAge[year]?.year?.reply || 0
//   );
//   const quoteData = years.map(
//     (year) => fetchedContentAge[year]?.year?.quote || 0
//   );
//   const postData = years.map(
//     (year) => fetchedContentAge[year]?.year?.post || 0
//   );

//   const chartOptions = {
//     title: {
//       text: null,
//     },
//     credits: {
//       enabled: false,
//     },
//     xAxis: {
//       categories: years.map((year) =>
//         year === "2007-2015"
//           ? `${toPersianNumber(2007)}-${toPersianNumber(2015)}`
//           : toPersianNumber(year)
//       ),
//       lineWidth: 1,
//       minorGridLineWidth: 0,
//       lineColor: "#949EB7",
//       labels: {
//         enabled: true,
//         style: {
//           fontSize: "12px",
//         },
//       },
//       minorTickLength: 0,
//       tickLength: 0,
//       gridLineWidth: 0,
//       startOnTick: false,
//       endOnTick: false,
//       title: {
//         text: "تاریخ ایجاد حساب کاربری",
//       },
//     },
//     yAxis: {
//       lineWidth: 1,
//       minorGridLineWidth: 0,
//       lineColor: "#949EB7",
//       labels: {
//         enabled: true,
//         formatter: function () {
//           return toPersianNumber(this.value);
//         },
//       },
//       minorTickLength: 0,
//       tickLength: 0,
//       gridLineWidth: 0,
//       startOnTick: false,
//       endOnTick: false,
//       title: {
//         text: "تعداد محتوا",
//       },
//     },
//     tooltip: {
//       formatter: function () {
//         const seriesName = this.series.name;
//         const value = toPersianNumber(this.y);
//         const year = this.x;
//         return `<b>${year}</b><br/>${seriesName}: ${value}`;
//       },
//     },
//     plotOptions: {
//       series: {
//         borderRadius: "25%",
//         point: {
//           events: {
//             click: function () {
//               const yearIndex = this.index;
//               const selectedYear = years[yearIndex];
//               setSelectedYear(selectedYear);
//             },
//           },
//         },
//       },
//     },
//     series: [
//       {
//         type: "column",
//         name: "توئیت",
//         data: postData,
//         color: "#6D72E5",
//       },
//       {
//         type: "column",
//         name: "ریتوئیت",
//         data: repostData,
//         color: "#1DCEA3",
//       },
//       {
//         type: "column",
//         name: "ریپلای",
//         data: replyData,
//         color: "#E0526A",
//       },
//       {
//         type: "column",
//         name: "کوت",
//         data: quoteData,
//         color: "#FFB946",
//       },
//       {
//         type: "line",
//         step: "center",
//         name: "میانگین",
//         data: contentCountData,
//         marker: {
//           lineWidth: 2,
//           lineColor: "red",
//           fillColor: "white",
//         },
//       },
//     ],
//   };

//   return <HighchartsReact highcharts={Highcharts} options={chartOptions} />;
// };

// export default ColumnAndLineChart;

import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { toPersianNumber } from "utils/helper";
import persianMonths from "../../constants/persianMonths";

// const persianMonths = {
//   1: "فروردین",
//   2: "اردیبهشت",
//   3: "خرداد",
//   4: "تیر",
//   5: "مرداد",
//   6: "شهریور",
//   7: "مهر",
//   8: "آبان",
//   9: "آذر",
//   10: "دی",
//   11: "بهمن",
//   12: "اسفند",
// };

const ColumnAndLineChart = ({
  fetchedContentAge,
  selectedYear,
  setSelectedYear,
}) => {
  if (!fetchedContentAge) {
    return (
      <div className="font-body-bold-large flex items-center justify-center h-screen">
        داده‌ای برای نمایش وجود ندارد
      </div>
    );
  }

  const years = Object.keys(fetchedContentAge)
    .filter((key) => key !== "total")
    .sort((a, b) => {
      if (a === "2007-2015") return -1;
      if (b === "2007-2015") return 1;
      return a - b;
    });

  const contentCountData = years.map(
    (year) => fetchedContentAge[year]?.year?.content_count / 4 || 0
  );
  const repostData = years.map(
    (year) => fetchedContentAge[year]?.year?.repost || 0
  );
  const replyData = years.map(
    (year) => fetchedContentAge[year]?.year?.reply || 0
  );
  const quoteData = years.map(
    (year) => fetchedContentAge[year]?.year?.quote || 0
  );
  const postData = years.map(
    (year) => fetchedContentAge[year]?.year?.post || 0
  );

  const yearChartOptions = {
    title: {
      text: null,
    },
    credits: {
      enabled: false,
    },
    xAxis: {
      categories: years.map((year) =>
        year === "2007-2015"
          ? `${toPersianNumber(2007)}-${toPersianNumber(2015)}`
          : toPersianNumber(year)
      ),
      lineWidth: 1,
      minorGridLineWidth: 0,
      lineColor: "#949EB7",
      labels: {
        enabled: true,
        style: {
          fontSize: "12px",
        },
      },
      minorTickLength: 0,
      tickLength: 0,
      gridLineWidth: 0,
      startOnTick: false,
      endOnTick: false,
      title: {
        text: "تاریخ ایجاد حساب کاربری",
      },
    },
    yAxis: {
      lineWidth: 1,
      minorGridLineWidth: 0,
      lineColor: "#949EB7",
      labels: {
        enabled: true,
        formatter: function () {
          return toPersianNumber(this.value);
        },
      },
      minorTickLength: 0,
      tickLength: 0,
      gridLineWidth: 0,
      startOnTick: false,
      endOnTick: false,
      title: {
        text: "تعداد محتوا",
      },
    },
    tooltip: {
      formatter: function () {
        const seriesName = this.series.name;
        const value = toPersianNumber(this.y);
        const year = toPersianNumber(this.x);
        return `<b>${year}</b><br/>${seriesName}: ${value}`;
      },
    },
    plotOptions: {
      series: {
        borderRadius: "25%",
      },
    },
    series: [
      {
        type: "column",
        name: "توئیت",
        data: postData,
        color: "#6D72E5",
        point: {
          events: {
            click: function () {
              const yearIndex = this.index;
              const selectedYear = years[yearIndex];
              setSelectedYear(selectedYear);
            },
          },
        },
      },
      {
        type: "column",
        name: "ریتوئیت",
        data: repostData,
        color: "#1DCEA3",
        point: {
          events: {
            click: function () {
              const yearIndex = this.index;
              const selectedYear = years[yearIndex];
              setSelectedYear(selectedYear);
            },
          },
        },
      },
      {
        type: "column",
        name: "ریپلای",
        data: replyData,
        color: "#E0526A",
        point: {
          events: {
            click: function () {
              const yearIndex = this.index;
              const selectedYear = years[yearIndex];
              setSelectedYear(selectedYear);
            },
          },
        },
      },
      {
        type: "column",
        name: "کوت",
        data: quoteData,
        color: "#FFB946",
        point: {
          events: {
            click: function () {
              const yearIndex = this.index;
              const selectedYear = years[yearIndex];
              setSelectedYear(selectedYear);
            },
          },
        },
      },
      {
        type: "line",
        step: "center",
        name: "میانگین",
        data: contentCountData,
        marker: {
          lineWidth: 2,
          lineColor: "red",
          fillColor: "white",
        },
      },
    ],
  };

  // Monthly chart configuration (only shown if a year is selected)
  let monthChartOptions = null;
  if (selectedYear && fetchedContentAge[selectedYear]?.month) {
    const months = Object.keys(fetchedContentAge[selectedYear].month).sort(
      (a, b) => Number(a) - Number(b)
    );

    const monthContentCountData = months.map(
      (month) =>
        fetchedContentAge[selectedYear].month[month]?.content_count / 4 || 0
    );
    const monthRepostData = months.map(
      (month) => fetchedContentAge[selectedYear].month[month]?.repost || 0
    );
    const monthReplyData = months.map(
      (month) => fetchedContentAge[selectedYear].month[month]?.reply || 0
    );
    const monthQuoteData = months.map(
      (month) => fetchedContentAge[selectedYear].month[month]?.quote || 0
    );
    const monthPostData = months.map(
      (month) => fetchedContentAge[selectedYear].month[month]?.post || 0
    );

    monthChartOptions = {
      title: {
        text: null,
      },
      credits: {
        enabled: false,
      },
      xAxis: {
        categories: months.map((month) => persianMonths[month] || month),
        lineWidth: 1,
        minorGridLineWidth: 0,
        lineColor: "#949EB7",
        labels: {
          enabled: true,
          formatter: function () {
            return toPersianNumber(this.value);
          },
          style: {
            fontSize: "12px",
          },
        },
        minorTickLength: 0,
        tickLength: 0,
        gridLineWidth: 0,
        startOnTick: false,
        endOnTick: false,
        title: {
          text: "ماه",
        },
      },
      yAxis: {
        lineWidth: 1,
        minorGridLineWidth: 0,
        lineColor: "#949EB7",
        labels: {
          enabled: true,
          formatter: function () {
            return toPersianNumber(this.value);
          },
        },
        minorTickLength: 0,
        tickLength: 0,
        gridLineWidth: 0,
        startOnTick: false,
        endOnTick: false,
        title: {
          text: "تعداد محتوا",
        },
      },
      tooltip: {
        formatter: function () {
          const seriesName = this.series.name;
          const value = toPersianNumber(this.y);
          const month = toPersianNumber(this.x);
          return `
          <div style="display:grid; gap:5px;">
          <b>${month}</b><br/>${seriesName}: ${value}
          </div>
          `;
        },
      },
      plotOptions: {
        series: {
          borderRadius: "25%",
        },
      },
      series: [
        {
          type: "column",
          name: "توئیت",
          data: monthPostData,
          color: "#6D72E5",
        },
        {
          type: "column",
          name: "ریتوئیت",
          data: monthRepostData,
          color: "#1DCEA3",
        },
        {
          type: "column",
          name: "ریپلای",
          data: monthReplyData,
          color: "#E0526A",
        },
        {
          type: "column",
          name: "کوت",
          data: monthQuoteData,
          color: "#FFB946",
        },
        {
          type: "line",
          step: "center",
          name: "میانگین",
          data: monthContentCountData,
          marker: {
            lineWidth: 2,
            lineColor: "red",
            fillColor: "white",
          },
        },
      ],
    };
  }

  return (
    <div>
      {monthChartOptions ? (
        <div className="mt-8">
          <HighchartsReact
            highcharts={Highcharts}
            options={monthChartOptions}
          />
        </div>
      ) : (
        <HighchartsReact highcharts={Highcharts} options={yearChartOptions} />
      )}
    </div>
  );
};

export default ColumnAndLineChart;
