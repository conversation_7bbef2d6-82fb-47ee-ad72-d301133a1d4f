import { useEffect, useState } from "react";
import Areaspline from "components/Charts/Areaspline";
import advanceSearch from "service/api/advanceSearch";
import { SpinnerGap } from "@phosphor-icons/react";
import PropTypes from "prop-types";
import useSearchStore from "store/searchStore";
import { buildRequestData } from "utils/requestData";
import ExportMenu from "components/ExportMenu/index.jsx";

const Content = () => {
  const { filters, query } = useSearchStore();
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [time_gap, setTime_gap] = useState("hour");
  const [process_range, setProcess_range] = useState("hourly");

  const getData = async () => {
    setLoading(true);
    try {
      const requestData = buildRequestData(
        {
          ...filters,
          q: query,
        },
        "process"
      );

      const res = await advanceSearch.search(requestData, null, {
        process_range,
      });

      const platformData = {
        twitter: res?.data?.data?.twitter,
        instagram: res?.data?.data?.instagram,
        telegram: res?.data?.data?.telegram,
        eitaa: res?.data?.data?.eitaa,
        news: res?.data?.data?.news,
      };
      const stinasData = platformData[filters.platform] || [];
      setData(stinasData);
    } catch (error) {
      console.error(error);
      setData([]);
    }
    setLoading(false);
  };

  // Prepare series and time for ExportMenu
  const series = [
    {
      name: "Content Publication",
      data: Array.isArray(data) ? data.map((item) => item.count) : [],
      time: Array.isArray(data)
        ? data.map((item) => item?.datetime || item?.time)
        : [],
    },
  ];

  const time = Array.isArray(data)
    ? data.map((item) => item?.datetime || item?.time)
    : [];

  useEffect(() => {
    getData();
  }, [filters, query, time_gap, process_range]);

  if (loading)
    return (
      <div className="w-full h-[430px] flex justify-center items-center">
        <SpinnerGap size={40} className="animate-spin" />
      </div>
    );

  const categories = Array.isArray(data)
    ? data.map((item) => item?.datetime || item?.time)
    : [];

  return (
    <div className="content-container">
      <div className="flex items-center justify-between mb-8">
        <ul className="flex gap-2 *:font-overline-medium *:p-2 *:rounded-md *:cursor-pointer">
          <li
            className={`${
              time_gap === "week"
                ? "bg-light-neutral-background-high"
                : "bg-light-neutral-background-low"
            }`}
            onClick={() => {
              setProcess_range("weekly");
              setTime_gap("week");
            }}
          >
            هفتگی
          </li>
          <li
            className={`${
              time_gap === "day"
                ? "bg-light-neutral-background-high"
                : "bg-light-neutral-background-low"
            }`}
            onClick={() => {
              setProcess_range("daily");
              setTime_gap("day");
            }}
          >
            روزانه
          </li>
          <li
            className={`${
              time_gap === "hour"
                ? "bg-light-neutral-background-high"
                : "bg-light-neutral-background-low"
            }`}
            onClick={() => {
              setProcess_range("hourly");
              setTime_gap("hour");
            }}
          >
            ساعتی
          </li>
        </ul>
        <div className="flex items-center gap-4">
          <p className="font-subtitle-large">روند انتشار محتوا</p>
          <ExportMenu
            chartSelector=".content-container"
            fileName="content-publication"
            series={series}
            time={time}
            excelHeaders={["Time", "Content Publication"]}
            onError={(error) => console.error("Export error:", error)}
            menuItems={["PNG", "JPEG", "Excel"]}
          />
        </div>
      </div>

      <Areaspline
        cat={categories}
        time_gap={time_gap}
        data={series[0].data}
        height={315}
      />
    </div>
  );
};

Content.propTypes = {
  platform: PropTypes.string,
  req_data: PropTypes.object,
  query: PropTypes.string,
};

export default Content;
