import { CButton } from "components/ui/CButton";
import { CInput } from "components/ui/CInput";
import PropTypes from "prop-types";

const Step1 = ({ step, setStep }) => {
  return (
    <form className="flex-1 bg-white mx-auto w-1/2 h-full p-6 rounded-lg">
      <CInput
        id="title"
        name="title"
        size="lg"
        validation="none"
        direction="rtl"
        title="عنوان گروه"
        placeholder="نام گروه یا سازمان موردنظر را بنویسید"
        className="w-full font-overline-large"
      />

      <div className="mb-6">
        <label
          htmlFor="description"
          className="font-overline-large mb-2 inline-block"
        >
          توضیحات
        </label>
        <textarea
          id="description"
          name="description"
          placeholder="به عنوان مثال این گروه برای مدیران خاص و سازمان خاص است."
          className="w-full h-40 rounded-md p-4 font-body-large outline-none border border-light-neutral-border-medium-rest"
        />
      </div>

      <div className="flex flex-row-reverse">
        <div className="w-[200px]">
          <CButton type="submit" onClick={() => setStep(step + 1)}>
            ادامه
          </CButton>
        </div>
      </div>
    </form>
  );
};
Step1.propTypes = {
  step: PropTypes.number.isRequired,
  setStep: PropTypes.func.isRequired,
};

export default Step1;
