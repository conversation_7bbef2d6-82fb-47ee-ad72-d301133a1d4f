import { Sliders } from "@phosphor-icons/react";
import clsx from "clsx";
import FontSettings from "./FontSettings.jsx";
import { useBulletinStore } from "store/bulletinStore";
import { PARSED_BASE_HIGHLIGHT } from "constants/bulletin-colors.js";
import PropTypes from "prop-types";
import CoverSettingSection from "pages/user/bulletin-create/step-4/components/CoverSettingSection.jsx";

const InputWithEdit = ({
  placeholder = "",
  title = "عنوان",
  name = "",
  FontSettingsPosition = "right",
  selected = false,
  handleClick,
  initialValue,
}) => {
  const setBulletin = useBulletinStore((state) => state.setBulletin);
  const { metadata } = useBulletinStore((state) => state.bulletin);

  const handleStyleChange = (x) => {
    x.text_highlight = PARSED_BASE_HIGHLIGHT[x.text_highlight];
    const help = JSON.parse(JSON.stringify(metadata));
    help.style[name] = { ...help.style[name], ...x };
    setBulletin({ metadata: { ...help } });
  };

  const handleTextChange = (e) => {
    const help = JSON.parse(JSON.stringify(metadata));
    help[name] = e.target.value;
    setBulletin({ metadata: { ...help } });
  };

  return (
    <div
      className={clsx(
        "flex flex-col gap-2 w-full relative",
        selected && "pb-14",
      )}
    >
      <div className="flex flex-col gap-2">
        <div>
          <span className="font-overline-large">{title}</span>
        </div>
        <div className="flex items-center gap-2">
          <input
            type="text"
            placeholder={placeholder}
            onChange={handleTextChange}
            value={metadata?.[name] || ""}
            className="px-4 py-2 outline-none border border-light-neutral-border-medium-rest rounded-md placeholder:text-light-neutral-text-medium w-full font-body-large"
          />
          <div
            className={clsx(
              "size-10 flex items-center justify-center rounded-lg cursor-pointer",
              selected
                ? "bg-light-primary-background-highlight"
                : "bg-light-neutral-background-medium",
            )}
            onClick={() => handleClick(name)}
          >
            <Sliders size={24} color={selected ? "#6F5CD1" : "black"} />
          </div>
        </div>
      </div>
      <div
        className={clsx(
          "absolute -bottom-1",
          FontSettingsPosition === "right"
            ? "right-0"
            : FontSettingsPosition === "left" && "left-0",
          !selected && "hidden",
        )}
      >
        <FontSettings
          handleChange={handleStyleChange}
          initialValue={initialValue || metadata.style[name]}
        />
      </div>
    </div>
  );
};

InputWithEdit.propTypes = {
  placeholder: PropTypes.string,
  name: PropTypes.string,
  onChange: PropTypes.func,
  initialValue: PropTypes.object,
  title: PropTypes.string,
  FontSettingsPosition: PropTypes.string,
  selected: PropTypes.bool,
  handleClick: PropTypes.func,
};

export default InputWithEdit;
