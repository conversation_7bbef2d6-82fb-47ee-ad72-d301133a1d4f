import { toP<PERSON>ian<PERSON><PERSON>ber } from "utils/helper";
import SimpleBubbleChart from "./charts/SimpleBubbleChart";
import { useState } from "react";
import { ArrowUUpLeft } from "@phosphor-icons/react";
import persianMonths from "../constants/persianMonths";

const ParticipantCredits = ({ fetchedParticipantCredits }) => {
  const [selectedYear, setSelectedYear] = useState(null);
  // Year chart data
  const yearKeys = Object.keys(fetchedParticipantCredits)?.sort();
  const yearChartData = yearKeys?.map((yearKey, index) => {
    const yearData = fetchedParticipantCredits[yearKey]?.year;
    return {
      x: index,
      y: yearData?.account_count,
      z: yearData?.content_count,
      name: yearKey,
      height: "100%",
    };
  });
  // Determine if the selected year is the special case "2007-2015"
  const isSpecialCase = selectedYear === "2007-2015";
  // Month (or year for 2007-2015) chart data for the selected year
  const monthChartData = selectedYear
    ? Object.keys(fetchedParticipantCredits[selectedYear]?.month || {})
        ?.sort((a, b) => parseInt(a) - parseInt(b)) // Sort numerically
        ?.map((key, index) => {
          const monthData =
            fetchedParticipantCredits[selectedYear]?.month?.[key];
          return {
            x: index,
            y: monthData?.account_count || 0,
            z: monthData?.content_count || 0,
            name: isSpecialCase ? key : persianMonths?.[key], // Use English months or persianMonths[key]
          };
        })
    : [];
  // xAxis categories for the month view
  const monthCategories = selectedYear
    ? Object.keys(fetchedParticipantCredits[selectedYear]?.month || {})
        ?.sort((a, b) => parseInt(a) - parseInt(b)) // Sort numerically
        ?.map(
          (key) => (isSpecialCase ? toPersianNumber(key) : persianMonths[key]) // Use English months or persianMonths[key]
        )
    : [];
  // Common chart options
  const getCommonOptions = (isMonthView) => ({
    chart: {
      type: "bubble",
      plotBorderWidth: 0,
      borderWidth: 0,
      height: "390",
    },
    title: {
      text: null,
    },
    credits: {
      enabled: false,
    },
    xAxis: {
      lineWidth: 1,
      minorGridLineWidth: 0,
      lineColor: "#949EB7",
      labels: {
        enabled: true,
        formatter: function () {
          if (isMonthView) {
            return monthCategories[this.value] || this.value;
          }
          return toPersianNumber(yearKeys[this.value] || this.value);
        },
        style: {
          fontSize: "12px",
          fontWeight: "light",
          color: "#949EB7",
        },
      },
      minorTickLength: 0,
      tickLength: 0,
      gridLineWidth: 0,
      title: {
        text: isMonthView ? (isSpecialCase ? "سال" : "ماه") : "سال ایجاد اکانت",
      },
      ...(isMonthView
        ? {
            categories: monthCategories,
            min: 0,
            max: monthCategories.length - 1,
          }
        : {
            categories: yearKeys,
            min: 0,
            max: yearKeys.length - 1,
          }),
    },
    yAxis: {
      lineWidth: 1,
      minorGridLineWidth: 0,
      lineColor: "#949EB7",
      labels: {
        enabled: true,
        formatter: function () {
          return toPersianNumber(this.value);
        },
        style: {
          fontSize: "12px",
          fontWeight: "light",
          color: "#949EB7",
        },
      },
      minorTickLength: 0,
      tickLength: 0,
      gridLineWidth: 0,
      startOnTick: false,
      endOnTick: false,
      title: {
        text: "تعداد اکانت ایجاد شده",
      },
    },
    legend: {
      enabled: false,
    },
    tooltip: {
      useHTML: true,
      formatter: function () {
        const label = isMonthView
          ? monthCategories[this?.point?.x] || this?.point?.x
          : toPersianNumber(this?.point?.name);
        return `
          <div style="font-family: 'iranYekan', sans-serif; text-align: right; display:grid; gap:5px;">
            <p>${
              isMonthView ? (isSpecialCase ? "سال" : "ماه") : "سال ایجاد اکانت"
            }: ${label}</p>
            <p>تعداد محتوا: ${toPersianNumber(this?.point?.z)}</p>
            <p>تعداد اکانت: ${toPersianNumber(this?.point?.y)}</p>
          </div>
        `;
      },
    },
    plotOptions: {
      bubble: {
        minSize: "5%",
        maxSize: "20%",
        dataLabels: {
          enabled: false,
        },
        cursor: selectedYear ? "auto" : "pointer",
      },
    },
  });
  const yearOptions = {
    ...getCommonOptions(false),
    plotOptions: {
      ...getCommonOptions(false).plotOptions,
      bubble: {
        ...getCommonOptions(false).plotOptions.bubble,
        point: {
          events: {
            click: function () {
              setSelectedYear(this?.name);
            },
          },
        },
      },
    },
    series: [
      {
        name: "فعالیت حساب‌ها",
        color: "#4D36BF",
        data: yearChartData,
        marker: {
          fillColor: {
            radialGradient: { cx: 0.4, cy: 0.3, r: 0.7 },
            stops: [
              [0, "white"],
              [1, "#4D36BF"],
            ],
          },
        },
      },
    ],
  };
  const monthOptions = {
    ...getCommonOptions(true),
    series: [
      {
        name: isSpecialCase
          ? "فعالیت حساب‌ها در سال‌ها"
          : "فعالیت حساب‌ها در ماه‌ها",
        color: "#4D36BF",
        data: monthChartData,
        marker: {
          fillColor: {
            radialGradient: { cx: 0.4, cy: 0.3, r: 0.7 },
            stops: [
              [0, "white"],
              [1, "#4D36BF"],
            ],
          },
        },
      },
    ],
  };
  return (
    <div
      className="bg-light-neutral-surface-card rounded-lg p-3 h-full"
      style={{
        boxShadow: "0px 2px 20px 0px #0000000D",
      }}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-1">
          <p className="font-subtitle-large">اعتبار کاربران مشارکت کننده</p>
        </div>

        {selectedYear && (
          <div
            onClick={() => setSelectedYear(null)}
            className="flex flex-row-reverse items-center gap-2 font-body-medium cursor-pointer"
          >
            <p>بازگشت به نمودار سالیانه</p>
            <ArrowUUpLeft size={19} />
          </div>
        )}
      </div>
      {Object.keys(fetchedParticipantCredits).length > 0 ? (
        <div className="grid grid-cols-12">
          <div className="col-span-4 flex justify-center items-center">
            <table className="min-w-full mt-5">
              <thead>
                <tr>
                  <th className="p-0 text-right font-body-medium text-light-neutral-text-medium">
                    {selectedYear
                      ? isSpecialCase
                        ? "سال"
                        : "ماه"
                      : "تاریخ ایجاد اکانت"}
                  </th>
                  <th className="p-0 text-center font-body-medium text-light-neutral-text-medium">
                    تعداد اکانت‌های ایجاد شده
                  </th>
                  <th className="p-0 text-center font-body-medium text-light-neutral-text-medium">
                    تعداد محتوا
                  </th>
                </tr>
              </thead>
              <tbody className="font-subtitle-medium">
                {selectedYear
                  ? // Month view (or years for 2007-2015)
                    Object.entries(
                      fetchedParticipantCredits[selectedYear]?.month || {}
                    )
                      .sort((a, b) => parseInt(a[0]) - parseInt(b[0])) // Sort by month/year key
                      .map(([key, monthData]) => (
                        <tr
                          key={key}
                          className="hover:bg-gray-50 cursor-pointer"
                        >
                          <td className="px-3 py-2 font-body-medium text-light-neutral-text-medium text-right">
                            {isSpecialCase
                              ? toPersianNumber(key)
                              : persianMonths[key] || key}
                          </td>
                          <td className="px-3 py-2 font-body-medium text-light-neutral-text-medium text-center">
                            {toPersianNumber(monthData.account_count)}
                          </td>
                          <td className="px-3 py-2 font-body-medium text-light-neutral-text-medium text-center">
                            {toPersianNumber(monthData.content_count)}
                          </td>
                        </tr>
                      ))
                  : // Year view
                    yearChartData.map((item) => (
                      <tr
                        key={item?.x}
                        className="hover:bg-gray-50 cursor-pointer"
                      >
                        <td className="p-1.5 font-body-medium text-light-neutral-text-medium text-right">
                          {toPersianNumber(item?.name)}
                        </td>
                        <td className="p-1.5 font-body-medium text-light-neutral-text-medium text-center">
                          {toPersianNumber(item?.y)}
                        </td>
                        <td className="p-1.5 font-body-medium text-light-neutral-text-medium text-center">
                          {toPersianNumber(item?.z)}
                        </td>
                      </tr>
                    ))}
              </tbody>
            </table>
          </div>
          <div className="col-span-8 max-h-96">
            <SimpleBubbleChart
              data={fetchedParticipantCredits}
              yearOptions={yearOptions}
              monthOptions={monthOptions}
              selectedYear={selectedYear}
              setSelectedYear={setSelectedYear}
            />
          </div>
        </div>
      ) : (
        <div
          colSpan={3}
          className="py-44 text-center font-body-bold-large text-light-neutral-text-medium"
        >
          موردی یافت نشد!
        </div>
      )}
    </div>
  );
};

export default ParticipantCredits;
