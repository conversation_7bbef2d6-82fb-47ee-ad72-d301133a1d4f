import Loading from "components/ui/Loading";
import { useState } from "react";
import Wrapper from "./Wrapper";
import Empty from "./Empty";
import Table from "./Table";
import { ToastContainer } from "react-toastify";

const AccessSettingsTable = ({ filter, setFilter ,className}) => {
  const [loading, setLoading] = useState(false);
  const [table, setTable] = useState([{}, {}, {}]);
  const [tableItemsLimit, setTableItemsLimit] = useState(20);
  const [page, setPage] = useState(1);

  // const sixMonthInMillis = 6 * 30 * 24 * 60 * 60 * 1000;
  // const [filter, setFilter] = useState({
  //   q: "",
  //   register_at: {
  //     date_from: formatDate(new Date(new Date().getTime() - sixMonthInMillis)),
  //     date_to: formatDate(new Date(new Date().getTime() + 24 * 60 * 60 * 1000)),
  //   },
  //   sort_rules: [
  //     {
  //       field: "login_at",
  //       direction: "desc",
  //     },
  //     {
  //       field: "register_at",
  //       direction: "desc",
  //     },
  //   ],
  // });
  // get users
  // const getUsers = async () => {
  //   setLoading(true);
  //   try {
  //     let newFiler = {};
  //     if (filter?.q.trim() !== "") newFiler.q = filter.q;
  //     newFiler.register_at = filter?.register_at;
  //     newFiler.sort_rules = filter?.sort_rules;

  //     let newQuery = {};
  //     newQuery.page = page;
  //     newQuery.page_size = tableItemsLimit;
  //     // newQuery.status =
  //     //   activeTab == "همه"
  //     //     ? "all"
  //     //     : activeTab == "کاربران فعال"
  //     //     ? "active_users"
  //     //     : activeTab == "کاربران غیرفعال"
  //     //     ? "inactive_users"
  //     //     : "managers";
  //     newQuery.role = filter?.role;

  //     const {
  //       data: { data },
  //     } = await userService.getUserList(newFiler, newQuery);
  //     setTable(data?.users);
  //   } catch (error) {
  //     console.log(error);
  //   }
  //   setLoading(false);
  // };
  // useEffect(() => {
  //   getUsers();
  // }, [page, tableItemsLimit, activeTab, filter]);

  return (
    <div className={`bg-light-neutral-surface-card rounded-md p-4 ${className}`}>
      <>
        {loading ? (
          <Loading />
        ) : (
          <>
            <div className="flex flex-col h-full w-full">
              <Wrapper filter={filter} setFilter={setFilter}>
                {table.length > 0 ? (
                  table?.map((item) => (
                    <>
                      <Table key={item.id} data={item} />
                    </>
                  ))
                ) : (
                  <Empty />
                )}
              </Wrapper>
            </div>
          </>
        )}
      </>
      <ToastContainer />
    </div>
  );
};

export default AccessSettingsTable;
