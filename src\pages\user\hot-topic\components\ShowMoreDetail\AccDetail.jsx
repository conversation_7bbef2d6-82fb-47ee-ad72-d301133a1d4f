import MediaBadge from "components/ui/MediaBadge";

const AccDetail = ({ data, media }) => {
  return (
    <div className="flex justify-between items-center">
      <div className="flex gap-4">
        <div
          className={
            "size-10 rounded-full bg-[url(/logo_small.png)] bg-contain ml-2"
          }
        ></div>

        <div className="flex flex-col gap-1 mb-6">
          <span className="font-subtitle-medium text-light-neutral-text-high">
            {data.name}
          </span>
          <MediaBadge media={media} showMediaName />
        </div>
      </div>

      <div className="flex gap-1 font-overline-medium text-light-neutral-text-medium">
        <span>۱۲:۳۴:۵۴</span>
        <span>-</span>
        <span>۱۴۰۲/۱۱/۰۱</span>
      </div>
    </div>
  );
};

export default AccDetail;
