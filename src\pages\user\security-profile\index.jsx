import { useState } from "react";
import Input from "../show-profile/components/Input";
import Divider from "components/ui/Divider";
import { Circle, CheckCircle, WarningDiamond } from "@phosphor-icons/react";
import authService from "service/api/authService";
import ErrorCard from "./components/ErrorCard";
import { notification } from "utils/helper";
import { ToastContainer } from "react-toastify";
import SuccessCard from "./components/SuccessCard";
import Loading from "components/ui/Loading";
import { NavLink, useNavigate } from "react-router-dom";
const SecurityProfile = () => {
  const navigate = useNavigate();
  const [oldPass, setOldPass] = useState({ value: "", showValue: false });
  const [newPass, setNewPass] = useState({ value: "", showValue: false });
  const [confirmPass, setConfirmPass] = useState({
    value: "",
    showValue: false,
    isTouched: false,
  });
  const [error, setError] = useState("");
  const [response, setResponse] = useState("");
  const [loading, setLoading] = useState();
  const changePassword = async () => {
    setLoading(true);
    try {
      const response = await authService.changePassword({
        old_password: oldPass.value,
        new_password: newPass.value,
      });
      setError("");
      setResponse(response.data.message);
    } catch (error) {
      setResponse("");
      if (error?.response?.data?.code == 400) {
        setError(error?.response?.data?.message);
      } else {
        notification.error(
          "در حال حاضر درخواست شما قابل اجرا نیست",
          <WarningDiamond size={32} className="text-light-error-text-rest" />,
        );
      }
    }
    setLoading(false);
  };
  // console.log( /^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*#?&])[A-Za-z\d@$!%*#?&]{8,}$/.test("akjdlakjdklasjdklasjdkl173718378#"))
  return (
    <div className="flex flex-col justify-center items-center gap-4 py-6">
      <div className="bg-white w-[600px] rounded-lg shadow-[0px_2px_20px_0px_#0000000D] p-6">
        <div className="flex gap-8 font-body-medium text-light-neutral-text-medium relative mb-6">
          <NavLink
            to="/app/user/profile"
            className="pb-[6px]"
            style={({ isActive }) => {
              return {
                color: isActive ? "#6F5CD1" : "",
                borderBottom: isActive ? "1px solid #6F5CD1" : "",
              };
            }}
          >
            اطلاعات کاربری
          </NavLink>
          <NavLink
            to="/app/user/security"
            className="pb-[6px]"
            style={({ isActive }) => {
              return {
                color: isActive ? "#6F5CD1" : "",
                borderBottom: isActive ? "1px solid #6F5CD1" : "",
              };
            }}
          >
            امنیت
          </NavLink>
          <div className="w-full absolute bottom-0">
            <Divider />
          </div>
        </div>
        <>
          {loading && <Loading />}
          <form
            className="flex flex-col gap-6"
            onSubmit={(e) => e.preventDefault()}
          >
            <Input
              label="رمز عبور قبلی"
              placeholder="رمز عبور قبلی را وارد کنید"
              controller={setOldPass}
              value={oldPass}
              onBlur={() => setResponse("")}
              onFocus={() => setError("")}
            />
            {error && <ErrorCard title={error} />}
            <Divider />

            <Input
              label="رمز عبور جدید"
              placeholder="رمز عبور جدید تعیین کنید"
              controller={setNewPass}
              value={newPass}
              onBlur={() => setResponse("")}
              onFocus={() => setError("")}
            />

            <div className="font-body-medium text-light-neutral-text-medium flex flex-col gap-2">
              <div className="flex items-center gap-2">
                <div>
                  {newPass.value.length > 8 ? (
                    <CheckCircle
                      className="text-light-success-text-rest"
                      size={18}
                    />
                  ) : (
                    <Circle size={18} />
                  )}
                </div>
                <span
                  className={
                    newPass.value.length > 8
                      ? "text-light-success-text-rest"
                      : "text-light-neutral-text-medium"
                  }
                >
                  حداقل ۸ کاراکتر باشد
                </span>
              </div>

              <div className="flex items-center gap-2">
                <div>
                  {/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d#@$!%*#?&]{8,}$/.test(
                    newPass.value,
                  ) ? (
                    <CheckCircle
                      className="text-light-success-text-rest"
                      size={18}
                    />
                  ) : (
                    <Circle size={18} />
                  )}
                </div>
                <span
                  className={
                    /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d#@$!%*#?&]{2,}$/.test(
                      newPass.value,
                    )
                      ? "text-light-success-text-rest"
                      : "text-light-neutral-text-medium"
                  }
                >
                  شامل حرف انگلیسی و عدد باشد
                </span>
              </div>

              <div className="flex items-center gap-2">
                <div>
                  {/(?=.*[a-z])(?=.*[A-Z])/.test(newPass.value) ? (
                    <CheckCircle
                      className="text-light-success-text-rest"
                      size={18}
                    />
                  ) : (
                    <Circle size={18} />
                  )}
                </div>
                <span
                  className={
                    /(?=.*[a-z])(?=.*[A-Z])/.test(newPass.value)
                      ? "text-light-success-text-rest"
                      : "text-light-neutral-text-medium"
                  }
                >
                  شامل حداقل یک حرف بزرگ و یک حرف کوچک باشد
                </span>
              </div>

              <div className="flex items-center gap-2">
                <div>
                  {/[#@$!%*#?&]{1,}/.test(newPass.value) ? (
                    <CheckCircle
                      className="text-light-success-text-rest"
                      size={18}
                    />
                  ) : (
                    <Circle size={18} />
                  )}
                </div>
                <span
                  className={
                    /[#@$!%*#?&]{1,}/.test(newPass.value)
                      ? "text-light-success-text-rest"
                      : "text-light-neutral-text-medium"
                  }
                >
                  شامل حداقل یک نماد مانند #@$!%*#?& باشد
                </span>
              </div>
            </div>

            <Input
              label="تکرار رمز عبور"
              placeholder="رمز عبور را دوباره بنویسید"
              controller={setConfirmPass}
              value={confirmPass}
              onFocus={() => {
                setConfirmPass((l) => {
                  return { ...l, isTouched: false };
                });
                setError("");
              }}
              onBlur={() => {
                setConfirmPass((l) => {
                  return { ...l, isTouched: true };
                });
                setResponse("");
              }}
            />

            {response ? (
              <SuccessCard text={response} />
            ) : (
              confirmPass.isTouched &&
              newPass.value !== confirmPass.value && (
                <ErrorCard
                  title="رمز عبور و تکرار آن با هم یکسان نیستند"
                  text="لطفا رمز عبور و تکرار آن را با دقت وارد نمایید"
                />
              )
            )}

            <div className="font-button-large flex gap-4 [direction:ltr]">
              <button
                className="rounded-lg h-12 w-[200px] bg-light-primary-background-rest text-white"
                onClick={() =>
                  newPass.value == confirmPass.value &&
                  /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[#@$!%*?&])[A-Za-z\d#@$!%*?&]{8,}$/.test(
                    newPass.value,
                  ) &&
                  changePassword()
                }
              >
                ذخیره
              </button>
              <button
                className="rounded-lg h-12 w-[100px] bg-light-neutral-background-medium"
                onClick={() => navigate("/app/dashboard")}
              >
                انصراف
              </button>
            </div>
          </form>
          <ToastContainer />
        </>
      </div>
    </div>
  );
};

export default SecurityProfile;
