import useFetch from "../index";

class Alert {
  verify(data) {
    return useFetch.post("/api/v1/alert/verify/", data);
  }
  new(data) {
    return useFetch.post("/api/v1/alert/", data);
  }
  get() {
    return useFetch.get("/api/v1/alert/");
  }
  getById(id) {
    return useFetch.get(`/api/v1/alert/item/${id}/`);
  }
  remove(id) {
    return useFetch.delete(`/api/v1/alert/item/${id}/`);
  }
  status(id) {
    return useFetch.put(`/api/v1/alert/item/status/${id}/`);
  }
  update(id, data) {
    return useFetch.put(`/api/v1/alert/item/${id}/`, data);
  }
}
export default new Alert();
