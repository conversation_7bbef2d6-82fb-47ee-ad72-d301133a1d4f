import { CaretLeft, CheckCircle, WarningCircle } from "@phosphor-icons/react";
import { CButton } from "components/ui/CButton";
import Divider from "components/ui/Divider";
import MediaBadge from "components/ui/MediaBadge";
import { useNavigate } from "react-router-dom";
import { ToastContainer } from "react-toastify";
import opinion from "service/api/opinion";
import { notification, parseTimeToPersianSummary } from "utils/helper";

const StepThree = ({ setStep, opinionMiningData }) => {
  console.log();
  const navigate = useNavigate();
  const saveOpinionReport = async () => {
    try {
      const res = await opinion.updateWaveAnalysisOpinion(
        opinionMiningData,
        opinionMiningData?.id
      );
      notification.success(
        res?.data?.message,
        <CheckCircle size={25} className="text-light-success-text-rest" />
      );
    } catch (error) {
      notification.error(
        error?.response?.data?.message,
        <WarningCircle size={25} className="text-light-error-text-rest" />
      );
    } finally {
      navigate("/app/opinion-mining/list");
    }
  };
  return (
    <>
      <div className="rounded-md bg-light-neutral-surface-card p-4">
        <div className="flex items-center mb-8">
          <p className="font-overline-large w-28">اطلاعات فیلتر</p>
          <Divider />
        </div>
        <div className="grid gap-8 font-button-small">
          <div className="flex items-center justify-between">
            <p>بستر</p>
            <div>
              <MediaBadge media={"twitter"} showMediaName />
            </div>
          </div>
          <div className="flex items-center justify-between">
            <p>عنوان گزارش</p>
            <div>{opinionMiningData?.title}</div>
          </div>
          {opinionMiningData?.description && (
            <div className="flex items-center justify-between">
              <p>توضیحات گزارش</p>
              <div>{opinionMiningData?.description}</div>
            </div>
          )}
          {opinionMiningData?.type === "new" && (
            <div className="flex items-center justify-between">
              <p>کلمات کلیدی</p>
              <div>
                {opinionMiningData?.query?.platform?.twitter?.hashtags?.join(
                  " OR "
                )}
              </div>
            </div>
          )}
          <div className="flex items-center justify-between">
            <p>بازه زمانی جستجو</p>
            <div>
              {parseTimeToPersianSummary(
                opinionMiningData?.date_range?.from_date
              )}{" "}
              -{" "}
              {parseTimeToPersianSummary(
                opinionMiningData?.date_range?.to_date
              )}
            </div>
          </div>
        </div>

        <div className="flex items-center gap-4 mt-6">
          <div className="w-full" onClick={() => setStep(2)}>
            <CButton role="neutral">مرحله قبل</CButton>
          </div>
          <div onClick={saveOpinionReport} className="w-full">
            <CButton rightIcon={<CaretLeft />}>ثبت و ذخیره</CButton>
          </div>
        </div>
      </div>
      <ToastContainer />
    </>
  );
};

export default StepThree;
