import { useState } from "react";
import formatDate from "../user-list/utils";
import SearchBar from "./components/SearchBar";
import GroupsTable from "./components/GroupsTable";

const GroupsList = () => {
  const oneYearInMillis = 12 * 30 * 24 * 60 * 60 * 1000;
  const [filter, setFilter] = useState({
    q: "",
    register_at: {
      date_from: formatDate(new Date(new Date().getTime() - oneYearInMillis)),
      date_to: formatDate(new Date(new Date().getTime() + 24 * 60 * 60 * 1000)),
    },
    sort_rules: [
      {
        field: "login_at",
        direction: "desc",
      },
      {
        field: "register_at",
        direction: "desc",
      },
    ],
  });

  return (
    <div>
      <div className="px-6 flex flex-col gap-4 h-full">
        <SearchBar filter={filter} setFilter={setFilter} />
        <GroupsTable filter={filter} setFilter={setFilter} />
      </div>
    </div>
  );
};

export default GroupsList;
