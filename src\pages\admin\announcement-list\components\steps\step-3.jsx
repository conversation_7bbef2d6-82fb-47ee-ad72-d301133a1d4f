import { Calendar } from "@phosphor-icons/react";
import { CButton } from "components/ui/CButton";
import RangeDatePicker from "components/ui/RangeDatePicker";
import TimeDropDown from "pages/user/bulletin-create/step-1/TimeDropDown";
import timer from "../../../../../assets/images/announcement/Timer.svg";
import lighting from "../../../../../assets/images/announcement/Lightning.svg";
import { useRef, useState } from "react";

const Step3 = ({ setStep, step, selectedCard, handleCardClick }) => {
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedDateRange, setSelectedDateRange] = useState({
    from: null,
    to: null,
  });
  const datePickerRef = useRef(null);

  const handleDateChange = ({ from, to }) => {
    setSelectedDateRange({ from, to });
  };

  const handleTimeChange = (time) => {
    console.log("Selected time:", time);
  };

  return (
    <div className="flex flex-col gap-2">
      <label className="font-overline-large" htmlFor="">
        زمان انتشار
      </label>
      <div className="flex flex-col items-center gap-5">
        <div
          className={`border bg-[#f7f9fb] rounded-lg w-full h-[80px] flex gap-3 px-2 hover:transition-colors cursor-pointer ${
            selectedCard === 5 ? "!bg-[#B4ABE34D] border-[#4D36BF]" : ""
          }`}
          onClick={() => handleCardClick(5)}
          tabIndex={0}
        >
          <img src={lighting} alt="icon" className="w-12 h-full" />
          <div className="flex flex-col justify-center gap-2">
            <span className="font-body-large font-bold">آنی</span>
            <span className="font-body-medium">
              در این حالت اطلاعیه شما بلافاصله منتشر خواهد شد
            </span>
          </div>
        </div>
        <div
          className={`border bg-[#f7f9fb] rounded-lg w-full h-[80px] flex gap-3 px-2 hover:transition-colors cursor-pointer ${
            selectedCard === 6 ? "!bg-[#B4ABE34D] border-[#4D36BF]" : ""
          }`}
          onClick={() => handleCardClick(6)}
          tabIndex={0}
        >
          <img src={timer} alt="icon" className="w-12 h-full" />
          <div className="flex flex-col justify-center gap-2">
            <span className="font-body-large font-bold">تعیین زمان</span>
            <span className="font-body-medium">
              در این حالت، شما می‌توانید زمان انتشار را مشخص کنید.
            </span>
          </div>
        </div>
      </div>

      {selectedCard === 6 && (
        <>
          <div className="flex flex-wrap pt-2 items-center gap-4 w-full">
            <div className="w-[47%] flex flex-col gap-2">
              <span className="font-body-medium w-[50%]">
                بازه زمانی بررسی:
              </span>
              <div
                className="flex items-center justify-between w-full rounded-md outline outline-1 outline-light-neutral-border-medium-rest bg-light-neutral-background-low px-2 py-[3.5%] text-[#7f7f7f] cursor-pointer"
                onClick={() => setShowDatePicker(true)}
              >
                <div ref={datePickerRef}>
                  <RangeDatePicker
                    onChange={handleDateChange}
                    from={
                      selectedDateRange.from ??
                      new Date(
                        new Date(
                          new Date().setMonth(new Date().getMonth() - 1)
                        ).setHours(0, 0, 0, 0)
                      )
                    }
                    to={selectedDateRange.to}
                  />
                </div>
                <span className="text-left">
                  <Calendar size={18} color="#00000059" />
                </span>
              </div>
            </div>
            <div className="w-[50%] flex flex-col gap-2">
              <span className="font-overline-large">ساعت بررسی:</span>
              <TimeDropDown
                handleChange={handleTimeChange}
                initialHour="12"
                initialMinute="30"
              />
            </div>
          </div>
        </>
      )}
      <div className="flex gap-6 w-full font-button-medium pt-6">
        <div
          className="bg-light-neutral-background-medium cursor-pointer rounded-lg w-full h-10 flex items-center justify-center"
          onClick={() => setStep(step - 1)}
        >
          گام قبل
        </div>
        <CButton type={"submit"} onClick={() => setStep(step + 1)}>
          ذخیره و انتشار
        </CButton>
      </div>
    </div>
  );
};

export default Step3;
