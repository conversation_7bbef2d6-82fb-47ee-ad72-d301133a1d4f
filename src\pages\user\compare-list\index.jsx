import { useEffect, useState } from "react";
import { CButton } from "components/ui/CButton";
import { Info, Plus } from "@phosphor-icons/react";
import { useNavigate } from "react-router-dom";
import { Card } from "components/ui/Card";
import List from "./components/List";
import { useBreadcrumb } from "hooks/useBreadcrumb";
import { useCompareStore } from "store/compareStore";
import Drawer from "components/Drawer";
import CompareInfo from "./components/CompareInfo";

const CompareList = () => {
  const [showMore, setShowMore] = useState(false);
  const navigate = useNavigate();
  useBreadcrumb([{ title: "مقایسه" }]);
  const resetCompare = useCompareStore((state) => state.resetCompare);
  useEffect(() => {
    resetCompare();
  }, []);

  return (
    <div className="p-6 flex flex-col gap-4 h-f">
      <div className="flex justify-end">
        <div className="flex items-center gap-2">
          <div className="[direction:ltr]">
            <CButton
              rightIcon={<Plus />}
              width={145}
              className={"[direction:ltr]"}
              mode="outline"
              onClick={() => navigate("/app/compare/create")}
            >
              مقایسه جدید
            </CButton>
          </div>
          <div className="w-[145px]" onClick={() => setShowMore(true)}>
            <CButton
              mode="outline"
              size="sm"
              role="neutral"
              className="gap-2 !h-10"
            >
              <Info size={17} className="text-light-neutral-text-medium" />
              <p className="text-light-neutral-text-medium font-button-medium">
                مقایسه چیست
              </p>
            </CButton>
          </div>
        </div>
      </div>

      <Card>
        <div className="flex flex-col gap-4 w-full">
          <span className="font-subtitle-medium">لیست مقایسه‌ها</span>
          <div className="grid grid-cols-6 justify-between *:font-overline-medium *:text-light-neutral-text-medium">
            <span>عنوان مقایسه</span>
            <span>نوع مقایسه</span>
            <span>تعداد موارد مقایسه</span>
            <span>بستر مقایسه</span>
            <span>تاریخ ایجاد</span>
            <span>اقدامات</span>
          </div>
          <div>
            <List />
          </div>
        </div>
      </Card>
      {showMore && (
        <Drawer setShowMore={setShowMore}>
          <CompareInfo />
        </Drawer>
      )}
    </div>
  );
};

export default CompareList;
