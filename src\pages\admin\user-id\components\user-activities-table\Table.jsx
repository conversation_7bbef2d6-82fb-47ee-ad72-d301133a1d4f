import { Eye, TrashSimple } from "@phosphor-icons/react";
import ToolTip from "components/ui/ToolTip";
import Status from "components/ui/Status";
import MediaBadge from "components/ui/MediaBadge";

import { Link } from "react-router-dom";

const Table = ({ data }) => {
  return (
    <>
      <div
        className="grid grid-cols-7 hover:bg-light-neutral-surface-highlight py-2 cursor-pointer font-body-medium"
        //   onClick={() => navigate(`/admin/ticket/list/${data?.id}`)}
      >
        <div>نام هشدار تست</div>
        <div className="w-fit">
          <MediaBadge showMediaName media={"telegram"} />
        </div>
        <div>نام شخص خاص</div>
        <div>۱۲:۳۴:۵۴ - ۱۴۰۲/۱۱/۰۱</div>
        <div>۱۲:۳۴:۵۴ - ۱۴۰۲/۱۱/۰۱</div>
        <div>
          <Status status onClick={() => console.log("test")} />
        </div>

        <div className="font-body-medium flex gap-4">
          <Link
            to={`/admin/user/list/${data?.id}`}
            className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
          >
            <ToolTip comp="نمایش جزئیات">
              <Eye size={16} />
            </ToolTip>
          </Link>
          {/* <div className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer">
                <ToolTip comp="ویرایش">
                  <PencilSimpleLine size={16} />
                </ToolTip>
              </div> */}
          <div className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer">
            <ToolTip comp="حذف">
              <TrashSimple size={16} />
            </ToolTip>
          </div>
        </div>
      </div>
    </>
  );
};

export default Table;
