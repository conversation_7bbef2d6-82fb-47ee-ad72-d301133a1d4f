import React, { useContext, useEffect, useMemo, useState } from "react";
import MediaBadge from "components/ui/MediaBadge";
// import Status from "./Status";
import {
  Eye,
  PencilSimpleLine,
  TrashSimple,
  Users,
  WarningCircle,
} from "@phosphor-icons/react";
// import RemoveAlert from "./RemoveAlert";
import { useNavigate } from "react-router-dom";
import ToolTip from "components/ui/ToolTip";
import { notification, toPersianNumber } from "utils/helper.js";
import AuthContext from "context/auth-context.jsx";
import DeletePopUp from "components/ui/DeletePopUp.jsx";
import filter from "service/api/filter.js";

const FilterCard = ({
  media,
  data,
  filter_id,
  setRerender,
  setLoading,
  isGroup = false,
}) => {
  const { profile } = useContext(AuthContext);
  const [showRemove, setShowRemove] = useState(false);
  const navigate = useNavigate();
  const dataToEdit = useMemo(() => {
    const help = JSON.parse(JSON.stringify(data));
    help.platform = help.params.platform;
    help.q = help.params.q;
    help.group = help?.group?.title || "";
    delete help.params;
    return help;
  }, []);

  const [deleteAccess, setDeleteAccess] = useState(true);
  const [editAccess, setEditAccess] = useState(true);

  const formatTimeWithoutSecondsToPersian = (dateString) => {
    const date = new Date(dateString);
    const hours = date.getHours().toString().padStart(2, "0");
    const minutes = date.getMinutes().toString().padStart(2, "0");
    const formattedDate = `${hours}:${minutes} - ${date.toLocaleDateString(
      "fa-IR",
    )}`;
    return toPersianNumber(formattedDate);
  };

  useEffect(() => {
    if (!isGroup) return;
    const userList = data.group?.contact || [];
    const permissions =
      userList.filter((item) => item.user === profile.username)?.[0]
        ?.permission || [];

    if (!permissions.includes("edit")) setEditAccess(false);
    setDeleteAccess(false);
  }, [data]);

  const deleteFilter = async () => {
    setLoading(true);
    try {
      const { data } = await filter.remove(filter_id);
      notification.success(
        data.message,
        <WarningCircle className="text-light-inform-text-rest" />,
      );
      setShowRemove(false);
      if (setRerender) {
        setRerender((l) => !l);
      }
      navigate("/app/filter/list");
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  return (
    <>
      <div className="flex justify-between hover:bg-light-neutral-surface-highlight rounded p-1 items-center">
        <div className="w-[140px] font-body-medium flex items-center">
          {isGroup && <Users className={"ml-1"} />}
          {data?.title}
        </div>
        <div className="w-[140px] flex gap-1">
          {media?.map((item) => (
            <div key={item} className="-mr-2">
              <MediaBadge media={item} />
            </div>
          ))}
        </div>
        <div className="w-40 flex gap-1 font-body-medium text-light-neutral-text-medium">
          <span>
            {data?.created_at
              ? formatTimeWithoutSecondsToPersian(data.created_at)
              : ""}
          </span>
        </div>
        <div className="w-40 flex gap-1 font-body-medium text-light-neutral-text-medium">
          <span>
            {data?.updated_at
              ? formatTimeWithoutSecondsToPersian(data?.updated_at)
              : ""}
          </span>
        </div>

        <div className="w-56">
          <div className="flex gap-6">
            <div
              className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
              onClick={() => navigate(`/app/filter/list/${filter_id}`)}
            >
              <ToolTip comp="نمایش جزئیات">
                <Eye size={16} />
              </ToolTip>
            </div>
            {editAccess && (
              <div
                className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
                onClick={() =>
                  navigate("/app/filter/edit", {
                    state: {
                      ...dataToEdit,
                    },
                  })
                }
              >
                <ToolTip comp="ویرایش">
                  <PencilSimpleLine size={16} />
                </ToolTip>
              </div>
            )}
            {deleteAccess && (
              <div
                className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
                onClick={() => setShowRemove(true)}
              >
                <ToolTip comp="حذف">
                  <TrashSimple size={16} />
                </ToolTip>
              </div>
            )}
          </div>
        </div>
      </div>

      <DeletePopUp
        onClose={() => setShowRemove(false)}
        isOpen={showRemove}
        submitHandler={deleteFilter}
        title="آیا مطمئن هستید؟"
        description="در صورت حذف این فیلتر، امکان بازیابی آن وجود ندارد"
      />
    </>
  );
};

export default FilterCard;
