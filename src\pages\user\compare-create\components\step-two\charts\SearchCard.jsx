import MediaBadge from "components/ui/MediaBadge.jsx";
import PropTypes from "prop-types";

const SearchCard = ({ item, onSelect, platform }) => {
  console.log(item);
  return (
    <li
      className="dropdown-item !justify-start gap-2 cursor-pointer"
      key={item.id || item?.user_id}
      onClick={() => onSelect(item)}
    >
      <div className="relative">
        <div
          className="w-10 h-10 rounded-full ml-2"
          style={{
            backgroundImage: `url(${item.avatar}), url(/logo_small.png)`,
            backgroundRepeat: "no-repeat",
            backgroundPosition: "center center",
            backgroundSize: "contain",
          }}
        ></div>
        <span className="absolute top-6 right-0 rounded-full overflow-hidden !w-[20px] !h-[20px]">
          <MediaBadge media={platform} className={"!h-[20px] !w-[20px]"} />
        </span>
      </div>
      <div className="flex flex-col right">
        <span className="font-body-bold-medium">
          {item.user_title || item?.full_name}
        </span>
        {platform === "news" ? (
          <div>
            <span>{item?.base_url}</span>
          </div>
        ) : (
          <div>
            <span>{item?.user_name}</span>
            <span>@</span>
          </div>
        )}
      </div>
    </li>
  );
};

SearchCard.propTypes = {
  item: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    user_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    logo_image: PropTypes.string,
    title: PropTypes.string,
    user_title: PropTypes.string,
    full_name: PropTypes.string,
    base_url: PropTypes.string,
    user_name: PropTypes.string,
  }).isRequired,
  onSelect: PropTypes.func.isRequired,
  platform: PropTypes.string.isRequired,
};

export default SearchCard;
