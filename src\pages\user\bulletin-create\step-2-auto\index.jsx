import { useEffect, useState } from "react";
import Accordion from "components/ui/Accordion";
import MediaBadge from "components/ui/MediaBadge";
import { useBulletinStore } from "store/bulletinStore";
import { Card } from "components/ui/Card.jsx";
import { Plus, TrashSimple } from "@phosphor-icons/react";
import BulletinSearchBox from "../components/bulletinSearchBox/index.jsx";

import useSearchStore from "store/searchStore";
import MultipleFilter from "./MultipleFilter.jsx";
import { CButton } from "components/ui/CButton.jsx";

const platforms = [
  { id: "pid1", text: "تنظیمات در ایکس (توییتر)", platform: "twitter" },
  { id: "pid2", text: "تنظیمات در تلگرام", platform: "telegram" },
  { id: "pid3", text: "تنظیمات در اینستاگرام", platform: "instagram" },
  { id: "pid4", text: "تنظیمات در سایت‌های خبری", platform: "news" },
];

const media = {
  telegram: [
    { text: "زمان انتشار", value: "date" },
    { text: "میزان بازدید", value: "view" },
  ],
  twitter: [
    { text: "زمان انتشار", value: "date" },
    { text: "تعداد لایک", value: "like" },
    { text: "ریتوئیت", value: "retweet" },
  ],
  instagram: [
    { text: "زمان انتشار", value: "date" },
    { text: "میزان بازدید", value: "view" },
  ],
  news: [
    { text: "زمان انتشار", value: "date" },
    // { text: "تعداد نظرات", value: "comment_count" },
  ],
};

const Step2Auto = () => {
  const { content } = useBulletinStore((state) => state.bulletin);
  const setBulletin = useBulletinStore((state) => state.setBulletin);
  const {
    filters,
    query,
    setQuery,
    setShowFilterList,
    setShowSearchBox,
    setIsFilterListOpen,
  } = useSearchStore();
  const [isOpen, setIsOpen] = useState(null);

  useEffect(() => {
    setShowFilterList(true);
    setShowSearchBox(false);
    setIsFilterListOpen(true);
    setIsOpen(0);
  }, []);

  useEffect(() => {
    const help = JSON.parse(JSON.stringify(content));
    const { sentiment, gender, spam, ...rest } = filters;
    help.filter = rest;
    help.q = query;
    setBulletin({ content: help });
  }, [filters, query]);

  const initialStatistical = {
    sort: "date",
    order: "desc",
    content_count: 10,
  };

  const [statistical, setStatistical] = useState({
    telegram:
      content?.platform?.telegram.length > 0
        ? content?.platform?.telegram
        : [initialStatistical],
    twitter:
      content?.platform?.twitter.length > 0
        ? content?.platform?.twitter
        : [initialStatistical],
    instagram:
      content?.platform?.instagram.length > 0
        ? content?.platform?.instagram
        : [initialStatistical],
    news:
      content?.platform?.news.length > 0
        ? content?.platform?.news
        : [initialStatistical],
  });

  const [availableFilters, setAvailableFilters] = useState({
    telegram: ["date", "view"],
    twitter: ["date", "retweet", "like"],
    instagram: ["date", "view"],
    news: ["date"],
  });

  const handleStatistical = (platform, stat, i) => {
    const help = JSON.parse(JSON.stringify(statistical));
    help[platform][i] = stat;
    setStatistical({ ...help });

    updateStatisticalDataStore(platform, help[platform]);
  };

  const updateStatisticalDataStore = (platform, data) => {
    const help = JSON.parse(JSON.stringify(content));
    help.platform[platform] = data;
    setBulletin({ content: { ...help } });
  };

  const addFilter = (platform, index) => {
    const availableFiltersCopy = JSON.parse(JSON.stringify(availableFilters));
    const usedSorts = content.platform[platform]?.map((item) => item.sort);
    availableFiltersCopy[platform] = availableFilters[platform].filter(
      (item) => !usedSorts.includes(item),
    );
    setAvailableFilters(availableFiltersCopy);

    const statisticalCopy = JSON.parse(JSON.stringify(statistical));
    statisticalCopy[platform] = [
      ...statisticalCopy[platform],
      {
        sort: availableFiltersCopy[platform][0] || "none",
        order: "desc",
        content_count: 10,
      },
    ];
    setStatistical(statisticalCopy);

    updateStatisticalDataStore(platform, statisticalCopy[platform]);
  };

  const deleteFilter = (platform, index) => {
    const availableFiltersCopy = JSON.parse(JSON.stringify(availableFilters));
    availableFiltersCopy[platform] = [
      ...new Set([
        ...availableFiltersCopy[platform],
        statistical?.[platform]?.[index]?.sort ===
        availableFiltersCopy[platform][
          availableFiltersCopy[platform].length - 1
        ]
          ? statistical?.[platform]?.[index - 1]?.sort || "none"
          : statistical?.[platform]?.[index]?.sort || "none",
      ]),
    ];
    setAvailableFilters(availableFiltersCopy);

    const statisticalCopy = JSON.parse(JSON.stringify(statistical));
    statisticalCopy[platform] = statisticalCopy[platform].filter(
      (_, i) => i !== index,
    );
    setStatistical(statisticalCopy);

    updateStatisticalDataStore(platform, statisticalCopy[platform]);
  };

  const getNonEmptyKeys = (obj) => {
    return Object.keys(obj).filter(
      (key) => Array.isArray(obj[key]) && obj[key].length > 0,
    );
  };

  const [activePlatforms, setActivePlatforms] = useState(
    getNonEmptyKeys(content.platform) || ["twitter"],
  );
  const handlePlatformSelect = (checked, platform) => {
    if (checked) {
      setActivePlatforms((prev) => [...prev, platform]);
      updateStatisticalDataStore(platform, statistical[platform]);
    } else {
      setActivePlatforms((prev) => prev.filter((p) => p !== platform));
      updateStatisticalDataStore(platform, []);
    }
  };

  return (
    <>
      <BulletinSearchBox query={query} setQuery={setQuery} readOnly={false} />
      <Card className="px-6 bg-white rounded-lg [direction:rtl]">
        <div className="[direction:rtl] w-full divide-y">
          {platforms.map((item, index) => (
            <div
              onClick={() => setIsOpen(index)}
              className="flex gap-4"
              key={item.id}
            >
              <div className="pt-7">
                <input
                  type="checkbox"
                  className="size-[18px]"
                  onChange={(e) => {
                    handlePlatformSelect(e.target.checked, item.platform);
                  }}
                  checked={activePlatforms.includes(item.platform)}
                />
              </div>
              <div className="w-full">
                <Accordion
                  isOpen={index === isOpen}
                  showBox={false}
                  Header={
                    <div className="flex items-center gap-6">
                      <div className="flex items-center gap-2">
                        <div>{<MediaBadge media={item.platform} />}</div>
                        <div className="font-body-medium">{item.text}</div>
                      </div>
                    </div>
                  }
                >
                  {content.platform?.[item.platform].length > 0 ? (
                    <>
                      {content.platform?.[item.platform]?.map((x, i, arr) => (
                        <div
                          className="flex gap-6 mt-2"
                          key={item.platform + x.sort + i}
                        >
                          <div className="w-8/12">
                            <MultipleFilter
                              platform={item.platform}
                              handleChange={(platform, stat) => {
                                handleStatistical(platform, stat, i);
                              }}
                              value={x}
                              subsets={media[item.platform].filter((m) =>
                                availableFilters[item.platform]?.includes(
                                  m.value,
                                ),
                              )}
                              disabled={i < arr.length - 1}
                            />
                          </div>
                          {arr.length !== 1 && (
                            <div
                              className="flex items-center cursor-pointer"
                              onClick={() => deleteFilter(item.platform, i)}
                            >
                              <TrashSimple size={20} color={"red"} />
                            </div>
                          )}
                          {i === arr.length - 1 &&
                            arr.length < media[item.platform].length && (
                              <div
                                className=""
                                onClick={() => addFilter(item.platform, i)}
                              >
                                <CButton
                                  type="button"
                                  role="neutral"
                                  rightIcon={<Plus size={16} />}
                                >
                                  افزودن
                                </CButton>
                              </div>
                            )}
                        </div>
                      ))}
                    </>
                  ) : (
                    <div className="flex gap-6 mt-2">
                      <div className="w-8/12">
                        <MultipleFilter
                          platform={item.platform}
                          handleChange={(platform, stat) => {
                            handleStatistical(platform, stat, 0);
                          }}
                          value={{
                            sort: "date",
                            order: "desc",
                            content_count: 10,
                          }}
                          subsets={media[item.platform].filter((x) =>
                            availableFilters[item.platform]?.includes(x.value),
                          )}
                          availableFilters={availableFilters}
                          disabled={false}
                        />
                      </div>

                      {item.platform !== "news" && (
                        <div
                          className=""
                          onClick={() => addFilter(item.platform, 0)}
                        >
                          <CButton
                            type="button"
                            role="neutral"
                            rightIcon={<Plus size={16} />}
                          >
                            افزودن
                          </CButton>
                        </div>
                      )}
                    </div>
                  )}
                </Accordion>
              </div>
            </div>
          ))}
        </div>
      </Card>
    </>
  );
};

export default Step2Auto;
