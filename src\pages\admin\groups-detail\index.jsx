import { useState } from "react";
import formatDate from "../user-list/utils";
import SearchBar from "../groups-list/components/SearchBar";
import GroupsTable from "../groups-list/components/GroupsTable";
import GroupInfo from "./components/GroupInfo";
import ChangeList from "./components/ChangeList";
import GroupMembers from "./components/GroupMembers";
import GpViewSearchbar from "./components/GpViewSearchbar";
import Alert from "components/ui/Alert";

const GroupsDetail = () => {
  const [isActivate, setIsActivate] = useState(false);
  const oneYearInMillis = 12 * 30 * 24 * 60 * 60 * 1000;
  const [filter, setFilter] = useState({
    q: "",
    register_at: {
      date_from: formatDate(new Date(new Date().getTime() - oneYearInMillis)),
      date_to: formatDate(new Date(new Date().getTime() + 24 * 60 * 60 * 1000)),
    },
    sort_rules: [
      {
        field: "login_at",
        direction: "desc",
      },
      {
        field: "register_at",
        direction: "desc",
      },
    ],
  });

  const handleGpActivate = (isGroupActive) => {
    setIsActivate(isGroupActive);
  };

  return (
    <div className="flex w-full px-5 gap-5">
      <div className="flex w-4/6 flex-col h-full">
        {isActivate && (
          <div className="pb-3">
            <Alert>
              {
                "این گروه غیرفعال شده است و امکان ویرایش و تغییرات در آن وجود ندارد. برای اعمال تغییرات گروه را فعال کنید"
              }
            </Alert>
          </div>
        )}
        <GpViewSearchbar filter={filter} setFilter={setFilter} />
        <GroupMembers filter={filter} setFilter={setFilter} />
      </div>
      <div className="w-2/6">
        <div className="flex flex-col gap-5">
          <GroupInfo onActive={handleGpActivate} />
          <ChangeList />
        </div>
      </div>
    </div>
  );
};

export default GroupsDetail;
