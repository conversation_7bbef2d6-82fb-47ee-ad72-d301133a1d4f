import useFetch from "../index";

class AdvanceSearchReq {
  /**
   *
   * @param data
   * @param abortController
   * @param query -> {process_range: enum[hourly, daily, weekly], cloud_type: enum[keywords, hashtags]}
   * @returns {Promise<axios.AxiosResponse<any>>}
   */
  search(data = {}, abortController, query = {}) {
    let Options = {};
    if (abortController) Options.signal = abortController.signal;
    return useFetch.post(
      `/api/v1/search/?${new URLSearchParams(query).toString()}`,
      data,
      Options,
    );
  }

  getResources(data, abortController) {
    let Options = {};
    if (abortController) Options.signal = abortController.signal;
    return useFetch.post("/api/v1/search/query/external/dv1/", data, Options);
  }
}
export default new AdvanceSearchReq();
