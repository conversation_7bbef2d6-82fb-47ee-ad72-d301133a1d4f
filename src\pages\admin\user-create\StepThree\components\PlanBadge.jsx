const PlanBadge = ({ icon: Icon, title, isSelected, onSelect }) => {
  return (
    <div
      className={`flex items-center justify-center cursor-pointer w-[106px] h-[106px] rounded-md select-none border ${
        isSelected
          ? "bg-light-primary-background-highlight border-light-primary-border-rest"
          : "bg-light-neutral-surface-low border-light-neutral-border-low-rest"
      }`}
      onClick={onSelect}
    >
      <div>{Icon && <Icon />}</div>
      <p className="font-body-medium">{title}</p>
    </div>
  );
};

export default PlanBadge;
