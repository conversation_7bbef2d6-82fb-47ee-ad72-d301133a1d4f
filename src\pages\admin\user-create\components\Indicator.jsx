import Container from "./Container";
import PropTypes from "prop-types";

const Indicator = ({ state, step, children, title }) => {
  return (
    <Container state={state} step={step} title={title}>
      {children}
    </Container>
  );
};

Indicator.propTypes = {
  state: PropTypes.number,
  step: PropTypes.number,
  children: PropTypes.node,
  title: PropTypes.string,
};

export default Indicator;
