import React, { useEffect, useRef } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import HighchartsMore from "highcharts/highcharts-more";

HighchartsMore(Highcharts);

const GaugeRateChart = ({ data }) => {
  const chartRef = useRef(null);

  const options = {
    chart: {
      type: "gauge",
      plotBackgroundColor: null,
      plotBackgroundImage: null,
      plotBorderWidth: 0,
      plotShadow: false,
      margin: [0, 0, 0, 0],
      padding: [0, 0, 0, 0],
      height: 250,
      width: 250,
    },
    accessibility: {
      enabled: false,
    },
    credits: {
      enabled: false,
    },
    title: {
      text: null,
    },
    pane: {
      startAngle: -90,
      endAngle: 90,
      background: null,
      center: ["50%", "65%"],
      size: "80%",
    },
    yAxis: {
      min: 0,
      max: 200,
      tickPixelInterval: 72,
      tickPosition: "inside",
      tickColor: Highcharts.defaultOptions.chart.backgroundColor || "#FFFFFF",
      tickLength: 20,
      tickWidth: 0,
      minorTickInterval: null,
      labels: {
        enabled: false,
      },
      lineWidth: 0,
      plotBands: [
        {
          from: 0,
          to: 130,
          color: "#1CB0A5",
          thickness: 20,
          borderRadius: "50%",
        },
        {
          from: 150,
          to: 200,
          color: "#C5CBD0",
          thickness: 20,
          borderRadius: "50%",
        },
        {
          from: 120,
          to: 160,
          color: "#E0526A",
          thickness: 20,
        },
      ],
      margin: [0, 0, 0, 0],
      padding: [0, 0, 0, 0],
    },
    tooltip: {
      enabled: false,
    },
    series: [
      {
        name: "Speed",
        data: [50],
        tooltip: {
          valueSuffix: " km/h",
        },
        dataLabels: {
          enabled: false,
        },
        dial: {
          radius: "70%",
          backgroundColor: "gray",
          baseWidth: 12,
          baseLength: "0%",
          rearLength: "0%",
        },
        pivot: {
          backgroundColor: "gray",
          radius: 6,
        },
      },
    ],
  };

  return (
    <HighchartsReact highcharts={Highcharts} options={options} ref={chartRef} />
  );
};

export default GaugeRateChart;
