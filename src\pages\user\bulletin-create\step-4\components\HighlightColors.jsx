import { BASE_HIGHLIGHT } from "constants/bulletin-colors";
import HighlightBaseCard from "./HighlightBaseCard.jsx";

const HighlightColors = ({ handleColor }) => {
  return (
    <div className="flex flex-col gap-2  w-[152px] h-[120px]">
      <div
        className="bg-light-neutral-background-medium rounded-lg flex items-center justify-center py-[5px] cursor-pointer"
        onClick={() => handleColor("auto")}
      >
        <span className="font-button-small">بدون رنگ</span>
      </div>
      <div className="grid grid-cols-5 gap-2 [direction:ltr]">
        {BASE_HIGHLIGHT.map((color, i) => (
          <div onClick={() => handleColor(color)} key={i}>
            <HighlightBaseCard color={color} />
          </div>
        ))}
      </div>
    </div>
  );
};

export default HighlightColors;
