import { CaretLeft, Check } from "@phosphor-icons/react";
import PropTypes from "prop-types";
import { useEffect, useRef, useState } from "react";

const CompareCount = ({ onChange }) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [selectedItems, setSelectedItems] = useState([]);
  const dropdownRef = useRef(null);

  const dropdownMap = {
    all: "همه",
    two: "۲",
    three: "۳",
    four: "۴",
  };

  const availableItems = [
    {
      value: "all",
      label: "همه",
    },
    {
      value: "two",
      label: "۲",
    },
    {
      value: "three",
      label: "۳",
    },
    {
      value: "four",
      label: "۴",
    },
  ];

  const getSelectedItemsLabel = () => {
    if (
      selectedItems &&
      selectedItems?.length > 0 &&
      selectedItems?.length !== availableItems.length
    ) {
      return selectedItems?.map((item) => dropdownMap[item] || item).join(", ");
    }
    return "همه";
  };

  const handleChange = (item) => {
    if (item.value === "all") {
      setSelectedItems(["all"]);
      onChange(["all"]);
    } else {
      setSelectedItems((prevItems) => {
        // If 'all' is currently selected, replace it with just this item
        if (prevItems.includes("all")) {
          const newItems = [item.value];
          onChange(newItems);
          return newItems;
        }

        // Toggle the item: remove if present, add if not
        const newItems = prevItems.includes(item.value)
          ? prevItems.filter((val) => val !== item.value)
          : [...prevItems, item.value];

        onChange(newItems);
        return newItems;
      });
    }
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowDropdown(false);
      }
    };

    if (showDropdown) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showDropdown]);

  return (
    <div>
      <div className="flex items-center gap-3 font-button-medium">
        <span
          className="flex gap-2 items-center cursor-pointer hover:bg-gray-200 transition duration-200 rounded-lg p-2"
          onClick={() => setShowDropdown(!showDropdown)}
        >
          تعداد موارد مقایسه:
          <span className="text-[#7f7f7f]">{getSelectedItemsLabel()}</span>
          <CaretLeft />
        </span>
      </div>

      {showDropdown && (
        <div
          className="bg-white h-auto font-body-small w-[12rem] shadow-md absolute z-[10] p-4 rounded"
          ref={dropdownRef}
        >
          {availableItems.map((item) => (
            <label
              key={item.value}
              onClick={() => handleChange(item)}
              className="flex justify-between items-center gap-2 py-2 px-2 rounded hover:bg-light-neutral-surface-highlight"
            >
              {item.label}
              {selectedItems.includes(item.value) && (
                <Check size={16} className={"inline-flex"} />
              )}
            </label>
          ))}
        </div>
      )}
    </div>
  );
};

export default CompareCount;

CompareCount.propTypes = {
  onChange: PropTypes.func,
};
