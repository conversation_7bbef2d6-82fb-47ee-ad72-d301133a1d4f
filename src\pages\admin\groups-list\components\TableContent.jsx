import { toPersian<PERSON><PERSON>ber } from "utils/helper";
import Paginate from "components/ui/Paginate";
import Empty from "pages/admin/user-list/components/management-table/Empty";
import UserInviteList from "./UserInviteList";
import UserInviteInfo from "./UserInviteInfo";

const TableContent = ({
  table,
  tableItemsLimit,
  setTableItemsLimit,
  page,
  setPage,
  filter,
  setFilter,
  dataCount = 100,
  per_page = 10,
  handleAddToSecondTable,
}) => {
  return (
    <div className="flex flex-col !h-full w-full">
      <UserInviteList filter={filter} setFilter={setFilter}>
        {table.length > 0 ? (
          table?.map((item) => (
            <UserInviteInfo
              key={item.id}
              data={item}
              onAddToSecondTable={handleAddToSecondTable}
            />
          ))
        ) : (
          <Empty />
        )}
      </UserInviteList>
      <div className="flex !items-center justify-between mt-7">
        <div className="flex items-center gap-3">
          <p className="font-body-medium">نمایش تعداد رکورد در صفحه:</p>
          <div className="flex items-center gap-4">
            <div
              className={`rounded-md cursor-pointer font-body-small w-8 h-8 flex items-center justify-center ${
                tableItemsLimit == 20
                  ? "bg-light-primary-background-highlight"
                  : ""
              }`}
              onClick={() => setTableItemsLimit(20)}
            >
              {toPersianNumber("20")}
            </div>
            <div
              className={`rounded-md cursor-pointer font-body-small w-8 h-8 flex items-center justify-center ${
                tableItemsLimit == 50
                  ? "bg-light-primary-background-highlight"
                  : ""
              }`}
              onClick={() => setTableItemsLimit(50)}
            >
              {toPersianNumber("50")}
            </div>
            <div
              className={`rounded-md cursor-pointer font-body-small w-8 h-8 flex items-center justify-center ${
                tableItemsLimit == 100
                  ? "bg-light-primary-background-highlight"
                  : ""
              }`}
              onClick={() => setTableItemsLimit(100)}
            >
              {toPersianNumber("100")}
            </div>
          </div>
        </div>
        <div className="flex items-center">
          <Paginate
            page={page}
            setPage={setPage}
            dataCount={dataCount}
            per_page={per_page}
          />
        </div>
      </div>
    </div>
  );
};

export default TableContent;
