import Profile from "./profile/index.jsx";
import { useCompareStore } from "store/compareStore";
import TopicCompare from "./topic";
import PropTypes from "prop-types";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";

const StepTwo = ({ isEdit = false }) => {
  const { type } = useCompareStore((state) => state.compare);
  const breadcrumbList = [
    { title: "مقایسه", link: "/app/compare/list" },
    { title: "نمایش مقایسه" },
  ];
  useBreadcrumb(breadcrumbList);
  // useEffect(() => {
  //   setBreadcrumb(breadcrumbList);
  // }, [alertData]);

  return (
    <>
      {type === "profile" ? (
        <Profile isEdit={isEdit} />
      ) : type === "topic" ? (
        <TopicCompare isEdit={isEdit} />
      ) : (
        ""
      )}
    </>
  );
};

StepTwo.propTypes = {
  isEdit: PropTypes.bool,
};

export default StepTwo;
