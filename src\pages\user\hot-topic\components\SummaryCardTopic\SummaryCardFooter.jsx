import { useState } from "react";
import {
  DotsThreeOutlineVertical,
  FrameCorners,
  FilePdf,
  Planet,
} from "@phosphor-icons/react";

import Drawer from "components/Drawer";
import ShowMoreDetail from "../ShowMoreDetail";
import DropDown from "./DropDown";
const SummaryCardFooter = ({ media, data, lowWidth = false }) => {
  const [showMore, setShowMore] = useState(false);
  return (
    <>
      <div className="flex p-4 justify-between w-full">
        <input type="checkbox" className="accent-[#6F5CD1] size-4" />

        <div className="flex gap-6">
          {!lowWidth && (
            <>
              <a
                href={data.url}
                target="_blank"
                className="flex items-center gap-2"
              >
                <span className="font-overline-medium text-light-neutral-text-high">
                  مشاهده در بستر
                </span>
                <Planet className="size-[14px]" />
              </a>

              <div className="flex items-center gap-2">
                <span className="font-overline-medium text-light-neutral-text-high">
                  افزودن به بولتن
                </span>
                <FilePdf className="size-[14px]" />
              </div>
            </>
          )}

          <div
            className="flex items-center gap-2 cursor-pointer"
            onClick={() => setShowMore(true)}
          >
            <span className="font-overline-medium text-light-neutral-text-high">
              مشاهده اطلاعات
            </span>
            <FrameCorners className="size-[14px]" />
          </div>

          <DropDown data={data} lowWidth={lowWidth}>
            <div className="flex items-center gap-2 bg-light-neutral-background-medium p-[2px] rounded-sm">
              <DotsThreeOutlineVertical className="size-[14px]" />
            </div>
          </DropDown>
        </div>
      </div>

      {showMore && (
        <Drawer setShowMore={setShowMore}>
          <ShowMoreDetail media={media} data={data} />
        </Drawer>
      )}
    </>
  );
};

export default SummaryCardFooter;
