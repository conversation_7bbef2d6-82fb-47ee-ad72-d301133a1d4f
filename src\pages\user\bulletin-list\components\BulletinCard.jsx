import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { parseTimeToPersian, toPersianNumber } from "utils/helper";
import MediaBadge from "components/ui/MediaBadge";
import Status from "components/ui/Status";
import {
  CheckCircle,
  Eye,
  PencilSimpleLine,
  ShareNetwork,
  TrashSimple,
} from "@phosphor-icons/react";
import ToolTip from "components/ui/ToolTip";
import StepState from "components/StepState";
import bulletin from "service/api/bulletin";
import { notification } from "utils/helper.js";
import Popup from "components/ui/PopUp";
import DeletePopUp from "components/ui/DeletePopUp.jsx";

const BulletinCard = ({ data, setRerender, setLoading }) => {
  const [isDeletePopUpOpen, setIsDeletePopUpOpen] = useState(false);
  const [selectedId, setSelectedId] = useState(null);
  const [showRemove, setShowRemove] = useState(false);
  const [isSharePopupOpen, setIsSharePopupOpen] = useState(false);
  const navigate = useNavigate();

  const changeStatus = async (id) => {
    setLoading(true);
    try {
      await bulletin.status(id);
      setRerender((l) => !l);
    } catch (error) {
      notification.success(
        error?.response?.data?.message,
        <CheckCircle className="text-light-success-text-rest" />,
      );
    }
    setLoading(false);
  };

  const deleteBulletin = async () => {
    setLoading(true);
    try {
      await bulletin.remove(selectedId);
      notification.success(
        "بولتن مورد نظر حذف شد.",
        <CheckCircle className="text-light-success-text-rest" size={26} />,
      );
      if (setRerender) {
        setRerender((l) => !l);
      }
    } catch (error) {
      notification.success(
        error?.response?.data?.message,
        <CheckCircle className="text-light-success-text-rest" />,
      );
    }
    setLoading(false);
  };

  const handleDeleteClick = (id) => {
    setSelectedId(id);
    setIsDeletePopUpOpen(true);
  };

  return (
    <>
      <div
        className={
          data?.bulletin_state === "draft" || !data?.is_active
            ? "text-light-neutral-text-low grid grid-cols-9 hover:bg-light-neutral-surface-highlight rounded p-1 items-center"
            : `grid grid-cols-9 hover:bg-light-neutral-surface-highlight rounded p-1 items-center`
        }
      >
        <div className="font-body-medium">{data.title}</div>
        <div className="flex font-body-medium">
          {data?.bulletin_type === "periodic" ? "دوره‌ای" : "موردی"}
        </div>
        <div className="font-body-medium">
          <p>{toPersianNumber(data?.content_count)}</p>
        </div>
        <div
          className={`flex justify-end items-center font-body-medium [direction:ltr] ${
            data?.bulletin_state === "draft" || !data?.is_active
              ? "opacity-50"
              : ""
          }`}
        >
          {Object.keys(data?.platforms)
            ?.filter((platform) => data?.platforms[platform]?.length > 0)
            ?.map((item) => (
              <div key={item} className="-mr-1">
                <MediaBadge media={item} />
              </div>
            ))}
        </div>
        <div className="font-body-medium">
          <p>
            {data.created_at
              ? parseTimeToPersian(data.created_at).split("-")[1]
              : ""}
          </p>
        </div>
        <div className="font-body-medium">
          <p>
            {data.updated_at
              ? parseTimeToPersian(data.updated_at).split("-")[1]
              : ""}
          </p>
        </div>
        <div
          className={`w-40 flex gap-1 font-body-medium ${
            data?.bulletin_state === "draft" || !data?.is_active
              ? "opacity-60"
              : ""
          }`}
        >
          <span>
            <StepState
              title={
                data?.bulletin_state === "draft"
                  ? "پیش‌نویس"
                  : data?.bulletin_state === "in_progress"
                    ? "در‌حال‌ایجاد"
                    : data?.bulletin_state === "created"
                      ? "ایجاد‌شده"
                      : ""
              }
              state={data?.bulletin_state}
            />
          </span>
        </div>
        {data?.bulletin_type !== "one_time" &&
        data?.bulletin_state !== "draft" ? (
          <div
            className={`w-40 flex gap-1 font-body-medium text-light-primary-text-rest cursor-pointer ${
              data?.bulletin_state === "draft" || !data?.is_active
                ? "opacity-50"
                : ""
            }`}
          >
            <Status
              status={data.is_active}
              onClick={() => changeStatus(data?.id)}
            />
          </div>
        ) : (
          <div>-</div>
        )}

        <div className="w-56">
          <div className="flex gap-4">
            <Link
              to={`/app/bulletin/edit/${data?.id}`}
              className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
            >
              <ToolTip comp="ویرایش">
                <PencilSimpleLine size={16} />
              </ToolTip>
            </Link>
            <div
              className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
              onClick={() => {
                handleDeleteClick(data?.id);
                setShowRemove(true);
              }}
            >
              <ToolTip comp="حذف">
                <TrashSimple size={16} />
              </ToolTip>
            </div>
            <div
              className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
              onClick={() => navigate(`/app/bulletin/list/${data?.id}`)}
            >
              <ToolTip comp="نمایش جزئیات">
                <Eye size={16} />
              </ToolTip>
            </div>
            {data?.files?.filename && (
              <div
                className="bg-light-neutral-background-medium rounded-[4px] p-[3px] flex items-center justify-center cursor-pointer"
                onClick={() => setIsSharePopupOpen(true)}
              >
                <ToolTip comp="اشتراک گذاری">
                  <ShareNetwork size={16} />
                </ToolTip>
              </div>
            )}
          </div>
        </div>
      </div>
      <Popup
        isOpen={isSharePopupOpen}
        onClose={() => setIsSharePopupOpen(!isSharePopupOpen)}
        title={"اشتراک‌گذاری"}
        isSharePopup
        width="352px"
        currentId={data?.id}
        urlToShare={data?.files?.filename}
        shareShortenedLink={data?.files?.filename.split("/").pop()}
      />

      <DeletePopUp
        onClose={() => setShowRemove(false)}
        isOpen={showRemove}
        submitHandler={deleteBulletin}
        title="آیا مطمئن هستید؟"
        description="در صورت حذف این بولتن، امکان بازیابی آن وجود ندارد"
      />
    </>
  );
};

export default BulletinCard;
