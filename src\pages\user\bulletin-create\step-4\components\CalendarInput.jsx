import { useBulletinStore } from "store/bulletinStore.js";
import CustomCalendar from "components/CustomCalendar";
import PropTypes from "prop-types";
import { toPersianNumber } from "utils/helper.js";
import { useEffect } from "react";
import { DateObject } from "react-multi-date-picker";
import persian from "react-date-object/calendars/persian";
import persian_en from "react-date-object/locales/persian_en";

const CalendarInput = ({ name = "", title = "" }) => {
  const setBulletin = useBulletinStore((state) => state.setBulletin);
  const { metadata } = useBulletinStore((state) => state.bulletin);

  const handleChange = (date) => {
    const help = JSON.parse(JSON.stringify(metadata));
    help[name] = toPersianNumber(date?.persian) || "";
    setBulletin({ metadata: { ...help } });
  };

  useEffect(() => {
    const help = JSON.parse(JSON.stringify(metadata));
    help[name] =
      toPersianNumber(new DateObject().convert(persian, persian_en).format()) ||
      "";
    setBulletin({ metadata: { ...help } });
  }, []);

  return (
    <CustomCalendar
      initialValue={metadata?.[name]}
      title={title}
      onChange={handleChange}
    />
  );
};

CalendarInput.propTypes = {
  name: PropTypes.string,
  title: PropTypes.string,
};

export default CalendarInput;
