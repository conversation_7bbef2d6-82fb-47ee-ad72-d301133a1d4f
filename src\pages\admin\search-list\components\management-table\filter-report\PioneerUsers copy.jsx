import PioneerUsersTable from "./PioneerUsersTable";

const PioneerUsers = () => {
  const data = [
    {
      id: 1,
      title: "منبع 1",
      accountId: "@Parham-ab",
      connections: 10,
      age: 25,
    },
    {
      id: 2,
      title: "منبع 2",
      accountId: "@Parham-ab",
      connections: 15,
      age: 30,
    },
    {
      id: 3,
      title: "منبع 3",
      accountId: "@Parham-ab",
      connections: 5,
      age: 20,
    },
    {
      id: 4,
      title: "منبع 3",
      accountId: "@Parham-ab",
      connections: 5,
      age: 20,
    },
    {
      id: 5,
      title: "منبع 3",
      accountId: "@Parham-ab",
      connections: 5,
      age: 20,
    },
    {
      id: 6,
      title: "منبع 3",
      accountId: "@Parham-ab",
      connections: 5,
      age: 20,
    },
    {
      id: 7,
      title: "منبع 3",
      accountId: "@Parham-ab",
      connections: 5,
      age: 20,
    },
  ];

  return (
    <div
      className="bg-light-neutral-surface-card rounded-lg p-3 h-full"
      style={{
        boxShadow: "0px 2px 20px 0px #0000000D",
      }}
    >
      <p className="font-subtitle-large mb-7">
        کاربران پیشرو در شبکه انتشار محتوا
      </p>
      <PioneerUsersTable data={data} />
    </div>
  );
};

export default PioneerUsers;
