import { useBulletinStore } from "store/bulletinStore.js";
import UploadBox from "components/ui/UploadBox.jsx";
import PropTypes from "prop-types";

const UploadBoxInput = ({ name = "", title = "", description = "" }) => {
  const setBulletin = useBulletinStore((state) => state.setBulletin);
  const { metadata } = useBulletinStore((state) => state.bulletin);

  const handleChange = (data) => {
    const help = JSON.parse(JSON.stringify(metadata));
    help[name] = data?.data?.image || undefined;
    setBulletin({ metadata: { ...help } });
  };

  return (
    <UploadBox
      initialPreview={metadata?.[name]}
      onChange={handleChange}
      name={name}
      title={title}
      description={description}
    />
  );
};

UploadBoxInput.propTypes = {
  name: PropTypes.string,
  title: PropTypes.string,
  description: PropTypes.string,
};

export default UploadBoxInput;
