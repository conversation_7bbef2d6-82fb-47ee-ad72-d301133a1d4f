import "./style.css";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";
import { useNavigate } from "react-router-dom";
import SearchBar from "./components/SearchBar";
import { useState } from "react";
import formatDate from "pages/admin/user-list/utils.js";
import ManagementTable from "./components/management-table";
import TopMenu from "./components/top-menu";
import CompareReport from "./components/compare-report";

function AdminCompareList() {
  const navigate = useNavigate();
  const breadcrumbList = [{ title: "مقایسه‌ها" }];
  useBreadcrumb(breadcrumbList);

  const [loading, setLoading] = useState(false);

  const [activeTab, setActiveTab] = useState("table"); // [table | report]

  const RULES = {
    REGISTER_AT: "register_at",
    LOGIN_AT: "login_at",
    ROLE: "role",
    USERNAME: "username",
    PHONE: "phone",
    EMAIL: "email",
  };
  const DIRECTION = {
    ASC: "asc",
    DESC: "desc",
  };

  // const [filter, setFilter] = useState({
  //   q: "",
  //   register_at: {
  //     date_from: formatDate(
  //       new Date(
  //         new Date(new Date().getTime() - sixMonthInMillis).setHours(0, 0, 0, 0)
  //       )
  //     ),
  //     date_to: formatDate(new Date()),
  //   },
  //   login_at: {
  //     date_from: formatDate(
  //       new Date(
  //         new Date(new Date().getTime() - sixMonthInMillis).setHours(0, 0, 0, 0)
  //       )
  //     ),
  //     date_to: formatDate(new Date()),
  //   },
  //   sort_rules: [{ field: RULES.REGISTER_AT, direction: DIRECTION.DESC }],
  //   page: 1,
  //   page_size: 10,
  // });
  const oneYearInMillis = 12 * 30 * 24 * 60 * 60 * 1000;
  const [filter, setFilter] = useState({
    q: "",
    register_at: {
      date_from: formatDate(new Date(new Date().getTime() - oneYearInMillis)),
      date_to: formatDate(new Date(new Date().getTime() + 24 * 60 * 60 * 1000)),
    },
    sort_rules: [
      {
        field: "login_at",
        direction: "desc",
      },
      {
        field: "register_at",
        direction: "desc",
      },
    ],
  });

  return (
    <div className="px-6 flex flex-col gap-4 h-full">
      <TopMenu
        filter={filter}
        setFilter={setFilter}
        activeTab={activeTab}
        setActiveTab={setActiveTab}
      />

      {activeTab === "table" && (
        <>
          <SearchBar filter={filter} setFilter={setFilter} loading={loading} />

          <ManagementTable filter={filter} setFilter={setFilter} />
        </>
      )}

      {activeTab === "report" && <CompareReport filter={filter} />}
    </div>
  );
}

export default AdminCompareList;
