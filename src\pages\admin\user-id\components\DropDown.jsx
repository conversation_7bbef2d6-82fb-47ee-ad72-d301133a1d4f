import { useState } from "react";
import { CaretDown, Check } from "@phosphor-icons/react";
import PropTypes from "prop-types";

const DropDown = ({
  subsets = [],
  handleChange,
  initialValue,
  title,
  size = "sm",
  loading = false,
  setEmptyAtFirst = false,
  placeholder,
}) => {
  const [open, setOpen] = useState(false);
  const [selected, setSelected] = useState(initialValue || subsets?.[0] || {});

  if (subsets.length > 0 && setEmptyAtFirst)
    subsets = [{ value: "", text: "انتخاب کنید" }, ...subsets];

  return (
    <div className={"flex flex-col gap-1"}>
      {title && <p className="font-overline-large pb-1">{title}</p>}
      <button
        onBlur={() => setOpen(false)}
        className="relative w-full"
        type="button"
      >
        <div
          className={`flex gap-4 items-center justify-between p-2 ${
            size === "md" ? "py-3" : size === "lg" ? "py-4" : ""
          } rounded-lg bg-white border border-light-neutral-border-medium-rest w-full`}
          style={{
            border: open && "1px solid #9198AD",
          }}
          onClick={() => setOpen((l) => !l)}
        >
          {loading ? (
            <span className="text-light-neutral-text-medium">
              در حال بارگذاری...
            </span>
          ) : subsets.length === 0 ? (
            "موردی یافت نشد"
          ) : selected?.text ? (
            <span className="flex gap-1 font-body-medium">
              {selected?.icon} {selected?.text}
            </span>
          ) : (
            <span className="text-light-neutral-text-medium">
              {placeholder}
            </span>
          )}

          <CaretDown
            style={{
              transform: open ? "rotate(180deg)" : "rotate(0deg)",
              transition: "all 0.5s",
            }}
          />
        </div>

        {open && (
          <div className="absolute top-12 left-0 bg-white rounded-lg shadow-[0px_4px_20px_0px_#0000001A] p-4 w-full z-50">
            <div className="font-body-small flex flex-col gap-1 ">
              {loading
                ? "در حال بارگذاری..."
                : subsets.length === 0
                ? "موردی یافت نشد!"
                : subsets?.map((item) => (
                    <div
                      key={item.text}
                      className={`flex items-center justify-between gap-4 p-2 rounded-lg hover:bg-light-neutral-surface-highlight ${
                        selected.value === item.value
                          ? "bg-light-primary-background-highlight"
                          : ""
                      }`}
                      onClick={() => {
                        setOpen(false);
                        handleChange(item);
                        setSelected(item);
                      }}
                    >
                      <span className="flex flex-row gap-1">
                        {item.icon} {item.text}
                      </span>
                      {selected.value === item.value && <Check />}
                    </div>
                  ))}
            </div>
          </div>
        )}
      </button>
    </div>
  );
};

DropDown.propTypes = {
  subsets: PropTypes.array,
  handleChange: PropTypes.func,
  initialValue: PropTypes.object,
  title: PropTypes.string,
  size: PropTypes.string,
  loading: PropTypes.bool,
  setEmptyAtFirst: PropTypes.bool,
  placeholder: PropTypes.string,
};

export default DropDown;
