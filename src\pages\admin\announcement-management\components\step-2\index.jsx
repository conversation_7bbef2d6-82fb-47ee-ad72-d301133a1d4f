import { FilePdf, Info } from "@phosphor-icons/react";
import { CButton } from "components/ui/CButton";
// import ContentChips from "../ContentChips";

const StepTwo = ({ setStep }) => {
  return (
    <>
      <div className="bg-light-neutral-surface-card p-6 rounded-[8px]">
        <p className="font-subtitle-medium">اطلاعات اطلاعیه</p>
        <div className="">
          <div className="flex items-center justify-between py-2">
            <p className="font-body-medium text-light-neutral-text-medium">
              عنوان اطلاعیه
            </p>
            <span className="font-body-medium">sss</span>
          </div>

          <div className="flex items-center justify-between py-2">
            <p className="font-body-medium text-light-neutral-text-medium">
              عکس هدر
            </p>
            <span className="font-body-medium">sss</span>
          </div>

          <div className="flex items-center justify-between py-2">
            <p className="font-body-medium text-light-neutral-text-medium">
              فایل‌های بارگذاری شده
            </p>
            <div className="flex items-center gap-2">
              {[1, 1, 1].map((item, index) => (
                <div key={index}>
                  {/* <ContentChips title={"item"} icon={FilePdf} /> */}
                </div>
              ))}
            </div>
          </div>
          <div className="flex items-center justify-between py-2">
            <p className="font-body-medium text-light-neutral-text-medium">
              متن اطلاعیه
            </p>
            <span className="font-body-medium">sss</span>
          </div>
          <div className="flex items-center justify-end gap-2 mt-10">
            <div className="w-[200px]">
              <CButton role="error" mode="outline" onClick={() => setStep(1)}>
                انصراف
              </CButton>
            </div>
            <div className="w-[200px]">
              <CButton onClick={() => console.log("sent!")}>ارسال</CButton>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default StepTwo;
