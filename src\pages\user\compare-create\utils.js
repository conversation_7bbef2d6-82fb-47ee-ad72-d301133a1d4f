export const countAvg = (array, key) => {
  const total = array.reduce((acc, item) => {
    if (!item) {
      console.warn("Skipping null/undefined item.");
      return acc;
    }

    const value = Object.values(item)
      .filter((nestedItem) => nestedItem && nestedItem.key && nestedItem.count)
      .reduce((sum, nestedItem) => {
        if (nestedItem.key === key) {
          return sum + nestedItem.count;
        }
        return sum;
      }, 0);

    return acc + value;
  }, 0);

  const result = array.map((item) => {
    if (!item) {
      console.warn("Skipping null/undefined item in map.");
      return { name: key, x: 0, y: 0 };
    }

    const value = Object.values(item)
      .filter((nestedItem) => nestedItem && nestedItem.key && nestedItem.count)
      .reduce((sum, nestedItem) => {
        if (nestedItem.key === key) {
          return sum + nestedItem.count;
        }
        return sum;
      }, 0);

    return {
      name: key,
      x: value,
      y: total ? (value / total) * 100 : 0,
    };
  });

  console.log("result");
  console.log(result.sort((a, b) => b.x - a.x));

  return result;
  // return result.sort((a, b) => b.x - a.x);
};
