import { notification, toPersianNumber } from "utils/helper";
import { CButton } from "components/ui/CButton";
import { CaretLeft, CheckCircle, Trash } from "@phosphor-icons/react";
import UserInvitedInfo from "./UserInvitedInfo";
import UserInviteList from "./UserInviteList";
import GroupsState from "./GroupsState";
import Empty from "./Empty";
import { useNavigate } from "react-router-dom";

const AddedGroupUsers = ({
  table,
  tableItemsLimit,
  setTableItemsLimit,
  isAddFromEdit = false,
  page,
  setPage,
  filter,
  setFilter,
  dataCount = 100,
  per_page = 10,
  handleAddToSecondTable,
  handleRemoveFromSecondTable,
}) => {
  const navigate = useNavigate();
  const ticketStates = ["Inactive", "Deleted", "Pending", "Active"];
  const randomState =
    ticketStates[Math.floor(Math.random() * ticketStates.length)];

  const ticketStateTitles = {
    Inactive: "غیرفعال",
    Deleted: "حذف شده",
    Pending: "در انتظار",
    Active: "فعال",
  };

  const handleCreateGp = () => {
    navigate("/admin/groups-list");
    setTimeout(() => {
      notification.success(
        `گروه با موفقیت ذخیره شد`,
        <CheckCircle className="text-light-success-text-rest" />
      );
    }, 100);
  };

  const handleAddFromEdit = () => {
    navigate("/admin/groups/detail");
    setTimeout(() => {
      notification.success(
        `اعضای جدید به گروه افزوده شدند`,
        <CheckCircle className="text-light-success-text-rest" />
      );
    }, 100);
  };

  return (
    <div className="flex flex-col h-full w-full">
      <UserInviteList filter={filter} col5={true} setFilter={setFilter}>
        <UserInvitedInfo onDropItem={handleAddToSecondTable} />
      </UserInviteList>
      <div className="mt-4 h-full">
        <div className="h-full">
          {table.length > 0 ? (
            table.map((item) => (
              <div
                key={item.id}
                className="grid grid-cols-5 hover:bg-light-neutral-surface-highlight items-center py-4 cursor-pointer"
              >
                <div className="flex items-center gap-3 font-body-medium">
                  <p>{item?.first_name || "نام گروه"}</p>
                </div>
                <div className="flex font-body-medium">
                  {toPersianNumber(item?.username)}
                </div>
                <div className="flex font-body-medium">
                  {toPersianNumber(item?.phone_number)}
                </div>
                <div className="font-body-medium">
                  <GroupsState
                    title={ticketStateTitles[randomState] || ""}
                    state={randomState}
                  />
                </div>
                <div
                  className="flex justify-center items-center border-red cursor-pointer"
                  onClick={() => handleRemoveFromSecondTable(item)}
                >
                  <Trash className="p-1" size={26} color="red" />
                </div>
              </div>
            ))
          ) : (
            <Empty />
          )}
        </div>
      </div>
      <div className="flex !items-center justify-end gap-3 mt-7">
        <div className="w-32">
          <CButton role="neutral">
            {isAddFromEdit ? "انصراف" : "مرحله قبل"}
          </CButton>
        </div>
        <CButton
          width={180}
          onClick={isAddFromEdit ? handleAddFromEdit : handleCreateGp}
        >
          {isAddFromEdit ? "افزودن به گروه" : "ساخت گروه"}
          <CaretLeft />
        </CButton>
      </div>
    </div>
  );
};

export default AddedGroupUsers;
