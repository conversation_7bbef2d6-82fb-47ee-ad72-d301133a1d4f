import { Insta<PERSON><PERSON><PERSON>, Rss, Teleg<PERSON><PERSON><PERSON>, <PERSON>L<PERSON> } from "@phosphor-icons/react";
import { shortener, toPersianNumber } from "utils/helper";

const NodeCardSelect = ({
  twitter,
  telegram,
  instagram,
  news,
  title,
  isSelected,
  handleSelect = () => {},
}) => {
  return (
    <div
      className={`select-none flex flex-col items-center justify-between p-3 border rounded-lg ${
        isSelected ? "bg-light-primary-background-highlight" : ""
      } h-40 cursor-pointer transition-all`}
      onClick={handleSelect}
    >
      <input
        type="checkbox"
        checked={isSelected}
        className="cursor-pointer absolute top-[5px] right-2"
      />
      <p className="font-body-bold-large text-center pt-1">
        {shortener(title, 19)}
      </p>
      <p className="font-body-small text-center">مجموع محتوا:</p>
      <div className="mx-auto w-full">
        <div className="flex items-center gap-1 w-full">
          <div className="flex items-center gap-1 bg-light-neutral-background-medium py-1 px-2 rounded-md flex-1 justify-between">
            <InstagramLogo className="text-[#E64787]" />
            <p className="font-body-small">
              {toPersianNumber(instagram).toLocaleString()}
            </p>
          </div>
          <div className="flex items-center gap-1 bg-light-neutral-background-medium py-1 px-2 rounded-md flex-1 justify-between">
            <TelegramLogo className="text-[#0084C7]" />
            <p className="font-body-small">
              {toPersianNumber(telegram).toLocaleString()}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-1 mt-1">
          <div className="flex items-center gap-1 bg-light-neutral-background-medium py-1 px-2 rounded-md flex-1 justify-between">
            <Rss className="text-[#ECA213]" />
            <p className="font-body-small">
              {toPersianNumber(news).toLocaleString()}
            </p>
          </div>
          <div className="flex items-center gap-1 bg-light-neutral-background-medium py-1 px-2 rounded-md flex-1 justify-between">
            <XLogo />
            <p className="font-body-small">
              {toPersianNumber(twitter).toLocaleString()}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NodeCardSelect;
