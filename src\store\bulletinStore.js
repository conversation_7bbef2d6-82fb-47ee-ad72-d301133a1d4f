import { create } from "zustand";
import { DateObject } from "react-multi-date-picker";
import persian from "react-date-object/calendars/persian";
import persian_en from "react-date-object/locales/persian_en";
import { toPersianNumber } from "utils/helper.js";

// Function to get today's date in the format YYYY/MM/DD
const getTodayDate = () => {
  return toPersianNumber(
    new DateObject().convert(persian, persian_en).format(),
  );
};

// Default bulletin values
const BulletinDefaultValue = {
  id: null,
  step: 1,
  link: "",
  type: "manual",
  title: "",
  description: "",
  metadata: {
    messenger: "none",
    cover_has_date_time: getTodayDate(), // Set today's date here
    has_toc: false,
    has_page_number: false,
    ordered_platform: ["instagram", "telegram", "twitter", "news"],
    style: {
      cover_title: {
        font: "fa_yekan",
        font_size: 24,
        bold: true,
        italic: false,
        underlined: false,
        text_color: "#000000",
        text_highlight: "auto",
        alignment: "center",
      },
      cover_subtitle: {
        font: "fa_yekan",
        font_size: 20,
        bold: false,
        italic: false,
        underlined: false,
        text_color: "#000000",
        text_highlight: "auto",
        alignment: "center",
      },
      creator: {
        font: "fa_yekan",
        font_size: 16,
        bold: false,
        italic: false,
        underlined: false,
        text_color: "#000000",
        text_highlight: "auto",
        alignment: "center",
      },
      header_title: {
        font: "fa_yekan",
        font_size: 11,
        bold: false,
        italic: false,
        underlined: false,
        text_color: "#000000",
        text_highlight: "auto",
        alignment: "center",
      },
      introduction_title: {
        font: "fa_yekan",
        font_size: 16,
        bold: false,
        italic: false,
        underlined: false,
        text_color: "#000000",
        text_highlight: "auto",
        alignment: "center",
      },
      introduction_description: {
        font: "fa_yekan",
        font_size: 16,
        bold: false,
        italic: false,
        underlined: false,
        text_color: "#000000",
        text_highlight: "auto",
        alignment: "justify",
      },
    },
  },
  content: {
    q: "",
    platform: {
      twitter: [
        {
          sort: "date",
          order: "desc",
          content_count: 10,
        },
      ],
      telegram: [],
      instagram: [],
      news: [],
    },
    filter: {},
  },
  contentManual: {
    q: "",
    twitter: [],
    telegram: [],
    instagram: [],
    news: [],
  },
  existingContent: [],
  chart: {},
};

export const useBulletinStore = create((set) => ({
  bulletin: BulletinDefaultValue,
  setBulletin: (param) =>
    set((state) => ({ bulletin: { ...state.bulletin, ...param } })),
  resetBulletin: () => set(() => ({ bulletin: BulletinDefaultValue })),
}));
