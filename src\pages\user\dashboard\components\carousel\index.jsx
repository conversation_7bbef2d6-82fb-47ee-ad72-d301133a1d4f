import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation, Pagination } from "swiper/modules";
import "swiper/css";
import SummaryCard from "components/SummaryCard";
import TextSlicer from "components/TextSlicer";
import ListTile from "../list_title";
import { CaretLeft, CaretRight } from "@phosphor-icons/react";
import SummaryCardFooter from "../../../hot-topic/components/SummaryCardTopic/SummaryCardFooter";

const Carousel = ({ data, media, base_url, isSidebarOpened }) => {
  return (
    <div className="flex flex-col gap-4">
      <ListTile data={data} base_url={base_url} media={media} />
      <div className="flex w-full">
        <Swiper
          spaceBetween={16}
          slidesPerView={3}
          navigation={{ enabled: true, nextEl: ".next", prevEl: ".prev" }}
          loop={true}
          modules={[Autoplay, Navigation, Pagination]}
          style={{ width: "auto" }}
        >
          {data?.data
            .filter((item, index, arr) => {
              if (item.id !== arr[index + 1]?.id) return true;
              // return true
            })
            .map((item) => (
              <SwiperSlide key={item.url}>
                <div className="shadow-[0px_2px_20px_0px_#0000000D] bg-white rounded-lg">
                  <SummaryCard media={media} data={item} height="224px">
                    <TextSlicer media={media} length={200}>
                      {media === "news" ? item.content : item.description}
                    </TextSlicer>
                  </SummaryCard>

                  <div className="[direction:rtl]">
                    <SummaryCardFooter
                      media={media}
                      data={item}
                      lowWidth
                      offDrawer={false}
                    />
                  </div>
                </div>
              </SwiperSlide>
            ))}
        </Swiper>
      </div>

      <div className="flex gap-4 self-end">
        <button className="size-10 bg-white rounded-[4px] shadow-[0px_2px_20px_0px_#0000000D] flex items-center justify-center prev">
          <CaretLeft color="#00000080" />
        </button>
        <button className="size-10 bg-white rounded-[4px] shadow-[0px_2px_20px_0px_#0000000D] flex items-center justify-center next">
          <CaretRight color="#00000080" />
        </button>
      </div>
    </div>
  );
};
export default Carousel;
