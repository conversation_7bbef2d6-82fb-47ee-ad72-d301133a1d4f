import PropTypes from "prop-types";

const Separator = ({ status, step }) => {
  return (
    <div className="w-6 flex justify-center">
      <div
        className="min-h-6 max-h-full border-r border-light-neutral-border-medium-rest"
        style={{
          ...(status > step ? { borderColor: "#19A399" } : {}),
        }}
      ></div>
    </div>
  );
};

Separator.propTypes = {
  status: PropTypes.number.isRequired,
  step: PropTypes.number.isRequired,
};

export default Separator;
