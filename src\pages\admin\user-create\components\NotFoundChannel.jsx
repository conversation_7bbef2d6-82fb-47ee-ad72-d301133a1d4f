import { Warning } from "@phosphor-icons/react";

const NotFoundChannel = () => {
  return (
    <div className="w-full flex flex-col gap-4 bg-light-error-background-highlight rounded-lg p-4 mt-6">
      <div className="flex items-center gap-2">
        <Warning color="#BE223C" className="size-6" />
        <p className="font-body-large">چنین کانالی یافت نشد</p>
      </div>
      <p className="font-body-medium">
        لطفا در وارد کردن شناسه عددی کانال یا حساب کاربری شخصی دقت کنید. همچنین
        ممکن است تنظیمات بات استیناس را start نکرده باشید. لطفا پس از بررسی٬
        دوباره برسی کنید.
      </p>
    </div>
  );
};

export default NotFoundChannel;
