import { useEffect } from "react";
import userService from "service/api/userService.js";

const FetchImage = ({ imageUrl, onFetchSuccess, defaultValue }) => {
  useEffect(() => {
    if (imageUrl) {
      userService
        .getImage(imageUrl)
        .then((response) => {
          const url = URL.createObjectURL(response.data);
          onFetchSuccess(url);
        })
        .catch((error) => {
          console.error("Error fetching the image:", error);
        });
    } else {
      onFetchSuccess(defaultValue);
    }
  }, [imageUrl, onFetchSuccess]);

  return null; // This component doesn't render anything
};

export default FetchImage;
