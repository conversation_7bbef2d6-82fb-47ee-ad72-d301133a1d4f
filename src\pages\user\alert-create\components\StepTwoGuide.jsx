import TextWithBullet from "components/TextWithBullet";
import ToolTip from "components/ui/ToolTip.jsx";

const StepTwoGuide = () => {
  return (
    <div className="font-body-medium">
      <h3 className="mb-4">راهنما</h3>
      <div className="list-disc list-inside flex flex-col gap-4">
        <TextWithBullet>
          بات
          <ToolTip
            comp="stinas_alert_bot@"
            className={"inline"}
            childrenStyle={"inline"}
          >
            <a
              href="https://t.me/stinas_alert_bot"
              className="underline underline-offset-4 text-light-inform-text-rest mx-1"
            >
              تلگرام
            </a>
          </ToolTip>
          یا{" "}
          <ToolTip
            comp="stinas_alert_bot@"
            className={"inline"}
            childrenStyle={"inline"}
          >
            <a
              href="#"
              className="underline underline-offset-4 text-light-inform-text-rest mx-1"
            >
              بله
            </a>
          </ToolTip>
          {/*<a*/}
          {/*  href="https://t.me/stinas_alert_bot"*/}
          {/*  className="underline underline-offset-4 text-light-inform-text-rest mx-1"*/}
          {/*>*/}
          {/*  stinas_alert_bot@*/}
          {/*</a>*/}
          سیناپس را جست‌وجو و start کنید.
        </TextWithBullet>
        <TextWithBullet>
          دو حالت جهت ارسال مطالب برای شما وجود دارد: ارسال به صورت مستقیم و
          ارسال در یک کانال.
        </TextWithBullet>
        <TextWithBullet>
          <span>در صورتی که قصد دارید مطالب به کانال ارسال شود بات</span>
          <b> stinas_alert_bot@ </b>
          <span>
            را به کانال خود اضافه کرده و دسترسی مدیریت را به آن اختصاص دهید (این
            دسترسی برای امکان ارسال مطالب به کانال ضروری است).
          </span>
        </TextWithBullet>
        <TextWithBullet>
          اگر هدف دریافت مطالب به صورت مستقیم از بات باشد نیازی به انجام کار
          خاصی نیست.
        </TextWithBullet>
        <TextWithBullet>
          شناسه کانال یا اکانت خود را جهت ارزیابی در باکس مربوطه وارد کرده و
          دکمه بررسی را کلیک کنید. در صورتی که مراحل قبل به درستی انجام شده باشد
          ارزیابی موفقیت‌آمیز بوده و دکمه ادامه فعال خواهد شد. ( لازم به ذکر است
          که بعد از start بات، شناسه اکانت شما با عنوان شناسه کاربری در قالب یک
          پیام خوشامدگویی برای شما ارسال می‌شود. )
        </TextWithBullet>
        <TextWithBullet>
          توجه داشته باشید که برای راه‌اندازی هشدار حتما نیازمند شناسه برای
          اکانت یا کانال خود می‌باشید.
        </TextWithBullet>
      </div>
    </div>
  );
};

export default StepTwoGuide;
