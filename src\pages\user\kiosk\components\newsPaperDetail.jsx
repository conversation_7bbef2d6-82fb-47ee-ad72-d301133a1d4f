import { DownloadSimple, MagnifyingGlassPlus } from "@phosphor-icons/react";
import { parseTimeToPersianSummary } from "utils/helper";
import { useParams } from "react-router-dom";
import { CButton } from "components/ui/CButton";
import { useEffect, useState } from "react";
import newsPaper from "service/api/newsPaper";
import RenderSwiperSection from "./renderSwiperSection";
import CompanyInfo from "./companyInfo";
import Popup from "./Popup";
import newspaper from "../../../../assets/images/kiosk/newspaper.jpg";
import { Card } from "components/ui/Card";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";
import { useLayoutContext } from "context/layout-context";
import ToolTip from "components/ui/ToolTip";

const fallbackImage = newspaper;

const NewsPaperDetail = () => {
  const [data, setData] = useState({});
  const [anotherDate, setAnotherDate] = useState([]);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState("");
  const [selectedImageUrl, setSelectedImageUrl] = useState("");
  const [isFallback, setIsFallback] = useState(false);
  const { id } = useParams();
  const [loaded, setLoaded] = useState(false);
  const { setBreadcrumb } = useLayoutContext();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [data]);

  const handleImageClick = (image, url) => {
    setSelectedImage(image);
    setSelectedImageUrl(url);
    setIsPopupOpen(true);
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await newsPaper.getById(id);
        setData(response.data.data);
        setLoaded(true);
      } catch (err) {
        console.error(err);
      }
    };
    fetchData();
  }, [id]);

  useEffect(() => {
    const fetchPostData = async () => {
      const today = new Date();
      const end_date = today.toISOString();
      const start_date = new Date(today);
      start_date.setDate(today.getDate() - 14);

      const params = {
        filter_id: id,
        agency: data?.agency_id,
        categories: [],
        start_date: start_date.toISOString(),
        end_date: end_date,
      };

      try {
        const response = await newsPaper.post(params);
        setAnotherDate(response.data.data);
      } catch (err) {
        console.error(err);
      }
    };
    if (data.agency_en) {
      fetchPostData();
    }
  }, [data]);

  const suggestionCards = data?.suggestion || [];
  const anotherDateNewsPaper = anotherDate?.newspaper || [];
  const breadcrumbInfo = [
    { title: "پیشخوان", link: "/app/newspaper" },
    { title: data?.information?.agency || "", link: "/app/newspaper" },
  ];
  useBreadcrumb(breadcrumbInfo);
  useEffect(() => {
    setBreadcrumb(breadcrumbInfo);
  }, [data]);

  return (
    <div style={{ fontFamily: "iranyekan" }} className="px-6">
      <div className="flex justify-between items-stretch pt-3 !w-[96.5%] mx-auto gap-5">
        <div
          className={`!w-[65%] h-full rounded-md my-[2px] shadow-md card-animation ${
            loaded ? `card-delay-1` : ""
          }`}
        >
          <Card className="flex flex-col w-[100%]">
            <div
              className="relative group h-[30rem] w-full overflow-hidden rounded-lg"
              onClick={() =>
                handleImageClick(`${data?.cover_image}`, data?.file)
              }
            >
              <img
                src={`${data?.cover_image}`}
                alt={`Cover for ${data?.agency}`}
                onError={(e) => {
                  e.target.src = fallbackImage;
                  setIsFallback(true);
                }}
                className={`rounded-lg w-full transition-all duration-300 ease-in-out group-hover:blur-sm ${
                  isFallback ? "" : "object-cover object-top"
                }`}
              />
              <div className="absolute inset-0 flex justify-center items-center opacity-0 transition-opacity duration-300 ease-in-out group-hover:opacity-100">
                <div className="bg-white rounded-full p-4 shadow-lg">
                  <MagnifyingGlassPlus
                    className="h-8 w-8 text-gray-800"
                    weight="bold"
                  />
                </div>
              </div>
            </div>
            <div className="w-full h-full bg-white">
              <div className="flex justify-between items-center mt-5">
                <div className="flex flex-col">
                  <span className="text-[12px] text-[#7f7f7f] pt-1 pr-2 font-subtitle-large">
                    {parseTimeToPersianSummary(data?.published_at)}
                  </span>
                </div>
                <div className="px-2">
                  {data?.file === null ? (
                    <ToolTip
                      comp={
                        "امکان دانلود روزنامه وجود ندارد. لطفا از سایر مراجع ورزشی در بخش روزنامه‌های مشابه استفاده نمایید."
                      }
                      position="top"
                    >
                      <CButton
                        type={"submit"}
                        size={"lg"}
                        className="[direction:rtl] [width:170px!important] [display:flex] [gap:6px] border rounded-lg"
                        readOnly
                      >
                        <DownloadSimple size={20} />
                        دانلود روزنامه
                      </CButton>
                    </ToolTip>
                  ) : (
                    <CButton
                      type={"submit"}
                      size={"lg"}
                      className="[direction:rtl] [width:170px!important] [display:flex] [gap:6px] rounded-lg"
                      onClick={() => {
                        const fileUrl = `${data?.file}`;
                        window.open(fileUrl);
                      }}
                    >
                      <DownloadSimple size={20} />
                      دانلود روزنامه
                    </CButton>
                  )}
                </div>
              </div>
            </div>
          </Card>
        </div>
        <CompanyInfo data={data} newsPaperId={data?.id} />
      </div>
      <RenderSwiperSection
        cards={anotherDateNewsPaper}
        title="تاریخ‌های دیگر از همین روزنامه"
        data={data}
        agencyId={true}
      />
      <RenderSwiperSection
        cards={suggestionCards}
        title="روزنامه‌های مشابه"
        showMore={false}
      />
      <Popup
        isOpen={isPopupOpen}
        onClose={() => setIsPopupOpen(false)}
        imageSrc={selectedImage}
        url={selectedImageUrl}
      />
    </div>
  );
};

export default NewsPaperDetail;
