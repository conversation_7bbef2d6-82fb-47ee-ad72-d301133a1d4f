const UserInvitedInfo = ({ onDropItem }) => {
  const handleDrop = (e) => {
    e.preventDefault();
    try {
      const droppedData = JSON.parse(e.dataTransfer.getData("text/plain"));
      if (droppedData && droppedData.id) {
        onDropItem(droppedData);
      }
    } catch (error) {
      console.error("Invalid drop data:", error);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  return (
    <div
      className="w-full h-20 flex items-center justify-center border border-dashed border-gray-300 bg-gray-50 text-gray-500"
      onDrop={handleDrop}
      onDragOver={handleDragOver}
    >
      <p>برای افزودن کاربر،از لیست کاربران سمت راست بکشید و اینجا رها کنید</p>
    </div>
  );
};

export default UserInvitedInfo;
