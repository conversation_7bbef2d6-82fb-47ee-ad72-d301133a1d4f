import Doughnut from "components/Charts/Doughnut";
import { memo } from "react";
import { fixPercentToShow, toPersianNumber } from "utils/helper";
import emotionsMap from "./PoliticalParties/constants/emotionsMap";

const transformEmotionsData = (emotionsAnalysis) => {
  const { total, twitter } = emotionsAnalysis;
  // const emotionsMap = [
  //   { key: "joy", name: "شاد", color: "#47DBB6" },
  //   { key: "sadness", name: "غمگین", color: "#004080" },
  //   { key: "anger", name: "خشم", color: "#FF3300" },
  //   { key: "disgust", name: "تنفر", color: "#8B4513" },
  //   { key: "fear", name: "ترس", color: "#660066" },
  //   { key: "surprise", name: "تعجب", color: "#FFA500" },
  //   { key: "other", name: "سایر", color: "#cccccc" },
  //   { key: "disappointment", name: "نا‌امیدی", color: "#cccccc" },
  //   { key: "optimism", name: "خوشبین<PERSON>", color: "#cccccc" },
  //   { key: "trust", name: "اعتماد", color: "#cccccc" },
  //   { key: "neutral", name: "خنثی", color: "#cccccc" },
  // ];
  const data = emotionsMap.map((emotion) => ({
    name: emotion.name,
    y: 0,
  }));

  twitter?.forEach(({ key, count }) => {
    let emotionIndex = emotionsMap.findIndex((e) => e.key === key);
    if (emotionIndex === -1) {
      emotionIndex = emotionsMap.findIndex((e) => e.name === "سایر");
    }
    if (emotionIndex !== -1) {
      data[emotionIndex].y += (count / total) * 100;
    }
  });
  return data;
};

const EmotionAnalysisPercentage = ({ emotionsAnalysis }) => {
  const data = transformEmotionsData(emotionsAnalysis)?.sort(
    (a, b) => b?.y - a?.y
  );

  if (data.every((item) => item.y === 0)) {
    return (
      <div className="bg-light-neutral-surface-card rounded-[8px] p-6 h-full">
        <p className="font-subtitle-large pb-4">تحلیل هیجانات</p>
        <p className="py-1.5 text-center font-body-bold-large text-light-neutral-text-medium mx-auto flex items-center justify-center h-80">
          داده ای یافت نشد
        </p>
      </div>
    );
  }

  return (
    <div className="bg-light-neutral-surface-card rounded-[8px] p-6">
      <p className="font-subtitle-large pb-4">تحلیل هیجانات</p>
      <Doughnut
        colLayout={true}
        name="sentiment"
        size={"100%"}
        height={400}
        data={data}
        legendFormatter={function () {
          return `<div style="font-family: iranyekan, sans-serif; display: grid; grid-template-columns: 1fr auto; gap: 16px; width: 100%; padding: 2px; align-items: center;">
                    <span style="font-size: 13px; color: #333;">
                       ${toPersianNumber(this?.options?.y.toFixed(1))}%
                    </span>
                    <div style="display: flex; gap: 8px; align-items: center;">
                        <div>${
                          this.color === "#47DBB6"
                            ? "<div style='display: flex; align-items: center; gap:2px;'>" +
                              "<b>شاد</b>" +
                              "</div>"
                            : this.color === "#004080"
                            ? "<div style='display: flex; align-items: center; gap:2px;'>" +
                              "<b>غمگین</b>" +
                              "</div>"
                            : this.color === "#FF3300"
                            ? "<div style='display: flex; align-items: center; gap:2px;'>" +
                              "<b>خشم</b>" +
                              "</div>"
                            : this.color === "#8B4513"
                            ? "<div style='display: flex; align-items: center; gap:2px;'>" +
                              "<b>تنفر</b>" +
                              "</div>"
                            : this.color === "#660066"
                            ? "<div style='display: flex; align-items: center; gap:2px;'>" +
                              "<b>ترس</b>" +
                              "</div>"
                            : this.color === "#d6ba2d"
                            ? "<div style='display: flex; align-items: center; gap:2px;'>" +
                              "<b>ناامیدی</b>" +
                              "</div>"
                            : this.color === "#04c421"
                            ? "<div style='display: flex; align-items: center; gap:2px;'>" +
                              "<b>خوش‌بینی</b>" +
                              "</div>"
                            : this.color === "#4fdbf7"
                            ? "<div style='display: flex; align-items: center; gap:2px;'>" +
                              "<b>اعتماد</b>" +
                              "</div>"
                            : this.color === "#cccccc"
                            ? "<div style='display: flex; align-items: center; gap:2px;'>" +
                              "<b>خنثی</b>" +
                              "</div>"
                            : this.color === "#FFA500"
                            ? "<div style='display: flex; align-items: center; gap:2px;'>" +
                              "<b>تعجب</b>" +
                              "</div>"
                            : "<div style='display: flex; align-items: center; gap:2px;'>" +
                              "<b>سایر</b>" +
                              "</div>"
                        }</div>
                        <div style="background-color: ${
                          this.color
                        }; border-radius: 50%; width: 12px; height: 12px;"></div>
                    </div>
                </div>`;
        }}
        tooltipFormatter={function () {
          return `<div style="display: flex; flex-direction: column; gap: 8px; text-align: center; font-family: iranyekan;">
                    <p>${this.key}</p>
                    <p>${fixPercentToShow(
                      (this.y / emotionsAnalysis?.total) * 100
                    )}</p>
                  </div>`;
        }}
        colors={[
          "#47DBB6",
          "#004080",
          "#FF3300",
          "#8B4513",
          "#660066",
          "#FFA500",
          "#cccccc",
        ]}
      />
    </div>
  );
};

export default memo(EmotionAnalysisPercentage);
