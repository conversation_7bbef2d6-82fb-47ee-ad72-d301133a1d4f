import PropTypes from "prop-types";
import Loading from "components/ui/Loading.jsx";
import CardsLayout from "layout/CardsLayout.jsx";
import SummaryCardTopic from "pages/user/hot-topic/components/SummaryCardTopic/index.jsx";
import { findPlatform } from "../utils.js";
import Paginate from "components/ui/Paginate.jsx";

const ContentTab = ({
  isLoading,
  platform,
  query,
  countLoadingOffset,
  dataArray,
  page,
  setPage,
  dataCount,
}) => {
  return (
    <>
      {isLoading ? (
        <Loading height={countLoadingOffset?.current?.clientHeight + 24} />
      ) : (
        <>
          {query && query !== "" && dataArray && dataArray.length === 0 ? (
            <div
              className={
                "flex w-full flex-col justify-center items-center min-h-[450px]"
              }
            >
              <div
                className={
                  "w-full h-[320px] bg-[url(/notFound.png)] bg-no-repeat bg-contain bg-center"
                }
              ></div>
              <p
                className={"font-body-large text-light-neutral-text-high mt-0"}
              >
                محتوای مورد نظر شما یافت نشد
              </p>
            </div>
          ) : dataArray && dataArray.length === 0 ? (
            <div
              className={
                "flex w-full flex-col justify-center items-center min-h-[350px]"
              }
            >
              <div
                className={
                  "w-full h-[50px] bg-[url(/empty_folder.png)] bg-no-repeat bg-contain bg-center"
                }
              ></div>
              <p
                className={"font-body-large text-light-neutral-text-high mt-3"}
              >
                هنوز محتوایی جست‌وجو نشده است
              </p>
              <p
                className={
                  "font-body-medium text-light-neutral-text-medium mt-3"
                }
              >
                لطفا کلمات موردنظر خود را جست‌و‌جو کنید
              </p>
            </div>
          ) : (
            <div className={"mt-4"}>
              <CardsLayout>
                {dataArray?.map((data) => (
                  <div className={"flex w-full [direction:rtl]"} key={data.id}>
                    <SummaryCardTopic
                      media={findPlatform(data)}
                      data={data}
                      platform={platform}
                    />
                  </div>
                ))}
              </CardsLayout>

              <div className="[direction:rtl]">
                <Paginate
                  page={page}
                  setPage={setPage}
                  dataCount={dataCount}
                  per_page={10}
                />
              </div>
            </div>
          )}
        </>
      )}
    </>
  );
};

ContentTab.propTypes = {
  isLoading: PropTypes.bool.isRequired,
  platform: PropTypes.string.isRequired,
  query: PropTypes.string,
  countLoadingOffset: PropTypes.any.isRequired,
  dataArray: PropTypes.array,
  page: PropTypes.number,
  setPage: PropTypes.func,
  dataCount: PropTypes.number,
};

export default ContentTab;
