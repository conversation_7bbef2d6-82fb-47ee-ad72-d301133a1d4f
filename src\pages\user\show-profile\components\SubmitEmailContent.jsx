import { Field, Form, Formik } from "formik";
import { useState } from "react";
import { CButton } from "components/ui/CButton";
import { COtpInput } from "components/ui/COtpInput";
import { Check } from "@phosphor-icons/react";
import styles from "./styles.module.css";
import { emailVerifyOTPSchema } from "utils/validationSchemas";
import authService from "service/api/authService";

const SubmitEmailContent = ({ profile }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isSubMenuOpen, setIsSubMenuOpen] = useState(false);
  const sendEmailNo = async (otp) => {
    setIsLoading(true);
    try {
      const res = await authService.verifyEmail({
        // email: profile?.email,
        // token: otp,
      });
    } catch (error) {
      console.log(error);
    }
    setIsLoading(false);
  };
  const handleSubmit = async (values, { setSubmitting }) => {
    sendEmailNo(values.otp);
    setSubmitting(false);
  };
  return (
    <div className="flex flex-col gap-4 items-center justify-center ">
      <div className="w-[605px] bg-light-neutral-surface-card rounded-lg">
        <Formik
          initialValues={{ otp: "" }}
          validationSchema={emailVerifyOTPSchema}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting, setFieldValue }) => (
            <Form className="font-body-medium flex-1 bg-white mx-auto w-full h-full max-w-[560px] overflow-hidden">
              <h2 className="font-subtitle-large text-gray-800 mb-5 text-right">
                کد تایید ایمیل
              </h2>
              <p className={"font-overline-large mb-3"}>
                هم‌اکنون برای شماره <EMAIL> یک ایمیل حاوی کد ۶
                رقمی ارسال می‌شود.
              </p>
              <Field
                id={"otp"}
                name={"otp"}
                component={COtpInput}
                timerIncluded={false}
                description={"چرا هنوز کد را دریافت نکردم"}
                size={"lg"}
                validation={"none"}
                direction={"ltr"}
                title={"کد ۶ رقمی"}
                onChange={(value) => {
                  setFieldValue("otp", value);
                }}
                infoClickHandler={() => setIsSubMenuOpen(true)}
              />
              {isSubMenuOpen && (
                <div className="bg-light-neutral-surface-highlight rounded-lg px-3 py-3 my-5 leading-7">
                  <p className="font-body-medium pb-2">
                    اگر ایمیل کد تایید دریافت نکرده‌اید، لطفا موارد زیر را بررسی
                    کنید:
                  </p>
                  <ul className={`mr-5 ${styles["custom-bullet"]}`}>
                    <li className="text-light-neutral-text-low">
                      <div className="text-black">
                        آدرسی که برای دریافت ایمیل بررسی می‌کنید،
                        <EMAIL> باشد.
                      </div>
                    </li>
                    <li className="text-light-neutral-text-low">
                      <div className="text-black">
                        پوشه‌ی هرزنامه‌ی (Spam) ایمیل خود را بررسی کنید.
                      </div>
                    </li>
                    <li className="text-light-neutral-text-low">
                      <div className="text-black">
                        فیلترشکن (VPN) شما خاموش باشد.
                      </div>
                    </li>
                    <li className="text-light-neutral-text-low">
                      <div className="text-black">
                        اگر همچنان ایمیل را دریافت نکردید، دوبار ه تلاش کنید یا
                        با پشتیبانی تماس بگیرید.
                      </div>
                    </li>
                  </ul>

                  <div className="flex justify-end">
                    <div
                      className="text-light-primary-background-rest flex items-center  gap-2 cursor-pointer w-fit"
                      onClick={() => setIsSubMenuOpen(false)}
                    >
                      <Check />
                      <span>متوجه شدم</span>
                    </div>
                  </div>
                </div>
              )}
              <CButton type={"submit"} size={"lg"} disabled={isSubmitting}>
                ثبت
              </CButton>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default SubmitEmailContent;
