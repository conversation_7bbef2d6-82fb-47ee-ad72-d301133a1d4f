import Alignment from "./Alignment.jsx";
import FontStyle from "./FontStyle.jsx";
import FontSize from "./FontSize.jsx";
import FontFamily from "./FontFamily.jsx";

const FontSettings = ({ handleChange, initialValue }) => {
  return (
    <div className="flex relative z-10">
      <div className="bg-light-neutral-surface-highlight rounded-lg px-2 py-1 flex gap-4 w-fit">
        <FontFamily
          initialValue={initialValue}
          handleChange={(x) => handleChange(x)}
        />
        <div className="border-[0.5px] h-10 border-light-neutral-border-low-rest"></div>
        <FontSize
          initialValue={initialValue}
          handleChange={(x) => handleChange(x)}
        />
        <div className="border-[0.5px] h-10 border-light-neutral-border-low-rest"></div>
        <FontStyle
          handleChange={(x) => handleChange(x)}
          initialValue={initialValue}
        />
        <div className="border-[0.5px] h-10  border-light-neutral-border-low-rest"></div>
        <Alignment
          initialValue={initialValue}
          handleChange={(x) => handleChange(x)}
        />
      </div>
    </div>
  );
};

export default FontSettings;
