import { useEffect, useRef, useState } from "react";
import clsx from "clsx";
import PropTypes from "prop-types";

const AttachmentBox = ({
  initialPreview,
  title,
  description,
  description2,
  name,
  fullWidth,
  fileSizeLimitInMB = 10,
  boxIcon: BoxIcon,
  multiple = false,
  onChange = () => {},
}) => {
  const fileInputRef = useRef(null);
  const [fileNames, setFileNames] = useState([]);
  const [isDragging, setIsDragging] = useState(false);
  const [imagePreview, setImagePreview] = useState(initialPreview);
  const [errorText, setErrorText] = useState("");

  const handleReset = () => {
    fileInputRef.current.value = null;
    setFileNames([]);
    setImagePreview(null);
    onChange([]);
  };

  const validateFileSize = (file) => {
    const fileSizeMB = file.size / 1024 / 1024;
    if (fileSizeMB > fileSizeLimitInMB) {
      setErrorText(`حجم فایل باید کمتر از ${fileSizeLimitInMB}MB باشد`);
      return false;
    }
    setErrorText("");
    return true;
  };

  const handleFileChange = async (event) => {
    const files = Array.from(event.target.files);
    const validFiles = files.filter((file) => validateFileSize(file));

    if (validFiles.length > 0) {
      setFileNames(validFiles);
      onChange(validFiles);
    } else {
      handleReset();
    }
  };

  const handleDrop = async (event) => {
    event.preventDefault();
    setIsDragging(false);
    const files = Array.from(event.dataTransfer.files);
    const validFiles = files.filter((file) => validateFileSize(file));

    if (validFiles.length > 0) {
      fileInputRef.current.files = event.dataTransfer.files;
      setFileNames(validFiles);
      onChange(validFiles);
    } else {
      handleReset();
    }
  };

  const handleDragOver = (event) => {
    event.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    fileInputRef.current.value = null;
    if (errorText) {
      setFileNames([]);
    }
  }, [errorText]);

  const boxBorder = clsx("border-[1px] border-[#D1D6DB]", {
    "border-solid": fileNames.length,
    "border-dashed": !fileNames.length,
  });

  return (
    <>
      <p className="font-overline-large pb-2">{title}</p>
      <div
        className={`flex items-center justify-center ${
          fullWidth ? "w-full" : "w-80"
        } h-[92px] ${isDragging ? "border-blue-500 bg-blue-50" : ""}`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <label
          htmlFor={name}
          className={`flex flex-col items-center justify-center ${
            fullWidth ? "w-full" : "w-80"
          } h-[92px] rounded-lg cursor-pointer ${boxBorder}`}
        >
          <div
            className={`flex flex-col items-center justify-center pt-5 pb-6 ${
              description2 ? "gap-2" : "gap-4"
            }`}
          >
            <BoxIcon size={25} className="text-light-neutral-text-medium" />
            <p className="font-button-medium">{description}</p>
            {description2 && (
              <p className="font-button-small text-light-neutral-text-medium">
                {description2}
              </p>
            )}
          </div>

          <input
            name={name}
            id={name}
            type="file"
            accept=".jpeg,.jpg,.png,.docx,.pdf,.doc,.csv,.xls,.txt,.zip"
            className="hidden"
            ref={fileInputRef}
            onChange={handleFileChange}
            multiple={multiple}
          />
        </label>
      </div>
      <span className="text-light-error-text-rest font-body-medium">
        {errorText}
      </span>
    </>
  );
};

AttachmentBox.propTypes = {
  initialPreview: PropTypes.string,
  title: PropTypes.string,
  description: PropTypes.string,
  name: PropTypes.string,
  fileSizeLimitInMB: PropTypes.number,
  onChange: PropTypes.func,
  multiple: PropTypes.bool,
};

export default AttachmentBox;
