import { useState } from "react";
import AuthService from "service/api/authService.js";
import { Field, Form, Formik } from "formik";
import { CButton } from "components/ui/CButton.jsx";
import { CaretLeft, CaretRight } from "@phosphor-icons/react";
import { CInput } from "components/ui/CInput.jsx";
import { FormErrorWrapper } from "components/ui/FormErrorArea.jsx";
import { forgetPasswordStep1Schema } from "utils/validationSchemas.js";

import logoImg from "assets/images/logo_main.png";
import PropTypes from "prop-types";

const ForgotPasswordStep1 = ({ setStep, setMobile }) => {
  const [formErrorTitle, setFormErrorTitle] = useState(null);
  const [formErrorText, setFormErrorText] = useState(null);

  const handleSubmit = async (values, { setSubmitting, setFieldError }) => {
    setFormErrorTitle(null);
    setSubmitting(true);
    try {
      const response = await AuthService.forgotPassword({
        parameter: values.mobile,
      });
      if (!(response?.data?.code === 200)) {
        setFormErrorTitle("خطا در فراموشی رمز عبور");
        setSubmitting(false);
        return false;
      }

      setMobile(values.mobile);
      setStep(2);
    } catch (error) {
      if (error.response) {
        setFieldError("general", error.response.data.message);
        setFieldError("mobile", " ");
        setFormErrorTitle(error.response.data.message);
      } else if (error.request) {
        // The request was made but no response was received
        console.error("No response received:", error.request);
        setFormErrorTitle("پاسخی از سرور دریافت نشد");
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error("Error", error.message);
        setFormErrorTitle("خطا در فرآیند!");
      }
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <>
      <Formik
        initialValues={{ mobile: "" }}
        validationSchema={forgetPasswordStep1Schema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting, handleChange, handleBlur, dirty, isValid }) => (
          <Form className="font-body-medium flex-1 bg-white mx-auto w-full h-full max-w-lg overflow-hidden">
            {/* Title */}
            <img
              className={"top-0 w-auto m-auto h-[96px]"}
              src={logoImg}
              alt="logo"
            />

            <div className={"flex w-[140px] mt-16"}>
              <CButton
                type={"button"}
                size={"md"}
                leftIcon={<CaretRight />}
                role={"neutral"}
                mode={"text"}
                onClick={() => (window.location.href = "/login")}
              >
                بازگشت به ورود
              </CButton>
            </div>

            <h2 className="font-headline-medium text-gray-800 mt-8 mb-5 text-right">
              فراموشی رمز عبور
            </h2>
            <p className={"font-paragraph-medium mb-8"}>
              لطفا شماره موبایل خود را وارد کنید تا کد ۶ رقمی برای شما ارسال
              شود. در صورتی که شماره خود را در سامانه سیناپس ثبت نکرده‌اید، لطفا
              با پشتیبانی تماس حاصل کنید.
            </p>
            <Field
              id={"mobile"}
              name={"mobile"}
              component={CInput}
              size={"lg"}
              validation={"none"}
              direction={"ltr"}
              title={"شماره موبایل"}
              placeholder={"برای مثال ۰۹۱۲۳۴۵۶۷۸۹"}
              onChange={handleChange("mobile")}
              onBlur={handleBlur("mobile")}
              inputProps={{ maxLength: 11, numeric: "true" }}
            ></Field>

            <FormErrorWrapper
              show={!!formErrorTitle}
              title={formErrorTitle}
              text={formErrorText}
            />

            {/* Submit button */}
            <div className="flex justify-end">
              <CButton
                rightIcon={<CaretLeft />}
                type={"submit"}
                size={"lg"}
                disabled={isSubmitting}
                readOnly={!(dirty && isValid)}
              >
                دریافت کد تایید
              </CButton>
            </div>
          </Form>
        )}
      </Formik>
    </>
  );
};

ForgotPasswordStep1.propTypes = {
  setStep: PropTypes.func.isRequired,
  setMobile: PropTypes.func.isRequired,
};

export default ForgotPasswordStep1;
