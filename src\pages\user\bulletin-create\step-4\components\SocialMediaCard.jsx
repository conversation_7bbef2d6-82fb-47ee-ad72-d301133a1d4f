import React from "react";
import { ArrowsOutCardinal } from "@phosphor-icons/react";
import MediaBadge from "components/ui/MediaBadge";
import { useDrag, useDrop } from "react-dnd";
const ITEM_TYPE = "SOCIAL_MEDIA_CARD";

const SocialMediaCard = ({ socialMediaTitle, media, index, moveCard }) => {
  const ref = React.useRef(null);

  const [, drop] = useDrop({
    accept: ITEM_TYPE,
    hover(item, monitor) {
      if (!ref.current) {
        return;
      }
      const dragIndex = item.index;
      const hoverIndex = index;

      if (dragIndex === hoverIndex) {
        return;
      }
      const hoverBoundingRect = ref.current.getBoundingClientRect();
      const hoverMiddleY =
        (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      const clientOffset = monitor.getClientOffset();
      const hoverClientY = clientOffset.y - hoverBoundingRect.top;

      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return;
      }
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return;
      }

      moveCard(dragIndex, hoverIndex);
      item.index = hoverIndex;
    },
  });
  const [{ isDragging }, drag] = useDrag({
    type: ITEM_TYPE,
    item: { type: ITEM_TYPE, index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });
  drag(drop(ref));

  return (
    <div
      ref={ref}
      className={`flex gap-2 ${isDragging ? "opacity-50" : "opacity-100"}`}
    >
      <div className="p-[7px] rounded-lg bg-light-neutral-background-medium cursor-pointer">
        <ArrowsOutCardinal size={18} />
      </div>
      <div className="w-full border border-light-neutral-border-low-rest rounded-lg flex items-center gap-3 px-4 py-1">
        <MediaBadge media={media} />
        <span className="font-overline-large">{socialMediaTitle}</span>
      </div>
    </div>
  );
};

export default SocialMediaCard;
