import { CButton } from "components/ui/CButton";
import { Calendar } from "@phosphor-icons/react";
import DropDown from "../../DropDown";
import SingleDatePicker from "./SingleDatePicker";
import { convertToToman, toPersianNumber } from "utils/helper";
import { useState } from "react";

const ChangePlanCard = ({
  setNewPlan,
  setStartPlan,
  setPlanPrice,
  setPlanChangeToggle,
  startPlan,
  planPrice,
}) => {
  const [planCostVal, setPlanCostVal] = useState("");

  return (
    <>
      <div
        className={`col-span-4 bg-light-neutral-surface-card rounded-[8px] p-6`}
      >
        <p className="font-subtitle-large mb-7">تغییر پلن</p>

        <div className="grid gap-4">
          <div className="">
            <DropDown
              subsets={[
                { text: "پایه", value: "basic" },
                { text: "حرفه‌ای", value: "pro" },
                { text: "پیشرفته", value: "advance" },
                { text: "سازمانی", value: "org" },
              ]}
              title="پلن را انتخاب کنید"
              handleChange={(e) => setNewPlan(e?.value)}
              disabled={false}
            />
          </div>
          <div className="">
            <DropDown
              subsets={[
                { text: "از امروز", value: "from_today" },
                {
                  text: "بلافاصله بعد از پایان پلن کنونی",
                  value: "after_expire",
                },
                { text: "تعیین دستی", value: "manual" },
              ]}
              title="تاریخ شروع"
              handleChange={(e) => setStartPlan(e?.value)}
              disabled={false}
            />
          </div>

          {startPlan === "manual" && (
            <div className="font-overline-large">
              <p className="pb-1">انتخاب تاریخ شروع</p>
              <div className="flex gap-4 items-center justify-between p-2 rounded-lg bg-white border border-light-neutral-border-medium-rest w-full">
                <Calendar size={22} className="text-light-neutral-text-low" />
                <SingleDatePicker />
              </div>
            </div>
          )}

          <div className="">
            <DropDown
              subsets={[
                {
                  text: "قیمت اصلی طرح (۶۰۰٬۰۰۰ تومان)",
                  value: "original_price",
                },
                { text: "رایگان", value: "free" },
                { text: "تعیین دستی", value: "manual" },
              ]}
              title="هزینه"
              handleChange={(e) => setPlanPrice(e?.value)}
              disabled={false}
            />
          </div>

          {planPrice === "manual" && (
            <div className="font-overline-large">
              <p className="pb-1">مبلغ</p>
              <input
                className="flex gap-4 items-center justify-between p-2 rounded-lg bg-white border border-light-neutral-border-medium-rest w-full outline-none mb-2"
                type="number"
                value={planCostVal}
                min={0}
                onChange={(e) => setPlanCostVal(e.target.value)}
                placeholder="مبلغ را وارد کنید"
              />
              <span className="font-caption-medium text-light-neutral-text-low">
                {toPersianNumber(convertToToman(planCostVal))}
              </span>
            </div>
          )}
        </div>

        <div className="flex items-center gap-2 mt-7">
          <div className="w-full" onClick={() => setPlanChangeToggle(false)}>
            <CButton role="neutral">انصراف</CButton>
          </div>
          <div className="w-full">
            <CButton role="neutral" readOnly>
              تایید
            </CButton>
          </div>
        </div>
      </div>
    </>
  );
};

export default ChangePlanCard;
