import { useCompareStore } from "store/compareStore.js";
import ContentCard from "./ContentCard.jsx";
import { countAvg } from "../../../utils.js";

const ContentTwitter = () => {
  const { content } = useCompareStore((state) => state.compare);

  const chartData = (key, displayName) => {
    const filteredContent = content.filter((item) => {
      return Object.values(item).some((nestedItem) => nestedItem?.key === key);
    });

    if (filteredContent.length === 0) return [];

    const data = countAvg(filteredContent, key).map((item) => ({
      ...item,
      name: displayName,
    }));

    return data;
  };

  return (
    <>
      <ContentCard
        titleOffset={-12.5 * content.length}
        title="توییت"
        data={chartData("post", "توییت")}
      />
      <ContentCard
        titleOffset={-12.5 * content.length}
        title="کوت"
        data={chartData("qoute", "کوت")}
      />
      <ContentCard
        titleOffset={-12.5 * content.length}
        title="ریتوییت"
        data={chartData("repost", "ریتوییت")}
      />
    </>
  );
};

export default ContentTwitter;
