import React from "react";
import { useCompareStore } from "store/compareStore.js";
import CotentCard from "./ContentCard.jsx";
import { countAvg } from "../../../utils.js";

const ContentTelegram = () => {
  const { content } = useCompareStore((state) => state.compare);

  const fixTextCount = (data) => {
    return data
      .filter((user) => user.id || user.q)
      .map((item) => {
        return {
          ...item,
          text_count: item.post_count - item.photo_count - item.video_count,
        };
      })
      .map((item, _, array) => {
        return {
          name: item.username,
          x: item.text_count,
          y:
            (item.text_count /
              array.reduce((x, y) => {
                if (y?.text_count) {
                  return x + +y?.text_count;
                }
                return x;
              }, 0)) *
            100,
        };
      });
  };

  return (
    <>
      <CotentCard
        titleOffset={-12.5 * content.length}
        title="مجموع محتوا"
        data={countAvg(content, "post_count")}
      />

      <CotentCard
        titleOffset={-12.5 * content.length}
        title="متن"
        data={fixTextCount(content)}
      />

      <CotentCard
        titleOffset={-12.5 * content.length}
        title="تصویر"
        data={countAvg(content, "photo_count")}
      />
      <CotentCard
        titleOffset={-12.5 * content.length}
        title="ویدئو"
        data={countAvg(content, "video_count")}
      />
    </>
  );
};

export default ContentTelegram;
