import { CheckCircle, Graph, TrashSimple } from "@phosphor-icons/react";
import { CButton } from "components/ui/CButton";
import { memo, useCallback, useEffect, useRef, useState } from "react";
import ForceGraph2D from "react-force-graph-2d";
import { forceCollide } from "d3-force-3d";
import defaultNodeImage from "assets/images/default.png";
import Divider from "components/ui/Divider";
import { notification, shortener } from "utils/helper";
import { useRelationStore } from "store/relationDetectStore";
import relationDetect from "service/api/relationDetect";
import { useNavigate } from "react-router-dom";
import { ToastContainer } from "react-toastify";

const loadImage = (src) => {
  return new Promise((resolve) => {
    const img = new Image();
    img.src = src;
    img.onload = () => resolve(img);
    img.onerror = () => {
      const fallbackImg = new Image();
      fallbackImg.src = defaultNodeImage;
      resolve(fallbackImg);
    };
  });
};

const GraphPreview = () => {
  const nodeDetails = useRelationStore((state) => state.relation.nodeDetails);
  const edgeDetails = useRelationStore((state) => state.relation.edgeDetails);
  const relationType = useRelationStore(
    (state) => state.relation.edgeDetails?.relationType
  );
  const setNodeDetails = useRelationStore((state) => state.setNodeDetails);
  const clearNodeDetails = useRelationStore((state) => state.clearNodeDetails);
  const [nodes, setNodes] = useState([]);
  const [links, setLinks] = useState([]);
  const [dimensions, setDimensions] = useState({ width: 300, height: 300 });
  const fgRef = useRef();
  const containerRef = useRef();
  const [imageCache, setImageCache] = useState({});
  const [finalGraphData, setFinalGraphData] = useState([]);
  const navigate = useNavigate();

  useEffect(() => {
    const checkDefaultImage = async () => {
      const img = await loadImage(defaultNodeImage);
      if (!img || !img.complete || img.naturalWidth === 0) {
        console.warn(
          "Default image failed to load. Please check the path:",
          defaultNodeImage
        );
      }
    };
    checkDefaultImage();
  }, []);

  useEffect(() => {
    fgRef.current?.d3Force(
      "collision",
      forceCollide((node) => Math.sqrt(node.level * 20))
    );
    fgRef.current?.d3Force("link")?.distance(() => 120);
  }, [nodes, links]);

  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current) {
        const { width, height } = containerRef.current.getBoundingClientRect();
        setDimensions({ width, height });
      }
    };
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    if (nodes.length > 0 && fgRef.current) {
      const nodeCount = nodes.length;
      let zoomLevel;
      if (nodeCount <= 5) {
        zoomLevel = 1.9;
      } else if (nodeCount <= 10) {
        zoomLevel = 1.5;
      } else if (nodeCount <= 20) {
        zoomLevel = 1;
      } else {
        zoomLevel = 0.6;
      }
      fgRef.current.zoom(zoomLevel, 400);
    }
  }, [nodes]);

  useEffect(() => {
    const preloadImages = async () => {
      const newCache = { ...imageCache };
      for (const node of nodes) {
        if (!newCache[node.id]) {
          const avatarUrl = nodeDetails.find(
            (detail) => `node-${detail.id}` === node.id
          )?.avatar;
          const imageSrc = avatarUrl || defaultNodeImage;
          const img = await loadImage(imageSrc);
          newCache[node.id] = img;
        }
      }
      setImageCache(newCache);
    };
    preloadImages();
  }, [nodes, nodeDetails]);

  const renderNode = (node, ctx, globalScale) => {
    const baseSize = 15;
    const scaleFactor = 3;
    const imgSize = baseSize + node.level * scaleFactor;
    const borderWidth = 1;
    const borderColor = "#9198AD";
    const padding = 1;
    const img = imageCache[node.id] || new Image();
    img.src = img.src || defaultNodeImage;
    if (!img.complete || img.naturalWidth === 0) {
      return;
    }

    ctx.save();
    ctx.beginPath();
    ctx.arc(node.x, node.y, imgSize / 2, 0, Math.PI * 2);
    ctx.closePath();
    ctx.clip();
    ctx.drawImage(
      img,
      node.x - imgSize / 2,
      node.y - imgSize / 2,
      imgSize,
      imgSize
    );
    ctx.restore();

    ctx.beginPath();
    ctx.arc(
      node.x,
      node.y,
      imgSize / 2 + padding + borderWidth / 2,
      0,
      Math.PI * 2
    );
    ctx.lineWidth = borderWidth;
    ctx.strokeStyle = borderColor;
    ctx.stroke();
    if (globalScale > 0.5) {
      ctx.font = `${12 / globalScale}px iranyekan`;
      ctx.textAlign = "center";
      ctx.textBaseline = "middle";
      ctx.fillStyle = "black";
      ctx.fillText(node.path, node.x, node.y + imgSize / 2 + 10);
    }
  };

  const nodePointerAreaPaint = (node, color, ctx) => {
    const baseSize = 15;
    const scaleFactor = 3;
    const imgSize = baseSize + node.level * scaleFactor;
    const padding = 1;
    const borderWidth = 1;
    const hitboxRadius = imgSize / 2 + padding + borderWidth + 5;

    ctx.beginPath();
    ctx.arc(node.x, node.y, hitboxRadius, 0, Math.PI * 2);
    ctx.fillStyle = color;
    ctx.fill();
  };

  const renderLink = (link, ctx) => {
    ctx.beginPath();
    ctx.moveTo(link.source.x, link.source.y);
    ctx.lineTo(link.target.x, link.target.y);
    ctx.lineWidth = 1.5;
    ctx.setLineDash(link.isIndirect ? [5, 3] : []);
    ctx.strokeStyle = "#9198AD";
    ctx.stroke();
    ctx.setLineDash([]);
  };

  const generateGraph = useCallback(() => {
    if (!nodeDetails || !edgeDetails) return;
    const newNodes = [];
    const newLinks = [];
    const isIndirect = relationType === "indirect";
    nodeDetails?.forEach((nodeDetail) => {
      const nodeId = `node-${nodeDetail.id}`;
      newNodes.push({
        id: nodeId,
        title: nodeDetail.user_name,
        path:
          shortener(nodeDetail.title, 15, "rtl") ||
          shortener(nodeDetail.user_title, 15, "rtl") ||
          shortener(nodeDetail.user_name, 15, "rtl"),
        level: 3,
      });
    });
    for (let i = 0; i < nodeDetails.length - 1; i++) {
      const currentNodeId = `node-${nodeDetails[i].id}`;
      const nextNodeId = `node-${nodeDetails[i + 1].id}`;
      newLinks.push({
        source: currentNodeId,
        target: nextNodeId,
        isIndirect,
      });
    }
    const getNodeConnections = (nodeId) => {
      return newLinks?.filter(
        (link) => link.source === nodeId || link.target === nodeId
      ).length;
    };
    const addConnection = (sourceId, targetId) => {
      newLinks.push({
        source: sourceId,
        target: targetId,
        isIndirect,
      });
    };
    const imageNodes = newNodes;
    for (let i = 0; i < imageNodes.length; i++) {
      const currentNode = imageNodes[i];
      const connections = getNodeConnections(currentNode.id);
      if (connections < 2) {
        let bestTargetIndex = -1;
        let minConnections = Infinity;
        for (let j = 0; j < imageNodes.length; j++) {
          if (i !== j) {
            const targetNode = imageNodes[j];
            const targetConnections = getNodeConnections(targetNode.id);
            const connectionExists = newLinks.some(
              (link) =>
                (link.source === currentNode.id &&
                  link.target === targetNode.id) ||
                (link.source === targetNode.id &&
                  link.target === currentNode.id)
            );
            if (!connectionExists && targetConnections < minConnections) {
              minConnections = targetConnections;
              bestTargetIndex = j;
            }
          }
        }
        if (bestTargetIndex !== -1) {
          addConnection(currentNode.id, imageNodes[bestTargetIndex].id);
        }
      }
    }
    setNodes(newNodes);
    setLinks(newLinks);
  }, [nodeDetails]);

  const deleteNodeHandler = (id) => {
    setNodeDetails(nodeDetails.find((item) => item.id === id));
  };

  useEffect(() => {
    generateGraph();
  }, [nodeDetails]);

  function separateData(data) {
    const person = [];
    const source = [];
    const organization = [];
    const location = [];

    data.forEach((item) => {
      const entry = item.title || item.user_name || "";

      if (item.id.startsWith("person-")) {
        person.push(entry);
      } else if (item.id.startsWith("organization-")) {
        organization.push(entry);
      } else if (item.id.startsWith("location-")) {
        location.push(entry);
      } else {
        source.push(entry);
      }
    });

    return { person, source, organization, location };
  }

  const generateGraphRelation = async () => {
    try {
      const separatedData = separateData(nodeDetails);
      const res = await relationDetect?.generateGraph({
        relation: separatedData,
        connection_mode: edgeDetails?.relationType,
        relation_type: edgeDetails?.relationIndex,
        node_weight: edgeDetails?.nodeWeight,
        start_date: edgeDetails?.timeSettings?.from,
        end_date: edgeDetails?.timeSettings?.to,
      });
      setFinalGraphData(res?.data?.data);
      console.log(res?.data?.data)
      navigate("/app/relation-detect/results", {
        state: {
          data: res?.data?.data,
          separatedData,
        },
      });
    } catch (error) {
      notification.error(
        "حد اقل دو گره را انتخاب کنید",
        <CheckCircle size={20} className="text-light-error-background-rest" />
      );
    }
  };

  useEffect(() => {
    clearNodeDetails();
  }, []);

  return (
    <div className="bg-light-neutral-surface-card rounded-lg p-6">
      <p className="font-subtitle-large mb-2">پیش نمایش</p>
      <Divider />
      <div className="flex items-center gap-3 overflow-x-auto scrollbar-thin mt-2 whitespace-nowrap pb-1 select-none">
        {nodeDetails?.map((item) => (
          <div
            key={item?.id}
            className="font-body-medium border rounded-md border-light-neutral-border-low-rest p-1 flex items-center justify-between gap-3 min-w-36"
          >
            <div className="flex items-center gap-1">
              <img
                src={item?.avatar || defaultNodeImage}
                className="rounded-full w-5"
                onError={(e) => (e.target.src = defaultNodeImage)}
              />
              <p>{shortener(item?.title || item?.user_title)}</p>
            </div>
            <div
              className="bg-light-error-background-highlight p-1 w-fit cursor-pointer"
              onClick={() => deleteNodeHandler(item?.id)}
            >
              <TrashSimple size={12} className="text-light-error-text-rest" />
            </div>
          </div>
        ))}
      </div>
      <div
        ref={containerRef}
        style={{
          width: "100%",
          height: "500px",
        }}
      >
        <ForceGraph2D
          ref={fgRef}
          graphData={{ nodes, links }}
          dagMode={null}
          dagLevelDistance={300}
          backgroundColor="transparent"
          linkColor={() => "#9198AD"}
          nodeRelSize={1}
          nodeId="id"
          nodeVal={(node) => node.level * 10}
          nodeLabel="title"
          linkDirectionalParticles={2}
          linkDirectionalParticleWidth={0}
          d3VelocityDecay={0.3}
          width={dimensions.width}
          height={dimensions.height}
          zoom={0.8}
          maxZoom={10}
          minZoom={0.4}
          nodeCanvasObject={renderNode}
          nodeCanvasObjectMode={() => "replace"}
          nodePointerAreaPaint={nodePointerAreaPaint}
          linkCanvasObject={renderLink}
        />
      </div>
      <div
        onClick={generateGraphRelation}
        className="mt-3 flex flex-row-reverse items-center justify-between"
      >
        <CButton leftIcon={<Graph size={17} />} role="neutral" width={200}>
          ایجاد گراف ارتباطی
        </CButton>
        <p className="font-body-medium text-light-neutral-text-medium">
          با توجه به گره‌ها و تنظیمات یال‌ها گراف ارتباط را بسازید. ساخت گراف
          ممکن است اندکی زمان‌بر باشد
        </p>
      </div>
      <ToastContainer />
    </div>
  );
};

export default memo(GraphPreview);
