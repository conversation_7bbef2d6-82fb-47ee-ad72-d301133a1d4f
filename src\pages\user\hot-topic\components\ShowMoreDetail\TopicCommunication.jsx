import { useMemo } from "react";
import Doughnut from "components/Charts/Doughnut";
import { toPersianNumber } from "utils/helper.js";

const TopicCommunication = ({ data: { category } }) => {
  const cat = useMemo(() => {
    const probs = category?.prob || {};
    const totalPercent = Object.values(probs).reduce(
      (sum, percent) => sum + percent,
      0,
    );

    return Object.entries(probs)
      .filter(([_, percent]) => percent > 0)
      .map(([category, percent]) => [
        category,
        totalPercent > 1 ? (percent / totalPercent) * 100 : percent * 100,
      ])
      .sort(([, a], [, b]) => b - a);
  }, [category]);

  const selectCategory = {
    culture_art: "فرهنگی و هنری",
    economic: "اقتصادی",
    politic: "سیاسی",
    religion: "مذهبی",
    dailylife: "روزمره",
    science_tech_health: "علم و فناوری و سلامت",
    security_defense: "امنیتی و دفاعی",
    social: "اجتماعی",
    sport: "ورزشی",
  };

  const filteredData = cat
    .map(([category, percent]) => ({
      name: selectCategory[category] || category,
      y: Math.round(percent),
    }))
    .filter((item) => item.y > 0);

  return filteredData.length ? (
    <div className="flex flex-col mb-8">
      <p className="font-subtitle-medium mb-4 self-start">دسته‌بندی موضوعی:</p>
      <div className="flex items-center gap-20">
        <Doughnut
          name="category"
          data={filteredData}
          height={220}
          legendFormatter={function () {
            if (this.y === 0) return "";
            return `<div dir="rtl" style="display:flex;align-items: center; justify-content: space-between; font-family:iranyekan;width:200px;padding:2px;">
                <div style="font-size:16px">
                  ${toPersianNumber(this.y)}٪
                </div>
                <div style="flex:1;font-size:14px;text-align:right;padding-right:4px">${
                  this.name
                }</div>
                <span style="width: 12px; height: 12px; background-color: ${this.color}; border-radius: 50%;"></span>
                
              </div>`;
          }}
          tooltipFormatter={function () {
            return `<div style="display:flex;flex-direction:column;gap:8px;text-align:center;font-family:iranyekan;font-size:14px">
              <div>${this.key}</div>
              <div>${toPersianNumber(this.y)}٪
              </div>
            </div>`;
          }}
          colors={[
            "#432FA7",
            "#792FA7",
            "#A72F85",
            "#E03838",
            "#E0B138",
            "#F4538A",
            "#8E7AB5",
            "#F1FADA",
          ]}
        />
      </div>
    </div>
  ) : null;
};

export default TopicCommunication;
