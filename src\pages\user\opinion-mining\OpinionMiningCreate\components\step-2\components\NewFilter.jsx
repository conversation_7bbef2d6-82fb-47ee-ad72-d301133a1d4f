import { useEffect, useState } from "react";
import Divider from "components/ui/Divider";
import SelectPlatform from "components/FilterSelector/SelectPlatform";
import { platformFilters } from "utils/platformFilters";

const NewFilter = ({
  alertData,
  setAlertData,
  setStatus,
  disabledNews,
  disabledInstagram,
  disabledTelegram,
  disabledTwitter,
}) => {
  const [newQuery, setNewQuery] = useState(alertData?.q || "");

  const setPlatform = (media) => {
    setAlertData((l) => {
      const copy = JSON.parse(JSON.stringify(l));
      copy.platform = {};
      media.forEach((item) => {
        copy.platform[item] = Object.hasOwn(l.platform, item)
          ? l.platform[item]
          : platformFilters[item];
      });
      return copy;
    });
  };

  useEffect(() => {
    setAlertData((l) => {
      const copy = JSON.parse(JSON.stringify(l));
      copy.q = newQuery;
      return copy;
    });
  }, [newQuery]);

  return (
    <div>
      <div className="mt-12 mb-6">
        <Divider />
      </div>

      <div className="flex flex-col gap-4">
        <span className="font-body-medium">بستر مورد بررسی:</span>
        <SelectPlatform
          handleChange={setPlatform}
          initialValue={
            alertData?.platform ? Object.keys(alertData.platform) : ["twitter"]
          }
          disabledNews={disabledNews}
          disabledInstagram={disabledInstagram}
          disabledTelegram={disabledTelegram}
          disabledTwitter={true}
        />
      </div>

      <div className="my-6">
        <Divider />
      </div>

      {/* <div className={!showAdvancedSearchDrawer ? "hidden" : "block"}>
        <CustomSearchDrawer
          inputQuery={newQuery}
          setShowMore={setShowAdvancedSearchDrawer}
          onSubmit={(e) => {
            setNewQuery(e);
            setShowAdvancedSearchDrawer(false);
          }}
        />
      </div> */}
    </div>
  );
};

export default NewFilter;
