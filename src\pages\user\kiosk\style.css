.hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.card-animation {
  opacity: 0;
  animation: fadeIn 0.8s ease-in-out forwards; 
}

.card-delay-1 {
  animation-delay: 0.2s;
}

.card-delay-2 {
  animation-delay: 0.4s;
}

.card-delay-3 {
  animation-delay: 0.6s;
}

.card-delay-4 {
  animation-delay: 0.8s;
}
.swiper-card {
  width: 23rem !important; 
}

@media (max-width: 1950px) {
  .swiper-card {
    width: 23rem !important;
    margin-right: 0.7rem;
    margin-left: 0.7rem;
  }

  .circle-cards {
    width: 8rem !important;
    height: 8rem !important;
  }

  .circle-cards-text {
    width: 8rem !important;
  }
}

@media (max-width: 1590px) {
  .swiper-card {
    width: 17rem !important;
  }

  .circle-cards {
    width: 6.5rem !important;
    height: 6.5rem !important;
  }

  .circle-cards-text {
    width: 6.5rem !important;
  }
}
