import { Field, Form, Formik } from "formik";
import { useContext, useState } from "react";
import { emailVerifyOTPSchema } from "utils/validationSchemas";
import { CButton } from "components/ui/CButton";
import { COtpInput } from "components/ui/COtpInput";
import { CheckCircle } from "@phosphor-icons/react";
import { CInput } from "components/ui/CInput";
import authService from "service/api/authService";
import DisabledOTP from "components/ui/DisabledOTP";
import { notification } from "utils/helper";
import AuthContext from "context/auth-context";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";

const NewEmail = ({ setStep }) => {
  const { loadProfile } = useContext(AuthContext);
  const [isLoading, setIsLoading] = useState(false);
  const [emailValue, setEmailValue] = useState("");
  const [currentStep, setCurrentStep] = useState(1);
  const sendEmail = async () => {
    setIsLoading(true);
    try {
      const res = await authService.changeEmail({
        email: emailValue,
      });
      setCurrentStep(2);
      notification.success(
        `کد تایید برای ایمیل ${emailValue} ارسال شد و تا لحظاتی دیگر آن را دریافت خواهید کرد. لطفا ایمیل خود را بررسی کنید.`,
        <CheckCircle className="text-light-success-text-rest" size={26} />
      );
    } catch (error) {
      console.log(error);
    }
    setIsLoading(false);
  };
  const handleSubmit = async (values, { setSubmitting }) => {
    try {
      const res = await authService.verifyEmail({
        email: emailValue,
        token: values?.otp,
      });
      setCurrentStep(2);
    } catch (error) {
      console.log(error);
    }
    setStep(3);
    setSubmitting(false);
    loadProfile();
  };
  const breadcrumbList = [
    { title: "پروفایل کاربری", link: "/app/user/profile" },
    { title: "احراز ایمیل" },
  ];
  useBreadcrumb(breadcrumbList);
  return (
    <div className="flex flex-col items-center mt-4 gap-4 min-h-screen">
      <div className="w-[570px] bg-light-neutral-surface-card rounded-lg">
        <div className="max-w-lg m-auto">
          <h2 className="font-subtitle-large text-gray-800 mt-8 mb-5 text-right">
            ثبت آدرس ایمیل جدید
          </h2>
          <p className={"font-overline-large pb-1"}>۱. ایمیل جدید</p>
          <CInput
            caption={
              "آدرس ایمیل جدید را وارد کرده و سپس روی «دریافت کد تایید» کلیک کنید"
            }
            placeholder={"برای مثال <EMAIL>"}
            direction="ltr"
            customActionText={"دریافت کد تایید"}
            customAction={sendEmail}
            value={emailValue}
            onChange={(e) => setEmailValue(e.target.value)}
          />
        </div>
        <Formik
          initialValues={{ otp: "" }}
          validationSchema={emailVerifyOTPSchema}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting, setFieldValue }) => (
            <Form className="font-body-medium flex-1 bg-white mx-auto w-full h-full max-w-lg overflow-hidden">
              <p
                className={"font-overline-large pb-1"}
                style={{ opacity: currentStep === 2 ? 1 : 0.5 }}
              >
                ۲. کد تایید ایمیل جدید
              </p>
              <Field
                id={"otp"}
                name={"otp"}
                component={currentStep === 2 ? COtpInput : DisabledOTP}
                size={"lg"}
                validation={"none"}
                direction={"ltr"}
                onChange={(value) => {
                  setFieldValue("otp", value);
                }}
              />
              <div className="flex items-center pb-5 justify-end">
                <div className="w-[200px]">
                  <CButton
                    type={"submit"}
                    size={"lg"}
                    disabled={isSubmitting}
                    readOnly={currentStep === 1}
                  >
                    ادامه
                  </CButton>
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default NewEmail;
