import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import bulletin from "service/api/bulletin";
import { useBulletinStore } from "store/bulletinStore.js";
import BulletinCreate from "pages/user/bulletin-create";
import useSearchStore from "store/searchStore.js";

const BulletinEdit = () => {
  const { id } = useParams();
  const [loading, setLoading] = useState(false);
  const setBulletin = useBulletinStore((state) => state.setBulletin);
  const { setQuery, setFilters } = useSearchStore();

  const getBulletin = async () => {
    setLoading(true);
    try {
      const { data } = await bulletin.getById(id, false);
      let modifiedData = JSON.parse(JSON.stringify(data?.data));

      if (modifiedData) {
        modifiedData.type =
          modifiedData.bulletin_type === "periodic" ? "auto" : "manual";
        if (modifiedData.bulletin_type === "periodic") {
          modifiedData.custom_period_time =
            modifiedData.period.custom_period_time;
          modifiedData.custom_period = modifiedData.period.custom_period;
          modifiedData.period = modifiedData.period.period;
        }
      }

      if (modifiedData.params?.content) {
        if (modifiedData.type === "manual")
          modifiedData.contentManual = modifiedData.params?.content;
        else modifiedData.content = modifiedData.params?.content;
        setFilters(modifiedData.params?.content?.filter);
        setQuery(modifiedData.params?.content?.q);
        modifiedData.step = 2;
      }

      if (modifiedData.params?.chart) {
        modifiedData.chart = modifiedData.params?.chart;
        modifiedData.step = 3;
      }

      if (modifiedData.params?.metadata) {
        modifiedData.metadata = modifiedData.params?.metadata;
        modifiedData.step = 4;
      }

      if (modifiedData?.bulletin_state === "created") {
        modifiedData.step = 1;
      }
      setBulletin(modifiedData);
    } catch (error) {
      console.log(error.response.data.message);
    }
    setLoading(false);
  };

  useEffect(() => {
    getBulletin();
  }, []);

  loading && "Loading...";

  return <BulletinCreate isUpdate={true} />;
};

export default BulletinEdit;
