import { useEffect, useRef, useState } from "react";
import clsx from "clsx";
import PropTypes from "prop-types";

import {
  File,
  FileImage,
  FilePdf,
  FileVideo,
  FileXls,
  MicrosoftWordLogo,
  Trash,
  TrashSimple,
} from "@phosphor-icons/react";

const AnnouncementAttach = ({
  initialPreview,
  title,
  description,
  description2,
  name,
  fullWidth,
  fileSizeLimitInMB = 10,
  boxIcon: BoxIcon,
  allowMultiple = false,
  imageOnly = false,
  accept = imageOnly
    ? ".jpeg,.jpg,.png"
    : ".jpeg,.jpg,.png,.docx,.pdf,.doc,.csv,.xls,.txt,.zip",
  onChange = () => {},
}) => {
  const fileInputRef = useRef(null);
  const [fileNames, setFileNames] = useState([]);
  const [isDragging, setIsDragging] = useState(false);
  const [imagePreview, setImagePreview] = useState(initialPreview);
  const [errorText, setErrorText] = useState("");

  const handleReset = () => {
    fileInputRef.current.value = null;
    setFileNames([]);
    setImagePreview(null);
    onChange([]);
  };

  const validateFileSize = (file) => {
    const fileSizeMB = file.size / 1024 / 1024;
    if (fileSizeMB > fileSizeLimitInMB) {
      setErrorText(`حجم فایل باید کمتر از ${fileSizeLimitInMB}MB باشد`);
      return false;
    }
    setErrorText("");
    return true;
  };

  const validateFileType = (file) => {
    if (imageOnly && !file.type.startsWith("image/")) {
      setErrorText("فقط فایل‌های تصویری مجاز هستند");
      return false;
    }
    return true;
  };

  const handleFileChange = async (event) => {
    const files = Array.from(event.target.files);
    const validFiles = files.filter(
      (file) => validateFileSize(file) && validateFileType(file)
    );

    if (validFiles.length > 0) {
      if (allowMultiple) {
        setFileNames((prevFiles) => {
          const updatedFiles = [...prevFiles, ...validFiles];
          onChange(updatedFiles);
          return updatedFiles;
        });
      } else {
        const singleFile = validFiles.slice(0, 1);
        setFileNames(singleFile);
        onChange(singleFile);
      }

      const file = validFiles[0];
      if (file.type.startsWith("image/")) {
        const reader = new FileReader();
        reader.onload = (e) => {
          setImagePreview(e.target.result);
        };
        reader.readAsDataURL(file);
      } else {
        setImagePreview(null);
      }
    } else {
      handleReset();
    }
  };

  const handleDrop = async (event) => {
    event.preventDefault();
    setIsDragging(false);
    const files = Array.from(event.dataTransfer.files);
    const validFiles = files.filter(
      (file) => validateFileSize(file) && validateFileType(file)
    );

    if (validFiles.length > 0) {
      if (allowMultiple) {
        setFileNames((prevFiles) => {
          const updatedFiles = [...prevFiles, ...validFiles];
          onChange(updatedFiles);
          return updatedFiles;
        });

        const dataTransfer = new DataTransfer();
        validFiles.forEach((file) => dataTransfer.items.add(file));
        fileInputRef.current.files = dataTransfer.files;
      } else {
        const singleFile = validFiles.slice(0, 1);
        setFileNames(singleFile);
        onChange(singleFile);

        const dataTransfer = new DataTransfer();
        singleFile.forEach((file) => dataTransfer.items.add(file));
        fileInputRef.current.files = dataTransfer.files;
      }

      const file = validFiles[0];
      if (file.type.startsWith("image/")) {
        const reader = new FileReader();
        reader.onload = (e) => {
          setImagePreview(e.target.result);
        };
        reader.readAsDataURL(file);
      } else {
        setImagePreview(null);
      }
    } else {
      handleReset();
    }
  };

  const handleDragOver = (event) => {
    event.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDeleteFile = (indexToDelete) => {
    setFileNames((prevFiles) => {
      const updatedFiles = prevFiles.filter(
        (_, index) => index !== indexToDelete
      );
      onChange(updatedFiles);
      return updatedFiles;
    });

    const dataTransfer = new DataTransfer();
    fileNames
      .filter((_, index) => index !== indexToDelete)
      .forEach((file) => dataTransfer.items.add(file));
    fileInputRef.current.files = dataTransfer.files;

    if (indexToDelete === 0 && fileNames.length > 0) {
      const nextFile = fileNames[1];
      if (nextFile && nextFile.type.startsWith("image/")) {
        const reader = new FileReader();
        reader.onload = (e) => {
          setImagePreview(e.target.result);
        };
        reader.readAsDataURL(nextFile);
      } else {
        setImagePreview(null);
      }
    }
  };

  useEffect(() => {
    if (errorText) {
      handleReset();
    }
  }, [errorText]);

  const getFileIcon = (file) => {
    const type = file.type;
    if (type.startsWith("image/")) {
      return <FileImage size={34} color="#90EE90" />;
    }
    if (type === "application/pdf") {
      return <FilePdf size={34} color="#FF0000" />;
    }
    if (type.includes("msword") || type.includes("wordprocessingml")) {
      return <MicrosoftWordLogo size={34} color="#0000FF" />;
    }
    if (type.includes("spreadsheetml") || type.includes("excel")) {
      return <FileXls size={34} color="#008000" />;
    }
    if (type.includes("zip") || type.includes("compressed")) {
      return <FileVideo size={34} color="#FFA500" />;
    }
    return <File size={34} color="#808080" />;
  };

  const boxBorder = clsx("border-[1px] border-[#D1D6DB]", {
    "border-solid": fileNames.length,
    "border-dashed": !fileNames.length,
  });

  return (
    <div className="flex flex-col gap-4">
      <div
        className={`flex items-center justify-center ${
          fullWidth ? "w-full" : "w-80"
        } h-[92px] ${isDragging ? "border-blue-500 bg-blue-50" : ""}`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <label
          htmlFor={name}
          className={`flex flex-col items-center justify-center ${
            fullWidth ? "w-full" : "w-80"
          } h-[112px] rounded-lg cursor-pointer ${boxBorder}`}
        >
          {imagePreview ? (
            <img
              src={imagePreview}
              alt="Preview"
              className="w-full h-full object-cover rounded-lg"
            />
          ) : (
            <div
              className={`flex flex-col items-center justify-center pt-5 pb-6 ${
                description2 ? "gap-2" : "gap-4"
              }`}
            >
              <BoxIcon size={25} className="text-light-neutral-text-medium" />
              <p className="font-button-medium">{description}</p>
              {description2 && (
                <p className="font-button-small text-light-neutral-text-medium">
                  {description2}
                </p>
              )}
            </div>
          )}

          <input
            name={name}
            id={name}
            type="file"
            accept={accept}
            className="hidden"
            ref={fileInputRef}
            onChange={handleFileChange}
            multiple={allowMultiple}
          />
        </label>
      </div>
      <span className="text-light-error-text-rest font-body-medium">
        {errorText}
      </span>

      {allowMultiple && fileNames.length > 0 && (
        <div className="flex flex-wrap w-full gap-4 mt-4">
          {fileNames.map((file, index) => (
            <div
              key={index}
              className="relative flex flex-col items-center justify-center w-36 h-20 border border-gray-300 rounded-lg p-2"
            >
              <button
                onClick={() => handleDeleteFile(index)}
                className="absolute top-2 left-2 p-1 bg-[#E1E8EF80] rounded-md hover:text-red-700"
              >
                <TrashSimple size={16} />
              </button>
              <div className="text-gray-600">{getFileIcon(file)}</div>
              <p className="text-sm text-center truncate w-full mt-1">
                {file.name}
              </p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

AnnouncementAttach.propTypes = {
  initialPreview: PropTypes.string,
  title: PropTypes.string,
  description: PropTypes.string,
  name: PropTypes.string,
  fileSizeLimitInMB: PropTypes.number,
  onChange: PropTypes.func,
  allowMultiple: PropTypes.bool,
  imageOnly: PropTypes.bool,
};

export default AnnouncementAttach;
