// src/components/Sentiment.jsx
import { memo, useEffect, useState } from "react";
import { Card } from "components/ui/Card.jsx";
import SentimentCard from "./SentimentCard.jsx";
import { SpinnerGap } from "@phosphor-icons/react";
import { useCompareStore } from "store/compareStore.js";
import advanceSearch from "service/api/advanceSearch.js";
import PropTypes from "prop-types";
import { countAvg } from "../../../utils.js";
import Title from "./Title.jsx";
import { buildRequestData } from "utils/requestData.js";
import ExportMenu from "components/ExportMenu/index.jsx";

const Sentiment = ({ compareType }) => {
  const { platform, date, fields, sentiment } = useCompareStore(
    (state) => state.compare
  );
  const setCompare = useCompareStore((state) => state.setCompare);
  const [loading, setLoading] = useState(false);
  const [fieldsCleared, setFieldsCleared] = useState([]);

  // Update fieldsCleared only when relevant field properties change
  useEffect(() => {
    const newFieldsCleared = fields.filter((value) =>
      compareType === "profile" ? value.id : value.q
    );
    const hasChanged =
      newFieldsCleared.length !== fieldsCleared.length ||
      newFieldsCleared.some((field, index) => {
        const prevField = fieldsCleared[index] || {};
        return compareType === "profile"
          ? field.id !== prevField.id
          : field.q !== prevField.q;
      });

    if (hasChanged) {
      console.log("fieldsCleared updated:", newFieldsCleared);
      setFieldsCleared(newFieldsCleared);
    }
  }, [fields, compareType]);

  const fetchProfileData = async (fieldsCleared) => {
    try {
      const foo = fieldsCleared?.map((field) => {
        const filterObject = {
          date,
          platform,
          q: "",
          sources: field?.id
            ? [field?.id.toString()]
            : field?.user_id
            ? [field?.user_id.toString()]
            : [],
        };

        let requestData = buildRequestData(filterObject, "sentiments");
        return requestData || [];
      });
      const reqs = foo.map((q) => advanceSearch.search(q));
      const response = await Promise.allSettled(reqs);

      return response
        .filter((res) => res.status === "fulfilled")
        .map((res) => {
          let total = res?.value?.data?.data?.total || 0;
          let data = res?.value?.data?.data?.[platform] || [];

          const formattedData = {};

          data.forEach((item) => {
            formattedData[item.key] = Number(
              ((item.count / total) * 100).toFixed(2)
            );
          });
          return formattedData;
        });
    } catch (e) {
      console.log(e);
      return [];
    }
  };

  const fetchTopicData = async (fieldsCleared, abortController) => {
    const reqsQueries = fieldsCleared?.map((field) => {
      const filterObject = {
        date,
        platform,
        q: field.q || field.username,
      };
      const requestData = buildRequestData(filterObject, "sentiments");
      return advanceSearch.search(requestData, abortController);
    });
    const response = await Promise.allSettled(reqsQueries);
    return response
      .filter((res) => res.status === "fulfilled")
      .map((res) => {
        let total = res?.value?.data?.data?.total || 0;
        let data = res?.value?.data?.data?.[platform] || [];

        const formattedData = {};

        data.forEach((item) => {
          formattedData[item.key] = Number(
            ((item.count / total) * 100).toFixed(2)
          );
        });
        return formattedData;
      });
  };

  const processResult2 = (result = []) => {
    return result.map((item) => ({
      negative: { key: "negative", count: item.negative * 100 },
      neutral: { key: "neutral", count: item.neutral * 100 },
      positive: { key: "positive", count: item.positive * 100 },
    }));
  };

  const getData = async (fieldsCleared, abortController) => {
    if (!fieldsCleared.length) return;
    setLoading(true);
    try {
      let result =
        compareType === "profile"
          ? await fetchProfileData(fieldsCleared)
          : await fetchTopicData(fieldsCleared, abortController);

      if (!abortController.signal.aborted) {
        const parsedResult = processResult2(result);
        setCompare({ sentiment: [...parsedResult] });
      }
    } catch (error) {
      if (!abortController.signal.aborted)
        console.error("Error fetching data:", error);
    } finally {
      if (!abortController.signal.aborted) setLoading(false);
    }
  };

  // Fetch data when fieldsCleared or date changes
  useEffect(() => {
    const abortController = new AbortController();
    console.log("Fetch effect triggered with:", {
      fieldsCleared,
      date,
      platform,
    });
    if (fieldsCleared.length) {
      getData(fieldsCleared, abortController);
    }

    return () => abortController.abort();
  }, [fieldsCleared, date, platform]);

  // Prepare series and time for ExportMenu
  const series = fieldsCleared.map((field, index) => ({
    name: field.q || field.name || field.user_name || `Sentiment ${index + 1}`,
    data: sentiment[index]
      ? [
          sentiment[index].positive?.count || 0,
          sentiment[index].neutral?.count || 0,
          sentiment[index].negative?.count || 0,
        ]
      : [0, 0, 0],
    time: ["Positive", "Neutral", "Negative"],
  }));

  const time = ["Positive", "Neutral", "Negative"];

  if (!fields.filter((user) => user.id || user.q).length) {
    return null;
  }

  return (
    <div className="flex compare-sentiment-container">
      <Card className="px-0">
        <div className="w-full flex flex-col gap-6">
          <div className="px-6 flex items-center justify-between">
            <Title title="تحلیل احساسات" />
            <ExportMenu
              chartSelector=".compare-sentiment-container"
              fileName="sentiment"
              series={series}
              time={time}
              excelHeaders={["Sentiment", ...series.map((s) => s.name)]}
              onError={(error) => console.error("Export error:", error)}
              menuItems={["PNG", "JPEG", "Excel"]}
            />
          </div>
          {loading ? (
            <div className="w-full h-[400px] flex justify-center items-center">
              <SpinnerGap size={40} className="animate-spin" />
            </div>
          ) : (
            <div className="flex items-center justify-center gap-4">
              <SentimentCard
                name="positive"
                data={countAvg(sentiment, "positive")}
              />
              <SentimentCard
                name="neutral"
                data={countAvg(sentiment, "neutral")}
              />
              <SentimentCard
                name="negative"
                data={countAvg(sentiment, "negative")}
              />
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

Sentiment.propTypes = {
  compareType: PropTypes.string.isRequired,
};

export default memo(Sentiment);
