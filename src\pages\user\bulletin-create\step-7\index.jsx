import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";
import { CButton } from "components/ui/CButton";
import { FileText, MicrosoftWordLogo } from "@phosphor-icons/react";
import { Link } from "react-router-dom";
import { useBulletinStore } from "store/bulletinStore.js";
import { ToastContainer } from "react-toastify";

const Step7 = () => {
  // const breadcrumbList = [{ title: "نام بولتن خاص" }];
  // useBreadcrumb(breadcrumbList);
  const setBulletin = useBulletinStore((state) => state.setBulletin);
  const { link } = useBulletinStore((state) => state.bulletin);

  return (
    <div className="flex justify-center items-center">
      <div className="flex flex-col justify-center items-center mt-16 w-[460px]">
        <FileText size={30} />
        <p className="font-subtitle-large my-6">
          فایل بولتن ساخته شد و قابل دانلود است
        </p>

        <div className="flex items-center justify-center gap-4 w-full">
          <Link
            to={"/app/bulletin/list"}
            className=" w-full"
            onClick={() => {
              setBulletin({
                step: 1,
              });
            }}
          >
            <CButton mode="outline">صفحه لیست بولتن</CButton>
          </Link>

          <CButton rightIcon={<MicrosoftWordLogo size={19} />}>
            <a href={`${link}`} target={"_blank"} rel="noreferrer">
              دانلود فایل
            </a>
          </CButton>
        </div>
      </div>
      <ToastContainer />
    </div>
  );
};

export default Step7;
