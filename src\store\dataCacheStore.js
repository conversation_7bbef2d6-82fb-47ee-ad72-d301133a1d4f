// store/chartDataStore.js
import { create } from "zustand";

const useChartDataStore = create((set) => ({
  chartData: {},
  setChartData: (key, data) =>
    set((state) => ({
      chartData: { ...state.chartData, [key]: data },
    })),
  clearChartData: (key) =>
    set((state) => {
      const newChartData = { ...state.chartData };
      delete newChartData[key];
      return { chartData: newChartData };
    }),
  clearAllChartData: () =>
    set(() => ({
      chartData: {},
    })),
}));

export default useChartDataStore;
