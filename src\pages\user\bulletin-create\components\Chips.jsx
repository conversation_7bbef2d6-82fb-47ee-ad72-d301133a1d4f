import React from "react";

const Chips = ({ children, isSelect = false, value, handleChange }) => {
  return (
    <div
      className="h-7 rounded-lg flex items-center justify-center w-full font-body-small cursor-pointer px-2 py-[6px]"
      style={{
        backgroundColor: isSelect ? "#5133E44D" : "#E1E8EF40",
        outline: isSelect ? "none" : "1px solid #D1D6DB",
      }}
      onClick={() => handleChange(value)}
    >
      {children}
    </div>
  );
};

export default Chips;
