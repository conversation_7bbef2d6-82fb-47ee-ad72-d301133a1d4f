import PLATFORMS from "constants/platforms.js";

export function convertFilterObjectToDataakQueryString(filterObject) {
  try {
    // const duration = filterObject.date.to.getTime() - filterObject.date.from.getTime();
    const fromTime = new Date(filterObject.date?.from);
    const toTime = new Date(filterObject.date?.to);

    // Get the time in seconds since the Unix epoch
    const from = parseInt(fromTime.getTime() / 1000);
    const to = parseInt(toTime.getTime() / 1000);

    let api_path = "";

    if (filterObject.platform) {
      if (filterObject.platform === "all")
        api_path += "api_path=/multi-platform/posts&want_tag_content=1";
      if (filterObject.platform === PLATFORMS.NEWS)
        api_path += "api_path=/news/posts&want_tag_content=1";
      if (filterObject.platform === PLATFORMS.TWITTER)
        api_path += "api_path=/twitter/posts&want_tag_content=1";
      if (filterObject.platform === PLATFORMS.INSTAGRAM)
        api_path += "api_path=/instagram/posts&want_tag_content=1";
      if (filterObject.platform === PLATFORMS.TELEGRAM)
        api_path += "api_path=/telegram/posts&want_tag_content=1";
      // if (filterObject.platform === "eitaa")
      //   api_path +=
      //     "api_path=/iranian-platforms/posts&want_tag_content=1&platforms[0]=eitaa_channel_post";
    }

    // api_path += `&q=${filterObject.q}&time_duration=${duration}&page=1&count=10&sort=time&sort_type=desc`;
    api_path += `&q=${encodeURIComponent(filterObject.q)}&from=${from}&to=${to}&page=${filterObject.page || "1"}&count=12&sort=${filterObject.sort || "time"}&sort_type=desc`;

    if (filterObject.language && filterObject.language?.includes("fa"))
      api_path += "&langs[0]=fa";
    if (filterObject.keywords) {
      for (let i = 0; i < filterObject.keywords?.length; i++) {
        api_path += `&extracted_phrases[${i}]=${filterObject.keywords[i]}`;
      }
    }
    if (filterObject.hashtags) {
      for (let i = 0; i < filterObject.hashtags?.length; i++) {
        api_path += `&extracted_hashtags[${i}]=${filterObject.hashtags[i]}`;
      }
    }
    if (filterObject.reshare) {
      if (filterObject.reshare.includes("republish"))
        api_path += "&source=copy";
      if (filterObject.reshare.includes("publish"))
        api_path += "&source=original";
    }
    if (filterObject.sentiment) {
      if (filterObject.sentiment.includes("positive"))
        api_path += "&sentiment_positive_min=50&sentiment_positive_max=100";
      if (filterObject.sentiment.includes("negative"))
        api_path += "&sentiment_negative_min=50&sentiment_negative_max=100";
      if (filterObject.sentiment.includes("neutral"))
        api_path += "&sentiment_neutral_min=50&sentiment_neutral_max=100";
    }
    if (filterObject.subjectCategory) {
      let categoryIndex = 0;
      let subCategoryIndex = 0;
      if (filterObject.subjectCategory.includes("varzeshi"))
        api_path += `&categories[${categoryIndex++}]=Varzeshi&sub_categories[${subCategoryIndex++}]=KoshtiVaVazneBardari&sub_categories[${subCategoryIndex++}]=SaiereHozeha&sub_categories[${subCategoryIndex++}]=FootballIran&sub_categories[${subCategoryIndex++}]=FootballJahan&sub_categories[${subCategoryIndex++}]=Razmi&sub_categories[${subCategoryIndex++}]=ToopVaTour`;
      if (filterObject.subjectCategory.includes("eghtesadi"))
        api_path += `&categories[${categoryIndex++}]=Eghtesadi&sub_categories[${subCategoryIndex++}]=EghtesadKalanVaBoodje&sub_categories[${subCategoryIndex++}]=TaavonVaEshteghal&sub_categories[${subCategoryIndex++}]=Boors&sub_categories[${subCategoryIndex++}]=BimeVaBank&sub_categories[${subCategoryIndex++}]=RahVaMaskan&sub_categories[${subCategoryIndex++}]=SanatTejaratBazargani&sub_categories[${subCategoryIndex++}]=KeshavarziVaOmooreDam&sub_categories[${subCategoryIndex++}]=NaftVaEnergy&sub_categories[${subCategoryIndex++}]=EghtesadeBinolmelal`;
      if (filterObject.subjectCategory.includes("honar"))
        api_path += `&categories[${categoryIndex++}]=FarhangHonarVaResane&sub_categories[${subCategoryIndex++}]=DiniVaMazhabi&sub_categories[${subCategoryIndex++}]=RadioVaTV&sub_categories[${subCategoryIndex++}]=CinemaVaTheatre&sub_categories[${subCategoryIndex++}]=MousighiVaHonarhaieTajasomi`;
      if (filterObject.subjectCategory.includes("international"))
        api_path += `&categories[${categoryIndex++}]=Beinolmelal&sub_categories[${subCategoryIndex++}]=AmricaVaEurope&sub_categories[${subCategoryIndex++}]=SharghAsiaVaOghianousie&sub_categories[${subCategoryIndex++}]=GharbAsia&sub_categories[${subCategoryIndex++}]=Africa`;
      if (filterObject.subjectCategory.includes("ejtemaie"))
        api_path += `&categories[${categoryIndex++}]=Ejtemaee&sub_categories[${subCategoryIndex++}]=Salamat&sub_categories[${subCategoryIndex++}]=AmouzeshVaParvaresh&sub_categories[${subCategoryIndex++}]=HoghoughiVaGhazaee&sub_categories[${subCategoryIndex++}]=EntezamiVaHavades&sub_categories[${subCategoryIndex++}]=MohitZistVaGardeshgari&sub_categories[${subCategoryIndex++}]=Shahri&sub_categories[${subCategoryIndex++}]=ZananVaJavanan&sub_categories[${subCategoryIndex++}]=RefahVaAsibhaieEjtemaee`;
      if (filterObject.subjectCategory.includes("siasi"))
        api_path += `&categories[${categoryIndex++}]=Siasi&sub_categories[${subCategoryIndex++}]=EmamVaRahbari&sub_categories[${subCategoryIndex++}]=AhzabVaTashakolha&sub_categories[${subCategoryIndex++}]=AmniatiVaDefaee&sub_categories[${subCategoryIndex++}]=Dolat&sub_categories[${subCategoryIndex++}]=Majles&sub_categories[${subCategoryIndex++}]=TashakolhaieDaneshghai`;
      if (filterObject.subjectCategory.includes("elmi"))
        api_path += `&categories[${categoryIndex++}]=ElmiVaDaneshghai&sub_categories[${subCategoryIndex++}]=Amouzesh&sub_categories[${subCategoryIndex++}]=ElamVaFanavariIran&sub_categories[${subCategoryIndex++}]=ElamVaFanavariJahan`;
      if (filterObject.subjectCategory.includes("roozmare"))
        api_path += `&categories[${categoryIndex++}]=Roozmare&sub_categories[${subCategoryIndex++}]=Roozmare`;
    }

    if (filterObject.gender) {
      if (filterObject.gender.includes("male")) api_path += "&gender[0]=male";
      if (filterObject.gender.includes("female"))
        api_path += "&gender[0]=female";
    }

    if (filterObject.resource) {
      for (let i = 0; i < filterObject.resource?.length; i++) {
        api_path += `&resource_ids[${i}]=${filterObject.resource[i]?.id}&post_resource_ids[${i}]=${filterObject.resource[i]?.id}`;
      }
    }

    if (filterObject.impact && filterObject.impact[0] !== "all") {
      for (let i = 0; i < filterObject.impact?.length; i++)
        api_path += `&resource_influence_categories[${i}]=${filterObject.impact[i]}`;
    }
    return api_path;
  } catch (e) {
    console.log(e);
    return "";
  }
}

export function convertResponseObjectToCardObject(responseObject) {
  try {
    let result = [];
    for (let data in responseObject) {
      let platform = null;
      if (
        data.avatar?.includes("twitter") ||
        data.url?.includes("twitter") ||
        data.platform?.includes("twitter")
      ) {
        platform = "twitter";
      }
      if (
        data.avatar?.includes("instagram") ||
        data.url?.includes("instagram") ||
        data.platform?.includes("instagram")
      ) {
        platform = "instagram";
      }
      if (
        data.avatar?.includes("telegram") ||
        data.url?.includes("telegram") ||
        data.platform?.includes("telegram")
      ) {
        platform = "telegram";
      }
      if (
        data.avatar?.includes("eitaa") ||
        data.url?.includes("eitaa") ||
        data.platform?.includes("eitaa")
      ) {
        platform = "eitaa";
      }
      if (
        data.avatar?.includes("news") ||
        data.url?.includes("news") ||
        data.platform?.includes("news")
      ) {
        platform = "news";
      }

      result.push({
        avatar: data.avatar,
        description: data.description,
        platform: platform,
        url: data.url,
        username: data.username,
        time: data.time,
        like_count: data.like_count || 0,
        copy_count: data.copy_count || 0,
        comment_count: data.comment_count || 0,
        view_count: data.view_count || 0,
        title: data.title,
        robot_see_date: data.robot_see_date,
        sentiment: data.sentiment,
        sentiment_negative: data.sentiment_negative,
        sentiment_neutral: data.sentiment_neutral,
        sentiment_positive: data.sentiment_positive,
        categories: data.categories,
      });
    }

    return result;
  } catch (e) {
    console.log(e);
    return [];
  }
}

export function convertFilterObjectToSourceQueryString(filterObject) {
  try {
    const fromTime = new Date(filterObject.date?.from);
    const toTime = new Date(filterObject.date?.to);

    const from = parseInt(fromTime.getTime() / 1000);
    const to = parseInt(toTime.getTime() / 1000);

    let api_path = "";

    if (filterObject.platform) {
      if (filterObject.platform === "all")
        api_path += "api_path=/multi-platform/posts&want_tag_content=1";
      if (filterObject.platform === PLATFORMS.NEWS)
        api_path += "api_path=/news/posts&want_tag_content=1";
      if (filterObject.platform === PLATFORMS.TWITTER)
        api_path += "api_path=/twitter/posts&want_tag_content=1";
      if (filterObject.platform === PLATFORMS.INSTAGRAM)
        api_path += "api_path=/instagram/posts&want_tag_content=1";
      if (filterObject.platform === PLATFORMS.TELEGRAM)
        api_path += "api_path=/telegram/posts&want_tag_content=1";
    }

    api_path += `&from=${from}&to=${to}&page=${filterObject.page || "1"}&count=12&sort=${filterObject.sort || "time"}&sort_type=desc`;

    if (filterObject.language && filterObject.language?.includes("fa"))
      api_path += "&langs[0]=fa";

    if (filterObject.keywords) {
      for (let i = 0; i < filterObject.keywords?.length; i++) {
        api_path += `&extracted_phrases[${i}]=${filterObject.keywords[i]}`;
      }
    }

    if (filterObject.hashtags) {
      for (let i = 0; i < filterObject.hashtags?.length; i++) {
        api_path += `&extracted_hashtags[${i}]=${filterObject.hashtags[i]}`;
      }
    }

    if (filterObject.reshare) {
      if (filterObject.reshare.includes("republish"))
        api_path += "&source=copy";
      if (filterObject.reshare.includes("publish"))
        api_path += "&source=original";
    }

    if (filterObject.sentiment) {
      if (filterObject.sentiment.includes("positive"))
        api_path += "&sentiment_positive_min=50&sentiment_positive_max=100";
      if (filterObject.sentiment.includes("negative"))
        api_path += "&sentiment_negative_min=50&sentiment_negative_max=100";
      if (filterObject.sentiment.includes("neutral"))
        api_path += "&sentiment_neutral_min=50&sentiment_neutral_max=100";
    }

    if (filterObject.subjectCategory) {
      let categoryIndex = 0;
      let subCategoryIndex = 0;
      if (filterObject.subjectCategory.includes("varzeshi"))
        api_path += `&categories[${categoryIndex++}]=Varzeshi&sub_categories[${subCategoryIndex++}]=KoshtiVaVazneBardari&sub_categories[${subCategoryIndex++}]=SaiereHozeha&sub_categories[${subCategoryIndex++}]=FootballIran&sub_categories[${subCategoryIndex++}]=FootballJahan&sub_categories[${subCategoryIndex++}]=Razmi&sub_categories[${subCategoryIndex++}]=ToopVaTour`;
      if (filterObject.subjectCategory.includes("eghtesadi"))
        api_path += `&categories[${categoryIndex++}]=Eghtesadi&sub_categories[${subCategoryIndex++}]=EghtesadKalanVaBoodje&sub_categories[${subCategoryIndex++}]=TaavonVaEshteghal&sub_categories[${subCategoryIndex++}]=Boors&sub_categories[${subCategoryIndex++}]=BimeVaBank&sub_categories[${subCategoryIndex++}]=RahVaMaskan&sub_categories[${subCategoryIndex++}]=SanatTejaratBazargani&sub_categories[${subCategoryIndex++}]=KeshavarziVaOmooreDam&sub_categories[${subCategoryIndex++}]=NaftVaEnergy&sub_categories[${subCategoryIndex++}]=EghtesadeBinolmelal`;
      if (filterObject.subjectCategory.includes("honar"))
        api_path += `&categories[${categoryIndex++}]=FarhangHonarVaResane&sub_categories[${subCategoryIndex++}]=DiniVaMazhabi&sub_categories[${subCategoryIndex++}]=RadioVaTV&sub_categories[${subCategoryIndex++}]=CinemaVaTheatre&sub_categories[${subCategoryIndex++}]=MousighiVaHonarhaieTajasomi`;
    }

    if (filterObject.gender) {
      if (filterObject.gender.includes("male")) api_path += "&gender[0]=male";
      if (filterObject.gender.includes("female"))
        api_path += "&gender[0]=female";
    }

    if (filterObject.resource) {
      for (let i = 0; i < filterObject.resource?.length; i++) {
        api_path += `&resource_ids[${i}]=${filterObject.resource[i]?.id}&post_resource_ids[${i}]=${filterObject.resource[i]?.id}`;
      }
    }

    if (filterObject.impact && filterObject.impact[0] !== "all") {
      for (let i = 0; i < filterObject.impact?.length; i++)
        api_path += `&resource_influence_categories[${i}]=${filterObject.impact[i]}`;
    }
    return api_path;
  } catch (e) {
    console.log(e);
    return "";
  }
}

