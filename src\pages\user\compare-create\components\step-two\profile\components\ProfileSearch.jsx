import { useCallback, useEffect, useRef, useState } from "react";

import PropTypes from "prop-types";
import { SpinnerGap } from "@phosphor-icons/react";
import debounce from "lodash.debounce";

import advanceSearch from "service/api/advanceSearch.js";
import SearchCard from "../../charts/SearchCard.jsx";
import { buildRequestData } from "utils/requestData.js";
import { useCompareStore } from "store/compareStore.js";

export const ProfileSearch = ({
  id,
  size = "md",
  inset = false,
  state = "rest",
  validation = "none",
  direction = "rtl",
  placeholder,
  value,
  headingIcon,
  disabled,
  className,
  onChange,
  onBlur,
  onFocus,
  inputProps,
  platform,
}) => {
  const { date } = useCompareStore((state) => state.compare);
  const [inputClasses, setInputClasses] = useState("");
  const [inputValue, setInputValue] = useState(value || "");
  const [selectedList, setSelectedList] = useState({});
  const [activeDropDown, setActiveDropdown] = useState(false);
  const [dropdownItems, setDropdownItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [prevPlatform, setPrevPlatform] = useState(platform);

  const dropdownRef = useRef(null);

  const initInput = () => {};

  const handleInputChange = (e) => {
    setInputValue(e.target.value);
    debouncedSearch(e.target.value);
  };

  const search = async (query) => {
    if (query.length < 2) {
      setActiveDropdown(false);
      return;
    }

    const filters = {
      date,
      platform: platform,
      q: query,
    };

    setLoading(true);
    try {
      const infoQuery = buildRequestData(filters, "search_in_source");
      const res = await advanceSearch.search(infoQuery);
      const parsedResponse = res?.data?.data?.[filters.platform] || [];
      setDropdownItems(parsedResponse);
      if (parsedResponse.length) setActiveDropdown(true);
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  const debouncedSearch = useCallback(debounce(search, 500), [platform]);

  const getInputClasses = useCallback(() => {
    let baseClass = "c-input";
    let classes = baseClass;
    classes += ` ${baseClass}-${size}`;
    if (inset) classes += ` ${baseClass}-inset`;
    classes += ` ${baseClass}-${state}`;
    classes += ` ${baseClass}-${direction}`;
    classes += ` ${className || ""}`;
    // return 'cinput cinput-sm cinput-rest cinput-ltr';
    return classes;
  }, [className, disabled, state, validation, size, inset, direction]);

  useEffect(() => {
    initInput();
  }, []);

  useEffect(() => {
    if (platform === prevPlatform) return;
    setSelectedList([]);
    setInputValue("");
    setPrevPlatform(platform);
    setActiveDropdown(false);
  }, [platform]);

  useEffect(() => {
    if (inputValue && inputValue !== "") setActiveDropdown(true);
    else setActiveDropdown(false);
  }, [inputValue]);

  useEffect(() => {
    setInputClasses(getInputClasses());
  }, [disabled, getInputClasses, validation]);

  useEffect(() => {
    onChange(selectedList);
  }, [selectedList]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setActiveDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const onItemSelect = (item) => {
    const { compare, setCompare } = useCompareStore.getState();

    const updatedFields = compare.fields.map((field) => ({
      ...field,
      source: item?.source_name || item?.user_name,
    }));

    setCompare({ fields: updatedFields });

    setSelectedList(item);
    setInputValue("");
  };

  useEffect(() => {
    if (inputValue) {
      search(inputValue);
    }
  }, []);

  const reqOnFocus = () => {
    if (inputValue) {
      search(inputValue);
    }
  };

  return (
    <div className={inputClasses} ref={dropdownRef}>
      <div className={"input-wrapper"}>
        {headingIcon && (
          <span className="action-icon text-left">{headingIcon}</span>
        )}
        <input
          type={"text"}
          id={id}
          name={name}
          placeholder={placeholder}
          onChange={handleInputChange}
          onBlur={onBlur}
          onFocus={onFocus || reqOnFocus}
          value={inputValue}
          // onKeyDown={onKeyDown}
          {...inputProps}
        />
      </div>

      {activeDropDown && (
        <div className={"input-dropdown"}>
          <ul className={"dropdown-wrapper"}>
            {loading ? (
              <SpinnerGap
                className={"animate-spin text-center w-full"}
              ></SpinnerGap>
            ) : (
              <>
                {dropdownItems?.length > 0 ? (
                  dropdownItems.map((item) => (
                    <SearchCard
                      onSelect={onItemSelect}
                      item={item}
                      platform={platform}
                      key={item.id}
                    />
                  ))
                ) : (
                  <li className={"text-center justify-center"}>
                    <span className={"inline-flex flex-1 text-body-small"}>
                      موردی یافت نشد
                    </span>
                  </li>
                )}
              </>
            )}
          </ul>
        </div>
      )}
    </div>
  );
};

ProfileSearch.propTypes = {
  id: PropTypes.string,
  size: PropTypes.oneOf(["sm", "md", "lg"]),
  inset: PropTypes.bool,
  state: PropTypes.oneOf(["rest", "typing", "filled", "disabled", "read-only"]),
  validation: PropTypes.oneOf(["none", "success", "error"]),
  direction: PropTypes.oneOf(["rtl", "ltr"]),
  placeholder: PropTypes.string,
  value: PropTypes.string,
  headingIcon: PropTypes.element,
  disabled: PropTypes.bool,
  platform: PropTypes.string,
  className: PropTypes.string,
  onChange: PropTypes.func,
  onFocus: PropTypes.func,
  onBlur: PropTypes.func,
  inputProps: PropTypes.object,
};

// const sample = {
//     "ages": "30-39",
//     "m3_age": "30-39",
//     "id": "1558856343",
//     "url": null,
//     "lang": "fa",
//     "image": "https://cdn1.picuki.com/hosted-by-instagram/q/yep6IPkO1EBGZyPbcMUVzOdehqB3RAlP.jpeg",
//     "score": 2,
//     "title": "parham rahnamaee studio",
//     "avatar": "files/instagram/2023_3_26/qkatglTG7maT2lQIkjDKmvGnxViUN8AqOSh2R5u6eQWYe3lpC1H7Fp1zc9eKz6tN.jpg",
//     "gender": "org",
//     "post_count": "40",
//     "like_count": null,
//     "comment_count": null,
//     "nationality": "fa",
//     "username": "parhamr2",
//     "influence": 22.924544382901267,
//     "influence_category": "low",
//     "robot_see_date": "2023-04-16 15:03:46",
//     "is_private": false,
//     "is_verified": false,
//     "description": "⚜  parham rahnamaee studio ⚜   wedding studio   09123888720  kamraniyeh",
//     "media_count": "40",
//     "follower_count": 215928,
//     "fake_follower_percentage": null,
//     "influence_date": 1687312683937,
//     "following_count": 16,
// }
