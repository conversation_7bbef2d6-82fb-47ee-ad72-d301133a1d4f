import PropTypes from "prop-types";
import InputWithEdit from "./InputWithEdit.jsx";
import Accordion from "components/ui/Accordion.jsx";
import { useBulletinStore } from "store/bulletinStore.js";

const IntroSettingSection = ({
  isOpen = false,
  selectedInput,
  setSelectedInput,
}) => {
  const { metadata } = useBulletinStore((state) => state.bulletin);

  return (
    <Accordion
      isOpen={isOpen}
      showBox={false}
      Header={<div className="font-subtitle-large">تنظیمات صفحه مقدمه</div>}
    >
      <div className="flex flex-col gap-6">
        <div className="w-1/2">
          <InputWithEdit
            FontSettingsPosition="right"
            handleClick={(id) =>
              setSelectedInput((l) => (l === "introduction_title" ? "" : id))
            }
            title="عنوان مقدمه"
            placeholder={"عنوان مقدمه بولتن"}
            name="introduction_title"
            selected={selectedInput === "introduction_title"}
            value={metadata?.introduction_title}
          />
        </div>
        <div>
          <InputWithEdit
            FontSettingsPosition="left"
            handleClick={(id) =>
              setSelectedInput((l) =>
                l === "introduction_description" ? "" : id,
              )
            }
            title="متن مقدمه"
            placeholder={"متن مقدمه بولتن"}
            name="introduction_description"
            selected={selectedInput === "introduction_description"}
            value={metadata?.introduction_description}
          />
        </div>
      </div>
    </Accordion>
  );
};

IntroSettingSection.propTypes = {
  isOpen: PropTypes.bool,
  selectedInput: PropTypes.string,
  setSelectedInput: PropTypes.func,
  metadata: PropTypes.object,
};

export default IntroSettingSection;
