import { Info, Plus } from "@phosphor-icons/react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import Drawer from "components/Drawer";
import AlertInfo from "./AlertInfo";
import { CButton } from "components/ui/CButton.jsx";
const AlertListHeader = () => {
  const [showMore, setShowMore] = useState(false);
  const navigate = useNavigate();

  return (
    <div className="pt-6 mb-4">
      <div className="flex flex-row-reverse items-center gap-2 font-button-large text-light-primary-text-rest">
        <div className="w-fit" onClick={() => setShowMore(true)}>
          <CButton
            mode="outline"
            size="sm"
            role="neutral"
            className="gap-2 !h-10"
          >
            <Info size={17} className="text-light-neutral-text-medium" />
            <p className="text-light-neutral-text-medium font-button-medium">
              هشدار چیست
            </p>
          </CButton>
        </div>
        <CButton
          onClick={() => navigate("/app/alert/create")}
          rightIcon={<Plus />}
          width={135}
          className={"[direction:ltr]"}
          mode="outline"
        >
          هشدار جدید
        </CButton>
      </div>
      {showMore && (
        <Drawer setShowMore={setShowMore}>
          <AlertInfo />
        </Drawer>
      )}
    </div>
  );
};

export default AlertListHeader;
