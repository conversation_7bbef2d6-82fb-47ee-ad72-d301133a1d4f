import { useEffect, useState } from "react";
import Wrapper from "./components/Wrapper";
import FilterListHeader from "./components/FIlterListHeader";
import Empty from "./components/Empty";
import List from "./components/List";
import { useLocation } from "react-router-dom";
import filter from "service/api/filter";
import { notification } from "utils/helper";
import { CheckCircle } from "@phosphor-icons/react";
import { ToastContainer } from "react-toastify";
import Loading from "components/ui/Loading";
import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";

const FilterList = () => {
  const { state } = useLocation();
  const breadcrumbList = [{ title: "فیلتر" }];
  useBreadcrumb(breadcrumbList);

  const [filters, setFilters] = useState([]);
  const [loading, setLoading] = useState(false);
  const [rerender, setRerender] = useState(false);

  const getFilters = async () => {
    setLoading(true);
    try {
      const {
        data: { data },
      } = await filter.get();
      setFilters(data);
    } catch (error) {
      console.log(error.response.data.message);
    }
    setLoading(false);
  };

  useEffect(() => {
    if (state?.success) {
      notification.success(
        state.message,
        <CheckCircle className="text-light-success-text-rest" />,
      );
    }
    window.history.replaceState({}, "");
  }, []);

  useEffect(() => {
    getFilters();
  }, [rerender]);

  return (
    <>
      {loading && <Loading />}
      <div className="flex flex-col h-full w-full px-6">
        <div>
          <FilterListHeader />
        </div>
        <Wrapper>
          {filters?.user?.length || filters?.group?.length ? (
            <List
              data={filters}
              setRerender={setRerender}
              setLoading={setLoading}
            />
          ) : (
            <Empty />
          )}
        </Wrapper>
      </div>
      <ToastContainer />
    </>
  );
};

export default FilterList;
