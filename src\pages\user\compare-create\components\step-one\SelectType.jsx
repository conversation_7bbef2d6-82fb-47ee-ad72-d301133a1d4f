import { useEffect, useState } from "react";
import clsx from "clsx";

const SelectType = ({ handleChange = () => {} }) => {
  const [type, setType] = useState("");
  useEffect(() => {
    handleChange(type);
  }, [type]);
  return (
    <div className="flex flex-col gap-4">
      <span className="font-subtitle-large">نوع مقایسه را انتخاب کنید</span>
      <div className="flex gap-[34px]">
        <div
          className={clsx(
            "flex flex-col items-center gap-4 w-[273px] h-[248px] p-4 text-justify border rounded-lg cursor-pointer",
            type === "topic"
              ? "border-light-primary-border-rest bg-light-primary-background-highlight"
              : "border-light-neutral-border-low-rest",
          )}
          onClick={() => setType("topic")}
        >
          <img src="/Compare-Subject.png" alt="compare subject" />
          <span>مقایسه موضوعی</span>
          <span className="font-body-small text-light-neutral-text-medium">
            این مقایسه بر اساس کلمات جست‌وجو شده و اعمال فیلترهای مختلف انجام
            می‌شود
          </span>
        </div>
        <div
          className={clsx(
            "flex flex-col items-center gap-4 w-[273px] h-[248px] p-4 text-justify border rounded-lg cursor-pointer",
            type === "profile"
              ? "border-light-primary-border-rest bg-light-primary-background-highlight"
              : "border-light-neutral-border-low-rest",
          )}
          onClick={() => setType("profile")}
        >
          <img src="/Compare-Profile.png" alt="compare profile" />
          <span>مقایسه پروفایلی</span>
          <span className="font-body-small text-light-neutral-text-medium">
            این مقایسه بر پروفایل کاربران در شبکه‌های اجتماعی خواهد بود
          </span>
        </div>
      </div>
    </div>
  );
};

export default SelectType;
