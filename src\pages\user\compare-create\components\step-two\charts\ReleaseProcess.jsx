import { memo, useEffect, useState } from "react";
import advanceSearch from "service/api/advanceSearch.js";
import { SpinnerGap } from "@phosphor-icons/react";
import { useCompareStore } from "store/compareStore.js";
import { Card } from "components/ui/Card.jsx";
import PropTypes from "prop-types";
import ReleaseChart from "components/Charts/LineChart.jsx";
import Title from "./Title.jsx";
import ExportMenu from "components/ExportMenu/index.jsx";
import { buildRequestData } from "utils/requestData.js";

const ReleaseChartContainer = ({ compareType }) => {
  const { fields, platform, date } = useCompareStore((state) => state.compare);
  const [series, setSeries] = useState([]);
  const [time, setTime] = useState([]);
  const [loading, setLoading] = useState(false);
  const [fieldsCleared, setFieldsCleared] = useState([]);
  useEffect(() => {
    const newFieldsCleared = fields.filter((value) =>
      compareType === "profile" ? value?.id || value?.user_id : value?.q
    );
    const hasChanged =
      newFieldsCleared.length !== fieldsCleared.length ||
      newFieldsCleared.some((field, index) => {
        const prevField = fieldsCleared[index] || {};
        return compareType === "profile"
          ? field.id !== prevField.id || field.user_id !== prevField.user_id
          : field.q !== prevField.q;
      });

    if (hasChanged) {
      console.log("fieldsCleared updated:", newFieldsCleared);
      setFieldsCleared(newFieldsCleared);
    }
  }, [fields, compareType]);

  const fetchChartData = async (abortController) => {
    setLoading(true);
    let newSeries = [];
    let newTime = [];
    try {
      for (const field of fieldsCleared) {
        const filters = { date, platform };
        if (compareType === "topic") filters["q"] = field?.q || "";
        else
          filters["sources"] = field?.user_id
            ? [field?.user_id.toString()]
            : field?.id
            ? [field?.id.toString()]
            : [];

        const infoQuery = buildRequestData(filters, "process");
        const res = await advanceSearch.search(infoQuery, abortController);

        const result = res.data.data?.[filters.platform];
        const timeData = result.map((item) => item.time || item.datetime);
        const seriesData = result.map((item) => item.count);

        newSeries.push({
          name: `${field.q || field.name || field.user_name}`,
          data: seriesData,
          time: timeData,
        });
        newTime.push(...timeData);
      }
    } catch (error) {
      if (!abortController.signal.aborted) {
        console.error("Error fetching topic chart data:", error);
      }
    }
    if (!abortController.signal.aborted) {
      setSeries(newSeries);
      setTime(newTime);
      setLoading(false);
    }
  };

  useEffect(() => {
    const abortController = new AbortController();
    if (fieldsCleared.length > 0) {
      console.log("Fetch effect triggered with:", {
        fieldsCleared,
        date,
        platform,
      });
      fetchChartData(abortController);
    }
    return () => abortController.abort();
  }, [fieldsCleared, date, platform]);

  return (
    <div className="flex compare-process-container">
      {fieldsCleared.length > 0 &&
        (loading ? (
          <Card
            className={`flex flex-col gap-4 ${
              fieldsCleared.length < 4
                ? "w-[83%]"
                : fieldsCleared.length === 4
                ? "w-full"
                : null
            }`}
          >
            <div className="!flex w-full h-[440px] justify-center items-center">
              <SpinnerGap size={40} className="animate-spin" />
            </div>
          </Card>
        ) : (
          <Card
            className={`${
              fieldsCleared.length < 4
                ? "w-[83%]"
                : fieldsCleared.length === 4
                ? "w-full"
                : null
            }`}
          >
            <div className="w-full">
              <div className="pb-3 flex items-center justify-between">
                <Title title="روند انتشار محتوا" />
                <ExportMenu
                  chartSelector=".compare-process-container"
                  fileName="process"
                  series={series}
                  time={time}
                  excelHeaders={["Time", ...series.map((s) => s.name)]}
                  onError={(error) => console.error("Export error:", error)}
                  menuItems={["PNG", "JPEG", "Excel"]}
                />
              </div>
              <div>
                <ReleaseChart series={series} time={time} isCompare={true} />
              </div>
            </div>
          </Card>
        ))}
    </div>
  );
};

ReleaseChartContainer.propTypes = {
  compareType: PropTypes.string.isRequired,
};

export default memo(ReleaseChartContainer);
