import { useBreadcrumb } from "hooks/useBreadcrumb";
import WaveActivities from "./components/WaveActivities";
import { CButton } from "components/ui/CButton";
import { CaretLeft, CaretRight, Info } from "@phosphor-icons/react";
import React, { useEffect, useRef, useState } from "react";
import Divider from "components/ui/Divider";
import WaveCardInfo from "./components/WaveCardInfo";
import Drawer from "components/Drawer";
import WaveAnalyticsGuide from "./components/WaveAnalyticsGuide";
import Circlepacking from "assets/images/waves/Circlepacking.png";
import Radialcluster from "assets/images/waves/Radialcluster.png";
import Tree from "assets/images/waves/Tree.png";
import Wave from "assets/images/waves/Waveactivities.png";
import RadialClusterChart from "./components/RadialCluster";
import CirclePackingChart from "./components/CirclePacking";
import { Card } from "components/ui/Card";
import waveAnalytics from "service/api/waveAnalytics";
import tabs from "./constants/waveAnalysisTabs";
import SingleDropDown from "components/ui/SingleDropDown";
import TreeMap from "./components/treeChart";
import DatePicker from "react-multi-date-picker";
import persian from "react-date-object/calendars/persian";
import persian_fa from "react-date-object/locales/persian_fa";

const WaveAnalysis = () => {
  const breadcrumbList = [{ title: "موج شناسی" }];
  useBreadcrumb(breadcrumbList);
  const [selectedChart, setSelectedChart] = useState("waveActivities");
  const [openDrawer, setOpenDrawer] = useState(false);
  const [activeEnglishTabName, setActiveEnglishTabName] = useState("all");
  const [activeTab, setActiveTab] = useState("همه موج‌ها");
  const [data, setData] = useState([]);
  const [selectedNode, setSelectedNode] = useState("");
  const [waveSort, setWaveSort] = useState("Impact");
  const [newestFilter, setNewestFilter] = useState("all");
  const [sortedData, setSortedData] = useState([]);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [currentHeight, setCurrentHeight] = useState(591);
  const fullAnalysisHandler = async (date) => {
    try {
      const startDate = new Date(date);
      const endDate = new Date(date);
      const { data } = await waveAnalytics.sendWaveData({
        report_type: "trends",
        platform: "twitter",
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString(),
      });
      setData(data?.data);
    } catch (error) {
      console.log(error);
    }
  };
  const handleDateChange = (date) => {
    if (date) {
      setSelectedDate(date);
      const jsDate = new Date(date.toDate());
      fullAnalysisHandler(jsDate);
    }
  };
  useEffect(() => {
    const today = new Date();
    setSelectedDate(selectedDate);
    fullAnalysisHandler(today);
  }, []);
  let increasing = {
    name: "موج‌های افزایشی",
    color: "#F60000",
    data: [],
    marker: {
      fillColor: {
        radialGradient: { cx: 0.4, cy: 0.3, r: 0.7 },
        stops: [
          [0, "white"],
          [1, "#F60000"],
        ],
      },
    },
  };
  let decreasing = {
    name: "موج‌های کاهشی",
    color: "#1371F4",
    data: [],
    marker: {
      fillColor: {
        radialGradient: { cx: 0.4, cy: 0.3, r: 0.7 },
        stops: [
          [0, "white"],
          [1, "#1371F4"],
        ],
      },
    },
  };
  let potential = {
    name: "موج‌های بالقوه",
    color: "#E9A506",
    data: [],
    marker: {
      fillColor: {
        radialGradient: { cx: 0.4, cy: 0.3, r: 0.7 },
        stops: [
          [0, "white"],
          [1, "#E9A506"],
        ],
      },
    },
  };
  let frequent = {
    name: "موج‌های فراگیر",
    color: "#04A286",
    data: [],
    marker: {
      fillColor: {
        radialGradient: { cx: 0.4, cy: 0.3, r: 0.7 },
        stops: [
          [0, "white"],
          [1, "#04A286"],
        ],
      },
    },
  };
  data?.trends?.map((item) => {
    if (item?.trend_type === "Increasing") {
      increasing?.data?.push({
        x: item?.stats?.posts,
        y: item?.stats?.impact,
        z: item?.stats?.users,
        title: item?.title,
      });
    } else if (item?.trend_type === "Decreasing") {
      decreasing?.data?.push({
        x: item?.stats?.posts,
        y: item?.stats?.impact,
        z: item?.stats?.users,
        title: item?.title,
      });
    } else if (item?.trend_type === "Potential") {
      potential?.data?.push({
        x: item?.stats?.posts,
        y: item?.stats?.impact,
        z: item?.stats?.users,
        title: item?.title,
      });
    } else if (item?.trend_type === "Frequent") {
      frequent?.data?.push({
        x: item?.stats?.posts,
        y: item?.stats?.impact,
        z: item?.stats?.users,
        title: item?.title,
      });
    }
  });
  useEffect(() => {
    if (activeTab === "موج شناسی") {
      setActiveEnglishTabName("all");
    } else if (activeTab === "موج‌های بالقوه") {
      setActiveEnglishTabName("Potential");
    } else if (activeTab === "موج‌های افزایشی") {
      setActiveEnglishTabName("Increasing");
    } else if (activeTab === "موج‌های کاهشی") {
      setActiveEnglishTabName("Decreasing");
    } else if (activeTab === "موج‌های فراگیر") {
      setActiveEnglishTabName("Frequent");
    }
  }, [activeTab]);
  const badgeRef = useRef({});
  useEffect(() => {
    if (badgeRef.current[selectedNode]) {
      badgeRef.current[selectedNode].scrollIntoView({
        behavior: "smooth",
        block: "center",
      });
    }
  }, [selectedNode]);
  useEffect(() => {
    const sortData = () => {
      let sortedTrends = [...(data?.trends || [])];
      if (newestFilter === "newest") {
        sortedTrends = sortedTrends.filter((item) => item?.is_new);
      } else if (newestFilter === "repetitive") {
        sortedTrends = sortedTrends.filter((item) => !item?.is_new);
      }
      switch (waveSort) {
        case "Impact":
          sortedTrends.sort((a, b) => b.stats.impact - a.stats.impact);
          break;
        case "Users":
          sortedTrends.sort((a, b) => b.stats.users - a.stats.users);
          break;
        case "Viral":
          sortedTrends.sort((a, b) => b.stats.likes - a.stats.likes);
          break;
        default:
          break;
      }
      setSortedData(sortedTrends);
    };
    if (data?.trends) {
      sortData();
    }
  }, [waveSort, data?.trends, newestFilter]);
  const chartOptions = [
    { type: "tree", image: Tree },
    { type: "circlePacking", image: Circlepacking },
    { type: "radialCluster", image: Radialcluster },
    { type: "waveActivities", image: Wave },
  ];
  const containerRef = useRef(null);
  useEffect(() => {
    if (containerRef.current) {
      const height = containerRef.current.offsetHeight;
      setCurrentHeight(height);
    }
  }, [selectedChart]);
  return (
    <div className="grid grid-cols-12 gap-2 px-6">
      <div className="col-span-12">
        <div className="flex items-center justify-between mb-1">
          <div className="flex items-center font-body-medium gap-4">
            {tabs?.map((item) => (
              <div key={item?.title} className="flex items-center gap-1">
                <div
                  className="rounded-full p-[2px]"
                  style={{
                    background: item?.background,
                    color: item?.color,
                    fontSize: 18,
                  }}
                >
                  {item.icon ? (
                    React.createElement(item.icon)
                  ) : (
                    <img className="w-[20px] h-[20px]" src={item?.img} />
                  )}
                </div>
                <p
                  className={`cursor-pointer ${
                    item?.title === activeTab
                      ? "border-b-2 border-light-primary-text-rest text-black"
                      : "text-light-neutral-text-medium"
                  }`}
                  onClick={() => setActiveTab(item?.title)}
                >
                  {item?.title}
                </p>
              </div>
            ))}
          </div>
          <div className="w-fit" onClick={() => setOpenDrawer(true)}>
            <CButton
              mode="outline"
              size="sm"
              role="neutral"
              className="gap-2 !h-10"
            >
              <Info size={17} className="text-light-neutral-text-medium" />
              <p className="text-light-neutral-text-medium font-button-medium">
                راهنمای موج‌شناسی
              </p>
            </CButton>
          </div>
        </div>
        <Divider />
      </div>
      {openDrawer && (
        <Drawer setShowMore={setOpenDrawer}>
          <WaveAnalyticsGuide />
        </Drawer>
      )}
      <div className="flex flex-col xl:col-span-7 col-span-8 gap-3 !w-full">
        <div>
          {selectedChart === "tree" && (
            <TreeMap
              active={activeTab}
              data={data}
              setSelectedNode={setSelectedNode}
              containerRef={containerRef}
            />
          )}
          {selectedChart === "circlePacking" && (
            <CirclePackingChart
              active={activeTab}
              data={data}
              setSelectedNode={setSelectedNode}
              containerRef={containerRef}
            />
          )}
          {selectedChart === "radialCluster" && (
            <RadialClusterChart
              active={activeTab}
              data={data}
              setSelectedNode={setSelectedNode}
              containerRef={containerRef}
            />
          )}
          {selectedChart === "waveActivities" && (
            <WaveActivities
              containerRef={containerRef}
              selectedNode={selectedNode}
              setSelectedNode={setSelectedNode}
              data={
                activeTab === "همه موج‌ها"
                  ? [increasing, decreasing, frequent, potential]
                  : activeTab === "موج‌های بالقوه"
                  ? [potential]
                  : activeTab === "موج‌های افزایشی"
                  ? [increasing]
                  : activeTab === "موج‌های کاهشی"
                  ? [decreasing]
                  : activeTab === "موج‌های فراگیر"
                  ? [frequent]
                  : []
              }
            />
          )}
        </div>

        <div className="flex justify-center items-center gap-2">
          {chartOptions.map(({ type, image }) => (
            <Card
              key={type}
              className={`h-[85%] !w-[9%] cursor-pointer flex justify-center items-center -mt-5 transition-colors duration-200 border rounded-lg ${
                selectedChart === type
                  ? "!bg-[#e9e6f7] border-purple-600"
                  : "bg-white hover:bg-[#e9e6f7] border-transparent"
              }`}
              onClick={() => setSelectedChart(type)}
              aria-selected={selectedChart === type}
              role="button"
              tabIndex={0}
            >
              <img
                src={image}
                className="w-full object-contain"
                alt={`${type} chart`}
              />
            </Card>
          ))}
        </div>
      </div>
      <div className="col-span-5 row-span-1">
        <div className=" gap-2 mb-2 flex">
          <div className="w-80">
            <SingleDropDown
              subsets={[
                { text: "ضریب تاثیرگذاری", value: "Impact" },
                { text: "تعداد کاربران", value: "Users" },
                { text: "میزان فراگیری", value: "Viral" },
              ]}
              title="مرتب‌سازی بر اساس"
              handleChange={(e) => setWaveSort(e?.value)}
              disabled={false}
            />
          </div>

          <div className="w-80">
            <SingleDropDown
              subsets={[
                { text: "همه", value: "all" },
                { text: "موج جدید", value: "newest" },
                { text: "موج تکراری", value: "repetitive" },
              ]}
              title="فیلتر بر اساس نوع موج"
              handleChange={(e) => setNewestFilter(e?.value)}
              disabled={false}
            />
          </div>

          <div className="w-80">
            <p className="font-body-medium mb-1">فیلتر بر اساس تاریخ</p>
            <div
              className="flex gap-4 items-center justify-between p-1 rounded-lg bg-light-neutral-background-medium border border-light-neutral-border-medium-rest w-full"
              style={{
                border: open && "1px solid #9198AD",
              }}
            >
              <DatePicker
                value={selectedDate}
                calendar={persian}
                locale={persian_fa}
                calendarPosition="bottom-right"
                containerClassName="datepicker-container"
                onChange={handleDateChange}
                renderButton={(direction, handleClick) => (
                  <button onClick={handleClick}>
                    {direction === "right" ? (
                      <CaretLeft size={20} />
                    ) : (
                      <CaretRight size={20} />
                    )}
                  </button>
                )}
              />
            </div>
          </div>
        </div>
        <div
          className="col-span-5 overflow-auto scrollbar-thin"
          style={{ height: currentHeight }}
        >
          {activeTab !== "همه موج‌ها" &&
            sortedData
              ?.filter((item) => item?.trend_type === activeEnglishTabName)
              .map((item) => (
                <div
                  className="mb-2"
                  key={item?.id}
                  ref={(el) => (badgeRef.current[item?.title] = el)}
                >
                  <WaveCardInfo
                    type={
                      activeTab === "موج‌های بالقوه"
                        ? "trend"
                        : activeTab === "موج‌های افزایشی"
                        ? "increasing"
                        : activeTab === "موج‌های فراگیر"
                        ? "neutral"
                        : activeTab === "موج‌های کاهشی"
                        ? "decreasing"
                        : "increasing"
                    }
                    title={item?.title}
                    impact={item?.stats?.impact}
                    badgeList={item?.elements?.map((x) => x?.content)}
                    isNew={item?.is_new}
                    elements={item?.elements}
                    date={data?.date}
                    query_start_time={data?.query_start_time}
                    query_end_time={data?.query_end_time}
                    selectedNode={selectedNode}
                  />
                </div>
              ))}
          {activeTab === "همه موج‌ها" &&
            sortedData.map((item) => (
              <div
                className="mb-2"
                key={item?.id}
                ref={(el) => (badgeRef.current[item?.title] = el)}
              >
                <WaveCardInfo
                  type={
                    item?.trend_type === "Potential"
                      ? "trend"
                      : item?.trend_type === "Increasing"
                      ? "increasing"
                      : item?.trend_type === "Decreasing"
                      ? "decreasing"
                      : item?.trend_type === "Frequent"
                      ? "neutral"
                      : ""
                  }
                  id={item?.id}
                  title={item?.title}
                  impact={item?.stats?.impact}
                  badgeList={item?.elements?.map((item) => item?.content)}
                  isNew={item?.is_new}
                  elements={item?.elements}
                  date={data?.date}
                  query_start_time={data?.query_start_time}
                  query_end_time={data?.query_end_time}
                  selectedNode={selectedNode}
                />
              </div>
            ))}
        </div>
      </div>
    </div>
  );
};

export default WaveAnalysis;
