import { useState } from "react";
import Step1 from "./steps/step-1";
import Step2 from "./steps/step-2";

const GroupsCreate = () => {
  const [step, setStep] = useState(1);

  return (
    <div className="min-h-full pt-4 flex items-center justify-center">
      <div className="flex flex-col gap-6 w-full">
        {step === 1 && <Step1 setStep={setStep} step={step} />}
        {step === 2 && <Step2 setStep={setStep} step={step} />}
      </div>
    </div>
  );
};

export default GroupsCreate;
