import { useEffect, useState } from "react";
import Loading from "components/ui/Loading";
import { ToastContainer } from "react-toastify";
import Empty from "./components/Empty";
import Wrapper from "./components/Wrapper";
import { useBreadcrumb } from "hooks/useBreadcrumb";
import TicketsTable from "./components/TicketsTable";
import ticket from "service/api/ticket";
import SearchBar from "./components/SearchBar";
import formatDate from "./utils";
import Paginate from "components/ui/Paginate";
const AdminTicketList = () => {
  const [isLoading, setIsLoading] = useState(false);
  const breadcrumbList = [{ title: "تیکت‌" }];
  useBreadcrumb(breadcrumbList);
  const [tickets, setTickets] = useState([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const oneMonthInMillis = 1 * 30 * 24 * 60 * 60 * 1000;
  const [filter, setFilter] = useState({
    q: "",
    created_at: {
      date_from: formatDate(new Date(new Date().getTime() - oneMonthInMillis)),
      end_date: formatDate(new Date()),
    },
  });
  const searchTicket = async () => {
    setIsLoading(true);
    try {
      let newFiler = {};
      if (filter.q.trim() !== "") newFiler.q = filter.q;
      newFiler.created_at = filter?.created_at;
      newFiler.sort_rules = filter?.sort_rules;
      let newQuery = {};
      newQuery.page = 1;
      newQuery.page_size = 15;
      newQuery.status = filter.status;
      newQuery.section = filter.section;
      const {
        data: { data },
      } = await ticket.searchTicket(newFiler, newQuery);
      setCurrentPage(data);
      setTickets(data?.tickets);
    } catch (error) {
      console.log(error.response.data.message);
    }
    setIsLoading(false);
    console.log(7)
  };
  useEffect(() => {
    searchTicket();
  }, [filter]);

  return (
    <>
      {loading ? (
        <Loading />
      ) : (
        <>
          <div className="flex flex-col h-full w-full px-6">
            <div className="mb-5">
              <SearchBar
                filter={filter}
                loading={loading}
                setFilter={setFilter}
              />
            </div>
            <Wrapper setFilter={setFilter}>
              {tickets.length > 0 ? (
                tickets.map((item) => (
                  <TicketsTable key={item.id} data={item} />
                ))
              ) : (
                <Empty />
              )}
            </Wrapper>
          </div>
        </>
      )}
      <div>
        <Paginate
          page={page}
          setPage={setPage}
          dataCount={tickets?.length}
          per_page={10}
        />
      </div>
      <ToastContainer />
    </>
  );
};

export default AdminTicketList;
