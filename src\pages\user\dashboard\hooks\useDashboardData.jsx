import { useCallback, useEffect, useState } from "react";
import dashboard from "service/api/dashboard.js";
import useSearchStore from "store/searchStore.js";

const useFetchClusterData = (
  filters,
  setClusterData,
  setClusterListActiveTab,
  setLoading,
) => {
  return useCallback(async () => {
    setLoading(true);
    try {
      const res = await dashboard.getData({
        platform: filters.platform,
      });

      if (res.data?.status !== "OK") {
        setLoading(false);
        return;
      }
      setClusterData(res.data?.data?.cluster_data || {});
      setClusterListActiveTab({ name: "all", index: -1 });
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  }, [filters, setClusterData, setClusterListActiveTab, setLoading]);
};

export const useDashboardData = () => {
  const { filters } = useSearchStore();
  const [clusterData, setClusterData] = useState({});
  const [loading, setLoading] = useState(false);
  const [clusterListActiveTab, setClusterListActiveTab] = useState({
    name: "all",
    index: -1,
  });

  const fetchData = useFetchClusterData(
    filters,
    setClusterData,
    setClusterListActiveTab,
    setLoading,
  );

  useEffect(() => {
    fetchData();
  }, [filters.platform, fetchData]);

  return {
    clusterData,
    loading,
    clusterListActiveTab,
    setClusterListActiveTab,
  };
};
