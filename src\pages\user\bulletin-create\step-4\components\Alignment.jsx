import { useEffect, useState } from "react";
import {
  TextAlignCenter,
  TextAlignJustify,
  TextAlignLeft,
  TextAlignRight,
} from "@phosphor-icons/react";
import clsx from "clsx";

const Alignment = ({ initialValue, handleChange }) => {
  const [align, setAlign] = useState(initialValue?.alignment || "center");
  useEffect(() => {
    handleChange({ alignment: align });
  }, [align]);
  return (
    <div className="flex items-center *:p-[7px] *:rounded-lg *:flex *:items-center *:justify-center *:size-8 *:cursor-pointer">
      <div
        className={clsx(
          "",
          align === "right" && "bg-light-primary-background-highlight",
        )}
        onClick={() => setAlign("right")}
      >
        <TextAlignRight size={18} />
      </div>
      <div
        className={clsx(
          "",
          align === "center" && "bg-light-primary-background-highlight",
        )}
        onClick={() => setAlign("center")}
      >
        <TextAlignCenter size={18} />
      </div>
      <div
        className={clsx(
          "",
          align === "left" && "bg-light-primary-background-highlight",
        )}
        onClick={() => setAlign("left")}
      >
        <TextAlignLeft size={18} />
      </div>
      <div
        className={clsx(
          "",
          align === "justify" && "bg-light-primary-background-highlight",
        )}
        onClick={() => setAlign("justify")}
      >
        <TextAlignJustify size={18} />
      </div>
    </div>
  );
};

export default Alignment;
