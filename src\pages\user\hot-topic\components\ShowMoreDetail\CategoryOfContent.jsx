const CategoryOfContent = () => {
  return (
    <div className="font-subtitle-medium">
      <h2 className="mb-4">دسته‌بندی مطالب</h2>
      <div className="flex justify-between">
        <div className="w-1/3 flex flex-col gap-4">
          <label className="flex gap-2 items-center">
            <input type="checkbox" className="accent-[#6F5CD1] size-4" />
            <span>همه</span>
          </label>
          <label className="flex gap-2 items-center">
            <input type="checkbox" className="accent-[#6F5CD1] size-4" />
            <span>ورزشی</span>
          </label>
          <label className="flex gap-2 items-center">
            <input type="checkbox" className="accent-[#6F5CD1] size-4" />
            <span>اقتصادی</span>
          </label>
          <label className="flex gap-2 items-center">
            <input type="checkbox" className="accent-[#6F5CD1] size-4" />
            <span>هنر و رسانه</span>
          </label>
          <label className="flex gap-2 items-center">
            <input type="checkbox" className="accent-[#6F5CD1] size-4" />
            <span>بین‌الملل</span>
          </label>
        </div>

        <div className="w-1/3 flex flex-col gap-4">
          <label className="flex gap-2 items-center">
            <input type="checkbox" className="accent-[#6F5CD1] size-4" />
            <span>اجتماعی</span>
          </label>
          <label className="flex gap-2 items-center">
            <input type="checkbox" className="accent-[#6F5CD1] size-4" />
            <span>سیاسی</span>
          </label>
          <label className="flex gap-2 items-center">
            <input type="checkbox" className="accent-[#6F5CD1] size-4" />
            <span>علمی</span>
          </label>
          <label className="flex gap-2 items-center">
            <input type="checkbox" className="accent-[#6F5CD1] size-4" />
            <span>روزمره</span>
          </label>
          <label className="flex gap-2 items-center">
            <input type="checkbox" className="accent-[#6F5CD1] size-4" />
            <span>فرهنگ</span>
          </label>
        </div>
      </div>
    </div>
  );
};

export default CategoryOfContent;
