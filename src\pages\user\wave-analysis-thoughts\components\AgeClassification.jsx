import { memo } from "react";
import Age from "./charts/Age";

const AgeClassification = ({ data }) => {
  if (!data?.length > 0) {
    return (
      <div className="bg-light-neutral-surface-card rounded-[8px] p-6 h-[450px]">
        <p className="font-subtitle-large pb-4">دسته‌بندی سن و سال</p>
        <div className="h-[240px] flex items-center justify-between mx-auto">
          <p className="py-1.5 text-center font-body-bold-large text-light-neutral-text-medium flex items-center justify-between mx-auto">
            داده ای یافت نشد
          </p>
        </div>
      </div>
    );
  }
  return (
    <div className="bg-light-neutral-surface-card rounded-[8px] p-6 h-full">
      <p className="font-subtitle-large pb-4">دسته‌بندی سن و سال</p>
      <Age width={260} agesData={data} />
    </div>
  );
};

export default memo(AgeClassification);
