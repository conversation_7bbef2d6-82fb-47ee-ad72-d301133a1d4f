import { Info, MagnifyingGlass, Plus, SpinnerGap } from "@phosphor-icons/react";
import Accordion from "components/ui/Accordion";
import Divider from "components/ui/Divider";
import { memo, useCallback, useState } from "react";
import RangeSlider from "components/ui/RangeSlider";
import NodeTypeCard from "./NodeTypeCard";
import { CInput } from "components/ui/CInput";
import { CButton } from "components/ui/CButton";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import SourceSelect from "./SourceSelect";
import PersonSelect from "./PersonSelect";
import { useRelationStore } from "store/relationDetectStore";
import { DateInput } from "./DateInput";
import PlaceSelect from "./PlaceSelect";
import OrgSelect from "./OrgSelect";
import { buildRequestData } from "utils/requestData";
import advanceSearch from "service/api/advanceSearch";
import nodeTypeData from "../constants/nodeType";
import connectivity_indicators from "../constants/connectivity_indicators";
import connectivity_type from "../constants/connectivity_type";

const EdgeSettings = () => {
  const [entityVal, setEntityVal] = useState("");
  const [selectedNode, setSelectedNode] = useState(1);
  const [date, setDate] = useState({
    from: new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000),
    to: new Date(),
  });
  const [loading, setLoading] = useState(false);
  const [activeDropDown, setActiveDropdown] = useState(false);
  const [dropdownItems, setDropdownItems] = useState([]);
  const [data, setData] = useState([]);
  const [nodesData, setNodesData] = useState([]);
  const [personNodesData, setPersonNodesData] = useState([]);
  const [organNodesData, setOrganNodesData] = useState([]);
  const [placeNodesData, setPlaceNodesData] = useState([]);

  const edgeTypeRenderer = {
    1: <SourceSelect nodesData={nodesData} data={data} setData={setData} />,
    2: (
      <PersonSelect
        personNodesData={personNodesData}
        data={data}
        setData={setData}
      />
    ),
    3: (
      <OrgSelect
        organNodesData={organNodesData}
        data={data}
        setData={setData}
      />
    ),
    4: (
      <PlaceSelect
        placeNodesData={placeNodesData}
        data={data}
        setData={setData}
      />
    ),
  };
  const setNodeDetailsBulk = useRelationStore(
    (state) => state?.setNodeDetailsBulk
  );
  const nodeDetails = useRelationStore((state) => state.relation.nodeDetails);
  const edgeDetails = useRelationStore((state) => state.relation.edgeDetails);
  const setType = useRelationStore((state) => state?.setType);
  const setEdgeDetails = useRelationStore((state) => state?.setEdgeDetails);
  const handleRelationTypeChange = (e) => {
    setEdgeDetails({ relationType: e.target.value });
  };
  const nodeRelationIndexChangeHandler = useCallback((e) => {
    const { value, checked } = e.target;
    setEdgeDetails({
      relationIndex: checked
        ? [...edgeDetails.relationIndex, value]
        : edgeDetails.relationIndex.filter((item) => item !== value),
    });
  }, []);
  const clearSearchData = () => {
    setNodesData([]);
    setPersonNodesData([]);
    setOrganNodesData([]);
    setPlaceNodesData([]);
  };
  const search = async () => {
    if (entityVal.length < 2) {
      setActiveDropdown(false);
      return;
    }
    setLoading(true);
    clearSearchData();
    try {
      const filterObject = {
        platform: selectedNode === 1 ? "twitter" : "all",
        q: entityVal,
        page: 1,
        rows: 10,
        date: {
          from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 24 hours ago
          to: new Date(Date.now()), // Now
        },
      };
      const req = buildRequestData(
        filterObject,
        selectedNode === 1 ? "search_in_source" : "similar_phrases"
      );
      const responseStinas = await advanceSearch.search(
        req,
        false,
        selectedNode === 2
          ? { cloud_type: "person" }
          : selectedNode === 3
          ? { cloud_type: "organ" }
          : selectedNode === 4
          ? { cloud_type: "location" }
          : ""
      );
      const parsedResponse = responseStinas?.data?.data?.["twitter"];
      selectedNode === 1
        ? setNodesData(parsedResponse)
        : selectedNode === 2
        ? setPersonNodesData(responseStinas?.data?.data?.phrases)
        : selectedNode === 3
        ? setOrganNodesData(responseStinas?.data?.data?.phrases)
        : setPlaceNodesData(responseStinas?.data?.data?.phrases);
      if (parsedResponse) {
        setDropdownItems(parsedResponse);
        setActiveDropdown(true);
      }
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };
  const searchNodeHandler = () => search();
  return (
    <div className="grid gap-3 mb-14">
      <Accordion
        Header={
          <div className="grid items-center">
            <h2 className="font-body-bold-large">تعیین گره‌ها</h2>
            <span className="text-light-neutral-text-low font-body-small">
              برای ایجاد گراف باید حداقل ۲ گره را انتخاب کرد.
            </span>
          </div>
        }
        isOpen
      >
        <div className="font-body-large">
          <p className="mb-2">نوع گره را انتخاب کنید</p>
          <div className="flex items-center justify-around my-4">
            {nodeTypeData?.map((item) => (
              <div
                key={item.id}
                onClick={() => {
                  setSelectedNode(item.id);
                  setType(item?.type);
                }}
              >
                <NodeTypeCard
                  img={item.img}
                  title={item.title}
                  isSelected={selectedNode === item.id}
                />
              </div>
            ))}
          </div>
          <div className="mt-8 mb-3">
            <p className="-mb-2">عنوان گره را جست‌و‌جو کنید</p>
            <div className="flex items-center gap-2">
              <CInput
                value={entityVal}
                onChange={(e) => setEntityVal(e?.target?.value)}
                inputProps={{
                  onKeyPress: (e) => {
                    if (e.key === "Enter") {
                      searchNodeHandler();
                    }
                  },
                }}
                headingIcon={<MagnifyingGlass />}
                placeholder="نام موجودیت مورد نظر را برای جست‌وجو وارد کنید"
                className="w-full pt-5"
              />
              <div className="w-20">
                <CButton
                  readOnly={!entityVal.trim() || loading}
                  onClick={searchNodeHandler}
                >
                  جست‌وجو
                </CButton>
              </div>
            </div>
          </div>
          {loading ? (
            <div className="flex justify-center mb-10">
              <SpinnerGap size={26} className="animate-spin" />
            </div>
          ) : (
            entityVal.trim() && edgeTypeRenderer[selectedNode]
          )}
          <CButton
            leftIcon={<Plus size={18} className="ml-2" />}
            readOnly={data.length < 1}
            onClick={() => setNodeDetailsBulk(data)}
          >
            افزودن گره
          </CButton>
        </div>
      </Accordion>
      <Accordion
        Header={
          <div className="grid items-center">
            <h2 className="font-body-bold-large">تعریف یال</h2>
            <span className="text-light-neutral-text-low font-body-small">
              تنظیمات نمایش یال‌ها را تعیین کنید
            </span>
          </div>
        }
        disableToggle={nodeDetails.length < 2}
        isOpen={nodeDetails.length >= 2}
      >
        <div className="grid gap-3">
          <div className="flex items-center gap-1">
            <p className="font-body-large">نوع ارتباط بین گره‌ها</p>
            <Info />
          </div>

          {connectivity_type?.map((item) => (
            <div key={item?.id} className="flex items-center gap-2">
              <input
                name="relationType"
                value={item?.id}
                checked={edgeDetails.relationType === item?.id}
                onChange={handleRelationTypeChange}
                id={item?.id}
                type="radio"
              />
              <label
                htmlFor={item?.id}
                className="font-body-medium select-none cursor-pointer"
              >
                {item?.title}
              </label>
            </div>
          ))}
        </div>

        <Divider className={"my-4"} />
        <div className="grid gap-3">
          <div className="flex items-center gap-1">
            <p className="font-body-large">شاخص‌های ارتباط بین گره‌ها</p>
            <Info />
          </div>

          {connectivity_indicators?.map((item) => (
            <div key={item?.id} className="flex items-center gap-2">
              <input
                name={item?.id}
                value={item?.id}
                checked={edgeDetails?.relationIndex?.includes(item?.id)}
                onChange={nodeRelationIndexChangeHandler}
                id={item?.id}
                type="checkbox"
              />
              <label
                htmlFor={item?.id}
                className="font-body-medium select-none cursor-pointer"
              >
                {item?.title}
              </label>
            </div>
          ))}
        </div>
        <Divider className={"my-4"} />
        <div className="flex items-center gap-1 mb-2">
          <p className="font-body-large">وزن گره‌ها</p>
          <Info />
        </div>
        <RangeSlider
          value={edgeDetails?.nodeWeight - 1}
          onChange={(e) => {
            setEdgeDetails({ nodeWeight: parseInt(e.target.value) + 1 });
          }}
          values={[
            { value: 1 },
            { value: 2 },
            { value: 3 },
            { value: 4 },
            { value: 5 },
            { value: 6 },
            { value: 7 },
            { value: 8 },
            { value: 9 },
            { value: 10 },
          ]}
        />
      </Accordion>
      <Accordion
        Header={
          <div className="grid items-center">
            <h2 className="font-body-bold-large">تنظیمات زمان</h2>
            <span className="text-light-neutral-text-low font-body-small">
              بازه زمانی بررسی گراف را تعیین کنید
            </span>
          </div>
        }
        disableToggle={nodeDetails.length < 2}
        isOpen={nodeDetails.length >= 2}
      >
        <DateInput
          id={"date"}
          name={"date"}
          inset={true}
          size={"lg"}
          validation={"none"}
          direction={"rtl"}
          className={"flex-1"}
          field={{}}
          form={{ errors: [], touched: [] }}
          value={date}
          onChange={(e) => {
            setEdgeDetails({ timeSettings: e });
          }}
        />
      </Accordion>
    </div>
  );
};

export default memo(EdgeSettings);
