import { CaretDown, Plus } from "@phosphor-icons/react";
import { CButton } from "components/ui/CButton";
import { useState } from "react";
import { Link } from "react-router-dom";

const Wrapper = ({ children, setFilter }) => {
  const [isDown, setIsDown] = useState(false);
  return (
    <div className="p-4 flex flex-col gap-4 rounded-lg bg-light-neutral-surface-card shadow-[0px_2px_20px_0px_#0000000D]">
      <div className="flex items-center justify-between">
        <div className="font-subtitle-medium text-light-neutral-text-high mb-4">
          لیست تیکت‌ها
        </div>
        <Link to={"/admin/ticket/create"}>
          <CButton
            rightIcon={<Plus />}
            width={200}
            className={"[direction:ltr]"}
            mode="outline"
          >
            تیکت جدید
          </CButton>
        </Link>
      </div>
      <ul className="font-overline-medium text-light-neutral-text-medium grid grid-cols-6 select-none">
        <li>شناسه</li>
        <li>نام کاربری</li>
        <li>عنوان تیکت</li>
        <li
          className="cursor-pointer flex items-center gap-1"
          onClick={() => {
            setFilter((prev) => ({
              ...prev,
              sort_rules: [
                {
                  field: "created_at",
                  direction:
                    prev.sort_rules?.[0]?.direction === "asc" ? "desc" : "asc",
                },
              ],
            }));
            setIsDown((prev) => !prev);
          }}
        >
          <p>تاریخ به‌روزرسانی</p>
          <CaretDown
            size={12}
            className={`${
              isDown ? "rotate-180 transition-all" : "transition-all"
            }`}
          />
        </li>

        <li>وضعیت تیکت</li>
        <li>واحد مربوطه</li>
      </ul>
      {children}
    </div>
  );
};

export default Wrapper;
