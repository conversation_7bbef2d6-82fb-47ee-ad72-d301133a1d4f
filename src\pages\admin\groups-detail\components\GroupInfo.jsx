import { Card } from "components/ui/Card";
import ChangeListState from "./ChangeListState";
import PopUp from "components/ui/PopUp";
import { useState } from "react";
import { TrashSimple, Warning, WarningCircle } from "@phosphor-icons/react";
import { notification } from "utils/helper";
import { useNavigate } from "react-router-dom";
import { CInput } from "components/ui/CInput";

const GroupInfo = ({ onActive }) => {
  const [isActivate, setIsActivate] = useState(false);
  const [isDelete, setIsDelete] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [isGroupActive, setIsGroupActive] = useState(true);
  const [groupName, setGroupName] = useState("نام گروه");
  const [groupDescription, setGroupDescription] = useState(
    "این گروه برای سازمان خاص و رده افراد خاص طراحی شده که به آن‌ها اختصاصی نوتیف بدهیم"
  );
  const [editName, setEditName] = useState(groupName);
  const [editDescription, setEditDescription] = useState(groupDescription);
  const ticketStates = [isGroupActive ? "successful" : "Unsuccessful"];
  const navigate = useNavigate();

  const randomState =
    ticketStates[Math.floor(Math.random() * ticketStates.length)];

  const ticketStateTitles = {
    successful: "فعال",
    Unsuccessful: "غیرفعال",
  };

  const handleActivateToggle = () => {
    if (isGroupActive) {
      setIsActivate(true);
    } else {
      setIsGroupActive(true);
      onActive(false);
    }
  };

  const handleSubmitDeactivate = () => {
    notification.info(
      `گروه غیرفعال شد`,
      <WarningCircle size={25} className="text-light-inform-text-rest" />
    );
    onActive(true);
    setIsGroupActive(false);
    setIsActivate(false);
  };

  const handleDeleteGroup = () => {
    navigate("/admin/groups-list");
    setTimeout(() => {
      notification.error(
        `گروه حذف شد`,
        <WarningCircle size={25} className="text-light-error-text-rest" />
      );
    }, 100);
    setIsGroupActive(false);
    setIsDelete(false);
  };

  const handleDeleteClick = () => {
    if (isGroupActive) {
      setIsDelete(true);
    }
  };

  const handleEditClick = () => {
    if (isGroupActive) {
      setEditName(groupName);
      setEditDescription(groupDescription);
      setIsEdit(true);
    }
  };

  const handleEditSubmit = () => {
    setGroupName(editName);
    setGroupDescription(editDescription);
    notification.success(
      `اطلاعات گروه به‌روزرسانی شد`,
      <WarningCircle size={25} className="text-light-success-text-rest" />
    );
    setIsEdit(false);
  };

  return (
    <>
      <Card className="flex flex-col gap-5">
        <h3 className="font-subtitle-large flex justify-start">اطلاعات گروه</h3>
        <div className="w-full truncate">
          <h1 className="font-headline-large">{groupName}</h1>
          <p className="font-body-large break-words whitespace-pre-wrap">
            {groupDescription}
          </p>
        </div>
        <div className="flex justify-between items-center">
          <div className="flex flex-col gap-4 text-[#00000080]">
            <div>تعداد اعضا</div>
            <div>تاریخ ایجاد</div>
            <div>آخرین تغییرات</div>
            <div>وضعیت</div>
          </div>
          <div className="flex flex-col text-left gap-4">
            <div>۵۰۰ کاربر</div>
            <div>۱۴۰۲/۱۱/۰۱ - ۱۲:۳۴</div>
            <div>۱۴۰۲/۱۱/۰۱ - ۱۲:۳۴</div>
            <div className="font-body-medium flex justify-center">
              <ChangeListState
                title={ticketStateTitles[randomState] || ""}
                state={randomState}
              />
            </div>
          </div>
        </div>
        <div className="flex gap-6 w-full font-button-medium pt-6">
          <div
            className={`bg-light-neutral-background-medium rounded-lg w-full h-10 flex items-center justify-center ${
              isGroupActive
                ? "cursor-pointer hover:bg-light-neutral-background-dark"
                : "opacity-50 cursor-not-allowed"
            }`}
            onClick={handleEditClick}
          >
            ویرایش اطلاعات
          </div>
          <div
            className="bg-light-neutral-background-medium cursor-pointer rounded-lg w-full h-10 flex items-center justify-center hover:bg-light-neutral-background-dark"
            onClick={handleActivateToggle}
          >
            {isGroupActive ? "غیرفعال‌سازی" : "فعال‌سازی"}
          </div>
          <div
            className={`text-[#BE223C] border border-light-error-text-rest rounded-lg w-full h-10 flex items-center justify-center ${
              isGroupActive
                ? "cursor-pointer hover:bg-light-error-background-light"
                : "opacity-50 cursor-not-allowed border-none"
            }`}
            onClick={handleDeleteClick}
          >
            حذف گروه
          </div>
        </div>
      </Card>
      {/* Deactivate Popup */}
      <PopUp
        isOpen={isActivate}
        onClose={() => setIsActivate(false)}
        hasCloseIcon={false}
        submitHandler={handleSubmitDeactivate}
        width="545px"
        agreeButton="غیرفعال‌سازی گروه"
        agreeButtonMode={"outline"}
        agreeButtonRole={"error"}
      >
        <div className="!flex !justify-center">
          <div className="flex justify-center flex-col gap-4 items-center">
            <Warning size={68} color="#E99E0C" />
            <h2 className="font-subtitle-large pt-2">
              آیا می‌خواهید گروه را غیرفعال کنید؟
            </h2>
            <div className="font-body-medium">
              <p>
                در صورت غیرفعال کردن گروه، اقدامات زیر را نمی‌توانید انجام دهید:
              </p>
              <li>افزودن یا حذف افراد گروه</li>
              <li>ویرایش اطلاعات گروه</li>
              <li>ارسال اطلاعیه به گروه</li>
              <li>تنظیم پلن برای گروه</li>
              <p className="py-3">
                شما هر زمان که بخواهید می‌توانید گروه را فعال کنید
              </p>
            </div>
          </div>
        </div>
      </PopUp>
      {/* Delete Popup */}
      <PopUp
        isOpen={isDelete}
        onClose={() => setIsDelete(false)}
        hasCloseIcon={false}
        submitHandler={handleDeleteGroup}
        width="545px"
        agreeButton="حذف گروه"
        agreeButtonMode={"outline"}
        agreeButtonRole={"error"}
      >
        <div className="!flex !justify-center">
          <div className="flex justify-center flex-col gap-4 items-center">
            <TrashSimple size={68} color="#BE223C" />
            <h2 className="font-subtitle-large pt-2">
              آیا می‌خواهید گروه را حذف کنید؟
            </h2>
            <div className="font-body-medium">
              <p>
                در صورت حذف گروه امکان بازیابی و فعال‌سازی مجدد آن وجود ندارد{" "}
              </p>
            </div>
          </div>
        </div>
      </PopUp>
      {/* Edit Popup */}
      <PopUp
        isOpen={isEdit}
        onClose={() => setIsEdit(false)}
        hasCloseIcon={true}
        submitHandler={handleEditSubmit}
        width="667px"
        agreeButton="ذخیره"
      >
        <div className="!flex !justify-center.Physics is a natural science that studies matter, its fundamental constituents, its motion and behavior through space and time, and the related entities of energy and force. It is one of the most fundamental scientific disciplines, with its main goal being to understand how the universe behaves.">
          <form className="flex-1 bg-white mx-auto w-full rounded-lg">
            <CInput
              id="title"
              name="title"
              size="lg"
              validation="none"
              direction="rtl"
              title="عنوان گروه"
              placeholder="نام گروه یا سازمان موردنظر را بنویسید"
              className="w-full font-overline-large"
              value={editName}
              onChange={(e) => setEditName(e.target.value)}
            />
            <div className="mb-6">
              <label
                htmlFor="description"
                className="font-overline-large mb-2 inline-block"
              >
                توضیحات
              </label>
              <textarea
                id="description"
                name="description"
                placeholder="به عنوان مثال این گروه برای مدیران خاص و سازمان خاص است."
                className="w-full h-40 rounded-md p-4 font-body-large outline-none border border-light-neutral-border-medium-rest"
                value={editDescription}
                onChange={(e) => setEditDescription(e.target.value)}
              />
            </div>
          </form>
        </div>
      </PopUp>
    </>
  );
};

export default GroupInfo;
