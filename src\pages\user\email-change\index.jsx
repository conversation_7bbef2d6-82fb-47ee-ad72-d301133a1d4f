import { useContext, useState } from "react";
import SuccessMessage from "components/ui/SuccessMessage";
import NewEmail from "./components/NewEmail";
import VerifyEmail from "./components/VerifyEmail";
import AuthContext from "context/auth-context";
import { ToastContainer } from "react-toastify";

const EmailChange = () => {
  const [step, setStep] = useState(1);
  const { profile } = useContext(AuthContext);
  const renderStep = () => {
    switch (step) {
      case 1:
        return <VerifyEmail profile={profile} setStep={setStep} />;
      case 2:
        return <NewEmail setStep={setStep} />;
      case 3:
        return (
          <SuccessMessage
            title={"آدرس ایمیل با موفقیت تغییر کرد"}
            description={
              "از این پس برای ورود به سامانه تحلیل از این ایمیل استفاده کنید."
            }
            clickHandler={() => setStep(1)}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div>
      {renderStep()}
      <ToastContainer />
    </div>
  );
};

export default EmailChange;
