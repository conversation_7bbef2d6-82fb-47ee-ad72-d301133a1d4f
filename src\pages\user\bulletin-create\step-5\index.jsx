import { useBreadcrumb } from "hooks/useBreadcrumb.jsx";
import { useBulletinStore } from "store/bulletinStore";
import BulletinCard from "./components/BulletinCard";
import pdfImg from "./img/PDF.png";
import docImg from "./img/Doc.png";
import Bulletin from "service/api/bulletin.js";
import { notification } from "utils/helper.js";
import { CheckCircle } from "@phosphor-icons/react";
import { useEffect } from "react";
import { CButton } from "components/ui/CButton.jsx";
const Step5 = () => {
  const setBulletin = useBulletinStore((state) => state.setBulletin);
  const { bulletin } = useBulletinStore();
  const { type, title, export_type } = useBulletinStore(
    (state) => state.bulletin
  );

  const breadcrumbList = [{ title: "ایجاد بولتن" }];
  useBreadcrumb(breadcrumbList);

  const clickHandler = (exportType, checked) => {
    if (checked) {
      if (!export_type) setBulletin({ export_type: [exportType] });
      else setBulletin({ export_type: [...export_type, exportType] });
    } else {
      setBulletin({
        export_type: export_type.filter((item) => item !== exportType),
      });
    }
  };

  const submit = async () => {
    try {
      let data = JSON.parse(JSON.stringify(bulletin));
      if (type === "auto") {
        const response = await Bulletin.updatePeriodic(bulletin.id, data);
        if (response.data.code === 200 || response.data.code === 201) {
          setBulletin({ step: 6 });
        } else {
          notification.error(
            "خطا در ایجاد بولتن",
            <CheckCircle className="text-light-error-text-rest" size={26} />
          );
        }
      } else {
        data.content = data.contentManual;
        data.contentManual = null;

        const response = await Bulletin.updateManual(bulletin.id, data);
        if (response.data.code === 200 || response.data.code === 201) {
          setBulletin({ step: 6 });
        } else {
          notification.error(
            "خطا در ایجاد بولتن",
            <CheckCircle className="text-light-error-text-rest" size={26} />
          );
        }
      }

      return true;
    } catch (e) {
      console.error(e);
    }
  };

  return (
    <div className="flex flex-col justify-center items-center mt-16">
      <p className="font-subtitle-large mb-8">قالب فایل بولتن را انتخاب کنید</p>
      <div className="flex items-center gap-4">
        <BulletinCard
          image={pdfImg}
          caption={"ساخت فایل PDF"}
          name={"pdf"}
          handleClick={clickHandler}
          checked={export_type?.includes("pdf")}
        />
        <BulletinCard
          image={docImg}
          caption={"ساخت فایل Word"}
          name={"word"}
          handleClick={clickHandler}
          checked={export_type?.includes("word")}
        />
      </div>
      <div onClick={submit} className="w-[526px] mt-8">
        <CButton>ساخت فایل</CButton>
      </div>
    </div>
  );
};

export default Step5;
